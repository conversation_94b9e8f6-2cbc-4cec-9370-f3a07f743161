<?php
/**
 * Simplify Tournament Schema
 * Remove registration_id dependency and create direct department-tournament relationship
 * SC_IMS Sports Competition and Event Management System
 */

require_once '../../config/database.php';
require_once '../../includes/admin_auth.php';

// Require admin authentication
requireAdmin();

header('Content-Type: application/json');

$database = new Database();
$conn = $database->getConnection();

try {
    $conn->beginTransaction();
    
    // Drop the existing tournament_participants table if it exists
    $sql = "DROP TABLE IF EXISTS tournament_participants";
    $conn->exec($sql);
    
    // Create the simplified tournament_participants table
    $sql = "CREATE TABLE tournament_participants (
        id INT AUTO_INCREMENT PRIMARY KEY,
        tournament_structure_id INT NOT NULL,
        department_id INT NOT NULL,
        team_name VARCHAR(255),
        seed_number INT,
        current_status ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active',
        points DECIMAL(10,2) DEFAULT 0,
        wins INT DEFAULT 0,
        losses INT DEFAULT 0,
        draws INT DEFAULT 0,
        performance_data JSON,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
        FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
        UNIQUE KEY unique_tournament_department (tournament_structure_id, department_id)
    )";
    $conn->exec($sql);
    
    $conn->commit();
    
    echo json_encode([
        'success' => true,
        'message' => 'Tournament participants table successfully simplified with direct department-tournament relationships.'
    ]);
    
} catch (Exception $e) {
    $conn->rollBack();
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
