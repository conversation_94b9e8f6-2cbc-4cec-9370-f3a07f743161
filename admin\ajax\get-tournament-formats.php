<?php
/**
 * AJAX endpoint to get tournament formats based on sport type
 */

// Prevent any output before JSON response
ob_start();

require_once __DIR__ . '/../auth.php';
require_once __DIR__ . '/../../config/database.php';
require_once __DIR__ . '/../../includes/functions.php';

// Clear any output that might have been generated
ob_clean();

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

// Debug logging
error_log("Tournament formats AJAX called with POST data: " . print_r($_POST, true));

try {
    $sport_type = $_POST['sport_type'] ?? 'traditional';

    // Map sport types to database categories
    $type_mapping = [
        'traditional' => ['traditional', 'team', 'individual'],
        'team' => ['traditional', 'team'],
        'individual' => ['traditional', 'individual'],
        'academic' => ['academic'],
        'judged' => ['judged', 'performance'],
        'performance' => ['performance', 'judged']
    ];

    $sport_types = $type_mapping[$sport_type] ?? ['traditional', 'team', 'individual'];

    // Build WHERE clause for multiple sport types
    $where_conditions = [];
    $params = [];

    foreach ($sport_types as $type) {
        $where_conditions[] = "sport_types LIKE ? OR sport_types LIKE ? OR sport_types LIKE ? OR sport_types = ?";
        $params[] = $type . ',%';  // type at beginning
        $params[] = '%,' . $type . ',%';  // type in middle
        $params[] = '%,' . $type;  // type at end
        $params[] = $type;  // type alone
    }

    $where_clause = '(' . implode(') OR (', $where_conditions) . ')';

    // Check which field exists in the table
    $stmt = $conn->prepare("SHOW COLUMNS FROM tournament_formats LIKE 'sport_type%'");
    $stmt->execute();
    $columns = $stmt->fetchAll();

    $sport_type_field = 'sport_types'; // default
    $has_category_field = false;
    $has_types_field = false;

    foreach ($columns as $col) {
        if ($col['Field'] === 'sport_type_category') {
            $sport_type_field = 'sport_type_category';
            $has_category_field = true;
            break;
        } elseif ($col['Field'] === 'sport_types') {
            $has_types_field = true;
        }
    }

    // Adjust WHERE clause based on field type
    if ($has_category_field) {
        // Use ENUM field matching
        $where_clause = "sport_type_category IN ('" . implode("','", $sport_types) . "') OR sport_type_category = 'all'";
        $params = [];
    } elseif ($has_types_field) {
        // Use the original LIKE matching for sport_types field
        $sport_type_field = 'sport_types';
        // Keep the original where_conditions logic
    } else {
        // No sport type field found, create the table structure
        error_log("No sport type field found in tournament_formats table");
        throw new Exception("Tournament formats table structure is incomplete");
    }

    // Get tournament formats that match the sport type
    $stmt = $conn->prepare("
        SELECT id, name, code, description, {$sport_type_field} as sport_types,
               min_participants, max_participants, requires_seeding, advancement_type
        FROM tournament_formats
        WHERE {$where_clause}
        ORDER BY name
    ");

    $stmt->execute($params);
    $formats = $stmt->fetchAll();

    // Debug logging
    error_log("Tournament formats query - Sport type: $sport_type, Mapped types: " . implode(',', $sport_types) . ", Found formats: " . count($formats));
    error_log("Query used: SELECT id, name, code, description, {$sport_type_field} as sport_types, min_participants, max_participants, requires_seeding, advancement_type FROM tournament_formats WHERE {$where_clause}");

    // If no formats found, create basic ones
    if (empty($formats)) {
        error_log("No tournament formats found, creating basic ones...");

        // Create basic tournament formats if they don't exist
        $basic_formats = [
            [
                'name' => 'Single Elimination',
                'code' => 'single_elimination',
                'description' => 'Traditional knockout tournament where teams/participants are eliminated after one loss.',
                'sport_types' => 'team,individual',
                'min_participants' => 2,
                'max_participants' => null
            ],
            [
                'name' => 'Double Elimination',
                'code' => 'double_elimination',
                'description' => 'Two-bracket system with winner\'s and loser\'s brackets.',
                'sport_types' => 'team,individual',
                'min_participants' => 3,
                'max_participants' => null
            ],
            [
                'name' => 'Round Robin',
                'code' => 'round_robin',
                'description' => 'Every team/participant plays every other team/participant once.',
                'sport_types' => 'team,individual',
                'min_participants' => 3,
                'max_participants' => 16
            ],
            [
                'name' => 'Swiss System',
                'code' => 'swiss_system',
                'description' => 'Pairing system commonly used for academic competitions.',
                'sport_types' => 'academic',
                'min_participants' => 4,
                'max_participants' => null
            ],
            [
                'name' => 'Judged Rounds',
                'code' => 'judged_rounds',
                'description' => 'Multiple judged rounds with scoring criteria.',
                'sport_types' => 'judged,performance',
                'min_participants' => 3,
                'max_participants' => null
            ],
            [
                'name' => 'Performance Competition',
                'code' => 'performance_competition',
                'description' => 'Structured performance competition with multiple rounds.',
                'sport_types' => 'performance',
                'min_participants' => 3,
                'max_participants' => null
            ],
            [
                'name' => 'Knockout Rounds',
                'code' => 'knockout_rounds',
                'description' => 'Academic elimination tournament with question pools and time limits.',
                'sport_types' => 'academic',
                'min_participants' => 4,
                'max_participants' => null
            ],
            [
                'name' => 'Quiz Bowl Format',
                'code' => 'quiz_bowl',
                'description' => 'Round robin format specifically designed for quiz bowl competitions.',
                'sport_types' => 'academic',
                'min_participants' => 3,
                'max_participants' => 12
            ],
            [
                'name' => 'Talent Showcase',
                'code' => 'talent_showcase',
                'description' => 'Showcase format with multiple performance rounds and audience voting.',
                'sport_types' => 'judged,performance',
                'min_participants' => 3,
                'max_participants' => 50
            ],
            [
                'name' => 'Artistic Judging',
                'code' => 'artistic_judging',
                'description' => 'Comprehensive artistic competition with technical and artistic components.',
                'sport_types' => 'judged,performance',
                'min_participants' => 3,
                'max_participants' => 30
            ]
        ];
        
        foreach ($basic_formats as $format) {
            try {
                $stmt = $conn->prepare("
                    INSERT IGNORE INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    $format['name'],
                    $format['code'],
                    $format['description'],
                    $format['sport_types'],
                    $format['min_participants'],
                    $format['max_participants']
                ]);
                error_log("Created tournament format: " . $format['name']);
            } catch (Exception $e) {
                error_log("Error creating tournament format " . $format['name'] . ": " . $e->getMessage());
            }
        }
        
        // Re-fetch formats using the same logic
        $stmt = $conn->prepare("
            SELECT id, name, code, description, {$sport_type_field} as sport_types, min_participants, max_participants
            FROM tournament_formats
            WHERE {$where_clause}
            ORDER BY name
        ");
        $stmt->execute($params);
        $formats = $stmt->fetchAll();
    }
    
    // Format the response
    $formatted_formats = [];
    foreach ($formats as $format) {
        $formatted_formats[] = [
            'id' => $format['id'],
            'name' => $format['name'],
            'code' => $format['code'],
            'description' => $format['description'],
            'min_participants' => $format['min_participants'],
            'max_participants' => $format['max_participants'],
            'requires_seeding' => $format['requires_seeding'] ?? in_array($format['code'], ['single_elimination', 'double_elimination', 'multi_stage']),
            'advancement_type' => $format['advancement_type'] ?? (in_array($format['code'], ['round_robin', 'swiss_system', 'quiz_bowl', 'academic_round_robin']) ? 'points' : 'elimination')
        ];
    }
    
    $response = [
        'success' => true,
        'formats' => $formatted_formats,
        'debug' => [
            'sport_type' => $sport_type,
            'mapped_types' => $sport_types,
            'total_formats' => count($formatted_formats)
        ]
    ];

    error_log("Tournament formats response: " . json_encode($response));
    echo json_encode($response);

} catch (Exception $e) {
    $error_response = [
        'success' => false,
        'message' => 'Error loading tournament formats: ' . $e->getMessage(),
        'debug' => [
            'sport_type' => $_POST['sport_type'] ?? 'not set',
            'error_line' => $e->getLine(),
            'error_file' => $e->getFile()
        ]
    ];

    error_log("Tournament formats error: " . json_encode($error_response));
    echo json_encode($error_response);
}
?>
