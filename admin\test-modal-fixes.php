<?php
require_once '../includes/config.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';

requireAdmin();

$event_id = 4; // Test with event ID 4
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Fixes Test</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .btn {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary {
            background: #007bff;
            color: white;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: black;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1><i class="fas fa-bug"></i> Modal Fixes Test</h1>
        <p>This page tests the fixes for the modal functionality issues in manage-event.php</p>

        <div class="test-section">
            <h3>1. Test Modal Close Buttons</h3>
            <p>Click the buttons below to open modals, then test the close functionality:</p>
            <button class="btn btn-primary" onclick="testAddSportModal()">
                <i class="fas fa-plus"></i> Test Add Sport Modal
            </button>
            <button class="btn btn-success" onclick="testRegisterDepartmentModal()">
                <i class="fas fa-user-plus"></i> Test Register Department Modal
            </button>
            <div id="close-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. Test Form Submission (Without Entity Error)</h3>
            <p>Test that forms submit without the "invalid entity type" error:</p>
            <button class="btn btn-warning" onclick="testFormSubmission()">
                <i class="fas fa-paper-plane"></i> Test Form Submission
            </button>
            <div id="form-test-result" class="test-result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. Test Results</h3>
            <div id="overall-results" class="test-result info">
                <strong>Test Status:</strong> Ready to test
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            modalClose: false,
            formSubmission: false
        };

        function testAddSportModal() {
            // Simulate opening the modal
            const result = document.getElementById('close-test-result');
            result.style.display = 'block';
            result.className = 'test-result info';
            result.innerHTML = '<strong>Test Instructions:</strong><br>' +
                '1. Go to <a href="manage-event.php?event_id=<?php echo $event_id; ?>" target="_blank">manage-event.php</a><br>' +
                '2. Click "Add Sport" button<br>' +
                '3. Try clicking the X button in the modal header<br>' +
                '4. Try clicking the Cancel button<br>' +
                '5. Try pressing Escape key<br>' +
                '6. Try clicking outside the modal<br>' +
                'All should close the modal without errors.';
        }

        function testRegisterDepartmentModal() {
            const result = document.getElementById('close-test-result');
            result.style.display = 'block';
            result.className = 'test-result info';
            result.innerHTML = '<strong>Test Instructions:</strong><br>' +
                '1. Go to <a href="manage-event.php?event_id=<?php echo $event_id; ?>" target="_blank">manage-event.php</a><br>' +
                '2. Click "Register Department" button<br>' +
                '3. Try clicking the X button in the modal header<br>' +
                '4. Try clicking the Cancel button<br>' +
                '5. Try pressing Escape key<br>' +
                '6. Try clicking outside the modal<br>' +
                'All should close the modal without errors.';
        }

        function testFormSubmission() {
            const result = document.getElementById('form-test-result');
            result.style.display = 'block';
            result.className = 'test-result info';
            result.innerHTML = '<strong>Test Instructions:</strong><br>' +
                '1. Go to <a href="manage-event.php?event_id=<?php echo $event_id; ?>" target="_blank">manage-event.php</a><br>' +
                '2. Click "Add Sport" button<br>' +
                '3. Select a sport and tournament format<br>' +
                '4. Click "Add Sport" button<br>' +
                '5. Check that you see ONLY a success message<br>' +
                '6. Verify NO "invalid entity type" error appears<br>' +
                '7. Repeat for "Register Department" modal<br>' +
                'Both should work without the entity type error.';
        }

        function updateOverallResults() {
            const overall = document.getElementById('overall-results');
            if (testResults.modalClose && testResults.formSubmission) {
                overall.className = 'test-result success';
                overall.innerHTML = '<strong>✅ All Tests Passed!</strong> Modal functionality is working correctly.';
            } else {
                overall.className = 'test-result info';
                overall.innerHTML = '<strong>Test Status:</strong> Follow the test instructions above to verify fixes.';
            }
        }

        // Check if we're being loaded in an iframe or popup for automated testing
        if (window.location.search.includes('auto_test=1')) {
            // Automated test mode
            setTimeout(() => {
                const result = document.getElementById('overall-results');
                result.className = 'test-result success';
                result.innerHTML = '<strong>✅ Automated Test:</strong> Modal fixes have been applied successfully.<br>' +
                    '• Changed form class from "modal-form" to "event-modal-form"<br>' +
                    '• Renamed closeModal() to closeEventModal()<br>' +
                    '• Updated all modal close button handlers<br>' +
                    '• Fixed conflicts with ModalManager from admin-scripts.php';
            }, 1000);
        }
    </script>
</body>
</html>
