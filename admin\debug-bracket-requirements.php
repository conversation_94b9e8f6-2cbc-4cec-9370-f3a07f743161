<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$event_id = 4;
$sport_id = 37;
$category_id = 16;

echo "<h2>Bracket Requirements Debug</h2>";

try {
    // Check event sport configuration
    $stmt = $conn->prepare("
        SELECT es.*, tf.name as format_name, tf.min_participants, tf.max_participants
        FROM event_sports es
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch(PDO::FETCH_ASSOC);

    echo "<h3>Event Sport Configuration:</h3>";
    echo "<pre>";
    print_r($event_sport);
    echo "</pre>";

    // Check participants
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM event_department_registrations
        WHERE event_id = ? AND status IN ('pending', 'approved')
    ");
    $stmt->execute([$event_id]);
    $participants = $stmt->fetch(PDO::FETCH_ASSOC);

    echo "<h3>Participant Count: " . $participants['count'] . "</h3>";

    // Check referee assignment for the category
    $stmt = $conn->prepare("
        SELECT referee_name, referee_contact
        FROM sport_categories
        WHERE id = ?
    ");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch(PDO::FETCH_ASSOC);

    echo "<h3>Category Referee Info:</h3>";
    echo "<pre>";
    print_r($category);
    echo "</pre>";

    // Test the BracketDisplay requirements
    require_once 'includes/bracket_display.php';
    $bracketDisplay = new BracketDisplay($conn, null, $event_sport['id']);
    
    echo "<h3>Bracket Display Requirements Test:</h3>";
    echo $bracketDisplay->renderBracket();

} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
