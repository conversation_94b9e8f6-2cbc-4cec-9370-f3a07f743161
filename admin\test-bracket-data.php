<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$event_id = 4;
$sport_id = 37;
$category_id = 16;

echo "<h2>Bracket Data Test</h2>";

try {
    // Get event sport ID
    $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event_sport) {
        echo "<p style='color: red;'>Event sport not found!</p>";
        exit;
    }
    
    $event_sport_id = $event_sport['id'];
    echo "<p><strong>Event Sport ID:</strong> $event_sport_id</p>";

    // Test the query from BracketDisplay
    $stmt = $conn->prepare("
        SELECT es.*, tf.name as format_name, tf.min_participants, tf.max_participants,
               s.name as sport_name, s.type as sport_type, e.name as event_name,
               sc.referee_name, sc.referee_contact
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        LEFT JOIN sport_categories sc ON es.id = sc.event_sport_id
        WHERE es.id = ?
    ");
    $stmt->execute([$event_sport_id]);
    $tournament = $stmt->fetch(PDO::FETCH_ASSOC);

    echo "<h3>Tournament Data:</h3>";
    echo "<pre>";
    print_r($tournament);
    echo "</pre>";

    // Test participant count
    $stmt = $conn->prepare("
        SELECT COUNT(DISTINCT edr.department_id) as participant_count
        FROM event_department_registrations edr
        WHERE edr.event_id = ? AND edr.status IN ('pending', 'approved')
    ");
    $stmt->execute([$tournament['event_id']]);
    $participants = $stmt->fetch(PDO::FETCH_ASSOC);
    $current_participants = $participants['participant_count'] ?? 0;

    echo "<h3>Participant Count: $current_participants</h3>";

    // Check requirements
    $min_participants = $tournament['min_participants'] ?? 2;
    $participants_met = $current_participants >= $min_participants;
    $format_set = !empty($tournament['tournament_format_id']);
    $referee_assigned = !empty($tournament['referee_name']);

    echo "<h3>Requirements Status:</h3>";
    echo "<ul>";
    echo "<li>Participants: " . ($participants_met ? "✅" : "❌") . " ($current_participants/$min_participants)</li>";
    echo "<li>Tournament Format: " . ($format_set ? "✅" : "❌") . " (" . ($tournament['format_name'] ?? 'Not set') . ")</li>";
    echo "<li>Referee: " . ($referee_assigned ? "✅" : "❌") . " (" . ($tournament['referee_name'] ?? 'Not assigned') . ")</li>";
    echo "</ul>";

    $all_met = $participants_met && $format_set && $referee_assigned;
    echo "<h3>All Requirements Met: " . ($all_met ? "✅ YES" : "❌ NO") . "</h3>";

} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
