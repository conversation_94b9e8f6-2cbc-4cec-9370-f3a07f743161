<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🧪 Tournament AJAX Test</h1>";
echo "<p>Testing the tournament bracket generation step by step...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Test parameters
    $eventId = 4;
    $sportId = 37;
    $categoryId = 15;
    
    echo "<h2>Step-by-Step AJAX Logic Test</h2>";
    echo "<p>Event ID: {$eventId}, Sport ID: {$sportId}, Category ID: {$categoryId}</p>";
    
    // Step 1: Get event sport
    echo "<h3>1. Get Event Sport</h3>";
    $stmt = $conn->prepare("
        SELECT 
            es.*,
            tf.name as format_name,
            tf.code as format_code,
            tf.algorithm_class,
            tf.min_participants,
            tf.max_participants
        FROM event_sports es
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$eventId, $sportId]);
    $eventSport = $stmt->fetch();
    
    if (!$eventSport) {
        throw new Exception('Event sport not found');
    }
    
    echo "<p style='color: green;'>✅ Event sport found (ID: {$eventSport['id']})</p>";
    echo "<p>Tournament Format: " . ($eventSport['format_name'] ?? 'Not configured') . "</p>";
    
    // Step 2: Check format
    echo "<h3>2. Check Tournament Format</h3>";
    if (empty($eventSport['tournament_format_id'])) {
        throw new Exception('Tournament format not configured for this sport');
    }
    echo "<p style='color: green;'>✅ Tournament format configured (ID: {$eventSport['tournament_format_id']})</p>";
    
    // Step 3: Get participants
    echo "<h3>3. Get Participants</h3>";
    $stmt = $conn->prepare("
        SELECT 
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            r.id as registration_id,
            JSON_LENGTH(r.participants) as total_participants
        FROM departments d
        JOIN registrations r ON d.id = r.department_id
        WHERE r.event_sport_id = ? AND r.status IN ('confirmed', 'registered', 'approved')
        ORDER BY d.name
    ");
    $stmt->execute([$eventSport['id']]);
    $participants = $stmt->fetchAll();
    
    echo "<p>Found " . count($participants) . " participants</p>";
    
    if (empty($participants)) {
        throw new Exception('No participants found');
    }
    
    $min_participants = $eventSport['min_participants'] ?? 2;
    if (count($participants) < $min_participants) {
        throw new Exception("Not enough participants. Need {$min_participants}, have " . count($participants));
    }
    
    echo "<p style='color: green;'>✅ Sufficient participants</p>";
    
    // Step 4: Check existing tournaments
    echo "<h3>4. Check Existing Tournaments</h3>";
    $stmt = $conn->prepare("
        SELECT id FROM tournament_structures 
        WHERE event_sport_id = ? AND status != 'cancelled'
    ");
    $stmt->execute([$eventSport['id']]);
    $existingTournament = $stmt->fetch();
    
    if ($existingTournament) {
        echo "<p style='color: red;'>❌ Tournament already exists (ID: {$existingTournament['id']})</p>";
        echo "<p><a href='?cancel=1' style='background: #dc3545; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Cancel Existing Tournament</a></p>";
        
        if (isset($_GET['cancel'])) {
            $stmt = $conn->prepare("UPDATE tournament_structures SET status = 'cancelled' WHERE id = ?");
            $stmt->execute([$existingTournament['id']]);
            echo "<p style='color: green;'>✅ Tournament cancelled. <a href='test-ajax-tournament.php'>Try again</a></p>";
            exit;
        } else {
            throw new Exception('Tournament already exists for this sport');
        }
    }
    
    echo "<p style='color: green;'>✅ No existing tournaments</p>";
    
    // Step 5: Test tournament creation
    echo "<h3>5. Test Tournament Creation</h3>";
    
    require_once '../includes/tournament_manager.php';
    $tournamentManager = new TournamentManager($conn);
    
    $tournamentName = "Test Tournament - " . date('H:i:s');
    $config = [
        'seeding_method' => 'random',
        'bracket_seeding' => true,
        'auto_generated' => false
    ];
    
    echo "<p>Creating tournament...</p>";
    
    $tournamentId = $tournamentManager->createTournament(
        $eventSport['id'],
        $eventSport['tournament_format_id'],
        $tournamentName,
        $config
    );
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>🎉 SUCCESS!</h3>";
    echo "<p>Tournament created with ID: <strong>{$tournamentId}</strong></p>";
    echo "<p>The bracket generation should now work in the category management page!</p>";
    echo "</div>";
    
    // Verify matches were created
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM matches WHERE tournament_structure_id = ?");
    $stmt->execute([$tournamentId]);
    $matchCount = $stmt->fetch()['count'];
    
    echo "<p>Matches created: {$matchCount}</p>";
    
    echo "<h2>Next Steps:</h2>";
    echo "<ul>";
    echo "<li><a href='manage-category.php?category_id={$categoryId}&event_id={$eventId}&sport_id={$sportId}'>Go to Category Management</a></li>";
    echo "<li>Try clicking 'Generate Matches' button - it should work now!</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ ERROR</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
    
    echo "<h2>What's Missing:</h2>";
    
    if (strpos($e->getMessage(), 'Event sport not found') !== false) {
        echo "<p>❌ <strong>Event sport not found</strong> - The combination of Event ID {$eventId} and Sport ID {$sportId} doesn't exist in the event_sports table.</p>";
    }
    
    if (strpos($e->getMessage(), 'Tournament format not configured') !== false) {
        echo "<p>❌ <strong>Tournament format not configured</strong> - The event sport doesn't have a tournament_format_id set.</p>";
    }
    
    if (strpos($e->getMessage(), 'No participants found') !== false) {
        echo "<p>❌ <strong>No participants</strong> - No departments are registered for this event sport.</p>";
        echo "<p>💡 <a href='add-test-participants.php'>Add test participants</a></p>";
    }
    
    if (strpos($e->getMessage(), 'Not enough participants') !== false) {
        echo "<p>❌ <strong>Insufficient participants</strong> - Need at least 2 participants for tournament.</p>";
        echo "<p>💡 <a href='add-test-participants.php'>Add more participants</a></p>";
    }
    
    if (strpos($e->getMessage(), 'Tournament already exists') !== false) {
        echo "<p>❌ <strong>Tournament already exists</strong> - An active tournament is already running for this event sport.</p>";
        echo "<p>💡 Cancel the existing tournament first</p>";
    }
}
?>
