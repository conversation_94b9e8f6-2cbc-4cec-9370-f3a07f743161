<?php
/**
 * Quick Category Check
 * Simple tool to check what categories exist
 */

require_once 'auth.php';
require_once '../config/database.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>Quick Category Check</h1>";

try {
    // Check if sport_categories table exists and has data
    echo "<h2>1. Sport Categories Table</h2>";
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM sport_categories");
    $stmt->execute();
    $count = $stmt->fetch()['count'];
    echo "<p><strong>Total categories:</strong> $count</p>";
    
    if ($count > 0) {
        echo "<h3>All Categories:</h3>";
        $stmt = $conn->prepare("
            SELECT sc.id, sc.category_name, sc.event_sport_id,
                   es.event_id, es.sport_id,
                   e.name as event_name, s.name as sport_name
            FROM sport_categories sc
            LEFT JOIN event_sports es ON sc.event_sport_id = es.id
            LEFT JOIN events e ON es.event_id = e.id
            LEFT JOIN sports s ON es.sport_id = s.id
            ORDER BY sc.id
        ");
        $stmt->execute();
        $categories = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Cat ID</th><th>Name</th><th>Event ID</th><th>Sport ID</th><th>Event Name</th><th>Sport Name</th><th>Test Link</th></tr>";
        
        foreach ($categories as $cat) {
            $link = "manage-category.php?event_id={$cat['event_id']}&sport_id={$cat['sport_id']}&category_id={$cat['id']}&debug=1";
            echo "<tr>";
            echo "<td>{$cat['id']}</td>";
            echo "<td>" . htmlspecialchars($cat['category_name']) . "</td>";
            echo "<td>{$cat['event_id']}</td>";
            echo "<td>{$cat['sport_id']}</td>";
            echo "<td>" . htmlspecialchars($cat['event_name'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($cat['sport_name'] ?? 'NULL') . "</td>";
            echo "<td><a href='$link' target='_blank'>Test</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'><strong>NO CATEGORIES FOUND!</strong> This is why navigation fails.</p>";
        echo "<p><a href='ajax/create-test-categories.php' target='_blank'>Create test categories</a></p>";
    }
    
    // Check event_sports table
    echo "<h2>2. Event Sports Table</h2>";
    $stmt = $conn->prepare("
        SELECT es.id, es.event_id, es.sport_id, e.name as event_name, s.name as sport_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        ORDER BY es.id
    ");
    $stmt->execute();
    $event_sports = $stmt->fetchAll();
    
    if ($event_sports) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ES ID</th><th>Event ID</th><th>Sport ID</th><th>Event Name</th><th>Sport Name</th><th>Categories Link</th></tr>";
        
        foreach ($event_sports as $es) {
            $link = "sport-categories.php?event_id={$es['event_id']}&sport_id={$es['sport_id']}";
            echo "<tr>";
            echo "<td>{$es['id']}</td>";
            echo "<td>{$es['event_id']}</td>";
            echo "<td>{$es['sport_id']}</td>";
            echo "<td>" . htmlspecialchars($es['event_name']) . "</td>";
            echo "<td>" . htmlspecialchars($es['sport_name']) . "</td>";
            echo "<td><a href='$link' target='_blank'>View Categories</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>No event-sport relationships found!</p>";
    }
    
    // Quick fix button
    echo "<h2>3. Quick Fix</h2>";
    echo "<p>If no categories exist, click here to create test data:</p>";
    echo "<button onclick=\"createTestData()\">Create Test Categories</button>";
    echo "<div id='result'></div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>

<script>
function createTestData() {
    fetch('ajax/create-test-categories.php', {method: 'POST'})
        .then(response => response.json())
        .then(data => {
            document.getElementById('result').innerHTML = 
                '<p style="color: ' + (data.success ? 'green' : 'red') + ';">' + data.message + '</p>';
            if (data.success) {
                setTimeout(() => location.reload(), 2000);
            }
        })
        .catch(error => {
            document.getElementById('result').innerHTML = '<p style="color: red;">Error: ' + error.message + '</p>';
        });
}
</script>
