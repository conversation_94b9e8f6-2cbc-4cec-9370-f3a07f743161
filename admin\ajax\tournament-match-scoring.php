<?php
/**
 * Tournament Match Scoring
 * Handles match score updates and tournament advancement
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/tournament_manager.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'save_match_score':
            handleSaveMatchScore($conn, $input);
            break;
        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Save match score and update tournament progression
 */
function handleSaveMatchScore($conn, $input) {
    $matchId = $input['match_id'] ?? '';
    $teamAScore = $input['team_a_score'] ?? null;
    $teamBScore = $input['team_b_score'] ?? null;
    $status = $input['status'] ?? 'scheduled';
    $venue = $input['venue'] ?? '';
    $refereeNotes = $input['referee_notes'] ?? '';

    if (empty($matchId)) {
        throw new Exception('Match ID is required');
    }

    $conn->beginTransaction();

    try {
        // Get current match details
        $stmt = $conn->prepare("
            SELECT 
                m.*,
                ts.tournament_format_id,
                tf.code as format_code
            FROM matches m
            LEFT JOIN tournament_structures ts ON m.tournament_structure_id = ts.id
            LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
            WHERE m.id = ?
        ");
        $stmt->execute([$matchId]);
        $match = $stmt->fetch();

        if (!$match) {
            throw new Exception('Match not found');
        }

        // Determine winner if match is completed
        $winnerId = null;
        if ($status === 'completed' && $teamAScore !== null && $teamBScore !== null) {
            if ($teamAScore > $teamBScore) {
                $winnerId = $match['team1_id'];
            } elseif ($teamBScore > $teamAScore) {
                $winnerId = $match['team2_id'];
            }
            // For draws, winnerId remains null (handled by tournament format)
        }

        // Update match
        $stmt = $conn->prepare("
            UPDATE matches 
            SET 
                team_a_score = ?,
                team_b_score = ?,
                status = ?,
                venue = ?,
                referee_notes = ?,
                winner_id = ?,
                actual_start_time = CASE 
                    WHEN status = 'scheduled' AND ? = 'ongoing' THEN NOW() 
                    ELSE actual_start_time 
                END,
                actual_end_time = CASE 
                    WHEN ? = 'completed' THEN NOW() 
                    ELSE actual_end_time 
                END
            WHERE id = ?
        ");
        $stmt->execute([
            $teamAScore,
            $teamBScore,
            $status,
            $venue,
            $refereeNotes,
            $winnerId,
            $status, // for start time check
            $status, // for end time check
            $matchId
        ]);

        // Check if tournament advancement is needed
        $advancementNeeded = false;
        if ($status === 'completed' && $match['tournament_structure_id']) {
            $advancementNeeded = checkRoundCompletion($conn, $match['tournament_structure_id'], $match['round_number']);
        }

        // Log admin activity
        logAdminActivity('UPDATE_MATCH_SCORE', 'matches', $matchId, null, [
            'team_a_score' => $teamAScore,
            'team_b_score' => $teamBScore,
            'status' => $status,
            'winner_id' => $winnerId
        ]);

        $conn->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Match score saved successfully',
            'match_id' => $matchId,
            'winner_id' => $winnerId,
            'advancement_needed' => $advancementNeeded,
            'status' => $status
        ]);

    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}

/**
 * Check if a round is completed and advancement is needed
 */
function checkRoundCompletion($conn, $tournamentId, $roundNumber) {
    // Get total matches in this round
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_matches,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_matches
        FROM matches 
        WHERE tournament_structure_id = ? AND round_number = ?
    ");
    $stmt->execute([$tournamentId, $roundNumber]);
    $roundStats = $stmt->fetch();

    // Check if all matches in round are completed
    if ($roundStats['total_matches'] > 0 && 
        $roundStats['completed_matches'] == $roundStats['total_matches']) {
        
        // Check if this is the final round
        $stmt = $conn->prepare("
            SELECT total_rounds, current_round 
            FROM tournament_structures 
            WHERE id = ?
        ");
        $stmt->execute([$tournamentId]);
        $tournament = $stmt->fetch();

        if ($roundNumber < $tournament['total_rounds']) {
            // Advance to next round
            advanceToNextRound($conn, $tournamentId, $roundNumber);
            return true;
        } else {
            // Tournament completed
            completeTournament($conn, $tournamentId);
            return false;
        }
    }

    return false;
}

/**
 * Advance tournament to next round
 */
function advanceToNextRound($conn, $tournamentId, $completedRound) {
    try {
        // Get tournament manager
        $tournamentManager = new TournamentManager($conn);
        
        // Advance the tournament
        $result = $tournamentManager->advanceToNextRound($tournamentId);
        
        // Log advancement
        logAdminActivity('ADVANCE_TOURNAMENT_ROUND', 'tournament_structures', $tournamentId, null, [
            'from_round' => $completedRound,
            'to_round' => $completedRound + 1
        ]);
        
        return $result;
        
    } catch (Exception $e) {
        error_log("Failed to advance tournament {$tournamentId}: " . $e->getMessage());
        return false;
    }
}

/**
 * Complete tournament
 */
function completeTournament($conn, $tournamentId) {
    try {
        // Update tournament status
        $stmt = $conn->prepare("
            UPDATE tournament_structures 
            SET status = 'completed' 
            WHERE id = ?
        ");
        $stmt->execute([$tournamentId]);

        // Log completion
        logAdminActivity('COMPLETE_TOURNAMENT', 'tournament_structures', $tournamentId);
        
        return true;
        
    } catch (Exception $e) {
        error_log("Failed to complete tournament {$tournamentId}: " . $e->getMessage());
        return false;
    }
}
