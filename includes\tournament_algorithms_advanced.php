<?php
/**
 * Advanced Tournament Algorithm Classes
 * Comprehensive bracket generation for all tournament formats
 */

abstract class BaseTournamentAlgorithm {
    protected $conn;
    protected $config = [];
    
    public function __construct($conn) {
        $this->conn = $conn;
    }
    
    abstract public function generateBracket($participants, $config = []);
    
    protected function handleByes($participants) {
        $count = count($participants);
        $nextPowerOfTwo = pow(2, ceil(log($count, 2)));
        $byesNeeded = $nextPowerOfTwo - $count;
        
        // Add bye entries
        for ($i = 0; $i < $byesNeeded; $i++) {
            $participants[] = [
                'id' => null,
                'name' => 'BYE',
                'is_bye' => true
            ];
        }
        
        return $participants;
    }
    
    protected function createMatchStructure($team1, $team2, $position, $round) {
        return [
            'team1_id' => $team1['id'] ?? null,
            'team1_name' => $team1['name'] ?? 'TBD',
            'team2_id' => $team2['id'] ?? null,
            'team2_name' => $team2['name'] ?? 'TBD',
            'bracket_position' => $position,
            'round' => $round,
            'is_bye' => ($team1['is_bye'] ?? false) || ($team2['is_bye'] ?? false),
            'status' => 'pending'
        ];
    }
}

class SingleEliminationAlgorithm extends BaseTournamentAlgorithm {
    public function generateBracket($participants, $config = []) {
        $this->config = array_merge([
            'seeding_method' => 'random',
            'third_place_playoff' => false
        ], $config);
        
        // Seed participants
        $seeded = TournamentSeeder::seedParticipants($participants, $this->config['seeding_method']);
        
        // Handle byes for non-power-of-two counts
        $seeded = $this->handleByes($seeded);
        
        $rounds = [];
        $currentRound = $seeded;
        $roundNumber = 1;
        
        while (count($currentRound) > 1) {
            $nextRound = [];
            $matches = [];
            
            for ($i = 0; $i < count($currentRound); $i += 2) {
                $team1 = $currentRound[$i];
                $team2 = $currentRound[$i + 1] ?? ['id' => null, 'name' => 'BYE', 'is_bye' => true];
                
                $match = $this->createMatchStructure(
                    $team1, 
                    $team2, 
                    "R{$roundNumber}M" . (($i / 2) + 1),
                    $roundNumber
                );
                
                $matches[] = $match;
                
                // Winner advances (for bye matches, non-bye team advances)
                if ($team1['is_bye'] ?? false) {
                    $nextRound[] = $team2;
                } elseif ($team2['is_bye'] ?? false) {
                    $nextRound[] = $team1;
                } else {
                    $nextRound[] = ['id' => null, 'name' => 'TBD', 'from_match' => $match['bracket_position']];
                }
            }
            
            $rounds[] = [
                'round_number' => $roundNumber,
                'round_name' => $this->getRoundName($roundNumber, count($rounds) + 1),
                'matches' => $matches
            ];
            
            $currentRound = $nextRound;
            $roundNumber++;
        }
        
        // Add third place playoff if configured
        if ($this->config['third_place_playoff'] && count($rounds) > 1) {
            $this->addThirdPlacePlayoff($rounds);
        }
        
        return [
            'format' => 'single_elimination',
            'total_rounds' => count($rounds),
            'total_matches' => array_sum(array_map(function($r) { return count($r['matches']); }, $rounds)),
            'rounds' => $rounds,
            'participants' => $participants
        ];
    }
    
    private function getRoundName($roundNumber, $totalRounds) {
        $remaining = $totalRounds - $roundNumber + 1;
        switch ($remaining) {
            case 1: return 'Final';
            case 2: return 'Semi-Final';
            case 3: return 'Quarter-Final';
            case 4: return 'Round of 16';
            case 5: return 'Round of 32';
            default: return "Round $roundNumber";
        }
    }
    
    private function addThirdPlacePlayoff(&$rounds) {
        if (count($rounds) < 2) return;
        
        $semiFinalRound = $rounds[count($rounds) - 2];
        $thirdPlaceMatch = $this->createMatchStructure(
            ['id' => null, 'name' => 'Loser SF1'],
            ['id' => null, 'name' => 'Loser SF2'],
            'THIRD_PLACE',
            count($rounds) + 1
        );
        
        $rounds[] = [
            'round_number' => count($rounds) + 1,
            'round_name' => 'Third Place Playoff',
            'matches' => [$thirdPlaceMatch]
        ];
    }
}

class DoubleEliminationAlgorithm extends BaseTournamentAlgorithm {
    public function generateBracket($participants, $config = []) {
        $this->config = array_merge([
            'seeding_method' => 'random'
        ], $config);
        
        $seeded = TournamentSeeder::seedParticipants($participants, $this->config['seeding_method']);
        $seeded = $this->handleByes($seeded);
        
        $winnersBracket = $this->generateWinnersBracket($seeded);
        $losersBracket = $this->generateLosersBracket($seeded, $winnersBracket);
        
        return [
            'format' => 'double_elimination',
            'winners_bracket' => $winnersBracket,
            'losers_bracket' => $losersBracket,
            'participants' => $participants
        ];
    }
    
    private function generateWinnersBracket($participants) {
        // Similar to single elimination but track losers
        $rounds = [];
        $currentRound = $participants;
        $roundNumber = 1;
        
        while (count($currentRound) > 1) {
            $nextRound = [];
            $matches = [];
            
            for ($i = 0; $i < count($currentRound); $i += 2) {
                $team1 = $currentRound[$i];
                $team2 = $currentRound[$i + 1] ?? ['id' => null, 'name' => 'BYE', 'is_bye' => true];
                
                $match = $this->createMatchStructure(
                    $team1, 
                    $team2, 
                    "WR{$roundNumber}M" . (($i / 2) + 1),
                    $roundNumber
                );
                
                $matches[] = $match;
                $nextRound[] = ['id' => null, 'name' => 'TBD', 'from_match' => $match['bracket_position']];
            }
            
            $rounds[] = [
                'round_number' => $roundNumber,
                'round_name' => "Winners Round $roundNumber",
                'matches' => $matches
            ];
            
            $currentRound = $nextRound;
            $roundNumber++;
        }
        
        return $rounds;
    }
    
    private function generateLosersBracket($participants, $winnersBracket) {
        // Complex losers bracket generation
        $rounds = [];
        // Implementation would continue here for full double elimination
        return $rounds;
    }
}

class RoundRobinAlgorithm extends BaseTournamentAlgorithm {
    public function generateBracket($participants, $config = []) {
        $this->config = array_merge([
            'legs' => 1, // Number of times each team plays each other
            'points_win' => 3,
            'points_draw' => 1,
            'points_loss' => 0
        ], $config);
        
        $matches = [];
        $matchNumber = 1;
        
        for ($leg = 1; $leg <= $this->config['legs']; $leg++) {
            for ($i = 0; $i < count($participants); $i++) {
                for ($j = $i + 1; $j < count($participants); $j++) {
                    $match = $this->createMatchStructure(
                        $participants[$i],
                        $participants[$j],
                        "RR_L{$leg}_M{$matchNumber}",
                        $leg
                    );
                    $matches[] = $match;
                    $matchNumber++;
                }
            }
        }
        
        return [
            'format' => 'round_robin',
            'total_rounds' => $this->config['legs'],
            'total_matches' => count($matches),
            'rounds' => [
                [
                    'round_number' => 1,
                    'round_name' => 'Round Robin',
                    'matches' => $matches
                ]
            ],
            'participants' => $participants,
            'scoring_config' => [
                'points_win' => $this->config['points_win'],
                'points_draw' => $this->config['points_draw'],
                'points_loss' => $this->config['points_loss']
            ]
        ];
    }
}

class MultiStageAlgorithm extends BaseTournamentAlgorithm {
    public function generateBracket($participants, $config = []) {
        $this->config = array_merge([
            'groups_count' => 4,
            'advance_per_group' => 2,
            'group_stage_legs' => 1
        ], $config);
        
        // Divide participants into groups
        $groups = $this->createGroups($participants);
        
        // Generate group stage
        $groupStage = $this->generateGroupStage($groups);
        
        // Generate knockout stage
        $knockoutStage = $this->generateKnockoutStage($groups);
        
        return [
            'format' => 'multi_stage',
            'group_stage' => $groupStage,
            'knockout_stage' => $knockoutStage,
            'participants' => $participants
        ];
    }
    
    private function createGroups($participants) {
        $groupSize = ceil(count($participants) / $this->config['groups_count']);
        $groups = [];
        
        for ($i = 0; $i < $this->config['groups_count']; $i++) {
            $groups[chr(65 + $i)] = array_slice($participants, $i * $groupSize, $groupSize);
        }
        
        return $groups;
    }
    
    private function generateGroupStage($groups) {
        $groupStages = [];
        
        foreach ($groups as $groupName => $groupParticipants) {
            $roundRobin = new RoundRobinAlgorithm($this->conn);
            $groupBracket = $roundRobin->generateBracket($groupParticipants, [
                'legs' => $this->config['group_stage_legs']
            ]);
            
            $groupStages[$groupName] = $groupBracket;
        }
        
        return $groupStages;
    }
    
    private function generateKnockoutStage($groups) {
        // Create knockout bracket from group winners
        $qualifiers = [];
        foreach ($groups as $groupName => $groupParticipants) {
            // Take top N from each group
            for ($i = 0; $i < $this->config['advance_per_group'] && $i < count($groupParticipants); $i++) {
                $qualifiers[] = ['id' => null, 'name' => "Winner Group $groupName", 'group' => $groupName];
            }
        }
        
        $singleElim = new SingleEliminationAlgorithm($this->conn);
        return $singleElim->generateBracket($qualifiers);
    }
}

// Specialized algorithms for judged competitions
class JudgedRoundsAlgorithm extends BaseTournamentAlgorithm {
    public function generateBracket($participants, $config = []) {
        $this->config = array_merge([
            'rounds' => 3,
            'judges_count' => 5,
            'scoring_criteria' => ['technique', 'presentation', 'creativity']
        ], $config);
        
        $rounds = [];
        for ($round = 1; $round <= $this->config['rounds']; $round++) {
            $rounds[] = [
                'round_number' => $round,
                'round_name' => "Round $round",
                'participants' => $participants,
                'judging_criteria' => $this->config['scoring_criteria']
            ];
        }
        
        return [
            'format' => 'judged_rounds',
            'rounds' => $rounds,
            'participants' => $participants,
            'judges_count' => $this->config['judges_count']
        ];
    }
}

class PerformanceCompetitionAlgorithm extends BaseTournamentAlgorithm {
    public function generateBracket($participants, $config = []) {
        return [
            'format' => 'performance_competition',
            'rounds' => [
                [
                    'round_number' => 1,
                    'round_name' => 'Performance Round',
                    'participants' => $participants,
                    'performance_order' => TournamentSeeder::fisherYatesShuffle($participants)
                ]
            ],
            'participants' => $participants
        ];
    }
}

class TalentShowcaseAlgorithm extends BaseTournamentAlgorithm {
    public function generateBracket($participants, $config = []) {
        return [
            'format' => 'talent_showcase',
            'rounds' => [
                [
                    'round_number' => 1,
                    'round_name' => 'Talent Showcase',
                    'participants' => $participants,
                    'showcase_order' => $participants // Keep original order for showcase
                ]
            ],
            'participants' => $participants
        ];
    }
}

class SwissSystemAlgorithm extends BaseTournamentAlgorithm {
    public function generateBracket($participants, $config = []) {
        $this->config = array_merge([
            'rounds' => ceil(log(count($participants), 2)),
            'avoid_rematches' => true
        ], $config);
        
        $rounds = [];
        for ($round = 1; $round <= $this->config['rounds']; $round++) {
            $rounds[] = [
                'round_number' => $round,
                'round_name' => "Swiss Round $round",
                'pairing_method' => 'swiss_system'
            ];
        }
        
        return [
            'format' => 'swiss_system',
            'rounds' => $rounds,
            'participants' => $participants
        ];
    }
}
?>
