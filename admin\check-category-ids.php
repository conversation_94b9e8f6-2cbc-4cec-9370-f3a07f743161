<?php
/**
 * Check Category IDs in Database
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔍 Check Category IDs in Database</h1>";

try {
    // Get all categories with their relationships
    $stmt = $conn->prepare("
        SELECT 
            sc.id as category_id,
            sc.category_name,
            sc.category_type,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name,
            es.id as event_sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        ORDER BY e.name, s.name, sc.category_name
    ");
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
    if (empty($categories)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ No Categories Found</h3>";
        echo "<p>No sport categories exist in the database.</p>";
        echo "<a href='comprehensive-database-fix.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create Test Data</a>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
        echo "<h3>✅ Found " . count($categories) . " Categories</h3>";
        echo "</div>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
        echo "<tr style='background: #007bff; color: white;'>";
        echo "<th>Category ID</th>";
        echo "<th>Category Name</th>";
        echo "<th>Type</th>";
        echo "<th>Event ID</th>";
        echo "<th>Sport ID</th>";
        echo "<th>Event Name</th>";
        echo "<th>Sport Name</th>";
        echo "<th>Categories Page</th>";
        echo "<th>Manage Page</th>";
        echo "<th>Debug Manage</th>";
        echo "</tr>";
        
        foreach ($categories as $cat) {
            $categories_url = "sport-categories.php?event_id={$cat['event_id']}&sport_id={$cat['sport_id']}";
            $manage_url = "manage-category.php?event_id={$cat['event_id']}&sport_id={$cat['sport_id']}&category_id={$cat['category_id']}";
            $debug_manage_url = "debug-manage-category.php?event_id={$cat['event_id']}&sport_id={$cat['sport_id']}&category_id={$cat['category_id']}";
            
            echo "<tr>";
            echo "<td>{$cat['category_id']}</td>";
            echo "<td>{$cat['category_name']}</td>";
            echo "<td>{$cat['category_type']}</td>";
            echo "<td>{$cat['event_id']}</td>";
            echo "<td>{$cat['sport_id']}</td>";
            echo "<td>{$cat['event_name']}</td>";
            echo "<td>{$cat['sport_name']}</td>";
            echo "<td><a href='$categories_url' target='_blank'>Categories</a></td>";
            echo "<td><a href='$manage_url' target='_blank'>Manage</a></td>";
            echo "<td><a href='$debug_manage_url' target='_blank'>Debug</a></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Show the specific case from the user's browser
        echo "<h2>🎯 Your Specific Case (event_id=4, sport_id=40)</h2>";
        
        $user_categories = array_filter($categories, function($cat) {
            return $cat['event_id'] == 4 && $cat['sport_id'] == 40;
        });
        
        if (empty($user_categories)) {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
            echo "<h3>⚠️ No Categories for event_id=4, sport_id=40</h3>";
            echo "<p>This explains why the sport-categories.php page shows no clickable links!</p>";
            echo "<p>The page is loading but there are no categories to display for this event-sport combination.</p>";
            echo "</div>";
            
            // Check if this event-sport relationship exists
            $stmt = $conn->prepare("
                SELECT es.id, e.name as event_name, s.name as sport_name
                FROM event_sports es
                JOIN events e ON es.event_id = e.id
                JOIN sports s ON es.sport_id = s.id
                WHERE es.event_id = 4 AND es.sport_id = 40
            ");
            $stmt->execute();
            $event_sport = $stmt->fetch();
            
            if ($event_sport) {
                echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
                echo "<h4>Event-Sport Relationship Exists</h4>";
                echo "<p><strong>Event:</strong> {$event_sport['event_name']}</p>";
                echo "<p><strong>Sport:</strong> {$event_sport['sport_name']}</p>";
                echo "<p><strong>Event-Sport ID:</strong> {$event_sport['id']}</p>";
                echo "<p>But no categories have been created for this event-sport combination.</p>";
                echo "</div>";
            } else {
                echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
                echo "<h4>Event-Sport Relationship Does Not Exist</h4>";
                echo "<p>There is no relationship between event_id=4 and sport_id=40 in the database.</p>";
                echo "</div>";
            }
            
        } else {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h3>✅ Found Categories for Your Case</h3>";
            echo "<p>Found " . count($user_categories) . " categories for event_id=4, sport_id=40</p>";
            echo "</div>";
            
            foreach ($user_categories as $cat) {
                $manage_url = "manage-category.php?event_id=4&sport_id=40&category_id={$cat['category_id']}";
                echo "<p><strong>{$cat['category_name']}</strong> - <a href='$manage_url' target='_blank'>Test Navigation</a></p>";
            }
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>📋 Next Steps</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<ol>";
echo "<li>If no categories found for your event-sport combination, create them using the database fix</li>";
echo "<li>If categories exist, test the navigation links above</li>";
echo "<li>Use the debug links to see exactly what's happening during navigation</li>";
echo "<li>Check if the sport-categories.php page is querying the correct data</li>";
echo "</ol>";
echo "</div>";
?>
