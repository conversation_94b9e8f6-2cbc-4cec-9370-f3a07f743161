<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>🔍 Bracket Render Test</h2>";

// Get the tournament
$stmt = $conn->prepare("
    SELECT ts.*, tf.name as format_name
    FROM tournament_structures ts
    LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
    WHERE ts.event_sport_id = 52
    ORDER BY ts.created_at DESC
    LIMIT 1
");
$stmt->execute();
$tournament = $stmt->fetch(PDO::FETCH_ASSOC);

if ($tournament) {
    $tournament_id = $tournament['id'];
    echo "<p>✅ Tournament ID: $tournament_id</p>";
    
    // Include CSS
    echo '<link rel="stylesheet" href="assets/css/bracket-styles.css">';
    echo '<link rel="stylesheet" href="assets/css/bracket-modals.css">';
    
    // Test BracketDisplay
    require_once 'includes/bracket_display.php';
    $bracketDisplay = new BracketDisplay($conn, $tournament_id, 52);
    
    echo "<h3>Bracket HTML Output:</h3>";
    echo "<div style='border: 2px solid #007bff; padding: 15px; background: #f8f9fa;'>";
    
    $html = $bracketDisplay->renderBracket();
    echo $html;
    
    echo "</div>";
    
    echo "<h3>Raw HTML Code:</h3>";
    echo "<textarea style='width: 100%; height: 300px;'>" . htmlspecialchars($html) . "</textarea>";
    
} else {
    echo "<p>❌ No tournament found</p>";
}

echo "<p><a href='manage-category.php?event_id=4&sport_id=37&category_id=16'>← Back to Category Management</a></p>";
?>
