<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>👥 Add Test Participants for Tournament</h1>";
echo "<p>Adding test participants to enable tournament creation...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get the test event sport (Event 4, Sport 37)
    $event_id = 4;
    $sport_id = 37;
    
    echo "<h2>1. Find Event Sport</h2>";
    
    $stmt = $conn->prepare("
        SELECT es.*, e.name as event_name, s.name as sport_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    
    if (!$event_sport) {
        echo "<p style='color: red;'>❌ Event sport not found! Creating...</p>";
        
        // Create the event sport if it doesn't exist
        $stmt = $conn->prepare("
            INSERT INTO event_sports (event_id, sport_id, tournament_format_id, max_teams, status)
            VALUES (?, ?, (SELECT id FROM tournament_formats LIMIT 1), 8, 'registration')
        ");
        $stmt->execute([$event_id, $sport_id]);
        
        $event_sport_id = $conn->lastInsertId();
        echo "<p style='color: green;'>✅ Created event sport with ID: {$event_sport_id}</p>";
        
        // Re-fetch the event sport
        $stmt = $conn->prepare("
            SELECT es.*, e.name as event_name, s.name as sport_name
            FROM event_sports es
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            WHERE es.id = ?
        ");
        $stmt->execute([$event_sport_id]);
        $event_sport = $stmt->fetch();
    } else {
        echo "<p style='color: green;'>✅ Found event sport: {$event_sport['event_name']} - {$event_sport['sport_name']}</p>";
    }
    
    $event_sport_id = $event_sport['id'];
    
    echo "<h2>2. Check Current Registrations</h2>";
    
    $stmt = $conn->prepare("
        SELECT r.*, d.name as department_name
        FROM registrations r
        JOIN departments d ON r.department_id = d.id
        WHERE r.event_sport_id = ?
    ");
    $stmt->execute([$event_sport_id]);
    $existing_registrations = $stmt->fetchAll();
    
    echo "<p>Current registrations: " . count($existing_registrations) . "</p>";
    
    if (!empty($existing_registrations)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>Department</th><th>Team Name</th><th>Status</th><th>Participants</th>";
        echo "</tr>";
        foreach ($existing_registrations as $reg) {
            $participants = json_decode($reg['participants'], true) ?: [];
            echo "<tr>";
            echo "<td>{$reg['department_name']}</td>";
            echo "<td>" . ($reg['team_name'] ?: 'N/A') . "</td>";
            echo "<td>{$reg['status']}</td>";
            echo "<td>" . count($participants) . " participants</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>3. Add Test Participants</h2>";
    
    if (count($existing_registrations) >= 2) {
        echo "<p style='color: green;'>✅ Already have " . count($existing_registrations) . " registrations - sufficient for tournament!</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Need more registrations. Adding test departments...</p>";
        
        // Get available departments
        $stmt = $conn->prepare("SELECT * FROM departments ORDER BY name LIMIT 5");
        $stmt->execute();
        $departments = $stmt->fetchAll();
        
        if (empty($departments)) {
            echo "<p style='color: red;'>❌ No departments found! Creating test departments...</p>";
            
            $test_departments = [
                'Computer Science Department',
                'Engineering Department', 
                'Business Administration',
                'Liberal Arts Department'
            ];
            
            foreach ($test_departments as $dept_name) {
                $stmt = $conn->prepare("INSERT INTO departments (name, description) VALUES (?, ?)");
                $stmt->execute([$dept_name, "Test department for tournament"]);
                echo "<p style='color: green;'>✅ Created department: {$dept_name}</p>";
            }
            
            // Re-fetch departments
            $stmt = $conn->prepare("SELECT * FROM departments ORDER BY name LIMIT 5");
            $stmt->execute();
            $departments = $stmt->fetchAll();
        }
        
        // Add registrations for departments that aren't already registered
        $registered_dept_ids = array_column($existing_registrations, 'department_id');
        $added_count = 0;
        
        foreach ($departments as $dept) {
            if (in_array($dept['id'], $registered_dept_ids)) {
                continue; // Skip already registered departments
            }
            
            if ($added_count >= 4) {
                break; // Limit to 4 additional registrations
            }
            
            // Create test participants for this department
            $test_participants = [
                "Player 1 - {$dept['name']}",
                "Player 2 - {$dept['name']}",
                "Player 3 - {$dept['name']}"
            ];
            
            $stmt = $conn->prepare("
                INSERT INTO registrations (event_sport_id, department_id, team_name, participants, status)
                VALUES (?, ?, ?, ?, 'confirmed')
            ");
            $stmt->execute([
                $event_sport_id,
                $dept['id'],
                $dept['name'] . " Team",
                json_encode($test_participants)
            ]);
            
            echo "<p style='color: green;'>✅ Added registration for: {$dept['name']} (3 participants)</p>";
            $added_count++;
        }
        
        echo "<p style='color: green;'><strong>✅ Added {$added_count} new registrations!</strong></p>";
    }
    
    echo "<h2>4. Final Registration Count</h2>";
    
    $stmt = $conn->prepare("
        SELECT COUNT(*) as total_registrations,
               SUM(JSON_LENGTH(participants)) as total_participants
        FROM registrations 
        WHERE event_sport_id = ? AND status IN ('confirmed', 'registered')
    ");
    $stmt->execute([$event_sport_id]);
    $counts = $stmt->fetch();
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>📊 <strong>Registration Summary</strong></h3>";
    echo "<p><strong>Total Registrations:</strong> {$counts['total_registrations']}</p>";
    echo "<p><strong>Total Participants:</strong> {$counts['total_participants']}</p>";
    
    if ($counts['total_registrations'] >= 2) {
        echo "<p style='color: #155724; font-weight: bold;'>🎉 Ready for tournament creation!</p>";
    } else {
        echo "<p style='color: #dc3545; font-weight: bold;'>❌ Still need more registrations</p>";
    }
    echo "</div>";
    
    echo "<h2>5. Test Tournament Creation</h2>";
    echo "<p><a href='complete-tournament-test.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🧪 Test Tournament Creation</a></p>";
    echo "<p><a href='manage-category.php?category_id=15&event_id=4&sport_id=37' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>📋 View Category Management</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}
?>
