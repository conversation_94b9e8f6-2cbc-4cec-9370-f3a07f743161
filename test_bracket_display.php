<?php
require_once 'config/database.php';
require_once 'admin/includes/bracket_display.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Testing Bracket Display</h2>";

// Get a test event sport
$stmt = $conn->prepare("SELECT id FROM event_sports LIMIT 1");
$stmt->execute();
$event_sport = $stmt->fetch();

if ($event_sport) {
    echo "<p>Testing with event_sport_id: " . $event_sport['id'] . "</p>";
    
    try {
        $bracketDisplay = new BracketDisplay($conn, $event_sport['id']);
        echo "<p>✅ BracketDisplay created successfully</p>";
        
        $html = $bracketDisplay->renderBracket();
        echo "<p>✅ Bracket rendered successfully</p>";
        echo "<div style='border: 1px solid #ccc; padding: 10px; margin: 10px 0;'>";
        echo $html;
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p>❌ Error: " . $e->getMessage() . "</p>";
        echo "<p>Stack trace:</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
} else {
    echo "<p>❌ No event sports found</p>";
}
?>
