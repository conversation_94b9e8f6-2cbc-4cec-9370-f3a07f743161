<?php
/**
 * Check Navigation Data for Category Management
 * Verify database has sufficient test data for navigation testing
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔍 Navigation Data Check</h1>";

try {
    // Check events
    echo "<h2>1. Events</h2>";
    $stmt = $conn->prepare("SELECT id, name, status FROM events ORDER BY id");
    $stmt->execute();
    $events = $stmt->fetchAll();
    
    if (empty($events)) {
        echo "<p style='color: red;'>❌ No events found</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($events) . " events:</p>";
        echo "<ul>";
        foreach ($events as $event) {
            echo "<li>ID: {$event['id']} - {$event['name']} ({$event['status']})</li>";
        }
        echo "</ul>";
    }
    
    // Check sports
    echo "<h2>2. Sports</h2>";
    $stmt = $conn->prepare("SELECT id, name, type FROM sports ORDER BY id");
    $stmt->execute();
    $sports = $stmt->fetchAll();
    
    if (empty($sports)) {
        echo "<p style='color: red;'>❌ No sports found</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($sports) . " sports:</p>";
        echo "<ul>";
        foreach ($sports as $sport) {
            echo "<li>ID: {$sport['id']} - {$sport['name']} ({$sport['type']})</li>";
        }
        echo "</ul>";
    }
    
    // Check event_sports relationships
    echo "<h2>3. Event-Sports Relationships</h2>";
    $stmt = $conn->prepare("
        SELECT es.id, es.event_id, es.sport_id, e.name as event_name, s.name as sport_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        ORDER BY es.id
    ");
    $stmt->execute();
    $event_sports = $stmt->fetchAll();
    
    if (empty($event_sports)) {
        echo "<p style='color: red;'>❌ No event-sports relationships found</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($event_sports) . " event-sports relationships:</p>";
        echo "<ul>";
        foreach ($event_sports as $es) {
            echo "<li>ID: {$es['id']} - {$es['event_name']} → {$es['sport_name']}</li>";
        }
        echo "</ul>";
    }
    
    // Check sport categories
    echo "<h2>4. Sport Categories</h2>";
    $stmt = $conn->prepare("
        SELECT sc.id, sc.category_name, sc.category_type, e.name as event_name, s.name as sport_name,
               es.event_id, es.sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        ORDER BY sc.id
    ");
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
    if (empty($categories)) {
        echo "<p style='color: red;'>❌ No sport categories found</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($categories) . " sport categories:</p>";
        echo "<ul>";
        foreach ($categories as $cat) {
            $url = "sport-categories.php?event_id={$cat['event_id']}&sport_id={$cat['sport_id']}";
            $manage_url = "manage-category.php?event_id={$cat['event_id']}&sport_id={$cat['sport_id']}&category_id={$cat['id']}";
            echo "<li>ID: {$cat['id']} - {$cat['category_name']} ({$cat['category_type']}) in {$cat['event_name']} → {$cat['sport_name']}<br>";
            echo "<small><a href='$url' target='_blank'>Categories Page</a> | <a href='$manage_url' target='_blank'>Direct Manage</a></small></li>";
        }
        echo "</ul>";
    }
    
    // Summary and recommendations
    echo "<h2>5. Summary & Recommendations</h2>";
    
    if (empty($events) || empty($sports) || empty($event_sports) || empty($categories)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
        echo "<h4>⚠️ Missing Data Detected</h4>";
        echo "<p>Some required data is missing for navigation testing. Click the button below to create test data:</p>";
        echo "<form method='POST' style='margin-top: 10px;'>";
        echo "<input type='hidden' name='action' value='create_test_data'>";
        echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>Create Test Data</button>";
        echo "</form>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<h4>✅ Navigation Ready</h4>";
        echo "<p>All required data is present for navigation testing. You can now test the category navigation flow.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

// Handle test data creation
if ($_POST['action'] ?? '' === 'create_test_data') {
    try {
        $conn->beginTransaction();
        
        // Create events if missing
        $event_count = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
        if ($event_count == 0) {
            $conn->exec("
                INSERT INTO events (name, description, start_date, end_date, venue, status) 
                VALUES 
                ('Test Championship 2024', 'Test event for navigation', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 14 DAY), 'Main Sports Complex', 'active'),
                ('Spring Tournament', 'Spring sports tournament', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 10 DAY), 'Athletic Center', 'active')
            ");
        }
        
        // Create sports if missing
        $sport_count = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
        if ($sport_count == 0) {
            $conn->exec("
                INSERT INTO sports (name, type, scoring_method, bracket_format) 
                VALUES 
                ('Basketball', 'traditional', 'point_based', 'single_elimination'),
                ('Volleyball', 'traditional', 'set_based', 'round_robin'),
                ('Badminton', 'traditional', 'point_based', 'single_elimination')
            ");
        }
        
        // Create event_sports relationships if missing
        $es_count = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
        if ($es_count == 0) {
            $conn->exec("
                INSERT INTO event_sports (event_id, sport_id, max_teams, status)
                SELECT e.id, s.id, 8, 'registration'
                FROM events e
                CROSS JOIN sports s
                LIMIT 6
            ");
        }
        
        // Create sport categories if missing
        $cat_count = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
        if ($cat_count == 0) {
            $stmt = $conn->prepare("
                INSERT INTO sport_categories (event_sport_id, category_name, category_type, referee_name, referee_email, venue)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $event_sports = $conn->query("SELECT id FROM event_sports")->fetchAll();
            $categories = [
                ['Men\'s Division', 'men'],
                ['Women\'s Division', 'women'],
                ['Mixed Division', 'mixed']
            ];
            
            foreach ($event_sports as $es) {
                foreach ($categories as $cat) {
                    $stmt->execute([
                        $es['id'],
                        $cat[0],
                        $cat[1],
                        'Test Referee',
                        '<EMAIL>',
                        'Sports Hall'
                    ]);
                }
            }
        }
        
        $conn->commit();
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745; margin-top: 20px;'>";
        echo "<h4>✅ Test Data Created Successfully</h4>";
        echo "<p>All required test data has been created. <a href='check-navigation-data.php'>Refresh this page</a> to see the updated data.</p>";
        echo "</div>";
        
    } catch (Exception $e) {
        $conn->rollback();
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545; margin-top: 20px;'>";
        echo "<h4>❌ Error Creating Test Data</h4>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
}
?>
