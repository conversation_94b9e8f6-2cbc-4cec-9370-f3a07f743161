<?php
require_once '../config/database.php';
require_once '../includes/advanced_tournament_engine.php';

$database = new Database();
$conn = $database->getConnection();

$eventSportId = 55; // Quiz Bowl event sport ID from the error
$categoryId = 19;   // Category ID from the error

echo "<h2>🔍 Tournament Generation Debug</h2>";

echo "<h3>1. Event Sport Information</h3>";
$stmt = $conn->prepare("
    SELECT es.*, s.name as sport_name, st.category as sport_category
    FROM event_sports es
    JOIN sports s ON es.sport_id = s.id
    JOIN sport_types st ON s.sport_type_id = st.id
    WHERE es.id = ?
");
$stmt->execute([$eventSportId]);
$eventSport = $stmt->fetch(PDO::FETCH_ASSOC);

if ($eventSport) {
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
    echo "<p><strong>Event Sport ID:</strong> " . $eventSport['id'] . "</p>";
    echo "<p><strong>Sport Name:</strong> " . $eventSport['sport_name'] . "</p>";
    echo "<p><strong>Sport Category:</strong> " . $eventSport['sport_category'] . "</p>";
    echo "<p><strong>Event ID:</strong> " . $eventSport['event_id'] . "</p>";
    echo "</div>";
} else {
    echo "<p>❌ Event Sport not found</p>";
    exit;
}

echo "<h3>2. Participant Retrieval Test</h3>";

// Test the exact participant retrieval logic from AdvancedTournamentEngine
$stmt = $conn->prepare("
    SELECT DISTINCT
        d.id,
        d.name,
        d.abbreviation,
        d.color_code,
        edr.status,
        edr.created_at as registration_date,
        edr.contact_person,
        edr.contact_email
    FROM event_department_registrations edr
    JOIN event_sports es ON edr.event_id = es.event_id
    JOIN departments d ON edr.department_id = d.id
    WHERE es.id = ? AND edr.status IN ('pending', 'approved')
    ORDER BY d.name
");
$stmt->execute([$eventSportId]);
$participants = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p><strong>Participants found:</strong> " . count($participants) . "</p>";

if (empty($participants)) {
    echo "<p>⚠️ No participants found with primary query. Trying fallback...</p>";
    
    // Fallback query
    $stmt = $conn->prepare("
        SELECT
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            'available' as status,
            NOW() as registration_date,
            NULL as contact_person,
            NULL as contact_email
        FROM departments d
        WHERE d.status = 'active'
        ORDER BY d.name
        LIMIT 10
    ");
    $stmt->execute();
    $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Fallback participants found:</strong> " . count($participants) . "</p>";
}

foreach ($participants as $participant) {
    echo "<div style='border: 1px solid #ddd; padding: 5px; margin: 2px 0;'>";
    echo "<p><strong>ID:</strong> " . $participant['id'] . " | <strong>Name:</strong> " . $participant['name'] . " | <strong>Status:</strong> " . $participant['status'] . "</p>";
    echo "</div>";
}

echo "<h3>3. Tournament Format Information</h3>";
$stmt = $conn->prepare("
    SELECT ts.*, tf.name as format_name, tf.code as format_code
    FROM tournament_structures ts
    JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
    WHERE ts.event_sport_id = ?
    ORDER BY ts.created_at DESC
    LIMIT 1
");
$stmt->execute([$eventSportId]);
$tournament = $stmt->fetch(PDO::FETCH_ASSOC);

if ($tournament) {
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
    echo "<p><strong>Tournament ID:</strong> " . $tournament['id'] . "</p>";
    echo "<p><strong>Format:</strong> " . $tournament['format_name'] . " (" . $tournament['format_code'] . ")</p>";
    echo "<p><strong>Status:</strong> " . $tournament['status'] . "</p>";
    echo "<p><strong>Total Participants:</strong> " . $tournament['total_participants'] . "</p>";
    echo "</div>";
} else {
    echo "<p>❌ No tournament structure found</p>";
}

echo "<h3>4. Test Tournament Generation</h3>";

if (!empty($participants) && $tournament) {
    echo "<p>🔧 Testing tournament generation with " . count($participants) . " participants...</p>";
    
    try {
        $engine = new AdvancedTournamentEngine($conn);
        
        // Test the generation process
        echo "<h4>Testing Match Creation Logic</h4>";
        
        // Simulate match data for different formats
        if ($tournament['format_code'] === 'judged_rounds') {
            echo "<p>Testing Judged Competition format...</p>";
            
            // For judged competitions, each participant performs individually
            $matchNumber = 1;
            for ($round = 1; $round <= 3; $round++) {
                foreach ($participants as $participant) {
                    $matchData = [
                        'team1_id' => $participant['id'],
                        'team2_id' => null, // No opponent in judged competitions
                        'bracket_position' => "R{$round}P{$matchNumber}",
                        'is_bye' => false
                    ];
                    
                    echo "<div style='background: #f0f8ff; padding: 5px; margin: 2px 0;'>";
                    echo "<p>Round $round, Performance $matchNumber: " . $participant['name'] . " (ID: " . $participant['id'] . ")</p>";
                    echo "<p>Match Data: " . json_encode($matchData) . "</p>";
                    echo "</div>";
                    
                    $matchNumber++;
                }
            }
        } elseif ($tournament['format_code'] === 'swiss_system') {
            echo "<p>Testing Academic Competition format...</p>";
            
            // For academic competitions, participants face each other
            $matchNumber = 1;
            for ($round = 1; $round <= 3; $round++) {
                $shuffled = $participants;
                shuffle($shuffled);
                
                for ($i = 0; $i < count($shuffled); $i += 2) {
                    if (isset($shuffled[$i + 1])) {
                        $matchData = [
                            'team1_id' => $shuffled[$i]['id'],
                            'team2_id' => $shuffled[$i + 1]['id'],
                            'bracket_position' => "R{$round}M{$matchNumber}",
                            'is_bye' => false
                        ];
                        
                        echo "<div style='background: #f0fff0; padding: 5px; margin: 2px 0;'>";
                        echo "<p>Round $round, Match $matchNumber: " . $shuffled[$i]['name'] . " vs " . $shuffled[$i + 1]['name'] . "</p>";
                        echo "<p>Team1 ID: " . $shuffled[$i]['id'] . ", Team2 ID: " . $shuffled[$i + 1]['id'] . "</p>";
                        echo "<p>Match Data: " . json_encode($matchData) . "</p>";
                        echo "</div>";
                        
                        $matchNumber++;
                    }
                }
            }
        }
        
        echo "<p>✅ Match data generation test completed successfully!</p>";
        
    } catch (Exception $e) {
        echo "<p>❌ Error during tournament generation test: " . $e->getMessage() . "</p>";
        echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
    }
} else {
    echo "<p>❌ Cannot test tournament generation - missing participants or tournament structure</p>";
}

echo "<h3>5. Check Existing Matches</h3>";
if ($tournament) {
    $stmt = $conn->prepare("
        SELECT m.*, d1.name as team1_name, d2.name as team2_name
        FROM matches m
        LEFT JOIN departments d1 ON m.team1_id = d1.id
        LEFT JOIN departments d2 ON m.team2_id = d2.id
        WHERE m.tournament_structure_id = ?
        ORDER BY m.round_number, m.match_number
    ");
    $stmt->execute([$tournament['id']]);
    $existingMatches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p><strong>Existing matches:</strong> " . count($existingMatches) . "</p>";
    
    foreach ($existingMatches as $match) {
        echo "<div style='border: 1px solid #ddd; padding: 5px; margin: 2px 0;'>";
        echo "<p>Match " . $match['match_number'] . " (Round " . $match['round_number'] . "): ";
        echo ($match['team1_name'] ?? 'NULL') . " vs " . ($match['team2_name'] ?? 'NULL');
        echo " | Team1 ID: " . ($match['team1_id'] ?? 'NULL') . " | Team2 ID: " . ($match['team2_id'] ?? 'NULL');
        echo "</p>";
        echo "</div>";
    }
}

echo "<p><a href='manage-category.php?event_id=4&sport_id=54&category_id=18'>← Back to Category Management</a></p>";
?>
