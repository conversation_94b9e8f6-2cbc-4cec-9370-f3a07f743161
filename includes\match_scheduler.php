<?php
/**
 * Match Scheduling & Management System
 * SC_IMS Sports Competition and Event Management System
 * 
 * Handles automated match creation, round-based scheduling, result entry,
 * and match progression logic for all tournament formats.
 */

class MatchScheduler {
    private $conn;
    private $tournamentId;
    
    public function __construct($conn, $tournamentId = null) {
        $this->conn = $conn;
        $this->tournamentId = $tournamentId;
    }
    
    /**
     * Create matches for a tournament based on bracket structure
     */
    public function createTournamentMatches($tournamentId, $bracket) {
        $this->tournamentId = $tournamentId;

        // Check if we're already in a transaction
        $inTransaction = $this->conn->inTransaction();

        try {
            if (!$inTransaction) {
                $this->conn->beginTransaction();
            }
            
            // Get tournament information
            $tournament = $this->getTournamentInfo($tournamentId);
            if (!$tournament) {
                throw new Exception('Tournament not found');
            }
            
            // Create matches based on tournament format
            switch ($bracket['format']) {
                case 'single_elimination':
                    $this->createSingleEliminationMatches($bracket);
                    break;
                case 'double_elimination':
                    $this->createDoubleEliminationMatches($bracket);
                    break;
                case 'round_robin':
                    $this->createRoundRobinMatches($bracket);
                    break;
                case 'multi_stage':
                    $this->createMultiStageMatches($bracket);
                    break;
                case 'swiss_system':
                    $this->createSwissSystemMatches($bracket);
                    break;
                default:
                    throw new Exception('Unsupported tournament format: ' . $bracket['format']);
            }

            if (!$inTransaction) {
                $this->conn->commit();
            }
            return true;

        } catch (Exception $e) {
            if (!$inTransaction && $this->conn->inTransaction()) {
                $this->conn->rollBack();
            }
            throw $e;
        }
    }
    
    /**
     * Create matches for single elimination tournament
     */
    private function createSingleEliminationMatches($bracket) {
        $participants = $bracket['participants'];
        $totalRounds = $bracket['rounds'];
        
        // Create first round matches
        $firstRoundMatches = [];
        for ($i = 0; $i < count($participants); $i += 2) {
            if (isset($participants[$i + 1])) {
                $match = $this->createMatch(
                    $participants[$i],
                    $participants[$i + 1],
                    1,
                    floor($i / 2) + 1
                );
                $firstRoundMatches[] = $match;
            }
        }
        
        // Create placeholder matches for subsequent rounds
        $this->createPlaceholderRounds($totalRounds, count($firstRoundMatches));
    }
    
    /**
     * Create matches for round robin tournament
     */
    private function createRoundRobinMatches($bracket) {
        $participants = $bracket['participants'];
        $participantCount = count($participants);
        
        $matchNumber = 1;
        
        // Generate all possible pairings
        for ($i = 0; $i < $participantCount; $i++) {
            for ($j = $i + 1; $j < $participantCount; $j++) {
                $this->createMatch(
                    $participants[$i],
                    $participants[$j],
                    1, // All matches in round 1 for round robin
                    $matchNumber++
                );
            }
        }
    }
    
    /**
     * Create matches for multi-stage tournament
     */
    private function createMultiStageMatches($bracket) {
        $structure = $bracket['structure'];
        
        // Create group stage matches
        if (isset($structure['group_stage'])) {
            $this->createGroupStageMatches($structure['group_stage']);
        }
        
        // Create placeholder knockout stage matches
        if (isset($structure['knockout_stage'])) {
            $this->createKnockoutStageMatches($structure['knockout_stage']);
        }
    }
    
    /**
     * Create group stage matches for multi-stage tournament
     */
    private function createGroupStageMatches($groupStage) {
        $groups = $groupStage['groups'];
        $matchNumber = 1;
        
        foreach ($groups as $groupName => $groupParticipants) {
            // Round robin within each group
            for ($i = 0; $i < count($groupParticipants); $i++) {
                for ($j = $i + 1; $j < count($groupParticipants); $j++) {
                    $match = $this->createMatch(
                        $groupParticipants[$i],
                        $groupParticipants[$j],
                        1, // Group stage round
                        $matchNumber++
                    );
                    
                    // Add group information
                    $this->updateMatchGroup($match['id'], $groupName);
                }
            }
        }
    }
    
    /**
     * Create knockout stage matches for multi-stage tournament
     */
    private function createKnockoutStageMatches($knockoutStage) {
        $teamsAdvancing = $knockoutStage['teams_advance'];
        $rounds = $knockoutStage['rounds'];
        
        // Create placeholder matches for knockout rounds
        $this->createPlaceholderRounds($rounds, $teamsAdvancing / 2, 2); // Start from round 2
    }
    
    /**
     * Create matches for Swiss system tournament
     */
    private function createSwissSystemMatches($bracket) {
        // Swiss system creates matches round by round based on standings
        // Create first round with initial pairings
        $participants = $bracket['participants'];
        $rounds = $bracket['rounds'];
        
        // Create first round matches
        $this->createSwissRoundMatches($participants, 1);
        
        // Create placeholder rounds for subsequent Swiss rounds
        for ($round = 2; $round <= $rounds; $round++) {
            $this->createSwissPlaceholderRound($round);
        }
    }
    
    /**
     * Create matches for a specific Swiss round
     */
    private function createSwissRoundMatches($participants, $round) {
        // Pair participants based on current standings
        $pairings = $this->generateSwissPairings($participants, $round);
        
        $matchNumber = 1;
        foreach ($pairings as $pairing) {
            $this->createMatch(
                $pairing['participant1'],
                $pairing['participant2'],
                $round,
                $matchNumber++
            );
        }
    }
    
    /**
     * Generate Swiss system pairings
     */
    private function generateSwissPairings($participants, $round) {
        if ($round === 1) {
            // First round: pair randomly or by seeding
            $pairings = [];
            for ($i = 0; $i < count($participants); $i += 2) {
                if (isset($participants[$i + 1])) {
                    $pairings[] = [
                        'participant1' => $participants[$i],
                        'participant2' => $participants[$i + 1]
                    ];
                }
            }
            return $pairings;
        }
        
        // Subsequent rounds: pair by standings
        // This is a simplified version - full Swiss pairing is more complex
        $standings = $this->getTournamentStandings();
        $pairings = [];
        
        for ($i = 0; $i < count($standings); $i += 2) {
            if (isset($standings[$i + 1])) {
                $pairings[] = [
                    'participant1' => $standings[$i],
                    'participant2' => $standings[$i + 1]
                ];
            }
        }
        
        return $pairings;
    }
    
    /**
     * Create a single match
     */
    private function createMatch($participant1, $participant2, $round, $matchNumber) {
        $sql = "INSERT INTO matches 
                (event_sport_id, tournament_structure_id, team1_id, team2_id, 
                 round_number, match_number, status, is_bye_match, created_at)
                VALUES (?, ?, ?, ?, ?, ?, 'scheduled', ?, NOW())";
        
        // Get event_sport_id from tournament
        $tournament = $this->getTournamentInfo($this->tournamentId);
        
        // Handle bye matches
        $isByeMatch = ($participant1['is_bye'] ?? false) || ($participant2['is_bye'] ?? false);
        
        // Get participant IDs (handle both department_id and registration_id)
        $team1Id = $this->getParticipantId($participant1);
        $team2Id = $this->getParticipantId($participant2);
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([
            $tournament['event_sport_id'],
            $this->tournamentId,
            $team1Id,
            $team2Id,
            $round,
            $matchNumber,
            $isByeMatch ? 1 : 0
        ]);
        
        $matchId = $this->conn->lastInsertId();
        
        // Auto-complete bye matches
        if ($isByeMatch) {
            $this->completeBye($matchId, $participant1, $participant2);
        }
        
        return [
            'id' => $matchId,
            'team1_id' => $team1Id,
            'team2_id' => $team2Id,
            'round' => $round,
            'match_number' => $matchNumber,
            'is_bye' => $isByeMatch
        ];
    }
    
    /**
     * Get participant ID for database storage
     */
    private function getParticipantId($participant) {
        if ($participant['is_bye'] ?? false) {
            return null; // Bye participants don't have IDs
        }
        
        // Try department_id first, then registration_id, then id
        return $participant['department_id'] ?? $participant['registration_id'] ?? $participant['id'] ?? null;
    }
    
    /**
     * Complete a bye match automatically
     */
    private function completeBye($matchId, $participant1, $participant2) {
        $winnerId = null;
        
        if ($participant1['is_bye'] ?? false) {
            $winnerId = $this->getParticipantId($participant2);
        } elseif ($participant2['is_bye'] ?? false) {
            $winnerId = $this->getParticipantId($participant1);
        }
        
        if ($winnerId) {
            $sql = "UPDATE matches 
                    SET status = 'completed', winner_id = ?, actual_start_time = NOW(), actual_end_time = NOW()
                    WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$winnerId, $matchId]);
        }
    }
    
    /**
     * Create placeholder rounds for elimination tournaments
     */
    private function createPlaceholderRounds($totalRounds, $firstRoundMatches, $startRound = 2) {
        $matchesInRound = $firstRoundMatches;
        
        for ($round = $startRound; $round <= $totalRounds; $round++) {
            $matchesInRound = ceil($matchesInRound / 2);
            
            for ($match = 1; $match <= $matchesInRound; $match++) {
                $this->createPlaceholderMatch($round, $match);
            }
        }
    }
    
    /**
     * Create a placeholder match for future rounds
     */
    private function createPlaceholderMatch($round, $matchNumber) {
        $sql = "INSERT INTO matches 
                (event_sport_id, tournament_structure_id, round_number, match_number, status, created_at)
                VALUES (?, ?, ?, ?, 'pending', NOW())";
        
        $tournament = $this->getTournamentInfo($this->tournamentId);
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([
            $tournament['event_sport_id'],
            $this->tournamentId,
            $round,
            $matchNumber
        ]);
        
        return $this->conn->lastInsertId();
    }
    
    /**
     * Create placeholder round for Swiss system
     */
    private function createSwissPlaceholderRound($round) {
        // Swiss system rounds are created dynamically based on standings
        // This creates a placeholder that will be populated later
        $sql = "INSERT INTO tournament_rounds 
                (tournament_structure_id, round_number, status, created_at)
                VALUES (?, ?, 'pending', NOW())";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId, $round]);
    }
    
    /**
     * Update match with group information
     */
    private function updateMatchGroup($matchId, $groupName) {
        $sql = "UPDATE matches SET bracket_position = ? WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$groupName, $matchId]);
    }
    
    /**
     * Get tournament information
     */
    private function getTournamentInfo($tournamentId) {
        $sql = "SELECT * FROM tournament_structures WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$tournamentId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get current tournament standings
     */
    private function getTournamentStandings() {
        $sql = "SELECT tp.*, r.team_name, r.department_id
                FROM tournament_participants tp
                JOIN registrations r ON tp.registration_id = r.id
                WHERE tp.tournament_structure_id = ?
                ORDER BY tp.points DESC, tp.wins DESC, tp.draws DESC";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Advance tournament to next round
     */
    public function advanceToNextRound($currentRound) {
        // Get completed matches from current round
        $sql = "SELECT * FROM matches 
                WHERE tournament_structure_id = ? AND round_number = ? AND status = 'completed'";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId, $currentRound]);
        $completedMatches = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Update next round matches with winners
        $this->updateNextRoundMatches($completedMatches, $currentRound + 1);
        
        // Update tournament current round
        $sql = "UPDATE tournament_structures SET current_round = ? WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$currentRound + 1, $this->tournamentId]);
    }
    
    /**
     * Update next round matches with winners from current round
     */
    private function updateNextRoundMatches($completedMatches, $nextRound) {
        $winners = [];
        foreach ($completedMatches as $match) {
            if ($match['winner_id']) {
                $winners[] = $match['winner_id'];
            }
        }
        
        // Get next round matches
        $sql = "SELECT * FROM matches 
                WHERE tournament_structure_id = ? AND round_number = ? AND status = 'pending'
                ORDER BY match_number";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId, $nextRound]);
        $nextRoundMatches = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Assign winners to next round matches
        $winnerIndex = 0;
        foreach ($nextRoundMatches as $match) {
            if ($winnerIndex < count($winners)) {
                $team1Id = $winners[$winnerIndex++] ?? null;
                $team2Id = $winners[$winnerIndex++] ?? null;
                
                $sql = "UPDATE matches SET team1_id = ?, team2_id = ?, status = 'scheduled' 
                        WHERE id = ?";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$team1Id, $team2Id, $match['id']]);
            }
        }
    }
}
?>
