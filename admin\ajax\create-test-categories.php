<?php
/**
 * Create Test Categories AJAX Endpoint
 * Creates sample categories for testing navigation
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get available event-sport combinations
    $stmt = $conn->prepare("
        SELECT es.id as event_sport_id, es.event_id, es.sport_id, 
               e.name as event_name, s.name as sport_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        ORDER BY es.id
        LIMIT 5
    ");
    $stmt->execute();
    $event_sports = $stmt->fetchAll();
    
    if (empty($event_sports)) {
        echo json_encode([
            'success' => false,
            'message' => 'No event-sport combinations found. Please create events and add sports to them first.'
        ]);
        exit;
    }
    
    $categories_created = 0;
    $categories_skipped = 0;
    
    foreach ($event_sports as $es) {
        // Create Men's and Women's categories for each sport
        $categories = [
            ['name' => "Men's " . $es['sport_name'], 'type' => 'men'],
            ['name' => "Women's " . $es['sport_name'], 'type' => 'women'],
            ['name' => "Mixed " . $es['sport_name'], 'type' => 'mixed']
        ];
        
        foreach ($categories as $cat) {
            // Check if category already exists
            $check_stmt = $conn->prepare("
                SELECT id FROM sport_categories 
                WHERE event_sport_id = ? AND category_name = ?
            ");
            $check_stmt->execute([$es['event_sport_id'], $cat['name']]);
            
            if (!$check_stmt->fetch()) {
                // Create the category
                $insert_stmt = $conn->prepare("
                    INSERT INTO sport_categories 
                    (event_sport_id, category_name, category_type, referee_name, referee_email, venue)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                
                $insert_stmt->execute([
                    $es['event_sport_id'],
                    $cat['name'],
                    $cat['type'],
                    'Test Referee',
                    '<EMAIL>',
                    'Main Sports Complex'
                ]);
                
                $categories_created++;
                
                // Log the creation
                logAdminActivity('CREATE_TEST_CATEGORY', 'sport_categories', $conn->lastInsertId(), null, [
                    'category_name' => $cat['name'],
                    'event_sport_id' => $es['event_sport_id'],
                    'event_name' => $es['event_name'],
                    'sport_name' => $es['sport_name']
                ]);
            } else {
                $categories_skipped++;
            }
        }
    }
    
    echo json_encode([
        'success' => true,
        'message' => "Successfully created $categories_created test categories" . 
                    ($categories_skipped > 0 ? " (skipped $categories_skipped existing)" : ""),
        'categories_created' => $categories_created,
        'categories_skipped' => $categories_skipped
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error creating test categories: ' . $e->getMessage()
    ]);
}
?>
