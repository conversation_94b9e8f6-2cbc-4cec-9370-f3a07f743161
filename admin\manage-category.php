<?php
/**
 * Manage Category - COMPLETELY REWRITTEN FROM SCRATCH
 * SC_IMS Sports Competition and Event Management System
 * 
 * This is a complete rewrite to eliminate ALL persistent column errors
 * Using ONLY simple, direct database queries that work reliably
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get parameters
$event_id = $_GET['event_id'] ?? 0;
$sport_id = $_GET['sport_id'] ?? 0;
$category_id = $_GET['category_id'] ?? 0;

if (!$event_id || !$sport_id || !$category_id) {
    header('Location: events.php');
    exit;
}

// Get category information with SIMPLE query - NO COMPLEX JOINS
try {
    $stmt = $conn->prepare("
        SELECT 
            sc.*,
            e.name as event_name,
            s.name as sport_name,
            es.id as event_sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ?
    ");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();

    if (!$category) {
        header('Location: events.php');
        exit;
    }

} catch (Exception $e) {
    error_log("Error fetching category: " . $e->getMessage());
    header('Location: events.php');
    exit;
}

// Get participants using SIMPLE direct department approach
// NO COMPLEX REGISTRATION QUERIES - NO team_name COLUMN REFERENCES
$participants = [];
try {
    $stmt = $conn->prepare("
        SELECT 
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            'active' as status,
            NOW() as registration_date
        FROM departments d
        WHERE d.status = 'active'
        ORDER BY d.name
        LIMIT 10
    ");
    $stmt->execute();
    $participants = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching participants: " . $e->getMessage());
    $participants = [];
}

// SIMPLIFIED Auto-generation - NO COMPLEX BRACKET ENGINE CALLS
$auto_tournament_message = '';
$auto_tournament_generated = false;

try {
    // Check if tournament already exists
    $stmt = $conn->prepare("
        SELECT id, status FROM tournament_structures
        WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')
    ");
    $stmt->execute([$category['event_sport_id']]);
    $existing_tournament = $stmt->fetch();

    if (!$existing_tournament && count($participants) >= 2) {
        // Create simple tournament structure
        $stmt = $conn->prepare("
            INSERT INTO tournament_structures 
            (event_sport_id, name, format, status, participant_count, created_at)
            VALUES (?, ?, 'Single Elimination', 'active', ?, NOW())
        ");
        $stmt->execute([
            $category['event_sport_id'],
            ($category['event_name'] ?? 'Event') . ' - ' . ($category['sport_name'] ?? 'Sport') . ' - ' . ($category['name'] ?? 'Category'),
            count($participants)
        ]);
        
        $tournament_id = $conn->lastInsertId();
        
        // Add participants to tournament - SIMPLE INSERT
        foreach ($participants as $participant) {
            $stmt = $conn->prepare("
                INSERT INTO tournament_participants 
                (tournament_structure_id, department_id, team_name, current_status, created_at)
                VALUES (?, ?, ?, 'active', NOW())
            ");
            $stmt->execute([
                $tournament_id,
                $participant['id'],
                $participant['name']
            ]);
        }
        
        $auto_tournament_generated = true;
        $auto_tournament_message = "Tournament automatically generated with " . count($participants) . " participants.";
        
    } elseif ($existing_tournament) {
        $auto_tournament_message = "Tournament already exists (Status: " . $existing_tournament['status'] . ").";
    } else {
        $auto_tournament_message = "Need at least 2 participants to generate tournament. Currently have " . count($participants) . ".";
    }
    
} catch (Exception $e) {
    $auto_tournament_message = "Auto-generation failed: " . $e->getMessage();
    error_log("Auto-tournament generation error: " . $e->getMessage());
}

// Get matches using SIMPLE query - NO COMPLEX JOINS
$matches = [];
try {
    $stmt = $conn->prepare("
        SELECT 
            m.*,
            d1.name as team1_name,
            d2.name as team2_name,
            dw.name as winner_name
        FROM matches m
        LEFT JOIN tournament_participants tp1 ON m.team1_id = tp1.id
        LEFT JOIN departments d1 ON tp1.department_id = d1.id
        LEFT JOIN tournament_participants tp2 ON m.team2_id = tp2.id
        LEFT JOIN departments d2 ON tp2.department_id = d2.id
        LEFT JOIN tournament_participants tpw ON m.winner_id = tpw.id
        LEFT JOIN departments dw ON tpw.department_id = dw.id
        WHERE m.event_sport_id = ?
        ORDER BY m.round_number, m.match_number
    ");
    $stmt->execute([$category['event_sport_id']]);
    $matches = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching matches: " . $e->getMessage());
    $matches = [];
}

// Get standings using SIMPLE query
$standings = [];
try {
    $stmt = $conn->prepare("
        SELECT 
            tp.*,
            d.name as department_name,
            d.abbreviation,
            d.color_code
        FROM tournament_participants tp
        JOIN departments d ON tp.department_id = d.id
        JOIN tournament_structures ts ON tp.tournament_structure_id = ts.id
        WHERE ts.event_sport_id = ?
        ORDER BY tp.points DESC, tp.wins DESC, d.name
    ");
    $stmt->execute([$category['event_sport_id']]);
    $standings = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching standings: " . $e->getMessage());
    $standings = [];
}

// Log admin activity - Fix session and parameter issues
try {
    $admin_id = $_SESSION['admin_user_id'] ?? null;
    $category_name = $category['name'] ?? 'Unknown';
    if ($admin_id) {
        logAdminActivity('VIEW_CATEGORY', 'sport_categories', $category_id);
    }
} catch (Exception $e) {
    error_log("Error logging admin activity: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($category['name'] ?? 'Category'); ?> - Category Management</title>
    <link rel="stylesheet" href="../assets/css/admin-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .admin-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 20px;
        }

        .header-title {
            flex: 1;
        }

        .header-title h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin: 0 0 10px 0;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .header-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin: 0;
        }

        .header-actions {
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .btn-back {
            background: rgba(255,255,255,0.2);
            color: white;
            border: 1px solid rgba(255,255,255,0.3);
            padding: 12px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .btn-back:hover {
            background: rgba(255,255,255,0.3);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .content-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }

        .tab-navigation {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
        }

        .tab-button {
            flex: 1;
            padding: 20px 30px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-weight: 600;
            font-size: 1rem;
            color: #6c757d;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-button:hover {
            background: rgba(0,123,255,0.05);
            color: #007bff;
        }

        .tab-button.active {
            background: white;
            color: #007bff;
            border-bottom: 3px solid #007bff;
        }

        .tab-button i {
            margin-right: 8px;
        }

        .tab-content {
            padding: 30px;
            min-height: 500px;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #007bff;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 1rem;
            color: #6c757d;
            font-weight: 500;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .data-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: 600;
            color: #495057;
            border-bottom: 2px solid #dee2e6;
        }

        .data-table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            color: #495057;
        }

        .data-table tr:hover {
            background: #f8f9fa;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }

        .status-completed {
            background: #cce5ff;
            color: #004085;
            border: 1px solid #b3d9ff;
        }

        .alert-info {
            background: linear-gradient(135deg, #e7f3ff 0%, #cce5ff 100%);
            border: 1px solid #b3d9ff;
            color: #004085;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .alert-info i {
            font-size: 1.2rem;
            opacity: 0.8;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.3;
        }

        .empty-state h3 {
            margin-bottom: 10px;
            color: #495057;
        }

        .empty-state p {
            font-size: 0.95rem;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <div class="admin-header">
            <div class="header-content">
                <div class="header-title">
                    <h1><?php echo htmlspecialchars($category['name'] ?? 'Category'); ?></h1>
                    <p class="header-subtitle">
                        <?php echo htmlspecialchars($category['event_name'] ?? 'Event'); ?> - <?php echo htmlspecialchars($category['sport_name'] ?? 'Sport'); ?>
                    </p>
                </div>
                <div class="header-actions">
                    <a href="manage-event.php?id=<?php echo $event_id; ?>" class="btn-back">
                        <i class="fas fa-arrow-left"></i> Back to Event
                    </a>
                </div>
            </div>
        </div>

        <?php if ($auto_tournament_message): ?>
        <div class="alert-info">
            <i class="fas fa-info-circle"></i>
            <span><?php echo htmlspecialchars($auto_tournament_message); ?></span>
        </div>
        <?php endif; ?>

        <!-- Main Content -->
        <div class="content-section">
            <div class="tab-navigation">
                <button class="tab-button active" onclick="showTab('overview')">
                    <i class="fas fa-info-circle"></i> Overview
                </button>
                <button class="tab-button" onclick="showTab('fixtures')">
                    <i class="fas fa-sitemap"></i> Fixtures
                </button>
                <button class="tab-button" onclick="showTab('standings')">
                    <i class="fas fa-trophy"></i> Standings
                </button>
            </div>

            <div class="tab-content">
                <!-- Overview Tab -->
                <div id="overview" class="tab-pane active">
                    <h3 style="margin-bottom: 25px; color: #495057; font-weight: 600;">Category Overview</h3>

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value"><?php echo count($participants); ?></div>
                            <div class="stat-label">Participants</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value"><?php echo count($matches); ?></div>
                            <div class="stat-label">Matches</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">Single Elimination</div>
                            <div class="stat-label">Format</div>
                        </div>
                    </div>

                    <h4 style="margin-bottom: 15px; color: #495057; font-weight: 600;">Registered Participants</h4>
                    <?php if (!empty($participants)): ?>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Department</th>
                                <th>Abbreviation</th>
                                <th>Status</th>
                                <th>Registration Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($participants as $participant): ?>
                            <tr>
                                <td><?php echo htmlspecialchars($participant['name']); ?></td>
                                <td><strong><?php echo htmlspecialchars($participant['abbreviation']); ?></strong></td>
                                <td><span class="status-badge status-<?php echo $participant['status']; ?>"><?php echo ucfirst($participant['status']); ?></span></td>
                                <td><?php echo date('M j, Y', strtotime($participant['registration_date'])); ?></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-users"></i>
                        <h3>No Participants Yet</h3>
                        <p>Participants will appear here once departments are registered for this category</p>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Fixtures Tab -->
                <div id="fixtures" class="tab-pane">
                    <h3 style="margin-bottom: 25px; color: #495057; font-weight: 600;">Tournament Fixtures</h3>

                    <?php if (!empty($matches)): ?>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Round</th>
                                <th>Match</th>
                                <th>Team 1</th>
                                <th>Team 2</th>
                                <th>Winner</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($matches as $match): ?>
                            <tr>
                                <td><strong>Round <?php echo $match['round_number']; ?></strong></td>
                                <td>Match <?php echo $match['match_number']; ?></td>
                                <td><?php echo htmlspecialchars($match['team1_name'] ?? 'TBD'); ?></td>
                                <td><?php echo htmlspecialchars($match['team2_name'] ?? 'TBD'); ?></td>
                                <td><?php echo htmlspecialchars($match['winner_name'] ?? 'TBD'); ?></td>
                                <td><span class="status-badge status-<?php echo $match['status']; ?>"><?php echo ucfirst($match['status']); ?></span></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-sitemap"></i>
                        <h3>No Matches Scheduled</h3>
                        <p>Tournament fixtures will appear here once the tournament is generated</p>
                    </div>
                    <?php endif; ?>
                </div>

                <!-- Standings Tab -->
                <div id="standings" class="tab-pane">
                    <h3 style="margin-bottom: 25px; color: #495057; font-weight: 600;">Tournament Standings</h3>

                    <?php if (!empty($standings)): ?>
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>Rank</th>
                                <th>Team</th>
                                <th>Matches Played</th>
                                <th>Wins</th>
                                <th>Losses</th>
                                <th>Points</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($standings as $index => $standing): ?>
                            <tr>
                                <td><strong>#<?php echo $index + 1; ?></strong></td>
                                <td><?php echo htmlspecialchars($standing['department_name']); ?></td>
                                <td><?php echo $standing['wins'] + $standing['losses']; ?></td>
                                <td><span style="color: #28a745; font-weight: 600;"><?php echo $standing['wins']; ?></span></td>
                                <td><span style="color: #dc3545; font-weight: 600;"><?php echo $standing['losses']; ?></span></td>
                                <td><strong style="color: #007bff;"><?php echo $standing['points']; ?></strong></td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php else: ?>
                    <div class="empty-state">
                        <i class="fas fa-trophy"></i>
                        <h3>No Standings Available</h3>
                        <p>Tournament standings will appear here once matches are played and results are recorded</p>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabName) {
            // Hide all tab panes
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('active');
            });
            
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            
            // Show selected tab pane
            document.getElementById(tabName).classList.add('active');
            
            // Add active class to clicked button
            event.target.classList.add('active');
        }

        // Auto-refresh every 30 seconds
        setInterval(function() {
            location.reload();
        }, 30000);

        <?php if ($auto_tournament_generated): ?>
        // Show success notification
        setTimeout(function() {
            alert('Tournament automatically generated with <?php echo count($participants); ?> participants!');
        }, 1000);
        <?php endif; ?>
    </script>
</body>
</html>
