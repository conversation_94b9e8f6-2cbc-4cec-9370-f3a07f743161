<?php
/**
 * Category Management Hub for SC_IMS Admin Panel
 * Comprehensive category management with Overview, Fixtures, and Standings
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get URL parameters
$event_id = $_GET['event_id'] ?? null;
$sport_id = $_GET['sport_id'] ?? null;
$category_id = $_GET['category_id'] ?? null;

// Debug: Log all access attempts
error_log("manage-category.php accessed - event_id: $event_id, sport_id: $sport_id, category_id: $category_id");

// Validate required parameters
if (empty($event_id) || empty($sport_id) || empty($category_id) || 
    !is_numeric($event_id) || !is_numeric($sport_id) || !is_numeric($category_id)) {
    
    error_log("manage-category.php REDIRECT - Invalid parameters");
    
    // If debug mode, show error instead of redirecting
    if (isset($_GET['debug'])) {
        die("DEBUG: Invalid parameters - event_id: $event_id, sport_id: $sport_id, category_id: $category_id");
    }
    
    header('Location: events.php');
    exit;
}

// Convert to integers
$event_id = intval($event_id);
$sport_id = intval($sport_id);
$category_id = intval($category_id);

try {
    // Get comprehensive category information
    // Check if venue column exists in events table
    $venue_column_exists = false;
    try {
        $check_stmt = $conn->query("SHOW COLUMNS FROM events LIKE 'venue'");
        $venue_column_exists = $check_stmt->rowCount() > 0;
    } catch (Exception $e) {
        // If check fails, assume venue column doesn't exist
        $venue_column_exists = false;
    }

    // Build query based on whether venue column exists
    $venue_select = $venue_column_exists ? "e.venue as event_venue," : "'Main Venue' as event_venue,";

    $stmt = $conn->prepare("
        SELECT
            sc.*,
            e.name as event_name,
            e.description as event_description,
            e.start_date,
            e.end_date,
            $venue_select
            s.name as sport_name,
            s.type as sport_type,
            s.description as sport_description,
            es.id as event_sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ? AND es.event_id = ? AND s.id = ?
    ");
    
    $stmt->execute([$category_id, $event_id, $sport_id]);
    $category = $stmt->fetch();
    
    if (!$category) {
        error_log("manage-category.php REDIRECT - Category not found");
        
        if (isset($_GET['debug'])) {
            die("DEBUG: Category not found - category_id: $category_id, event_id: $event_id, sport_id: $sport_id");
        }
        
        header('Location: events.php');
        exit;
    }
    
    error_log("manage-category.php SUCCESS - Category found: " . $category['category_name']);

    // Get tournament format information from event sports configuration
    $tournament_format = null;
    try {
        // First, get the event sport configuration with tournament format
        $stmt = $conn->prepare("
            SELECT
                es.tournament_format_id,
                es.bracket_type,
                tf.name as format_name,
                tf.description as format_description,
                tf.code as format_code
            FROM event_sports es
            LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
            WHERE es.id = ?
        ");
        $stmt->execute([$category['event_sport_id']]);
        $event_sport_config = $stmt->fetch();

        if ($event_sport_config) {
            if ($event_sport_config['format_name']) {
                // Use the configured tournament format from event sports
                $tournament_format = [
                    'format_name' => $event_sport_config['format_name'],
                    'format_description' => $event_sport_config['format_description'] ?? 'Tournament format configured for this event',
                    'format_code' => $event_sport_config['format_code'],
                    'tournament_status' => 'configured',
                    'participant_count' => 0,
                    'total_rounds' => 0,
                    'current_round' => 0,
                    'seeding_method' => 'random'
                ];
            } elseif ($event_sport_config['bracket_type']) {
                // Use legacy bracket_type from event sports
                $tournament_format = [
                    'format_name' => ucwords(str_replace('_', ' ', $event_sport_config['bracket_type'])),
                    'format_description' => 'Tournament format configured for this event',
                    'format_code' => $event_sport_config['bracket_type'],
                    'tournament_status' => 'configured',
                    'participant_count' => 0,
                    'total_rounds' => 0,
                    'current_round' => 0,
                    'seeding_method' => 'random'
                ];
            }
        }
    } catch (Exception $e) {
        error_log("Error fetching event sport tournament format: " . $e->getMessage());
    }

    // If no event sport format found, fall back to sport default
    if (!$tournament_format) {
        try {
            $stmt = $conn->prepare("SELECT bracket_format FROM sports WHERE id = ?");
            $stmt->execute([$sport_id]);
            $sport_format = $stmt->fetch();
            if ($sport_format) {
                $tournament_format = [
                    'format_name' => ucwords(str_replace('_', ' ', $sport_format['bracket_format'])),
                    'format_description' => 'Default format for this sport (not configured for event)',
                    'format_code' => $sport_format['bracket_format'],
                    'tournament_status' => 'default',
                    'participant_count' => 0,
                    'total_rounds' => 0,
                    'current_round' => 0,
                    'seeding_method' => 'random'
                ];
            }
        } catch (Exception $e) {
            error_log("Error fetching sport format: " . $e->getMessage());
        }
    }

} catch (Exception $e) {
    error_log("manage-category.php ERROR: " . $e->getMessage());
    
    if (isset($_GET['debug'])) {
        die("DEBUG: Database error - " . $e->getMessage());
    }
    
    header('Location: events.php');
    exit;
}

// Get registered departments for this category using the same logic as UnifiedBracketEngine
try {
    // Use the same participant retrieval logic as the UnifiedBracketEngine for consistency
    require_once '../includes/unified_bracket_engine.php';
    $bracketEngine = new UnifiedBracketEngine($conn);
    $rawParticipants = $bracketEngine->getEventParticipants($category['event_sport_id']);

    // Convert to the format expected by the UI
    $participants = [];
    foreach ($rawParticipants as $participant) {
        $participants[] = [
            'id' => $participant['department_id'] ?? $participant['id'],
            'name' => $participant['department_name'] ?? $participant['team_name'],
            'abbreviation' => '', // Will be filled from departments table if needed
            'color_code' => $participant['color_code'] ?? '#007bff',
            'registration_id' => $participant['id'],
            'registration_date' => $participant['created_at'] ?? $participant['registration_date'] ?? date('Y-m-d H:i:s'),
            'registration_status' => $participant['status'] ?? 'registered',
            'total_participants' => 1, // Default to 1 participant per department
            'matches_played' => 0,
            'wins' => 0,
            'losses' => 0,
            'points' => 0
        ];
    }

    // If still no participants, try to get from general event registrations as final fallback
    if (empty($participants)) {
        $stmt = $conn->prepare("
            SELECT
                d.id,
                d.name,
                d.abbreviation,
                d.color_code,
                edr.id as registration_id,
                edr.registration_date,
                edr.status as registration_status,
                edr.total_participants,
                0 as matches_played,
                0 as wins,
                0 as losses,
                0 as points
            FROM departments d
            JOIN event_department_registrations edr ON d.id = edr.department_id
            WHERE edr.event_id = ? AND edr.status IN ('approved', 'pending')
            ORDER BY d.name
        ");
        $stmt->execute([$event_id]);
        $participants = $stmt->fetchAll();
    }

    // If we have matches for this category, calculate stats
    if (!empty($participants)) {
        foreach ($participants as &$participant) {
            // Get match statistics for this department in this category
            // Note: This assumes matches will be linked to categories in the future
            // For now, we'll show 0 stats since the matches table structure needs updating
            $participant['matches_played'] = 0;
            $participant['wins'] = 0;
            $participant['losses'] = 0;
            $participant['points'] = 0;
        }
    }

} catch (Exception $e) {
    $participants = [];
    error_log("Error fetching participants: " . $e->getMessage());
}

// Auto-generate tournament if conditions are met
$auto_tournament_generated = false;
$auto_tournament_message = '';
try {
    // Check if automatic tournament generation should occur
    if (count($participants) >= 2 && $tournament_format && $tournament_format['tournament_status'] === 'configured') {

        // Check if tournament already exists
        $stmt = $conn->prepare("
            SELECT id, status FROM tournament_structures
            WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')
        ");
        $stmt->execute([$category['event_sport_id']]);
        $existing_tournament = $stmt->fetch();

        if (!$existing_tournament) {
            // Auto-generate tournament using unified bracket engine
            require_once '../includes/unified_bracket_engine.php';
            $bracketEngine = new UnifiedBracketEngine($conn);

            // First, check participants for debugging
            $debugParticipants = $bracketEngine->getEventParticipants($category['event_sport_id']);
            $participantCount = count($debugParticipants);

            error_log("Debug: Found $participantCount participants for event_sport_id: " . $category['event_sport_id']);
            error_log("Debug: Participants: " . json_encode($debugParticipants));

            $config = [
                'seeding_method' => 'random',
                'bracket_seeding' => true,
                'auto_generated' => true,
                'comprehensive_validation' => true,
                'fallback_enabled' => true
            ];

            try {
                if ($participantCount >= 2) {
                    $result = $bracketEngine->generateBracketForCategory($category['event_sport_id'], $config);

                    if ($result['success']) {
                        $auto_tournament_generated = true;
                        $auto_tournament_message = "Tournament automatically generated with " . $result['participant_count'] . " participants using " . $result['format'] . " format.";
                        error_log("Auto-generated tournament ID: " . $result['tournament_id'] . " for event_sport_id: " . $category['event_sport_id']);
                    } else {
                        $auto_tournament_message = "Auto-generation failed: " . ($result['message'] ?? 'Unknown error');
                    }
                } else {
                    $auto_tournament_message = "Auto-generation failed: At least 2 participants required for tournament generation (found $participantCount). <a href='debug-participants.php?event_sport_id=" . $category['event_sport_id'] . "' target='_blank'>Debug Participants</a>";
                }
            } catch (Exception $e) {
                $auto_tournament_message = "Auto-generation failed: " . $e->getMessage() . " <a href='debug-participants.php?event_sport_id=" . $category['event_sport_id'] . "' target='_blank'>Debug Participants</a>";
                error_log("Auto-tournament generation error: " . $e->getMessage());
            }

        } else {
            $auto_tournament_message = "Tournament already exists (Status: " . $existing_tournament['status'] . ").";
        }
    } else {
        if (count($participants) < 2) {
            $auto_tournament_message = "Need at least 2 participants to auto-generate tournament. Currently have " . count($participants) . ".";
        } elseif (!$tournament_format) {
            $auto_tournament_message = "No tournament format configured for auto-generation.";
        } else {
            $auto_tournament_message = "Tournament format not properly configured.";
        }
    }
} catch (Exception $e) {
    $auto_tournament_message = "Auto-generation failed: " . $e->getMessage();
    error_log("Auto-tournament generation error: " . $e->getMessage());
}

// Get matches for this category
try {
    // Since matches table doesn't have sport_category_id column yet,
    // we'll get matches by event_sport_id for now
    // TODO: Add category-specific match filtering when matches table is updated
    $stmt = $conn->prepare("
        SELECT
            m.*,
            ta.name as team_a_name,
            ta.abbreviation as team_a_abbr,
            ta.color_code as team_a_color,
            tb.name as team_b_name,
            tb.abbreviation as team_b_abbr,
            tb.color_code as team_b_color,
            w.name as winner_name,
            w.abbreviation as winner_abbr
        FROM matches m
        LEFT JOIN departments ta ON m.team1_id = ta.id
        LEFT JOIN departments tb ON m.team2_id = tb.id
        LEFT JOIN departments w ON m.winner_id = w.id
        WHERE m.event_sport_id = ?
        ORDER BY m.round_number, m.match_number
    ");
    $stmt->execute([$category['event_sport_id']]);
    $matches = $stmt->fetchAll();
} catch (Exception $e) {
    $matches = [];
    error_log("Error fetching matches: " . $e->getMessage());
}

// Calculate statistics
$total_participants = count($participants);
$total_matches = count($matches);
$completed_matches = count(array_filter($matches, function($m) { return $m['status'] === 'completed'; }));
$pending_matches = $total_matches - $completed_matches;

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($category['category_name']); ?> - <?php echo htmlspecialchars($category['sport_name']); ?> | SC_IMS Admin</title>
    <?php include 'includes/admin-styles.php'; ?>
    <style>
        /* Tab Navigation Styles */
        .tab-navigation {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 20px;
            border-radius: 8px 8px 0 0;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #6c757d;
            transition: all 0.3s ease;
            position: relative;
            z-index: 10;
            pointer-events: auto;
        }

        .tab-button:hover {
            background: #e9ecef;
            color: #495057;
        }

        .tab-button.active {
            background: #007bff;
            color: white;
        }

        /* Tab Content Styles */
        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Category Header Styles */
        .category-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .category-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .category-subtitle {
            opacity: 0.9;
            font-size: 1rem;
        }

        /* Statistics Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }

        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        /* Card Styles */
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #495057;
            margin: 0;
        }

        .card-body {
            padding: 20px;
        }

        /* Button Styles */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
            position: relative;
            z-index: 10;
            pointer-events: auto;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <!-- Breadcrumb Navigation -->
        <nav aria-label="breadcrumb" style="margin-bottom: 20px;">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="events.php">Events</a></li>
                <li class="breadcrumb-item"><a href="manage-event.php?id=<?php echo $event_id; ?>"><?php echo htmlspecialchars($category['event_name']); ?></a></li>
                <li class="breadcrumb-item"><a href="sport-categories.php?event_id=<?php echo $event_id; ?>&sport_id=<?php echo $sport_id; ?>"><?php echo htmlspecialchars($category['sport_name']); ?> Categories</a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($category['category_name']); ?></li>
            </ol>
        </nav>

        <!-- Category Header -->
        <div class="category-header">
            <div class="category-title"><?php echo htmlspecialchars($category['category_name']); ?></div>
            <div class="category-subtitle">
                <?php echo htmlspecialchars($category['sport_name']); ?> - <?php echo htmlspecialchars($category['event_name']); ?>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-number"><?php echo $total_participants; ?></div>
                <div class="stats-label">Participants</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $total_matches; ?></div>
                <div class="stats-label">Total Matches</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $completed_matches; ?></div>
                <div class="stats-label">Completed</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $pending_matches; ?></div>
                <div class="stats-label">Pending</div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="tab-navigation">
            <button class="tab-button active" onclick="showTab('overview', this)">
                <i class="fas fa-info-circle"></i> Overview
            </button>
            <button class="tab-button" onclick="showTab('fixtures', this)">
                <i class="fas fa-sitemap"></i> Fixtures
            </button>
            <button class="tab-button" onclick="showTab('standings', this)">
                <i class="fas fa-trophy"></i> Standings
            </button>
        </div>

        <!-- Tab Content -->
        <div id="overview-tab" class="tab-content active">
            <!-- Overview Tab Content -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Category Information</h3>
                    <div>
                        <button class="btn btn-success btn-sm" onclick="alert('JavaScript is working!'); console.log('Test button clicked!');" style="margin-right: 10px;">
                            <i class="fas fa-check"></i> Test JS
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="openEditCategoryModal()">
                            <i class="fas fa-edit"></i> Edit Category
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div>
                            <h5>Basic Details</h5>
                            <p><strong>Category Name:</strong> <?php echo htmlspecialchars($category['category_name']); ?></p>
                            <p><strong>Category Type:</strong> <?php echo htmlspecialchars($category['category_type']); ?></p>
                            <p><strong>Sport:</strong> <?php echo htmlspecialchars($category['sport_name']); ?></p>
                            <p><strong>Event:</strong> <?php echo htmlspecialchars($category['event_name']); ?></p>
                            <p><strong>Tournament Format:</strong>
                                <?php
                                if ($tournament_format) {
                                    echo '<span style="color: #007bff; font-weight: 600;">' . htmlspecialchars($tournament_format['format_name']) . '</span>';
                                    if (!empty($tournament_format['format_description'])) {
                                        echo '<br><small style="color: #6c757d;">' . htmlspecialchars($tournament_format['format_description']) . '</small>';
                                    }
                                    if ($tournament_format['tournament_status'] !== 'setup') {
                                        echo '<br><small style="color: #28a745;">Status: ' . ucfirst($tournament_format['tournament_status']) . '</small>';
                                    }
                                } else {
                                    echo '<span style="color: #6c757d;">Not configured</span>';
                                    echo '<br><small style="color: #ffc107;">⚠️ Tournament format needs to be set up</small>';
                                }
                                ?>
                            </p>
                        </div>
                        <div>
                            <h5>Officials & Venue</h5>
                            <p><strong>Referee:</strong> <?php echo htmlspecialchars($category['referee_name'] ?? 'Not assigned'); ?></p>
                            <p><strong>Referee Email:</strong> <?php echo htmlspecialchars($category['referee_email'] ?? 'Not provided'); ?></p>
                            <p><strong>Venue:</strong> <?php echo htmlspecialchars($category['venue'] ?? 'Not specified'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Participants List -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-users"></i> Registered Participants
                    </h3>
                    <span class="badge badge-primary"><?php echo count($participants); ?> Departments</span>
                </div>
                <div class="card-body">
                    <?php if (empty($participants)): ?>
                        <div style="text-align: center; padding: 30px; color: #6c757d;">
                            <i class="fas fa-users" style="font-size: 2rem; margin-bottom: 10px; opacity: 0.3;"></i>
                            <p>No participants registered yet</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">#</th>
                                        <th>Department</th>
                                        <th style="width: 80px; text-align: center;">Matches</th>
                                        <th style="width: 80px; text-align: center;">Wins</th>
                                        <th style="width: 80px; text-align: center;">Points</th>
                                        <th style="width: 100px; text-align: center;">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($participants as $index => $participant): ?>
                                        <tr>
                                            <td>
                                                <div style="
                                                    width: 30px;
                                                    height: 30px;
                                                    background: <?php echo $participant['color_code'] ?? '#6c757d'; ?>;
                                                    border-radius: 50%;
                                                    display: flex;
                                                    align-items: center;
                                                    justify-content: center;
                                                    color: white;
                                                    font-weight: bold;
                                                    font-size: 0.9rem;
                                                ">
                                                    <?php echo $index + 1; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div style="
                                                        width: 35px;
                                                        height: 35px;
                                                        background: <?php echo $participant['color_code'] ?? '#6c757d'; ?>;
                                                        border-radius: 8px;
                                                        display: flex;
                                                        align-items: center;
                                                        justify-content: center;
                                                        color: white;
                                                        font-weight: bold;
                                                        font-size: 0.8rem;
                                                    ">
                                                        <?php echo strtoupper(substr($participant['abbreviation'], 0, 2)); ?>
                                                    </div>
                                                    <div>
                                                        <div style="font-weight: 600; color: #2d3748;">
                                                            <?php echo htmlspecialchars($participant['name']); ?>
                                                        </div>
                                                        <small style="color: #718096;">
                                                            <?php echo htmlspecialchars($participant['abbreviation']); ?>
                                                        </small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td style="text-align: center;">
                                                <span class="badge badge-info"><?php echo $participant['matches_played']; ?></span>
                                            </td>
                                            <td style="text-align: center;">
                                                <span class="badge badge-success"><?php echo $participant['wins']; ?></span>
                                            </td>
                                            <td style="text-align: center;">
                                                <span class="badge" style="background: <?php echo $participant['color_code'] ?? '#6c757d'; ?>; color: white;">
                                                    <?php echo $participant['points']; ?>
                                                </span>
                                            </td>
                                            <td style="text-align: center;">
                                                <span class="badge <?php echo $participant['registration_status'] === 'approved' ? 'badge-success' : 'badge-warning'; ?>">
                                                    <?php echo ucfirst($participant['registration_status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div id="fixtures-tab" class="tab-content">
            <!-- Fixtures Tab Content -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Tournament Fixtures</h3>
                    <div>
                        <?php if ($auto_tournament_generated): ?>
                            <span class="badge badge-success">
                                <i class="fas fa-check"></i> Auto-Generated
                            </span>
                        <?php endif; ?>
                        <button class="btn btn-secondary btn-sm" onclick="refreshFixtures()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($matches)): ?>
                        <div style="text-align: center; padding: 40px; color: #6c757d;">
                            <i class="fas fa-sitemap" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.3;"></i>
                            <p>No matches scheduled yet</p>
                            <?php if ($auto_tournament_message): ?>
                                <p style="font-size: 0.9rem; color: #007bff;">
                                    <i class="fas fa-info-circle"></i> <?php echo htmlspecialchars($auto_tournament_message); ?>
                                </p>
                            <?php else: ?>
                                <p style="font-size: 0.9rem;">Tournament will auto-generate when participants are registered</p>
                            <?php endif; ?>

                            <?php if (count($participants) >= 2): ?>
                                <div class="mt-3">
                                    <button type="button" class="btn btn-primary" onclick="openTournamentConfig(<?php echo $category['event_sport_id']; ?>, <?php echo count($participants); ?>)">
                                        <i class="fas fa-cogs"></i> Configure Tournament
                                    </button>
                                    <p class="mt-2" style="font-size: 0.8rem; color: #6c757d;">
                                        Customize tournament format, seeding, and advanced options
                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <div id="matches-container">
                            <?php foreach ($matches as $match): ?>
                                <div class="match-box" data-match-id="<?php echo $match['id']; ?>">
                                    <div class="match-teams">
                                        <div class="team-info">
                                            <div class="team-color" style="background: <?php echo $match['team_a_color'] ?? '#6c757d'; ?>;"></div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($match['team_a_name'] ?? 'TBD'); ?></strong>
                                                <div style="font-size: 0.9rem; color: #6c757d;"><?php echo htmlspecialchars($match['team_a_abbr'] ?? ''); ?></div>
                                            </div>
                                        </div>
                                        <div class="vs-divider">VS</div>
                                        <div class="team-info">
                                            <div class="team-color" style="background: <?php echo $match['team_b_color'] ?? '#6c757d'; ?>;"></div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($match['team_b_name'] ?? 'TBD'); ?></strong>
                                                <div style="font-size: 0.9rem; color: #6c757d;"><?php echo htmlspecialchars($match['team_b_abbr'] ?? ''); ?></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="score-section">
                                        <div>
                                            <label>Score A:</label>
                                            <input type="number" class="score-input" id="score-a-<?php echo $match['id']; ?>"
                                                   value="<?php echo $match['team_a_score'] ?? ''; ?>" min="0" max="999">
                                        </div>
                                        <div>
                                            <label>Score B:</label>
                                            <input type="number" class="score-input" id="score-b-<?php echo $match['id']; ?>"
                                                   value="<?php echo $match['team_b_score'] ?? ''; ?>" min="0" max="999">
                                        </div>
                                        <div>
                                            <?php if ($match['status'] === 'completed'): ?>
                                                <button class="btn btn-secondary btn-sm" onclick="editMatch(<?php echo $match['id']; ?>)">
                                                    <i class="fas fa-edit"></i> Edit
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-success btn-sm" onclick="saveMatch(<?php echo $match['id']; ?>)">
                                                    <i class="fas fa-save"></i> Save
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <?php if ($match['status'] === 'completed' && $match['winner_name']): ?>
                                        <div style="text-align: center; margin-top: 10px; padding: 8px; background: #d4edda; border-radius: 4px; color: #155724;">
                                            <i class="fas fa-crown"></i> Winner: <strong><?php echo htmlspecialchars($match['winner_name']); ?></strong>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div id="standings-tab" class="tab-content">
            <!-- Standings Tab Content -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Current Standings</h3>
                    <button class="btn btn-secondary btn-sm" onclick="refreshStandings()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
                <div class="card-body">
                    <?php if (empty($participants)): ?>
                        <div style="text-align: center; padding: 40px; color: #6c757d;">
                            <i class="fas fa-trophy" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.3;"></i>
                            <p>No standings available yet</p>
                            <p style="font-size: 0.9rem;">Standings will appear once matches are completed</p>
                        </div>
                    <?php else: ?>
                        <div class="standings-table">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: #007bff; color: white;">
                                        <th style="padding: 15px; text-align: center;">Rank</th>
                                        <th style="padding: 15px; text-align: left;">Department</th>
                                        <th style="padding: 15px; text-align: center;">Matches</th>
                                        <th style="padding: 15px; text-align: center;">Wins</th>
                                        <th style="padding: 15px; text-align: center;">Losses</th>
                                        <th style="padding: 15px; text-align: center;">Points</th>
                                        <th style="padding: 15px; text-align: center;">Win Rate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $rank = 1;
                                    foreach ($participants as $participant):
                                        $win_rate = $participant['matches_played'] > 0 ?
                                            round(($participant['wins'] / $participant['matches_played']) * 100, 1) : 0;
                                        $rank_class = '';
                                        if ($rank == 1) $rank_class = 'rank-1';
                                        elseif ($rank == 2) $rank_class = 'rank-2';
                                        elseif ($rank == 3) $rank_class = 'rank-3';
                                        else $rank_class = 'rank-other';
                                    ?>
                                        <tr style="border-bottom: 1px solid #dee2e6;">
                                            <td style="padding: 12px; text-align: center;">
                                                <div class="rank-badge <?php echo $rank_class; ?>">
                                                    <?php echo $rank; ?>
                                                </div>
                                            </td>
                                            <td style="padding: 12px;">
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div style="width: 20px; height: 20px; background: <?php echo $participant['color_code'] ?? '#6c757d'; ?>; border-radius: 50%;"></div>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($participant['name']); ?></strong>
                                                        <div style="font-size: 0.8rem; color: #6c757d;"><?php echo htmlspecialchars($participant['abbreviation']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td style="padding: 12px; text-align: center;"><?php echo $participant['matches_played']; ?></td>
                                            <td style="padding: 12px; text-align: center; color: #28a745; font-weight: bold;"><?php echo $participant['wins']; ?></td>
                                            <td style="padding: 12px; text-align: center; color: #dc3545; font-weight: bold;"><?php echo $participant['losses']; ?></td>
                                            <td style="padding: 12px; text-align: center;">
                                                <span style="background: #007bff; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold;">
                                                    <?php echo $participant['points']; ?>
                                                </span>
                                            </td>
                                            <td style="padding: 12px; text-align: center;">
                                                <span style="color: <?php echo $win_rate >= 70 ? '#28a745' : ($win_rate >= 50 ? '#ffc107' : '#dc3545'); ?>; font-weight: bold;">
                                                    <?php echo $win_rate; ?>%
                                                </span>
                                            </td>
                                        </tr>
                                    <?php
                                        $rank++;
                                    endforeach;
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab switching functionality
        function showTab(tabName, clickedButton) {
            var tabContents = document.querySelectorAll('.tab-content');
            for (var i = 0; i < tabContents.length; i++) {
                tabContents[i].classList.remove('active');
            }

            var tabButtons = document.querySelectorAll('.tab-button');
            for (var i = 0; i < tabButtons.length; i++) {
                tabButtons[i].classList.remove('active');
            }

            var targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
            }

            if (clickedButton) {
                clickedButton.classList.add('active');
            }
        }

        // Modal functions
        function openEditCategoryModal() {
            var modal = document.getElementById('editCategoryModal');
            if (modal) {
                // Show modal with proper centering
                modal.style.display = 'flex';
                modal.classList.add('show');

                // Populate form with current values
                document.getElementById('edit_category_name').value = <?php echo json_encode($category['category_name'] ?? ''); ?>;
                document.getElementById('edit_category_type').value = <?php echo json_encode($category['category_type'] ?? 'men'); ?>;
                document.getElementById('edit_referee_name').value = <?php echo json_encode($category['referee_name'] ?? ''); ?>;
                document.getElementById('edit_referee_email').value = <?php echo json_encode($category['referee_email'] ?? ''); ?>;
                document.getElementById('edit_venue').value = <?php echo json_encode($category['venue'] ?? ''); ?>;
                document.getElementById('edit_status').value = <?php echo json_encode($category['status'] ?? 'registration'); ?>;
            }
        }

        function closeEditCategoryModal() {
            var modal = document.getElementById('editCategoryModal');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(function() {
                    modal.style.display = 'none';
                }, 300);
            }
        }

        function saveCategory() {
            var formData = new FormData();
            formData.append('action', 'update_category');
            formData.append('category_id', <?php echo json_encode($category_id ?? 0); ?>);
            formData.append('category_name', document.getElementById('edit_category_name').value);
            formData.append('category_type', document.getElementById('edit_category_type').value);
            formData.append('referee_name', document.getElementById('edit_referee_name').value);
            formData.append('referee_email', document.getElementById('edit_referee_email').value);
            formData.append('venue', document.getElementById('edit_venue').value);
            formData.append('status', document.getElementById('edit_status').value);

            // For now, just show what would be saved
            alert('Category would be saved with:\n' +
                  'Name: ' + document.getElementById('edit_category_name').value + '\n' +
                  'Type: ' + document.getElementById('edit_category_type').value + '\n' +
                  'Referee: ' + document.getElementById('edit_referee_name').value);

            closeEditCategoryModal();
        }

        // Enhanced match scoring functionality
        function scoreMatch(matchId) {
            openMatchScoringModal(matchId);
        }

        function editMatch(matchId) {
            openMatchScoringModal(matchId, true);
        }

        function openMatchScoringModal(matchId, isEdit = false) {
            // Fetch match details
            fetch('ajax/tournament-match-details.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_match_details',
                    match_id: matchId
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showMatchScoringModal(data.match, isEdit);
                } else {
                    showNotification('Failed to load match details', 'error');
                }
            })
            .catch(error => {
                console.error('Failed to load match details:', error);
                showNotification('Failed to load match details', 'error');
            });
        }

        function showMatchScoringModal(match, isEdit = false) {
            const modalHTML = `
                <div id="matchScoringModal" class="modal" style="display: flex;">
                    <div class="modal-dialog">
                        <div class="modal-header">
                            <h3 class="modal-title">
                                <i class="fas fa-${isEdit ? 'edit' : 'plus'}"></i>
                                ${isEdit ? 'Edit' : 'Score'} Match
                            </h3>
                            <button type="button" class="modal-close" onclick="closeMatchScoringModal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                        <div class="modal-body">
                            <div class="match-scoring-form">
                                <div class="match-info">
                                    <h4>Round ${match.round_number} - Match ${match.match_number}</h4>
                                    <p class="match-venue">${match.venue || 'Venue TBD'}</p>
                                </div>

                                <div class="teams-scoring">
                                    <div class="team-scoring">
                                        <div class="team-header">
                                            <div class="team-color" style="background: ${match.team_a_color || '#6c757d'};"></div>
                                            <div class="team-name">
                                                <strong>${match.team_a_name || 'Team A'}</strong>
                                                <small>${match.team_a_abbr || ''}</small>
                                            </div>
                                        </div>
                                        <div class="score-input-group">
                                            <label>Score:</label>
                                            <input type="number" id="team_a_score" class="form-control score-input"
                                                   value="${match.team_a_score || ''}" min="0" max="999">
                                        </div>
                                    </div>

                                    <div class="vs-separator">VS</div>

                                    <div class="team-scoring">
                                        <div class="team-header">
                                            <div class="team-color" style="background: ${match.team_b_color || '#6c757d'};"></div>
                                            <div class="team-name">
                                                <strong>${match.team_b_name || 'Team B'}</strong>
                                                <small>${match.team_b_abbr || ''}</small>
                                            </div>
                                        </div>
                                        <div class="score-input-group">
                                            <label>Score:</label>
                                            <input type="number" id="team_b_score" class="form-control score-input"
                                                   value="${match.team_b_score || ''}" min="0" max="999">
                                        </div>
                                    </div>
                                </div>

                                <div class="match-details">
                                    <div class="form-row">
                                        <div>
                                            <label for="match_status">Match Status:</label>
                                            <select id="match_status" class="form-control">
                                                <option value="scheduled" ${match.status === 'scheduled' ? 'selected' : ''}>Scheduled</option>
                                                <option value="ongoing" ${match.status === 'ongoing' ? 'selected' : ''}>Ongoing</option>
                                                <option value="completed" ${match.status === 'completed' ? 'selected' : ''}>Completed</option>
                                                <option value="cancelled" ${match.status === 'cancelled' ? 'selected' : ''}>Cancelled</option>
                                            </select>
                                        </div>
                                        <div>
                                            <label for="match_venue">Venue:</label>
                                            <input type="text" id="match_venue" class="form-control"
                                                   value="${match.venue || ''}" placeholder="Match venue">
                                        </div>
                                    </div>

                                    <div class="form-group">
                                        <label for="referee_notes">Referee Notes:</label>
                                        <textarea id="referee_notes" class="form-control" rows="3"
                                                  placeholder="Optional notes about the match">${match.referee_notes || ''}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" onclick="closeMatchScoringModal()">
                                <i class="fas fa-times"></i> Cancel
                            </button>
                            <button type="button" class="btn btn-primary" onclick="saveMatchScore(${match.id})">
                                <i class="fas fa-save"></i> Save Match
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('matchScoringModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add modal to page
            document.body.insertAdjacentHTML('beforeend', modalHTML);
        }

        function closeMatchScoringModal() {
            const modal = document.getElementById('matchScoringModal');
            if (modal) {
                modal.classList.remove('show');
                setTimeout(() => modal.remove(), 300);
            }
        }

        function saveMatchScore(matchId) {
            const teamAScore = document.getElementById('team_a_score').value;
            const teamBScore = document.getElementById('team_b_score').value;
            const matchStatus = document.getElementById('match_status').value;
            const matchVenue = document.getElementById('match_venue').value;
            const refereeNotes = document.getElementById('referee_notes').value;

            // Validate scores
            if (matchStatus === 'completed' && (teamAScore === '' || teamBScore === '')) {
                showNotification('Please enter scores for both teams to complete the match', 'error');
                return;
            }

            const saveBtn = document.querySelector('[onclick="saveMatchScore(' + matchId + ')"]');
            if (saveBtn) {
                saveBtn.disabled = true;
                saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Saving...';
            }

            fetch('ajax/tournament-match-scoring.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'save_match_score',
                    match_id: matchId,
                    team_a_score: teamAScore,
                    team_b_score: teamBScore,
                    status: matchStatus,
                    venue: matchVenue,
                    referee_notes: refereeNotes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showNotification('Match score saved successfully!', 'success');
                    closeMatchScoringModal();
                    refreshFixtures();
                    refreshStandings();
                    updateStatistics();

                    // Check if tournament advancement is needed
                    if (data.advancement_needed) {
                        checkTournamentAdvancement();
                    }
                } else {
                    showNotification(data.message || 'Failed to save match score', 'error');
                }
            })
            .catch(error => {
                console.error('Failed to save match score:', error);
                showNotification('Failed to save match score', 'error');
            })
            .finally(() => {
                if (saveBtn) {
                    saveBtn.disabled = false;
                    saveBtn.innerHTML = '<i class="fas fa-save"></i> Save Match';
                }
            });
        }

        function checkTournamentAdvancement() {
            fetch('ajax/tournament-advancement.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'check_advancement',
                    event_id: <?php echo json_encode($event_id ?? 0); ?>,
                    sport_id: <?php echo json_encode($sport_id ?? 0); ?>,
                    category_id: <?php echo json_encode($category_id ?? 0); ?>
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success && data.advancement_available) {
                    showNotification('Round completed! Advancing to next round...', 'success');
                    setTimeout(() => {
                        refreshFixtures();
                        refreshStandings();
                    }, 2000);
                }
            })
            .catch(error => {
                console.error('Tournament advancement check failed:', error);
            });
        }

        // Auto-generation is now handled on page load, no manual trigger needed

        // Tournament generation is now automatic on page load

        // Real-time fixtures refresh
        function refreshFixtures() {
            fetch('ajax/tournament-fixtures.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_fixtures',
                    event_id: <?php echo json_encode($event_id ?? 0); ?>,
                    sport_id: <?php echo json_encode($sport_id ?? 0); ?>,
                    category_id: <?php echo json_encode($category_id ?? 0); ?>
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    updateFixturesDisplay(data.fixtures);
                    updateTournamentInfo(data.tournament_info);
                }
            })
            .catch(error => {
                console.error('Fixtures refresh failed:', error);
            });
        }

        // Real-time standings refresh
        function refreshStandings() {
            fetch('ajax/tournament-standings.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_standings',
                    event_id: <?php echo json_encode($event_id ?? 0); ?>,
                    sport_id: <?php echo json_encode($sport_id ?? 0); ?>,
                    category_id: <?php echo json_encode($category_id ?? 0); ?>
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    updateStandingsDisplay(data.standings);
                }
            })
            .catch(error => {
                console.error('Standings refresh failed:', error);
            });
        }

        // Helper functions for real-time updates
        function updateFixturesDisplay(fixtures) {
            const fixturesContainer = document.getElementById('matches-container');
            if (!fixturesContainer) return;

            if (!fixtures || fixtures.length === 0) {
                fixturesContainer.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #6c757d;">
                        <i class="fas fa-sitemap" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.3;"></i>
                        <p>No matches scheduled yet</p>
                        <p style="font-size: 0.9rem;">Tournament will auto-generate when participants are registered</p>
                    </div>
                `;
                return;
            }

            let html = '';
            fixtures.forEach(match => {
                html += generateMatchHTML(match);
            });
            fixturesContainer.innerHTML = html;
        }

        function generateMatchHTML(match) {
            return `
                <div class="match-box" data-match-id="${match.id}">
                    <div class="match-header">
                        <span class="round-info">Round ${match.round_number} - Match ${match.match_number}</span>
                        <span class="match-status status-${match.status}">${match.status.toUpperCase()}</span>
                    </div>
                    <div class="match-teams">
                        <div class="team-info">
                            <div class="team-color" style="background: ${match.team_a_color || '#6c757d'};"></div>
                            <div>
                                <strong>${match.team_a_name || 'TBD'}</strong>
                                <div style="font-size: 0.9rem; color: #6c757d;">${match.team_a_abbr || ''}</div>
                            </div>
                            <div class="team-score">${match.team_a_score || '0'}</div>
                        </div>
                        <div class="vs-divider">VS</div>
                        <div class="team-info">
                            <div class="team-color" style="background: ${match.team_b_color || '#6c757d'};"></div>
                            <div>
                                <strong>${match.team_b_name || 'TBD'}</strong>
                                <div style="font-size: 0.9rem; color: #6c757d;">${match.team_b_abbr || ''}</div>
                            </div>
                            <div class="team-score">${match.team_b_score || '0'}</div>
                        </div>
                    </div>
                    <div class="match-actions">
                        ${match.status === 'completed' ?
                            `<button class="btn btn-secondary btn-sm" onclick="editMatch(${match.id})">
                                <i class="fas fa-edit"></i> Edit
                            </button>` :
                            `<button class="btn btn-success btn-sm" onclick="scoreMatch(${match.id})">
                                <i class="fas fa-plus"></i> Score
                            </button>`
                        }
                    </div>
                    ${match.winner_name ?
                        `<div class="match-winner">
                            <i class="fas fa-crown"></i> Winner: <strong>${match.winner_name}</strong>
                        </div>` : ''
                    }
                </div>
            `;
        }

        function updateStandingsDisplay(standings) {
            const standingsTable = document.querySelector('.standings-table tbody');
            if (!standingsTable) return;

            if (!standings || standings.length === 0) {
                standingsTable.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; padding: 40px; color: #6c757d;">
                            <i class="fas fa-trophy" style="font-size: 2rem; margin-bottom: 10px; opacity: 0.3;"></i>
                            <p>No standings available yet</p>
                        </td>
                    </tr>
                `;
                return;
            }

            let html = '';
            standings.forEach((participant, index) => {
                const rank = index + 1;
                const winRate = participant.matches_played > 0 ?
                    Math.round((participant.wins / participant.matches_played) * 100) : 0;

                html += `
                    <tr>
                        <td style="text-align: center;">
                            <div class="rank-badge rank-${rank <= 3 ? rank : 'other'}">${rank}</div>
                        </td>
                        <td>
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div style="width: 20px; height: 20px; background: ${participant.color_code || '#6c757d'}; border-radius: 50%;"></div>
                                <div>
                                    <strong>${participant.name}</strong>
                                    <div style="font-size: 0.8rem; color: #6c757d;">${participant.abbreviation}</div>
                                </div>
                            </div>
                        </td>
                        <td style="text-align: center;">${participant.matches_played}</td>
                        <td style="text-align: center; color: #28a745; font-weight: bold;">${participant.wins}</td>
                        <td style="text-align: center; color: #dc3545; font-weight: bold;">${participant.losses}</td>
                        <td style="text-align: center;">
                            <span style="background: #007bff; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold;">
                                ${participant.points}
                            </span>
                        </td>
                        <td style="text-align: center;">
                            <span style="color: ${winRate >= 70 ? '#28a745' : (winRate >= 50 ? '#ffc107' : '#dc3545')}; font-weight: bold;">
                                ${winRate}%
                            </span>
                        </td>
                    </tr>
                `;
            });
            standingsTable.innerHTML = html;
        }

        function updateStatistics() {
            fetch('ajax/tournament-statistics.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'get_statistics',
                    event_id: <?php echo json_encode($event_id ?? 0); ?>,
                    sport_id: <?php echo json_encode($sport_id ?? 0); ?>,
                    category_id: <?php echo json_encode($category_id ?? 0); ?>
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    updateStatisticsCards(data.statistics);
                }
            })
            .catch(error => {
                console.error('Statistics update failed:', error);
            });
        }

        function updateStatisticsCards(stats) {
            const cards = document.querySelectorAll('.stats-number');
            if (cards.length >= 4) {
                cards[0].textContent = stats.participants || 0;
                cards[1].textContent = stats.total_matches || 0;
                cards[2].textContent = stats.completed_matches || 0;
                cards[3].textContent = stats.pending_matches || 0;
            }
        }

        function updateTournamentInfo(tournamentInfo) {
            if (tournamentInfo && tournamentInfo.status) {
                // Update tournament status indicators
                const statusElements = document.querySelectorAll('.tournament-status');
                statusElements.forEach(el => {
                    el.textContent = tournamentInfo.status.toUpperCase();
                    el.className = `tournament-status status-${tournamentInfo.status}`;
                });
            }
        }

        function showNotification(message, type = 'info') {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;
            notification.innerHTML = `
                <div class="notification-content">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
                    <span>${message}</span>
                </div>
                <button class="notification-close" onclick="this.parentElement.remove()">
                    <i class="fas fa-times"></i>
                </button>
            `;

            // Add to page
            document.body.appendChild(notification);

            // Auto-remove after 5 seconds
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 5000);
        }

        // Initialize page with automatic tournament system
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Automatic tournament management system initialized');
            console.log('Event ID:', <?php echo json_encode($event_id ?? 0); ?>);
            console.log('Sport ID:', <?php echo json_encode($sport_id ?? 0); ?>);
            console.log('Category ID:', <?php echo json_encode($category_id ?? 0); ?>);

            // Show overview tab by default
            showTab('overview');

            // Set up periodic updates for real-time data
            setInterval(function() {
                refreshFixtures();
                refreshStandings();
                updateStatistics();
            }, 30000); // Refresh every 30 seconds

            <?php if ($auto_tournament_generated): ?>
            // Tournament was auto-generated, show success message and refresh displays
            setTimeout(function() {
                refreshFixtures();
                refreshStandings();
                updateStatistics();
                showNotification('Tournament automatically generated with <?php echo count($participants); ?> participants!', 'success');
            }, 1000);
            <?php elseif ($auto_tournament_message): ?>
            // Show auto-generation status message
            setTimeout(function() {
                showNotification('<?php echo addslashes($auto_tournament_message); ?>', 'info');
            }, 1000);
            <?php endif; ?>
        });

        // Close modal when clicking outside
        window.onclick = function(event) {
            var modal = document.getElementById('editCategoryModal');
            if (event.target == modal) {
                closeEditCategoryModal();
            }
        };
    </script>

    <!-- Edit Category Modal -->
    <div id="editCategoryModal" class="modal" style="display: none;">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title">
                    <i class="fas fa-edit"></i> Edit Category Information
                </h3>
                <button type="button" class="modal-close" onclick="closeEditCategoryModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form id="editCategoryForm" class="modal-form">
                    <div class="form-row">
                        <div>
                            <label for="edit_category_name">Category Name</label>
                            <input type="text" id="edit_category_name" class="form-control" required>
                        </div>
                        <div>
                            <label for="edit_category_type">Category Type</label>
                            <select id="edit_category_type" class="form-control" required>
                                <option value="men">Men</option>
                                <option value="women">Women</option>
                                <option value="mixed">Mixed</option>
                                <option value="open">Open</option>
                                <option value="youth">Youth</option>
                                <option value="senior">Senior</option>
                                <option value="other">Other</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div>
                            <label for="edit_referee_name">Referee Name</label>
                            <input type="text" id="edit_referee_name" class="form-control">
                        </div>
                        <div>
                            <label for="edit_referee_email">Referee Email</label>
                            <input type="email" id="edit_referee_email" class="form-control">
                        </div>
                    </div>

                    <div class="form-row">
                        <div>
                            <label for="edit_venue">Venue</label>
                            <input type="text" id="edit_venue" class="form-control">
                        </div>
                        <div>
                            <label for="edit_status">Status</label>
                            <select id="edit_status" class="form-control" required>
                                <option value="registration">Registration</option>
                                <option value="ongoing">Ongoing</option>
                                <option value="completed">Completed</option>
                                <option value="cancelled">Cancelled</option>
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeEditCategoryModal()">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="button" class="btn btn-primary" onclick="saveCategory()">
                    <i class="fas fa-save"></i> Save Changes
                </button>
            </div>
        </div>
    </div>

    <style>
        /* Custom table styling for participants */
        .table-hover tbody tr:hover {
            background-color: rgba(0,123,255,0.05);
        }

        .badge {
            font-size: 0.8rem;
            padding: 4px 8px;
        }

        /* Enhanced Match Box Styling */
        .match-box {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            padding: 20px;
            transition: all 0.3s ease;
            border-left: 4px solid #007bff;
        }

        .match-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #e9ecef;
        }

        .round-info {
            font-weight: 600;
            color: #495057;
        }

        .match-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-scheduled { background: #e3f2fd; color: #1976d2; }
        .status-ongoing { background: #fff3e0; color: #f57c00; }
        .status-completed { background: #e8f5e8; color: #388e3c; }
        .status-cancelled { background: #ffebee; color: #d32f2f; }

        .match-teams {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 20px;
            align-items: center;
            margin-bottom: 15px;
        }

        .team-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .team-color {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .team-score {
            font-size: 1.5rem;
            font-weight: bold;
            color: #007bff;
            min-width: 40px;
            text-align: center;
        }

        .vs-divider {
            background: #f8f9fa;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            color: #6c757d;
            text-align: center;
        }

        .match-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
        }

        .match-winner {
            text-align: center;
            margin-top: 15px;
            padding: 10px;
            background: #d4edda;
            border-radius: 8px;
            color: #155724;
            font-weight: 600;
        }

        /* Match Scoring Modal Styles */
        .match-scoring-form {
            max-width: 600px;
        }

        .match-info {
            text-align: center;
            margin-bottom: 25px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .match-info h4 {
            margin: 0 0 5px 0;
            color: #495057;
        }

        .match-venue {
            margin: 0;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .teams-scoring {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 20px;
            align-items: center;
            margin-bottom: 25px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }

        .team-scoring {
            text-align: center;
        }

        .team-header {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .team-name {
            text-align: center;
        }

        .team-name strong {
            display: block;
            font-size: 1.1rem;
            color: #495057;
        }

        .team-name small {
            color: #6c757d;
            font-size: 0.85rem;
        }

        .score-input-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }

        .score-input-group label {
            font-weight: 600;
            color: #495057;
        }

        .score-input {
            width: 80px;
            text-align: center;
            font-size: 1.2rem;
            font-weight: bold;
            margin: 0 auto;
        }

        .vs-separator {
            background: #007bff;
            color: white;
            padding: 10px 15px;
            border-radius: 50%;
            font-weight: bold;
            font-size: 0.9rem;
        }

        .match-details {
            border-top: 1px solid #dee2e6;
            padding-top: 20px;
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: flex;
            align-items: center;
            justify-content: space-between;
            animation: slideInRight 0.3s ease;
        }

        .notification-success {
            background: #d4edda;
            color: #155724;
            border-left: 4px solid #28a745;
        }

        .notification-error {
            background: #f8d7da;
            color: #721c24;
            border-left: 4px solid #dc3545;
        }

        .notification-info {
            background: #d1ecf1;
            color: #0c5460;
            border-left: 4px solid #17a2b8;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .notification-close {
            background: none;
            border: none;
            color: inherit;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            opacity: 0.7;
        }

        .notification-close:hover {
            opacity: 1;
            background: rgba(0,0,0,0.1);
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Rank Badge Styles */
        .rank-badge {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            color: white;
        }

        .rank-1 { background: #ffd700; color: #333; }
        .rank-2 { background: #c0c0c0; color: #333; }
        .rank-3 { background: #cd7f32; color: white; }
        .rank-other { background: #6c757d; color: white; }

        /* Tournament Status Indicators */
        .tournament-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .match-teams {
                grid-template-columns: 1fr;
                gap: 15px;
                text-align: center;
            }

            .vs-divider {
                order: 2;
            }

            .teams-scoring {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .vs-separator {
                order: 2;
                align-self: center;
            }

            .notification {
                right: 10px;
                left: 10px;
                max-width: none;
            }
        }
    </style>

    <!-- Include Tournament Configuration Modal -->
    <?php include 'includes/tournament-config-modal.php'; ?>
</body>
</html>
