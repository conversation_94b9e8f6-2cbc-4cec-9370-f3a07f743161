<?php
/**
 * Manage Category - COMPLETELY REWRITTEN FROM SCRATCH
 * SC_IMS Sports Competition and Event Management System
 * 
 * This is a complete rewrite to eliminate ALL persistent column errors
 * Using ONLY simple, direct database queries that work reliably
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get parameters
$event_id = $_GET['event_id'] ?? 0;
$sport_id = $_GET['sport_id'] ?? 0;
$category_id = $_GET['category_id'] ?? 0;

if (!$event_id || !$sport_id || !$category_id) {
    header('Location: events.php');
    exit;
}

// Get category information with tournament format inheritance
try {
    $stmt = $conn->prepare("
        SELECT
            sc.*,
            e.name as event_name,
            s.name as sport_name,
            s.type as sport_type,
            es.id as event_sport_id,
            es.tournament_format_id,
            tf.name as tournament_format_name,
            tf.code as tournament_format_code,
            tf.description as tournament_format_description,
            tf.algorithm_class,
            tf.sport_types as format_sport_types,
            tf.min_participants as format_min_participants,
            tf.max_participants as format_max_participants
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE sc.id = ?
    ");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();

    if (!$category) {
        header('Location: events.php');
        exit;
    }

} catch (Exception $e) {
    error_log("Error fetching category: " . $e->getMessage());
    header('Location: events.php');
    exit;
}

// Get participants from unified department registration system
// Pull from event-level registrations that automatically participate in all sports
$participants = [];
try {
    // First try: Get participants from unified event registration (departments registered for the event automatically participate in all sports)
    $stmt = $conn->prepare("
        SELECT
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            edr.status,
            edr.registration_date,
            edr.contact_person,
            edr.total_participants
        FROM event_department_registrations edr
        JOIN departments d ON edr.department_id = d.id
        WHERE edr.event_id = ?
        AND edr.status IN ('pending', 'approved')
        ORDER BY d.name
    ");
    $stmt->execute([$event_id]);
    $participants = $stmt->fetchAll();

    // If no participants found through unified system, try old registrations table
    if (empty($participants)) {
        $stmt = $conn->prepare("
            SELECT
                d.id,
                d.name,
                d.abbreviation,
                d.color_code,
                r.status,
                r.registration_date,
                r.team_name,
                r.participants as participant_list
            FROM registrations r
            JOIN departments d ON r.department_id = d.id
            WHERE r.event_sport_id = ?
            AND r.status IN ('registered', 'confirmed')
            ORDER BY d.name
        ");
        $stmt->execute([$category['event_sport_id']]);
        $participants = $stmt->fetchAll();
    }

    // If still no participants, show all active departments as potential participants
    if (empty($participants)) {
        $stmt = $conn->prepare("
            SELECT
                d.id,
                d.name,
                d.abbreviation,
                d.color_code,
                'available' as status,
                NOW() as registration_date,
                NULL as contact_person,
                0 as total_participants
            FROM departments d
            WHERE d.status = 'active'
            ORDER BY d.name
            LIMIT 10
        ");
        $stmt->execute();
        $participants = $stmt->fetchAll();
    }
} catch (Exception $e) {
    error_log("Error fetching participants: " . $e->getMessage());
    $participants = [];
}

// Advanced Tournament Generation with Automatic Data Inheritance
require_once '../includes/advanced_tournament_engine.php';
require_once '../includes/tournament_algorithms_advanced.php';

$auto_tournament_message = '';
$auto_tournament_generated = false;
$tournament_status = 'waiting';

try {
    // Check if tournament already exists
    $stmt = $conn->prepare("
        SELECT id, status, tournament_format_id, created_at FROM tournament_structures
        WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')
        ORDER BY created_at DESC LIMIT 1
    ");
    $stmt->execute([$category['event_sport_id']]);
    $existing_tournament = $stmt->fetch();

    $min_required = $category['format_min_participants'] ?? 2;
    $current_participants = count($participants);

    if ($existing_tournament) {
        $tournament_status = 'exists';
        $auto_tournament_message = "Tournament active (Status: " . ucfirst($existing_tournament['status']) . ").";
    } elseif ($current_participants >= $min_required &&
              !empty($category['tournament_format_id']) &&
              !empty($category['referee_name'])) {
        // All conditions met - attempt auto-generation
        $tournament_status = 'generating';

        // Use advanced tournament engine for auto-generation
        $engine = new AdvancedTournamentEngine($conn);
        $result = $engine->generateTournament($category['event_sport_id'], $category_id, [
            'seeding_method' => 'random',
            'third_place_playoff' => false,
            'scoring_config' => [
                'points_win' => 3,
                'points_draw' => 1,
                'points_loss' => 0
            ]
        ]);

        if ($result['success']) {
            $auto_tournament_generated = true;
            $tournament_status = 'generated';
            $auto_tournament_message = "✅ Tournament automatically generated using {$result['format']} format with {$result['participants_count']} participants.";
        } else {
            $tournament_status = 'failed';
            $auto_tournament_message = "❌ Auto-generation failed: " . $result['message'];
        }
    } else {
        $tournament_status = 'waiting';
        $missing_requirements = [];

        if ($current_participants < $min_required) {
            $needed = $min_required - $current_participants;
            $missing_requirements[] = "{$needed} more participant(s) (have {$current_participants}/{$min_required})";
        }

        if (empty($category['tournament_format_id'])) {
            $missing_requirements[] = "tournament format configuration";
        }

        if (empty($category['referee_name'])) {
            $missing_requirements[] = "referee assignment";
        }

        $auto_tournament_message = "⏳ Waiting for: " . implode(', ', $missing_requirements) . ".";
    }

} catch (Exception $e) {
    $tournament_status = 'error';
    $auto_tournament_message = "❌ Auto-generation error: " . $e->getMessage();
    error_log("Auto-tournament generation error: " . $e->getMessage());
}

// Get matches using SIMPLE query - NO COMPLEX JOINS
$matches = [];
try {
    $stmt = $conn->prepare("
        SELECT 
            m.*,
            d1.name as team1_name,
            d2.name as team2_name,
            dw.name as winner_name
        FROM matches m
        LEFT JOIN tournament_participants tp1 ON m.team1_id = tp1.id
        LEFT JOIN departments d1 ON tp1.department_id = d1.id
        LEFT JOIN tournament_participants tp2 ON m.team2_id = tp2.id
        LEFT JOIN departments d2 ON tp2.department_id = d2.id
        LEFT JOIN tournament_participants tpw ON m.winner_id = tpw.id
        LEFT JOIN departments dw ON tpw.department_id = dw.id
        WHERE m.event_sport_id = ?
        ORDER BY m.round_number, m.match_number
    ");
    $stmt->execute([$category['event_sport_id']]);
    $matches = $stmt->fetchAll();
} catch (Exception $e) {
    error_log("Error fetching matches: " . $e->getMessage());
    $matches = [];
}

// Get standings - Calculate from participants and matches
$standings = [];
try {
    // Create standings from participants with match statistics
    $standings_data = [];

    foreach ($participants as $participant) {
        $dept_id = $participant['id'];
        $dept_name = $participant['name'];
        $dept_abbr = $participant['abbreviation'];

        // Initialize stats
        $stats = [
            'department_id' => $dept_id,
            'department_name' => $dept_name,
            'abbreviation' => $dept_abbr,
            'color_code' => $participant['color_code'] ?? '#007bff',
            'matches_played' => 0,
            'wins' => 0,
            'losses' => 0,
            'draws' => 0,
            'points_for' => 0,
            'points_against' => 0,
            'points_difference' => 0,
            'tournament_points' => 0,
            'position' => 0
        ];

        // Calculate stats from matches
        foreach ($matches as $match) {
            if ($match['team1_id'] == $dept_id || $match['team2_id'] == $dept_id) {
                if ($match['status'] == 'completed' && $match['winner_id']) {
                    $stats['matches_played']++;

                    if ($match['team1_id'] == $dept_id) {
                        $stats['points_for'] += $match['team1_score'] ?? 0;
                        $stats['points_against'] += $match['team2_score'] ?? 0;
                        if ($match['winner_id'] == $dept_id) {
                            $stats['wins']++;
                            $stats['tournament_points'] += 3; // 3 points for win
                        } else {
                            $stats['losses']++;
                        }
                    } else {
                        $stats['points_for'] += $match['team2_score'] ?? 0;
                        $stats['points_against'] += $match['team1_score'] ?? 0;
                        if ($match['winner_id'] == $dept_id) {
                            $stats['wins']++;
                            $stats['tournament_points'] += 3; // 3 points for win
                        } else {
                            $stats['losses']++;
                        }
                    }
                }
            }
        }

        $stats['points_difference'] = $stats['points_for'] - $stats['points_against'];
        $standings_data[] = $stats;
    }

    // Sort standings by tournament points, then by points difference, then by points for
    usort($standings_data, function($a, $b) {
        if ($a['tournament_points'] != $b['tournament_points']) {
            return $b['tournament_points'] - $a['tournament_points'];
        }
        if ($a['points_difference'] != $b['points_difference']) {
            return $b['points_difference'] - $a['points_difference'];
        }
        return $b['points_for'] - $a['points_for'];
    });

    // Assign positions
    for ($i = 0; $i < count($standings_data); $i++) {
        $standings_data[$i]['position'] = $i + 1;
    }

    $standings = $standings_data;

} catch (Exception $e) {
    error_log("Error calculating standings: " . $e->getMessage());
    $standings = [];
}

// Log admin activity - Fix session and parameter issues
try {
    $admin_id = $_SESSION['admin_user_id'] ?? null;
    $category_name = $category['name'] ?? 'Unknown';
    if ($admin_id) {
        logAdminActivity('VIEW_CATEGORY', 'sport_categories', $category_id);
    }
} catch (Exception $e) {
    error_log("Error logging admin activity: " . $e->getMessage());
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($category['name'] ?? 'Category'); ?> Management - <?php echo APP_NAME; ?></title>

    <?php include 'includes/admin-styles.php'; ?>

    <!-- Enhanced Bracket Styles -->
    <link rel="stylesheet" href="assets/css/bracket-styles.css">
    <link rel="stylesheet" href="assets/css/bracket-modals.css">

    <!-- Additional bracket visibility styles -->
    <style>
        .sc-bracket-container {
            display: block !important;
            visibility: visible !important;
        }
        .sc-match-card {
            display: block !important;
            visibility: visible !important;
            margin: 10px 0;
            border: 1px solid #ddd;
            padding: 10px;
            background: white;
        }
        .sc-round {
            display: block !important;
            visibility: visible !important;
        }
    </style>

    <!-- Tab Functionality Script - Load First -->
    <script type="text/javascript">
        // Define showTab function in global scope
        window.showTab = function(tabName, clickedButton) {
            console.log('showTab function called:', tabName);

            try {
                // Hide all tab panes
                var panes = document.querySelectorAll('.tab-pane');
                for (var i = 0; i < panes.length; i++) {
                    panes[i].classList.remove('active');
                }

                // Remove active class from all tab buttons
                var buttons = document.querySelectorAll('.tab-button');
                for (var i = 0; i < buttons.length; i++) {
                    buttons[i].classList.remove('active');
                }

                // Show selected tab pane
                var targetPane = document.getElementById(tabName);
                if (targetPane) {
                    targetPane.classList.add('active');
                    console.log('Tab activated:', tabName);
                } else {
                    console.error('Tab not found:', tabName);
                }

                // Add active class to clicked button
                if (clickedButton) {
                    clickedButton.classList.add('active');
                }

                return false; // Prevent default action
            } catch (error) {
                console.error('Error in showTab:', error);
                return false;
            }
        };

        // Test function availability immediately
        console.log('showTab function defined:', typeof window.showTab);
    </script>

    <style>
        .tab-navigation {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            margin: 0;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            border: none;
            background: transparent;
            cursor: pointer;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
        }

        .tab-button:hover {
            background: rgba(52, 144, 220, 0.1);
            color: #3490dc;
        }

        .tab-button.active {
            background: white;
            color: #3490dc;
            border-bottom-color: #3490dc;
        }

        .tab-button i {
            margin-right: 8px;
        }

        .tab-content {
            padding: 25px;
        }

        .tab-pane {
            display: none;
        }

        .tab-pane.active {
            display: block;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 25px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #e3e6f0;
            text-align: center;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .stat-value {
            font-size: 2rem;
            font-weight: 700;
            color: #3490dc;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 500;
        }

        .alert-info {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            color: #004085;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .empty-state {
            text-align: center;
            padding: 40px 20px;
            color: #6c757d;
        }

        .empty-state i {
            font-size: 3rem;
            margin-bottom: 15px;
            opacity: 0.3;
        }

        .empty-state h4 {
            margin-bottom: 8px;
            color: #495057;
        }

        .empty-state p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        /* Bracket Display Styles */
        .bracket-container {
            display: flex;
            gap: 30px;
            overflow-x: auto;
            padding: 20px 0;
            min-height: 400px;
        }

        .bracket-round {
            min-width: 250px;
            display: flex;
            flex-direction: column;
        }

        .round-header {
            text-align: center;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .round-header h5 {
            margin: 0;
            color: #495057;
            font-weight: 600;
        }

        .round-matches {
            display: flex;
            flex-direction: column;
            gap: 20px;
            flex: 1;
            justify-content: center;
        }

        .match-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            transition: all 0.3s ease;
            position: relative;
        }

        .match-card:hover {
            border-color: #3490dc;
            box-shadow: 0 4px 12px rgba(52, 144, 220, 0.15);
        }

        .match-card.match-completed {
            border-color: #28a745;
            background: #f8fff9;
        }

        .match-card.match-active {
            border-color: #ffc107;
            background: #fffdf5;
        }

        .match-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .match-number {
            font-size: 0.85rem;
            color: #6c757d;
            font-weight: 500;
        }

        .match-actions {
            display: flex;
            gap: 5px;
        }

        .edit-match-btn {
            padding: 4px 8px;
            font-size: 0.75rem;
        }

        .match-team {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 4px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }

        .match-team.winner {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            font-weight: 600;
        }

        .team-name {
            font-weight: 500;
            color: #495057;
        }

        .team-score {
            font-weight: 700;
            color: #3490dc;
            font-size: 1.1rem;
        }

        .match-vs {
            text-align: center;
            font-size: 0.8rem;
            color: #6c757d;
            font-weight: 600;
            margin: 5px 0;
        }

        .match-status {
            text-align: center;
            margin-top: 10px;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 500;
            text-transform: uppercase;
        }

        .status-pending {
            background: #e9ecef;
            color: #6c757d;
        }

        .status-in_progress {
            background: #fff3cd;
            color: #856404;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .empty-bracket {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 300px;
        }

        /* Round Robin Styles */
        .round-robin-table {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 30px;
        }

        .standings-table {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        .matches-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            border: 1px solid #e9ecef;
        }

        /* Multi-Stage Styles */
        .group-stage, .knockout-stage {
            margin-bottom: 30px;
        }

        .group-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #e9ecef;
        }

        .group-matches {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 15px;
        }

        /* Auto-Generation Status Styles */
        .tournament-status {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-label {
            font-weight: 600;
            color: #495057;
        }

        .status-value {
            font-weight: 700;
            color: #3490dc;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }

        .status-ready {
            background: #d4edda;
            color: #155724;
        }

        .status-waiting {
            background: #fff3cd;
            color: #856404;
        }

        .status-generating {
            background: #cce7ff;
            color: #0066cc;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .auto-generation-info {
            font-size: 0.85rem;
        }

        .auto-generation-status {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 300px;
            padding: 20px;
        }

        .status-card {
            background: white;
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            border: 2px solid #e9ecef;
            max-width: 400px;
            width: 100%;
        }

        .status-card i {
            font-size: 3rem;
            margin-bottom: 20px;
        }

        .status-generating {
            border-color: #3490dc;
            background: linear-gradient(135deg, #f8fbff 0%, #e7f3ff 100%);
        }

        .status-generating i {
            color: #3490dc;
        }

        .status-waiting i {
            color: #ffc107;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
            margin: 15px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3490dc, #2980b9);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .status-generating .progress-fill {
            animation: progress-animation 2s ease-in-out infinite;
        }

        @keyframes progress-animation {
            0% { width: 0%; }
            50% { width: 100%; }
            100% { width: 0%; }
        }

        .participant-progress {
            margin-top: 20px;
        }

        .progress-text {
            font-size: 0.9rem;
            color: #6c757d;
            font-weight: 600;
        }

        /* Tournament Requirements Styling */
        .tournament-requirements {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .requirements-list {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .requirement-item {
            display: flex;
            align-items: center;
            padding: 12px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }

        .requirement-item:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .requirement-met {
            border-left-color: #28a745;
        }

        .requirement-pending {
            border-left-color: #ffc107;
        }

        .auto-generation-indicator {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 6px;
            padding: 12px;
            color: #155724;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <!-- Main Content Area -->
    <div class="admin-main">
        <!-- Header -->
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <i class="fas fa-layer-group"></i>
                        <span>Category Management</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="user-info">
                    <span class="user-name">
                        <i class="fas fa-user-shield"></i>
                        <?php echo htmlspecialchars($current_admin['username'] ?? 'admin'); ?>
                    </span>
                    <a href="logout.php" class="btn btn-sm btn-secondary">
                        <i class="fas fa-sign-out-alt"></i>
                        Logout
                    </a>
                </div>
            </div>
        </header>

        <!-- Content -->
        <div class="admin-content">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title"><?php echo htmlspecialchars($category['name'] ?? 'Category'); ?></h1>
                <p class="page-description">
                    <?php echo htmlspecialchars($category['event_name'] ?? 'Event'); ?> - <?php echo htmlspecialchars($category['sport_name'] ?? 'Sport'); ?>
                </p>
                <div class="page-actions">
                    <a href="manage-event.php?id=<?php echo $event_id; ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i>
                        Back to Event
                    </a>
                </div>
            </div>

            <?php if ($auto_tournament_message): ?>
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i>
                <?php echo htmlspecialchars($auto_tournament_message); ?>
            </div>
            <?php endif; ?>

            <!-- Main Content Card -->
            <div class="card">
                <div class="tab-navigation">
                    <a href="#overview" class="tab-button active" data-tab="overview" onclick="return window.showTab('overview', this);">
                        <i class="fas fa-info-circle"></i> Overview
                    </a>
                    <a href="#fixtures" class="tab-button" data-tab="fixtures" onclick="return window.showTab('fixtures', this);">
                        <i class="fas fa-sitemap"></i> Fixtures
                    </a>
                    <a href="#standings" class="tab-button" data-tab="standings" onclick="return window.showTab('standings', this);">
                        <i class="fas fa-trophy"></i> Standings
                    </a>
                </div>

                <div class="tab-content">
                    <!-- Overview Tab -->
                    <div id="overview" class="tab-pane active">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value"><?php echo count($participants); ?></div>
                                <div class="stat-label">Participants</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value"><?php echo count($matches); ?></div>
                                <div class="stat-label">Matches</div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-value"><?php echo htmlspecialchars($category['tournament_format_name'] ?? 'Single Elimination'); ?></div>
                                <div class="stat-label">Format</div>
                            </div>
                        </div>

                        <h4>Registered Participants</h4>
                        <?php if (!empty($participants)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Department</th>
                                        <th>Abbreviation</th>
                                        <th>Status</th>
                                        <th>Registration Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($participants as $participant): ?>
                                    <tr>
                                        <td><?php echo htmlspecialchars($participant['name']); ?></td>
                                        <td><strong><?php echo htmlspecialchars($participant['abbreviation']); ?></strong></td>
                                        <td>
                                            <span class="badge badge-<?php echo $participant['status'] == 'active' ? 'success' : 'warning'; ?>">
                                                <?php echo ucfirst($participant['status']); ?>
                                            </span>
                                        </td>
                                        <td><?php echo date('M j, Y', strtotime($participant['registration_date'])); ?></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-users"></i>
                            <h4>No Participants Yet</h4>
                            <p>Participants will appear here once departments are registered for this category</p>
                        </div>
                        <?php endif; ?>
                    </div>

                    <!-- Fixtures Tab -->
                    <div id="fixtures" class="tab-pane">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i> Fixtures tab loaded successfully
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4>Tournament Bracket</h4>
                            <div class="tournament-status">
                                <div class="status-indicator">
                                    <span class="status-label">Participants:</span>
                                    <span class="status-value"><?php echo count($participants); ?>/<?php echo $category['format_min_participants'] ?? 2; ?></span>
                                    <?php
                                    switch ($tournament_status) {
                                        case 'exists':
                                        case 'generated':
                                            echo '<span class="status-badge status-ready">
                                                <i class="fas fa-check-circle"></i> Active
                                            </span>';
                                            break;
                                        case 'generating':
                                            echo '<span class="status-badge status-generating">
                                                <i class="fas fa-cog fa-spin"></i> Generating
                                            </span>';
                                            break;
                                        case 'waiting':
                                            echo '<span class="status-badge status-waiting">
                                                <i class="fas fa-clock"></i> Waiting
                                            </span>';
                                            break;
                                        case 'failed':
                                        case 'error':
                                            echo '<span class="status-badge status-error">
                                                <i class="fas fa-exclamation-triangle"></i> Error
                                            </span>';
                                            break;
                                    }
                                    ?>
                                </div>
                                <div class="auto-generation-info">
                                    <small class="text-muted">
                                        <i class="fas fa-magic"></i>
                                        <?php echo $auto_tournament_message; ?>
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div id="bracketDisplay">
                            <?php
                            // Load and display bracket
                            require_once 'includes/bracket_display.php';

                            // Get tournament structure ID
                            $tournament_id = null;
                            try {
                                $stmt = $conn->prepare("
                                    SELECT id FROM tournament_structures
                                    WHERE event_sport_id = ?
                                    ORDER BY created_at DESC LIMIT 1
                                ");
                                $stmt->execute([$category['event_sport_id']]);
                                $tournament = $stmt->fetch();
                                if ($tournament) {
                                    $tournament_id = $tournament['id'];
                                }
                            } catch (Exception $e) {
                                error_log("Error getting tournament ID: " . $e->getMessage());
                            }

                            if ($tournament_id) {
                                echo "<div class='alert alert-success'>";
                                echo "<i class='fas fa-check-circle'></i> Tournament found (ID: $tournament_id)";
                                echo "</div>";

                                $bracketDisplay = new BracketDisplay($conn, $tournament_id, $category['event_sport_id']);
                                echo $bracketDisplay->renderBracket();
                            } else {
                                echo "<div class='alert alert-info'>";
                                echo "<i class='fas fa-info-circle'></i> No tournament found - showing requirements";
                                echo "</div>";

                                // Create bracket display without tournament structure to show requirements
                                $bracketDisplay = new BracketDisplay($conn, null, $category['event_sport_id']);
                                echo $bracketDisplay->renderBracket();
                            }
                            ?>
                        </div>

                        <!-- Traditional table view toggle -->
                        <div class="mt-4">
                            <button class="btn btn-outline-secondary btn-sm" onclick="toggleTableView()">
                                <i class="fas fa-table"></i> Show Table View
                            </button>
                        </div>

                        <div id="tableView" style="display: none;">
                            <?php if (!empty($matches)): ?>
                            <div class="table-responsive mt-3">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Round</th>
                                            <th>Match</th>
                                            <th>Team 1</th>
                                            <th>Team 2</th>
                                            <th>Winner</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($matches as $match): ?>
                                        <tr>
                                            <td><strong>Round <?php echo $match['round_number']; ?></strong></td>
                                            <td>Match <?php echo $match['match_number']; ?></td>
                                            <td><?php echo htmlspecialchars($match['team1_name'] ?? 'TBD'); ?></td>
                                            <td><?php echo htmlspecialchars($match['team2_name'] ?? 'TBD'); ?></td>
                                            <td><?php echo htmlspecialchars($match['winner_name'] ?? 'TBD'); ?></td>
                                            <td>
                                                <span class="badge badge-<?php echo $match['status'] == 'completed' ? 'success' : ($match['status'] == 'pending' ? 'warning' : 'secondary'); ?>">
                                                    <?php echo ucfirst($match['status']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-primary" onclick="editMatch('<?php echo $match['id']; ?>')">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Standings Tab -->
                    <div id="standings" class="tab-pane">
                        <h4>Tournament Standings</h4>

                        <?php if (!empty($standings)): ?>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead class="thead-light">
                                    <tr>
                                        <th>Rank</th>
                                        <th>Department</th>
                                        <th>Played</th>
                                        <th>Wins</th>
                                        <th>Losses</th>
                                        <th>Points For</th>
                                        <th>Points Against</th>
                                        <th>Difference</th>
                                        <th>Tournament Points</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($standings as $standing): ?>
                                    <tr>
                                        <td>
                                            <strong class="badge badge-primary">#<?php echo $standing['position']; ?></strong>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="team-color" style="width: 12px; height: 12px; background: <?php echo $standing['color_code']; ?>; border-radius: 50%; margin-right: 8px;"></div>
                                                <div>
                                                    <strong><?php echo htmlspecialchars($standing['department_name']); ?></strong>
                                                    <br><small class="text-muted"><?php echo htmlspecialchars($standing['abbreviation']); ?></small>
                                                </div>
                                            </div>
                                        </td>
                                        <td><span class="badge badge-secondary"><?php echo $standing['matches_played']; ?></span></td>
                                        <td><span class="text-success font-weight-bold"><?php echo $standing['wins']; ?></span></td>
                                        <td><span class="text-danger font-weight-bold"><?php echo $standing['losses']; ?></span></td>
                                        <td><?php echo $standing['points_for']; ?></td>
                                        <td><?php echo $standing['points_against']; ?></td>
                                        <td>
                                            <span class="<?php echo $standing['points_difference'] >= 0 ? 'text-success' : 'text-danger'; ?>">
                                                <?php echo $standing['points_difference'] >= 0 ? '+' : ''; ?><?php echo $standing['points_difference']; ?>
                                            </span>
                                        </td>
                                        <td><strong class="text-primary"><?php echo $standing['tournament_points']; ?></strong></td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php else: ?>
                        <div class="empty-state" style="text-align: center; padding: 40px;">
                            <i class="fas fa-trophy" style="font-size: 4em; color: #6c757d; margin-bottom: 20px;"></i>
                            <h4 style="color: #343a40; margin-bottom: 15px;">No Standings Available</h4>
                            <p style="color: #6c757d; margin-bottom: 25px;">Tournament standings will appear here once participants are registered and matches are played</p>

                            <?php if (!empty($participants)): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                <strong><?php echo count($participants); ?> participant(s) registered</strong> - Tournament will auto-generate when conditions are met
                            </div>
                            <?php endif; ?>

                            <button class="btn btn-info" onclick="showStandingsDemo()">
                                <i class="fas fa-eye"></i> View Standings Demo
                            </button>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Match Edit Modal -->
        <?php
        if ($tournament_id) {
            $bracketDisplay = new BracketDisplay($conn, $tournament_id, $category['event_sport_id']);
            echo $bracketDisplay->renderMatchEditModal();
        }
        ?>
    </div>

    <!-- Enhanced Bracket Modal System -->
    <script>
        // Set global variables for modal system
        window.currentEventId = <?php echo json_encode($event_id); ?>;
        window.currentSportId = <?php echo json_encode($sport_id); ?>;
    </script>
    <script src="assets/js/bracket-modals.js"></script>

    <script>
        // DOM ready event listeners for tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, setting up tab listeners');

            // Add click listeners to all tab buttons as backup
            document.querySelectorAll('.tab-button').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    const tabName = this.getAttribute('data-tab');

                    if (tabName) {
                        console.log('Event listener calling showTab with:', tabName);
                        showTab(tabName, this);
                    }
                });
            });

            // Test if showTab function is available
            if (typeof window.showTab === 'function') {
                console.log('showTab function is available');
            } else {
                console.error('showTab function is NOT available');
            }
        });

        // Auto-refresh every 15 seconds to check for tournament generation
        let autoRefreshInterval;

        function startAutoRefresh() {
            autoRefreshInterval = setInterval(function() {
                // Check if tournament should be auto-generated
                checkTournamentStatus();
            }, 15000);
        }

        function checkTournamentStatus() {
            fetch('ajax/check_tournament_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'event_sport_id=<?php echo $category['event_sport_id']; ?>&category_id=<?php echo $category_id; ?>'
            })
            .then(response => response.json())
            .then(data => {
                if (data.tournament_generated || data.status_changed) {
                    // Reload page to show new tournament
                    location.reload();
                }
            })
            .catch(error => {
                console.error('Error checking tournament status:', error);
            });
        }

        // Sidebar toggle functionality
        document.addEventListener('DOMContentLoaded', function() {
            const sidebarToggle = document.getElementById('sidebarToggle');
            const sidebar = document.querySelector('.admin-sidebar');
            const body = document.body;

            if (sidebarToggle) {
                sidebarToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('collapsed');
                    body.classList.toggle('sidebar-collapsed');
                });
            }

            // Start auto-refresh when page loads
            startAutoRefresh();
        });

        // Demo function for enhanced bracket (removed manual generation)
        function showEnhancedDemo() {
            alert('Enhanced Bracket Demo\\n\\nThe automatic tournament generation system is now active!\\n\\nFeatures:\\n• Auto-generates when participants are ready\\n• Real-time status indicators\\n• Background monitoring\\n• Seamless user experience\\n\\nNo manual intervention required!');
        }

        // Demo functions for enhanced bracket
        function openMatchEditModal(matchId) {
            if (typeof scBracketModals !== 'undefined') {
                scBracketModals.openMatchEditModal(matchId);
            } else {
                alert('Enhanced Modal System Demo\\n\\nMatch ID: ' + matchId + '\\n\\nThis would open a comprehensive modal dialog with:\\n• Team selection dropdowns\\n• Score input fields\\n• Match status controls\\n• Winner determination\\n• Notes and scheduling\\n• Real-time validation');
            }
        }

        function sendToReferee(matchId) {
            alert('Send to Referee Demo\\n\\nMatch ID: ' + matchId + '\\n\\nThis would:\\n• Generate secure referee token\\n• Send email/SMS notification\\n• Create mobile-optimized scoring interface\\n• Enable real-time score updates\\n• Sync across all interfaces');
        }

        function showStandingsDemo() {
            alert('Standings Demo\\n\\nThe automatic tournament system will generate standings once:\\n• Tournament is auto-generated\\n• Matches are played\\n• Results are recorded\\n\\nFeatures include:\\n• Real-time ranking updates\\n• Win/Loss tracking\\n• Points differential\\n• Color-coded indicators\\n• Professional design');
        }

        function toggleTableView() {
            const tableView = document.getElementById('tableView');
            const button = event.target.closest('button');

            if (tableView.style.display === 'none') {
                tableView.style.display = 'block';
                button.innerHTML = '<i class="fas fa-sitemap"></i> Show Bracket View';
            } else {
                tableView.style.display = 'none';
                button.innerHTML = '<i class="fas fa-table"></i> Show Table View';
            }
        }

        function editMatch(matchId) {
            // Load match data and show modal
            fetch('ajax/advanced-tournament-management.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: 'action=get_match&match_id=' + matchId
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    populateMatchEditModal(data.match);
                    $('#editMatchModal').modal('show');
                } else {
                    alert('Error loading match data: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to load match data');
            });
        }

        function populateMatchEditModal(match) {
            document.getElementById('editMatchId').value = match.id;
            document.getElementById('editTeam1Score').value = match.team1_score || '';
            document.getElementById('editTeam2Score').value = match.team2_score || '';

            // Set winner radio button
            if (match.winner_id) {
                if (match.winner_id == match.team1_id) {
                    document.querySelector('input[name="winner"][value="team1"]').checked = true;
                } else if (match.winner_id == match.team2_id) {
                    document.querySelector('input[name="winner"][value="team2"]').checked = true;
                }
            }

            // Set status
            document.querySelector('select[name="status"]').value = match.status || 'pending';
        }

        function saveMatchResult() {
            const form = document.getElementById('editMatchForm');
            const formData = new FormData(form);
            formData.append('action', 'save_match_result');

            fetch('ajax/advanced-tournament-management.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Match result saved successfully!');
                    $('#editMatchModal').modal('hide');
                    location.reload();
                } else {
                    alert('Error saving match result: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Failed to save match result');
            });
        }

        <?php if ($auto_tournament_generated): ?>
        // Show success notification
        setTimeout(function() {
            alert('Tournament automatically generated with <?php echo count($participants); ?> participants!');
        }, 1000);
        <?php endif; ?>
    </script>
</body>
</html>
