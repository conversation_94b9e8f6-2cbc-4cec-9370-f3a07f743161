<?php
/**
 * Test Category Navigation
 * Comprehensive test to verify category navigation works
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Category Navigation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 Test Category Navigation</h1>
        <p>Comprehensive test to verify category navigation works correctly</p>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>1. Database Status Check</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    // Check all required tables
                    $events_count = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
                    $sports_count = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
                    $event_sports_count = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
                    $categories_count = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
                    
                    echo "<div class='row'>";
                    echo "<div class='col-md-3'><div class='alert alert-info text-center'><h4>$events_count</h4>Events</div></div>";
                    echo "<div class='col-md-3'><div class='alert alert-info text-center'><h4>$sports_count</h4>Sports</div></div>";
                    echo "<div class='col-md-3'><div class='alert alert-info text-center'><h4>$event_sports_count</h4>Event-Sports</div></div>";
                    echo "<div class='col-md-3'><div class='alert alert-info text-center'><h4>$categories_count</h4>Categories</div></div>";
                    echo "</div>";
                    
                    if ($categories_count == 0) {
                        echo "<div class='alert alert-danger'>";
                        echo "<h5>❌ CRITICAL ISSUE: No categories found!</h5>";
                        echo "<p>This is why navigation fails. Categories must exist for navigation to work.</p>";
                        echo "<button class='btn btn-primary' onclick='createTestData()'>Create Test Categories</button>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-success'>";
                        echo "<h5>✅ Categories exist. Checking relationships...</h5>";
                        echo "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database Error: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>2. Category-Event-Sport Relationships</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    $stmt = $conn->prepare("
                        SELECT 
                            sc.id as category_id,
                            sc.category_name,
                            sc.event_sport_id,
                            es.event_id,
                            es.sport_id,
                            e.name as event_name,
                            s.name as sport_name
                        FROM sport_categories sc
                        JOIN event_sports es ON sc.event_sport_id = es.id
                        JOIN events e ON es.event_id = e.id
                        JOIN sports s ON es.sport_id = s.id
                        ORDER BY sc.id
                        LIMIT 10
                    ");
                    $stmt->execute();
                    $relationships = $stmt->fetchAll();
                    
                    if ($relationships) {
                        echo "<div class='table-responsive'>";
                        echo "<table class='table table-striped'>";
                        echo "<thead><tr><th>Category ID</th><th>Category Name</th><th>Event</th><th>Sport</th><th>Navigation Test</th></tr></thead>";
                        echo "<tbody>";
                        
                        foreach ($relationships as $rel) {
                            $test_url = "manage-category.php?event_id={$rel['event_id']}&sport_id={$rel['sport_id']}&category_id={$rel['category_id']}";
                            $categories_url = "sport-categories.php?event_id={$rel['event_id']}&sport_id={$rel['sport_id']}";
                            
                            echo "<tr>";
                            echo "<td>{$rel['category_id']}</td>";
                            echo "<td>" . htmlspecialchars($rel['category_name']) . "</td>";
                            echo "<td>" . htmlspecialchars($rel['event_name']) . "</td>";
                            echo "<td>" . htmlspecialchars($rel['sport_name']) . "</td>";
                            echo "<td>";
                            echo "<a href='$categories_url' class='btn btn-sm btn-primary me-1' target='_blank'>Categories Page</a>";
                            echo "<a href='$test_url' class='btn btn-sm btn-success' target='_blank'>Direct Test</a>";
                            echo "</td>";
                            echo "</tr>";
                        }
                        
                        echo "</tbody></table>";
                        echo "</div>";
                        
                        echo "<div class='alert alert-info mt-3'>";
                        echo "<h6>🧪 How to Test:</h6>";
                        echo "<ol>";
                        echo "<li>Click 'Categories Page' to go to sport-categories.php</li>";
                        echo "<li>On that page, click on a category name</li>";
                        echo "<li>You should see the three-tab interface (Overview, Fixtures, Standings)</li>";
                        echo "<li>If it redirects to events.php, there's still an issue</li>";
                        echo "</ol>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-warning'>No valid category relationships found.</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>3. Direct manage-category.php Tests</h3>
            </div>
            <div class="card-body">
                <p>Test manage-category.php directly with different parameter combinations:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Valid Tests:</h6>
                        <div class="list-group">
                            <a href="manage-category.php?event_id=1&sport_id=1&category_id=1" class="list-group-item list-group-item-action" target="_blank">
                                Standard: event_id=1, sport_id=1, category_id=1
                            </a>
                            <a href="manage-category.php?event_id=1&sport_id=1&category_id=1&debug=1" class="list-group-item list-group-item-action" target="_blank">
                                Debug Mode: Shows detailed error info
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Invalid Tests (should redirect):</h6>
                        <div class="list-group">
                            <a href="manage-category.php" class="list-group-item list-group-item-action" target="_blank">
                                No parameters (should redirect to events.php)
                            </a>
                            <a href="manage-category.php?event_id=999&sport_id=999&category_id=999&debug=1" class="list-group-item list-group-item-action" target="_blank">
                                Invalid IDs (should show debug info)
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3>4. Create Test Data (If Needed)</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5>🔧 If Navigation Still Fails:</h5>
                    <p>Click the button below to create comprehensive test data that will ensure navigation works.</p>
                </div>
                
                <button class="btn btn-success btn-lg" onclick="createTestData()">
                    <i class="fas fa-database"></i> Create Complete Test Data
                </button>
                
                <div id="result" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script>
        function createTestData() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Creating comprehensive test data...</div>';
            
            fetch('instant-fix-navigation.php?autofix=1')
                .then(response => {
                    if (response.redirected) {
                        resultDiv.innerHTML = '<div class="alert alert-success">Test data created successfully! Refreshing page...</div>';
                        setTimeout(() => location.reload(), 2000);
                    } else {
                        return response.text();
                    }
                })
                .then(text => {
                    if (text) {
                        resultDiv.innerHTML = '<div class="alert alert-warning">Response: ' + text + '</div>';
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = '<div class="alert alert-danger">Error: ' + error.message + '</div>';
                });
        }
    </script>
</body>
</html>
