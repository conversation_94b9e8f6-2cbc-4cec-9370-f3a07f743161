<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🔧 Check and Fix Tournament Database Tables</h1>";
echo "<p>Checking and fixing database structure for tournament management...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $fixes_applied = [];
    $errors = [];
    
    echo "<h2>1. Checking Required Tables</h2>";
    
    // Check if tournament_formats table exists
    try {
        $stmt = $conn->query("DESCRIBE tournament_formats");
        echo "<p style='color: green;'>✅ tournament_formats table exists</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ tournament_formats table missing</p>";
        echo "<h3>Creating tournament_formats table...</h3>";
        
        $sql = "CREATE TABLE tournament_formats (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            description TEXT,
            code VARCHAR(50) UNIQUE NOT NULL,
            algorithm_class VARCHAR(100) NOT NULL,
            sport_type_category ENUM('team', 'individual', 'academic', 'judged', 'all') DEFAULT 'all',
            rounds_formula VARCHAR(100),
            matches_formula VARCHAR(100),
            min_participants INT DEFAULT 2,
            max_participants INT DEFAULT 128,
            supports_seeding BOOLEAN DEFAULT TRUE,
            supports_byes BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )";
        
        $conn->exec($sql);
        echo "<p style='color: green;'>✅ tournament_formats table created</p>";
        $fixes_applied[] = "Created tournament_formats table";
        
        // Insert default formats
        $default_formats = [
            [
                'name' => 'Single Elimination',
                'description' => 'Traditional knockout tournament where teams/participants are eliminated after one loss',
                'code' => 'single_elimination',
                'algorithm_class' => 'SingleEliminationAlgorithm',
                'rounds_formula' => 'ceil(log2(n))',
                'matches_formula' => 'n - 1',
                'min_participants' => 2,
                'max_participants' => 128
            ],
            [
                'name' => 'Round Robin',
                'description' => 'Every team/participant plays every other team/participant once',
                'code' => 'round_robin',
                'algorithm_class' => 'RoundRobinAlgorithm',
                'rounds_formula' => 'n - 1',
                'matches_formula' => 'n * (n - 1) / 2',
                'min_participants' => 2,
                'max_participants' => 16
            ],
            [
                'name' => 'Double Elimination',
                'description' => 'Teams/participants must lose twice to be eliminated',
                'code' => 'double_elimination',
                'algorithm_class' => 'DoubleEliminationAlgorithm',
                'rounds_formula' => '2 * ceil(log2(n)) - 1',
                'matches_formula' => '2 * n - 2',
                'min_participants' => 2,
                'max_participants' => 64
            ]
        ];
        
        foreach ($default_formats as $format) {
            $stmt = $conn->prepare("
                INSERT INTO tournament_formats 
                (name, description, code, algorithm_class, rounds_formula, matches_formula, min_participants, max_participants)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $format['name'],
                $format['description'],
                $format['code'],
                $format['algorithm_class'],
                $format['rounds_formula'],
                $format['matches_formula'],
                $format['min_participants'],
                $format['max_participants']
            ]);
        }
        echo "<p style='color: green;'>✅ Default tournament formats inserted</p>";
        $fixes_applied[] = "Inserted default tournament formats";
    }
    
    // Check if tournament_structures table exists
    try {
        $stmt = $conn->query("DESCRIBE tournament_structures");
        echo "<p style='color: green;'>✅ tournament_structures table exists</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ tournament_structures table missing</p>";
        echo "<h3>Creating tournament_structures table...</h3>";
        
        $sql = "CREATE TABLE tournament_structures (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_sport_id INT NOT NULL,
            tournament_format_id INT NOT NULL,
            name VARCHAR(200) NOT NULL,
            participant_count INT DEFAULT 0,
            seeding_method ENUM('random', 'ranked', 'manual') DEFAULT 'random',
            scoring_config JSON,
            bracket_data JSON,
            total_rounds INT DEFAULT 0,
            current_round INT DEFAULT 1,
            status ENUM('setup', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
            start_date DATETIME,
            end_date DATETIME,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
            FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id) ON DELETE RESTRICT
        )";
        
        $conn->exec($sql);
        echo "<p style='color: green;'>✅ tournament_structures table created</p>";
        $fixes_applied[] = "Created tournament_structures table";
    }
    
    // Check if tournament_participants table exists
    try {
        $stmt = $conn->query("DESCRIBE tournament_participants");
        echo "<p style='color: green;'>✅ tournament_participants table exists</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ tournament_participants table missing</p>";
        echo "<h3>Creating tournament_participants table...</h3>";
        
        $sql = "CREATE TABLE tournament_participants (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tournament_structure_id INT NOT NULL,
            registration_id INT NOT NULL,
            seed_number INT,
            group_assignment VARCHAR(10),
            current_status ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active',
            points DECIMAL(10,2) DEFAULT 0,
            wins INT DEFAULT 0,
            losses INT DEFAULT 0,
            draws INT DEFAULT 0,
            performance_data JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
            FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE,
            UNIQUE KEY unique_tournament_participant (tournament_structure_id, registration_id)
        )";
        
        $conn->exec($sql);
        echo "<p style='color: green;'>✅ tournament_participants table created</p>";
        $fixes_applied[] = "Created tournament_participants table";
    }
    
    // Check if tournament_rounds table exists
    try {
        $stmt = $conn->query("DESCRIBE tournament_rounds");
        echo "<p style='color: green;'>✅ tournament_rounds table exists</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ tournament_rounds table missing</p>";
        echo "<h3>Creating tournament_rounds table...</h3>";
        
        $sql = "CREATE TABLE tournament_rounds (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tournament_structure_id INT NOT NULL,
            round_number INT NOT NULL,
            round_name VARCHAR(100) NOT NULL,
            round_type ENUM('group', 'elimination', 'final', 'consolation') DEFAULT 'elimination',
            status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
            start_date DATETIME,
            end_date DATETIME,
            matches_count INT DEFAULT 0,
            completed_matches INT DEFAULT 0,
            advancement_criteria JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
            UNIQUE KEY unique_tournament_round (tournament_structure_id, round_number)
        )";
        
        $conn->exec($sql);
        echo "<p style='color: green;'>✅ tournament_rounds table created</p>";
        $fixes_applied[] = "Created tournament_rounds table";
    }
    
    echo "<h2>2. Checking and Fixing Missing Columns in Existing Tables</h2>";

    // Check and fix tournament_participants table columns
    echo "<h3>Checking tournament_participants table...</h3>";
    try {
        $stmt = $conn->query("DESCRIBE tournament_participants");
        $columns = $stmt->fetchAll();
        $existing_columns = array_column($columns, 'Field');

        $required_columns = [
            'tournament_structure_id' => 'INT NOT NULL',
            'registration_id' => 'INT NOT NULL',
            'seed_number' => 'INT',
            'current_status' => "ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active'"
        ];

        foreach ($required_columns as $column => $definition) {
            if (!in_array($column, $existing_columns)) {
                echo "<p style='color: red;'>❌ tournament_participants missing column: {$column}</p>";
                try {
                    $sql = "ALTER TABLE tournament_participants ADD COLUMN {$column} {$definition}";
                    $conn->exec($sql);
                    echo "<p style='color: green;'>✅ Added {$column} column to tournament_participants</p>";
                    $fixes_applied[] = "Added {$column} column to tournament_participants";
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Failed to add {$column}: " . $e->getMessage() . "</p>";
                    $errors[] = "Failed to add {$column}: " . $e->getMessage();
                }
            } else {
                echo "<p style='color: green;'>✅ tournament_participants has {$column} column</p>";
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ tournament_participants table issue: " . $e->getMessage() . "</p>";
    }

    // Check and fix tournament_rounds table columns
    echo "<h3>Checking tournament_rounds table...</h3>";
    try {
        $stmt = $conn->query("DESCRIBE tournament_rounds");
        $columns = $stmt->fetchAll();
        $existing_columns = array_column($columns, 'Field');

        $required_columns = [
            'tournament_structure_id' => 'INT NOT NULL',
            'round_number' => 'INT NOT NULL',
            'round_name' => 'VARCHAR(100) NOT NULL',
            'status' => "ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending'"
        ];

        foreach ($required_columns as $column => $definition) {
            if (!in_array($column, $existing_columns)) {
                echo "<p style='color: red;'>❌ tournament_rounds missing column: {$column}</p>";
                try {
                    $sql = "ALTER TABLE tournament_rounds ADD COLUMN {$column} {$definition}";
                    $conn->exec($sql);
                    echo "<p style='color: green;'>✅ Added {$column} column to tournament_rounds</p>";
                    $fixes_applied[] = "Added {$column} column to tournament_rounds";
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Failed to add {$column}: " . $e->getMessage() . "</p>";
                    $errors[] = "Failed to add {$column}: " . $e->getMessage();
                }
            } else {
                echo "<p style='color: green;'>✅ tournament_rounds has {$column} column</p>";
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ tournament_rounds table issue: " . $e->getMessage() . "</p>";
    }

    // Check and fix matches table columns
    echo "<h3>Checking matches table...</h3>";
    $stmt = $conn->query("DESCRIBE matches");
    $columns = $stmt->fetchAll();
    $existing_columns = array_column($columns, 'Field');

    $required_columns = [
        'tournament_structure_id' => 'INT',
        'tournament_round_id' => 'INT',
        'bracket_position' => 'VARCHAR(50)',
        'is_bye_match' => 'BOOLEAN DEFAULT FALSE'
    ];

    foreach ($required_columns as $column => $definition) {
        if (!in_array($column, $existing_columns)) {
            echo "<p style='color: red;'>❌ matches table missing column: {$column}</p>";
            echo "<h4>Adding {$column} column...</h4>";

            try {
                $sql = "ALTER TABLE matches ADD COLUMN {$column} {$definition}";
                $conn->exec($sql);
                echo "<p style='color: green;'>✅ Added {$column} column to matches table</p>";
                $fixes_applied[] = "Added {$column} column to matches table";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Failed to add {$column}: " . $e->getMessage() . "</p>";
                $errors[] = "Failed to add {$column}: " . $e->getMessage();
            }
        } else {
            echo "<p style='color: green;'>✅ matches table has {$column} column</p>";
        }
    }
    
    echo "<h2>3. Adding Foreign Key Constraints</h2>";
    
    // Add foreign key constraints if they don't exist
    try {
        // Check if foreign key constraints exist
        $stmt = $conn->query("
            SELECT CONSTRAINT_NAME 
            FROM information_schema.KEY_COLUMN_USAGE 
            WHERE TABLE_SCHEMA = DATABASE() 
            AND TABLE_NAME = 'matches' 
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");
        $existing_fks = $stmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (!in_array('matches_ibfk_tournament_structure', $existing_fks)) {
            $sql = "ALTER TABLE matches ADD CONSTRAINT matches_ibfk_tournament_structure 
                    FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE";
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Added tournament_structure_id foreign key constraint</p>";
            $fixes_applied[] = "Added tournament_structure_id foreign key constraint";
        } else {
            echo "<p style='color: green;'>✅ tournament_structure_id foreign key constraint exists</p>";
        }
        
        if (!in_array('matches_ibfk_tournament_round', $existing_fks)) {
            $sql = "ALTER TABLE matches ADD CONSTRAINT matches_ibfk_tournament_round 
                    FOREIGN KEY (tournament_round_id) REFERENCES tournament_rounds(id) ON DELETE CASCADE";
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Added tournament_round_id foreign key constraint</p>";
            $fixes_applied[] = "Added tournament_round_id foreign key constraint";
        } else {
            echo "<p style='color: green;'>✅ tournament_round_id foreign key constraint exists</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠️ Foreign key constraint issue: " . $e->getMessage() . "</p>";
        echo "<p style='color: orange;'>This is not critical for basic functionality</p>";
    }
    
    echo "<h2>4. Creating Performance Indexes</h2>";
    
    $indexes = [
        'idx_tournament_structures_event_sport' => 'CREATE INDEX idx_tournament_structures_event_sport ON tournament_structures(event_sport_id)',
        'idx_tournament_structures_status' => 'CREATE INDEX idx_tournament_structures_status ON tournament_structures(status)',
        'idx_tournament_rounds_tournament' => 'CREATE INDEX idx_tournament_rounds_tournament ON tournament_rounds(tournament_structure_id)',
        'idx_tournament_participants_tournament' => 'CREATE INDEX idx_tournament_participants_tournament ON tournament_participants(tournament_structure_id)',
        'idx_matches_tournament_structure' => 'CREATE INDEX idx_matches_tournament_structure ON matches(tournament_structure_id)',
        'idx_matches_tournament_round' => 'CREATE INDEX idx_matches_tournament_round ON matches(tournament_round_id)'
    ];
    
    foreach ($indexes as $index_name => $sql) {
        try {
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Created index: {$index_name}</p>";
            $fixes_applied[] = "Created index: {$index_name}";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<p style='color: green;'>✅ Index {$index_name} already exists</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Index {$index_name} issue: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h2>5. Summary</h2>";
    
    if (!empty($fixes_applied)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ Fixes Applied Successfully</h3>";
        echo "<ul>";
        foreach ($fixes_applied as $fix) {
            echo "<li>{$fix}</li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ Database Structure is Complete</h3>";
        echo "<p>All required tables and columns are present.</p>";
        echo "</div>";
    }
    
    if (!empty($errors)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Errors Encountered</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>{$error}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li><a href='fix-tournament-format.php?event_id=4&sport_id=37' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Configure Tournament Format</a></li>";
    echo "<li><a href='tournament-diagnostic-tool.php?event_id=4&sport_id=37&category_id=15' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Run Full Diagnostic</a></li>";
    echo "<li><a href='manage-category.php?category_id=15&event_id=4&sport_id=37' style='background: #ffc107; color: black; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Test Auto-Generation</a></li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Database Check Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
