<?php
/**
 * Fix Missing Event-Sport Relationship
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Fix Missing Event-Sport Relationship</h1>";

$event_id = 4;
$sport_id = 40;

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; margin-bottom: 20px;'>";
echo "<h2 style='margin-top: 0;'>🎯 Target: Event ID $event_id, Sport ID $sport_id</h2>";
echo "<p>Checking and fixing the event-sport relationship that's causing the constraint error.</p>";
echo "</div>";

try {
    echo "<h2>1. Check Current State</h2>";
    
    // Check if event exists
    $stmt = $conn->prepare("SELECT id, name FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch();
    
    if ($event) {
        echo "<p>✅ <strong>Event exists:</strong> {$event['name']} (ID: {$event['id']})</p>";
    } else {
        echo "<p>❌ <strong>Event does not exist:</strong> ID $event_id</p>";
        exit;
    }
    
    // Check if sport exists
    $stmt = $conn->prepare("SELECT id, name FROM sports WHERE id = ?");
    $stmt->execute([$sport_id]);
    $sport = $stmt->fetch();
    
    if ($sport) {
        echo "<p>✅ <strong>Sport exists:</strong> {$sport['name']} (ID: {$sport['id']})</p>";
    } else {
        echo "<p>❌ <strong>Sport does not exist:</strong> ID $sport_id</p>";
        exit;
    }
    
    // Check if event-sport relationship exists
    $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    
    if ($event_sport) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Event-Sport relationship exists:</strong> event_sport_id = {$event_sport['id']}</p>";
        echo "<p>The relationship is already there. The issue might be elsewhere.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Event-Sport relationship missing!</strong></p>";
        echo "<p>This is the root cause of the constraint error.</p>";
        echo "</div>";
        
        echo "<h2>2. Create Missing Event-Sport Relationship</h2>";
        
        try {
            $stmt = $conn->prepare("
                INSERT INTO event_sports (event_id, sport_id, max_teams, venue, status)
                VALUES (?, ?, 8, 'Main Sports Hall', 'registration')
            ");
            $stmt->execute([$event_id, $sport_id]);
            $new_event_sport_id = $conn->lastInsertId();
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>✅ <strong>Success!</strong> Created event-sport relationship</p>";
            echo "<p><strong>New event_sport_id:</strong> $new_event_sport_id</p>";
            echo "<p><strong>Event:</strong> {$event['name']}</p>";
            echo "<p><strong>Sport:</strong> {$sport['name']}</p>";
            echo "</div>";
            
            // Log the activity
            logAdminActivity('CREATE_EVENT_SPORT', 'event_sports', $new_event_sport_id, null, [
                'event_id' => $event_id,
                'sport_id' => $sport_id,
                'event_name' => $event['name'],
                'sport_name' => $sport['name']
            ]);
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>❌ <strong>Error creating relationship:</strong> " . $e->getMessage() . "</p>";
            echo "</div>";
            exit;
        }
    }
    
    echo "<h2>3. Test Category Creation</h2>";
    
    // Get the event_sport_id (either existing or newly created)
    $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    $event_sport_id = $event_sport['id'];
    
    echo "<p><strong>Using event_sport_id:</strong> $event_sport_id</p>";
    
    // Test category creation
    try {
        $test_category_name = "Test Category " . date('His');
        
        $stmt = $conn->prepare("
            INSERT INTO sport_categories 
            (event_sport_id, category_name, category_type, referee_name, referee_email, venue)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            $event_sport_id,
            $test_category_name,
            'mixed',
            'Test Referee',
            '<EMAIL>',
            'Test Venue'
        ]);
        
        $category_id = $conn->lastInsertId();
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Category creation test successful!</strong></p>";
        echo "<p><strong>Created category:</strong> $test_category_name (ID: $category_id)</p>";
        echo "<p>The constraint error has been fixed.</p>";
        echo "</div>";
        
        // Clean up test category
        $stmt = $conn->prepare("DELETE FROM sport_categories WHERE id = ?");
        $stmt->execute([$category_id]);
        echo "<p><small>🧹 Cleaned up test category</small></p>";
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Category creation still failing:</strong> " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "<h2>4. Test the Sport Categories Page</h2>";
    
    $categories_url = "sport-categories.php?event_id=$event_id&sport_id=$sport_id";
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>Ready to Test!</h3>";
    echo "<p>The event-sport relationship has been fixed. You can now:</p>";
    echo "<ol>";
    echo "<li>Visit the sport-categories page</li>";
    echo "<li>Click 'Add New Category'</li>";
    echo "<li>Fill out the form and submit</li>";
    echo "<li>The constraint error should be resolved</li>";
    echo "</ol>";
    echo "<p><a href='$categories_url' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block; margin-top: 10px;'>";
    echo "<i class='fas fa-external-link-alt'></i> Test Sport Categories Page";
    echo "</a></p>";
    echo "</div>";
    
    echo "<h2>5. Check All Event-Sport Relationships</h2>";
    
    $stmt = $conn->prepare("
        SELECT 
            es.id as event_sport_id,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name,
            COUNT(sc.id) as categories_count
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN sport_categories sc ON es.id = sc.event_sport_id
        GROUP BY es.id, es.event_id, es.sport_id, e.name, s.name
        ORDER BY e.name, s.name
    ");
    $stmt->execute();
    $all_event_sports = $stmt->fetchAll();
    
    if (!empty($all_event_sports)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #007bff; color: white;'>";
        echo "<th>Event Sport ID</th><th>Event</th><th>Sport</th><th>Categories</th><th>Actions</th>";
        echo "</tr>";
        
        foreach ($all_event_sports as $es) {
            $bg_color = ($es['event_id'] == $event_id && $es['sport_id'] == $sport_id) ? '#fff3cd' : 'white';
            echo "<tr style='background: $bg_color;'>";
            echo "<td>{$es['event_sport_id']}</td>";
            echo "<td>{$es['event_name']}</td>";
            echo "<td>{$es['sport_name']}</td>";
            echo "<td style='text-align: center;'>{$es['categories_count']}</td>";
            
            $test_url = "sport-categories.php?event_id={$es['event_id']}&sport_id={$es['sport_id']}";
            echo "<td><a href='$test_url' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Manage</a></td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<p><small>💡 <strong>Highlighted row</strong> shows the relationship we just fixed.</small></p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>6. Summary</h2>";

echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;'>";
echo "<h3>✅ Constraint Error Fixed!</h3>";
echo "<p><strong>Root Cause:</strong> The event-sport relationship was missing from the event_sports table.</p>";
echo "<p><strong>Solution:</strong> Created the missing relationship between Event ID $event_id and Sport ID $sport_id.</p>";
echo "<p><strong>Result:</strong> Category creation should now work without constraint errors.</p>";
echo "<p><strong>Next Step:</strong> Test the sport-categories page to confirm the fix works.</p>";
echo "</div>";
?>
