<?php
require_once '../config/database.php';
require_once '../includes/advanced_tournament_engine.php';
require_once '../includes/tournament_algorithms_advanced.php';

$database = new Database();
$conn = $database->getConnection();

$event_sport_id = 52; // From our previous tests
$category_id = 16;

echo "<h2>Manual Auto-Generation Test</h2>";

try {
    // Check current status
    $stmt = $conn->prepare("
        SELECT ts.*, tf.name as format_name
        FROM tournament_structures ts
        LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
        WHERE ts.event_sport_id = ? AND ts.status NOT IN ('cancelled', 'completed')
        ORDER BY ts.created_at DESC LIMIT 1
    ");
    $stmt->execute([$event_sport_id]);
    $existing_tournament = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($existing_tournament) {
        echo "<p style='color: orange;'>⚠️ Tournament already exists (ID: {$existing_tournament['id']}, Status: {$existing_tournament['status']})</p>";
        echo "<p><strong>Format:</strong> " . ($existing_tournament['format_name'] ?? 'Unknown') . "</p>";
        echo "<p><strong>Created:</strong> " . $existing_tournament['created_at'] . "</p>";
        
        // Check if it has matches
        $stmt = $conn->prepare("SELECT COUNT(*) as match_count FROM tournament_matches WHERE tournament_id = ?");
        $stmt->execute([$existing_tournament['id']]);
        $match_data = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p><strong>Matches:</strong> " . $match_data['match_count'] . "</p>";
        
    } else {
        echo "<p style='color: blue;'>ℹ️ No existing tournament found. Attempting auto-generation...</p>";
        
        // Test auto-generation
        $engine = new AdvancedTournamentEngine($conn);
        $result = $engine->generateTournament($event_sport_id, $category_id, [
            'seeding_method' => 'random',
            'third_place_playoff' => false,
            'scoring_config' => [
                'points_win' => 3,
                'points_draw' => 1,
                'points_loss' => 0
            ]
        ]);

        if ($result['success']) {
            echo "<p style='color: green;'>✅ Tournament successfully generated!</p>";
            echo "<p><strong>Tournament ID:</strong> " . $result['tournament_id'] . "</p>";
            echo "<p><strong>Format:</strong> " . $result['format'] . "</p>";
            echo "<p><strong>Participants:</strong> " . $result['participants_count'] . "</p>";
        } else {
            echo "<p style='color: red;'>❌ Auto-generation failed: " . $result['message'] . "</p>";
        }
    }

    echo "<p><a href='manage-category.php?event_id=4&sport_id=37&category_id=16'>← Back to Category Management</a></p>";

} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
