<?php
require_once '../admin/includes/config.php';

header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['token']) || !isset($input['status'])) {
        throw new Exception('Missing required parameters');
    }
    
    $token = $input['token'];
    $status = $input['status'];
    
    // Validate status
    $valid_statuses = ['pending', 'in_progress', 'completed'];
    if (!in_array($status, $valid_statuses)) {
        throw new Exception('Invalid match status');
    }
    
    // Verify token and get match details
    $stmt = $pdo->prepare("
        SELECT rs.match_id
        FROM referee_sessions rs
        JOIN tournament_matches m ON rs.match_id = m.id
        WHERE rs.token = ? AND rs.status = 'active' AND rs.expires_at > NOW()
    ");
    
    $stmt->execute([$token]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        throw new Exception('Invalid or expired referee session');
    }
    
    // Update match status
    $stmt = $pdo->prepare("
        UPDATE tournament_matches 
        SET status = ?, 
            updated_at = NOW()
        WHERE id = ?
    ");
    
    $stmt->execute([$status, $session['match_id']]);
    
    // If match is completed, mark referee session as used
    if ($status === 'completed') {
        $stmt = $pdo->prepare("UPDATE referee_sessions SET status = 'used' WHERE token = ?");
        $stmt->execute([$token]);
    }
    
    // Update referee session last accessed
    $stmt = $pdo->prepare("UPDATE referee_sessions SET last_accessed = NOW() WHERE token = ?");
    $stmt->execute([$token]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Match status updated successfully'
    ]);
    
} catch (Exception $e) {
    error_log("Error in update_match_status.php: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Error updating match status: ' . $e->getMessage()
    ]);
}
?>
