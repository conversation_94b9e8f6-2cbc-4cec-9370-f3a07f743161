<?php
/**
 * Complete Category Navigation Fix
 * Comprehensive solution to fix all category navigation issues
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$action = $_POST['action'] ?? $_GET['action'] ?? '';

if ($action === 'fix_all') {
    try {
        $conn->beginTransaction();
        
        $messages = [];
        
        // Step 1: Ensure we have events
        $event_count = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
        if ($event_count == 0) {
            $conn->exec("
                INSERT INTO events (name, description, start_date, end_date, venue, status) 
                VALUES 
                ('Intramural Championship 2024', 'Annual intramural sports championship', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 14 DAY), 'Main Sports Complex', 'active'),
                ('Spring Sports Festival', 'Spring semester sports activities', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 10 DAY), 'Athletic Center', 'active')
            ");
            $messages[] = "Created test events";
        }
        
        // Step 2: Ensure we have sports
        $sport_count = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
        if ($sport_count == 0) {
            $conn->exec("
                INSERT INTO sports (name, type, description) 
                VALUES 
                ('Basketball', 'traditional', 'Indoor basketball competition'),
                ('Volleyball', 'traditional', 'Indoor volleyball matches'),
                ('Football', 'traditional', 'Outdoor football games'),
                ('Badminton', 'traditional', 'Indoor badminton tournament')
            ");
            $messages[] = "Created test sports";
        }
        
        // Step 3: Create event_sports relationships
        $es_count = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
        if ($es_count == 0) {
            $conn->exec("
                INSERT INTO event_sports (event_id, sport_id) 
                SELECT e.id, s.id 
                FROM events e 
                CROSS JOIN sports s 
                WHERE e.id <= 2 AND s.id <= 4
            ");
            $messages[] = "Created event-sport relationships";
        }
        
        // Step 4: Create sport categories
        $cat_count = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
        if ($cat_count == 0) {
            // Get event_sports to create categories for
            $stmt = $conn->prepare("
                SELECT es.id as event_sport_id, e.name as event_name, s.name as sport_name
                FROM event_sports es
                JOIN events e ON es.event_id = e.id
                JOIN sports s ON es.sport_id = s.id
                ORDER BY es.id
            ");
            $stmt->execute();
            $event_sports = $stmt->fetchAll();
            
            $categories_created = 0;
            foreach ($event_sports as $es) {
                // Create Men's, Women's, and Mixed categories for each sport
                $categories = [
                    ['name' => "Men's " . $es['sport_name'], 'type' => 'men'],
                    ['name' => "Women's " . $es['sport_name'], 'type' => 'women'],
                    ['name' => "Mixed " . $es['sport_name'], 'type' => 'mixed']
                ];
                
                foreach ($categories as $cat) {
                    $stmt = $conn->prepare("
                        INSERT INTO sport_categories 
                        (event_sport_id, category_name, category_type, referee_name, referee_email, venue)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    
                    $stmt->execute([
                        $es['event_sport_id'],
                        $cat['name'],
                        $cat['type'],
                        'Referee ' . substr($cat['name'], 0, 10),
                        'referee' . $categories_created . '@sports.com',
                        'Sports Hall ' . (($categories_created % 3) + 1)
                    ]);
                    $categories_created++;
                }
            }
            $messages[] = "Created $categories_created sport categories";
        }
        
        // Step 5: Create departments
        $dept_count = $conn->query("SELECT COUNT(*) FROM departments")->fetchColumn();
        if ($dept_count == 0) {
            $departments = [
                ['Computer Science', 'CS', '#007bff'],
                ['Engineering', 'ENG', '#28a745'],
                ['Business Administration', 'BA', '#ffc107'],
                ['Arts & Sciences', 'AS', '#dc3545'],
                ['Medicine', 'MED', '#6f42c1'],
                ['Education', 'EDU', '#fd7e14'],
                ['Law', 'LAW', '#20c997'],
                ['Architecture', 'ARCH', '#e83e8c']
            ];
            
            $stmt = $conn->prepare("
                INSERT INTO departments (name, abbreviation, color_code, contact_person, contact_email)
                VALUES (?, ?, ?, ?, ?)
            ");
            
            foreach ($departments as $dept) {
                $stmt->execute([
                    $dept[0],
                    $dept[1], 
                    $dept[2],
                    'Contact Person',
                    strtolower($dept[1]) . '@university.edu'
                ]);
            }
            $messages[] = "Created " . count($departments) . " departments";
        }
        
        // Step 6: Create event registrations
        $reg_count = $conn->query("SELECT COUNT(*) FROM event_department_registrations")->fetchColumn();
        if ($reg_count == 0) {
            $conn->exec("
                INSERT INTO event_department_registrations (event_id, department_id, registration_date)
                SELECT e.id, d.id, CURDATE()
                FROM events e
                CROSS JOIN departments d
            ");
            $messages[] = "Created department registrations";
        }
        
        $conn->commit();
        
        // Return success response
        if (isset($_POST['action'])) {
            echo json_encode([
                'success' => true,
                'message' => 'Navigation fix completed: ' . implode(', ', $messages)
            ]);
        } else {
            header('Location: fix-category-navigation-complete.php?success=1&msg=' . urlencode(implode(', ', $messages)));
        }
        exit;
        
    } catch (Exception $e) {
        $conn->rollback();
        
        if (isset($_POST['action'])) {
            echo json_encode([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage()
            ]);
        } else {
            header('Location: fix-category-navigation-complete.php?error=' . urlencode($e->getMessage()));
        }
        exit;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Category Navigation Fix</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🔧 Complete Category Navigation Fix</h1>
        <p>Comprehensive solution to fix all category navigation issues</p>
        
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success">
                <h4>✅ Fix Applied Successfully!</h4>
                <p><?php echo htmlspecialchars($_GET['msg']); ?></p>
                <p><strong>Navigation should now work correctly!</strong></p>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger">
                <h4>❌ Error Occurred</h4>
                <p><?php echo htmlspecialchars($_GET['error']); ?></p>
            </div>
        <?php endif; ?>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>Current Database Status</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    $events = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
                    $sports = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
                    $event_sports = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
                    $categories = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
                    $departments = $conn->query("SELECT COUNT(*) FROM departments")->fetchColumn();
                    $registrations = $conn->query("SELECT COUNT(*) FROM event_department_registrations")->fetchColumn();
                    
                    echo "<div class='row'>";
                    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-primary'>$events</h4><small>Events</small></div></div>";
                    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-success'>$sports</h4><small>Sports</small></div></div>";
                    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-info'>$event_sports</h4><small>Event-Sports</small></div></div>";
                    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-warning'>$categories</h4><small>Categories</small></div></div>";
                    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-danger'>$departments</h4><small>Departments</small></div></div>";
                    echo "<div class='col-md-2'><div class='text-center'><h4 class='text-secondary'>$registrations</h4><small>Registrations</small></div></div>";
                    echo "</div>";
                    
                    $all_good = ($events > 0 && $sports > 0 && $event_sports > 0 && $categories > 0 && $departments > 0 && $registrations > 0);
                    
                    if ($all_good) {
                        echo "<div class='alert alert-success mt-3'>";
                        echo "<h5>✅ All Required Data Present</h5>";
                        echo "<p>Navigation should work. Test the links below.</p>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-warning mt-3'>";
                        echo "<h5>⚠️ Missing Required Data</h5>";
                        echo "<p>Some data is missing. Apply the complete fix below.</p>";
                        echo "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database Error: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>Apply Complete Fix</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5>🔧 What This Fix Does:</h5>
                    <ul>
                        <li>Creates comprehensive test events and sports</li>
                        <li>Establishes proper event-sport relationships</li>
                        <li>Creates Men's, Women's, and Mixed categories for each sport</li>
                        <li>Sets up departments with proper color coding</li>
                        <li>Creates event registrations linking departments to events</li>
                        <li>Ensures all database relationships are correct</li>
                    </ul>
                </div>
                
                <button class="btn btn-success btn-lg" onclick="applyFix()">
                    <i class="fas fa-magic"></i> Apply Complete Navigation Fix
                </button>
                
                <div id="fix-result" class="mt-3"></div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3>Test Navigation After Fix</h3>
            </div>
            <div class="card-body">
                <div id="test-links">
                    <p>Apply the fix first, then test links will appear here.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function applyFix() {
            const resultDiv = document.getElementById('fix-result');
            resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Applying complete navigation fix...</div>';
            
            fetch('fix-category-navigation-complete.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=fix_all'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">Error: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">Network error: ${error.message}</div>`;
            });
        }
        
        // Load test links after page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadTestLinks();
        });
        
        function loadTestLinks() {
            // Create test links based on available data
            const testLinksDiv = document.getElementById('test-links');
            testLinksDiv.innerHTML = `
                <h5>Navigation Test Links:</h5>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Step-by-Step Test</h6>
                                <p class="card-text">Test the complete navigation flow</p>
                                <a href="sport-categories.php?event_id=1&sport_id=1" class="btn btn-primary" target="_blank">
                                    <i class="fas fa-list"></i> Go to Categories Page
                                </a>
                                <p class="mt-2"><small class="text-muted">Click above, then click on a category name to test navigation</small></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">Direct Test</h6>
                                <p class="card-text">Test manage-category.php directly</p>
                                <a href="manage-category.php?event_id=1&sport_id=1&category_id=1" class="btn btn-success" target="_blank">
                                    <i class="fas fa-cog"></i> Direct Category Management
                                </a>
                                <p class="mt-2"><small class="text-muted">Should show three-tab interface</small></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-info mt-3">
                    <h6>🧪 Expected Navigation Flow:</h6>
                    <ol>
                        <li>Click "Go to Categories Page" above</li>
                        <li>On the categories page, click on any category name</li>
                        <li>You should see the manage-category.php page with three tabs: Overview, Fixtures, Standings</li>
                        <li>If it redirects to events.php, there's still an issue</li>
                    </ol>
                </div>
            `;
        }
    </script>
</body>
</html>
