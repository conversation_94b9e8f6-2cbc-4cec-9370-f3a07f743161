<?php
/**
 * Final Modal Test - Exact replica of Add Sport to Event modal
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get available sports for event ID 1
$available_sports = getAvailableSports($conn, 1);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Modal Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-info { background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 5px; border: 1px solid #dee2e6; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🎯 Final Modal Test</h1>
        <p>Testing the exact "Add Sport to Event" modal functionality</p>
        
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addSportModal">
            Open Add Sport Modal
        </button>
        
        <div class="debug-info">
            <h5>Debug Information</h5>
            <div id="debug-output">
                <p>Select a sport to see debug information...</p>
            </div>
        </div>
    </div>

    <!-- Add Sport Modal (Exact replica from manage-event.php) -->
    <div class="modal fade" id="addSportModal" tabindex="-1" aria-labelledby="addSportModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addSportModalLabel">Add Sport to Event</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form id="addSportForm" method="POST">
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <strong>Automatic Department Registration</strong><br>
                            All departments currently registered for this event will automatically be added as participants in this sport.
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label" for="sport_id">Sport:</label>
                            <select name="sport_id" id="sport_id" class="form-control" required onchange="loadBracketTypes()">
                                <option value="">Select a sport...</option>
                                <?php foreach ($available_sports as $sport): ?>
                                    <option value="<?php echo $sport['id']; ?>"
                                            data-type="<?php echo $sport['sport_type_category'] ?? $sport['type']; ?>"
                                            data-sport-type-name="<?php echo htmlspecialchars($sport['sport_type_name'] ?? ''); ?>">
                                        <?php echo htmlspecialchars($sport['name']); ?>
                                        <?php if ($sport['sport_type_name']): ?>
                                            (<?php echo htmlspecialchars($sport['sport_type_name']); ?>)
                                        <?php else: ?>
                                            (<?php echo ucfirst($sport['type'] ?? 'Unknown'); ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label" for="tournament_format_id">Tournament Format:</label>
                            <select name="tournament_format_id" id="tournament_format_id" class="form-control" required>
                                <option value="">Select tournament format...</option>
                            </select>
                        </div>

                        <div class="form-group mb-3">
                            <label class="form-label" for="seeding_method">Seeding Method:</label>
                            <select name="seeding_method" id="seeding_method" class="form-control" required>
                                <option value="random">Random Seeding</option>
                                <option value="ranking">Ranking-based Seeding</option>
                                <option value="manual">Manual Seeding</option>
                                <option value="hybrid">Hybrid Seeding</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Sport
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Exact replica of loadBracketTypes function from manage-event.php
        function loadBracketTypes() {
            const sportSelect = document.getElementById('sport_id');
            const formatSelect = document.getElementById('tournament_format_id');
            const debugOutput = document.getElementById('debug-output');
            
            const sportId = sportSelect.value;
            
            // Clear debug output
            debugOutput.innerHTML = '';
            
            // Reset format selection
            formatSelect.innerHTML = '<option value="">Loading...</option>';
            
            if (!sportId) {
                formatSelect.innerHTML = '<option value="">Select a sport first...</option>';
                debugOutput.innerHTML = '<p class="info">No sport selected</p>';
                return;
            }
            
            // Get sport type from the selected option
            const selectedSportOption = sportSelect.options[sportSelect.selectedIndex];
            const sportType = selectedSportOption.dataset.type || 'traditional';
            const sportName = selectedSportOption.textContent;
            const sportTypeName = selectedSportOption.dataset.sportTypeName || 'Unknown';
            
            // Display debug information
            debugOutput.innerHTML = `
                <p><strong>Selected Sport:</strong> ${sportName}</p>
                <p><strong>Sport ID:</strong> ${sportId}</p>
                <p><strong>Sport Type Name:</strong> ${sportTypeName}</p>
                <p><strong>Data-Type Attribute:</strong> ${sportType}</p>
                <p><strong>AJAX Request:</strong> Sending sport_type=${sportType}</p>
            `;
            
            // Fetch tournament formats from database
            fetch('ajax/get-tournament-formats.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `sport_type=${encodeURIComponent(sportType)}`
            })
            .then(response => {
                debugOutput.innerHTML += `<p><strong>Response Status:</strong> ${response.status}</p>`;
                return response.json();
            })
            .then(data => {
                formatSelect.innerHTML = '<option value="">Select tournament format...</option>';
                
                if (data.success && data.formats) {
                    debugOutput.innerHTML += `<p class="success">✓ Success: Found ${data.formats.length} tournament formats</p>`;
                    
                    data.formats.forEach(format => {
                        const option = document.createElement('option');
                        option.value = format.id;
                        option.textContent = format.name;
                        option.dataset.description = format.description;
                        option.dataset.minParticipants = format.min_participants;
                        option.dataset.maxParticipants = format.max_participants || 'Unlimited';
                        formatSelect.appendChild(option);
                    });
                    
                    // Show format details
                    debugOutput.innerHTML += '<p><strong>Available Formats:</strong></p><ul>';
                    data.formats.forEach(format => {
                        debugOutput.innerHTML += `<li><strong>${format.name}</strong> - ${format.description}</li>`;
                    });
                    debugOutput.innerHTML += '</ul>';
                    
                    if (data.debug) {
                        debugOutput.innerHTML += `<p><strong>Debug Info:</strong> ${JSON.stringify(data.debug)}</p>`;
                    }
                } else {
                    debugOutput.innerHTML += `<p class="error">❌ Error: ${data.message || 'No formats found'}</p>`;
                    formatSelect.innerHTML = '<option value="">No formats available</option>';
                }
            })
            .catch(error => {
                debugOutput.innerHTML += `<p class="error">❌ Fetch Error: ${error.message}</p>`;
                console.error('Error loading tournament formats:', error);
                formatSelect.innerHTML = '<option value="">Error loading formats</option>';
            });
        }
        
        // Form submission handler
        document.getElementById('addSportForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const debugOutput = document.getElementById('debug-output');
            
            debugOutput.innerHTML += '<hr><p><strong>Form Submission Test:</strong></p>';
            debugOutput.innerHTML += `<p>Sport ID: ${formData.get('sport_id')}</p>`;
            debugOutput.innerHTML += `<p>Tournament Format ID: ${formData.get('tournament_format_id')}</p>`;
            debugOutput.innerHTML += `<p>Seeding Method: ${formData.get('seeding_method')}</p>`;
            debugOutput.innerHTML += '<p class="success">✓ Form validation passed - ready for actual submission</p>';
        });
    </script>
</body>
</html>
