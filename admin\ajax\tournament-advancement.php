<?php
/**
 * Tournament Advancement System
 * Handles automatic tournament progression and round advancement
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/tournament_manager.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

// Start output buffering to catch any unexpected output
ob_start();

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'check_advancement':
            handleCheckAdvancement($conn, $input);
            break;
        case 'advance_round':
            handleAdvanceRound($conn, $input);
            break;
        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    // Clear any output buffer to prevent HTML errors
    ob_clean();

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Clean up output buffer
ob_end_flush();

/**
 * Check if tournament advancement is available
 */
function handleCheckAdvancement($conn, $input) {
    $eventId = $input['event_id'] ?? '';
    $sportId = $input['sport_id'] ?? '';
    $categoryId = $input['category_id'] ?? '';

    if (empty($eventId) || empty($sportId) || empty($categoryId)) {
        throw new Exception('Missing required parameters');
    }

    // Get tournament structure
    $stmt = $conn->prepare("
        SELECT 
            ts.*,
            tf.name as format_name,
            tf.code as format_code
        FROM tournament_structures ts
        JOIN event_sports es ON ts.event_sport_id = es.id
        LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ? AND ts.status = 'in_progress'
        ORDER BY ts.created_at DESC
        LIMIT 1
    ");
    $stmt->execute([$eventId, $sportId]);
    $tournament = $stmt->fetch();

    if (!$tournament) {
        echo json_encode([
            'success' => true,
            'advancement_available' => false,
            'message' => 'No active tournament found'
        ]);
        return;
    }

    // Check current round completion
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_matches,
            SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_matches,
            SUM(CASE WHEN status = 'ongoing' THEN 1 ELSE 0 END) as ongoing_matches
        FROM matches 
        WHERE tournament_structure_id = ? AND round_number = ?
    ");
    $stmt->execute([$tournament['id'], $tournament['current_round']]);
    $roundStats = $stmt->fetch();

    $roundCompleted = ($roundStats['total_matches'] > 0 && 
                     $roundStats['completed_matches'] == $roundStats['total_matches']);

    $canAdvance = ($roundCompleted && 
                  $tournament['current_round'] < $tournament['total_rounds']);

    $tournamentCompleted = ($roundCompleted && 
                           $tournament['current_round'] >= $tournament['total_rounds']);

    echo json_encode([
        'success' => true,
        'advancement_available' => $canAdvance,
        'tournament_completed' => $tournamentCompleted,
        'current_round' => $tournament['current_round'],
        'total_rounds' => $tournament['total_rounds'],
        'round_stats' => [
            'total_matches' => $roundStats['total_matches'],
            'completed_matches' => $roundStats['completed_matches'],
            'ongoing_matches' => $roundStats['ongoing_matches'],
            'pending_matches' => $roundStats['total_matches'] - $roundStats['completed_matches'] - $roundStats['ongoing_matches']
        ],
        'tournament_info' => [
            'id' => $tournament['id'],
            'name' => $tournament['name'],
            'format' => $tournament['format_name'],
            'status' => $tournament['status']
        ]
    ]);
}

/**
 * Manually advance tournament to next round
 */
function handleAdvanceRound($conn, $input) {
    $eventId = $input['event_id'] ?? '';
    $sportId = $input['sport_id'] ?? '';
    $categoryId = $input['category_id'] ?? '';

    if (empty($eventId) || empty($sportId) || empty($categoryId)) {
        throw new Exception('Missing required parameters');
    }

    $conn->beginTransaction();

    try {
        // Get tournament structure
        $stmt = $conn->prepare("
            SELECT ts.*
            FROM tournament_structures ts
            JOIN event_sports es ON ts.event_sport_id = es.id
            WHERE es.event_id = ? AND es.sport_id = ? AND ts.status = 'in_progress'
            ORDER BY ts.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$eventId, $sportId]);
        $tournament = $stmt->fetch();

        if (!$tournament) {
            throw new Exception('No active tournament found');
        }

        // Verify current round is completed
        $stmt = $conn->prepare("
            SELECT 
                COUNT(*) as total_matches,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_matches
            FROM matches 
            WHERE tournament_structure_id = ? AND round_number = ?
        ");
        $stmt->execute([$tournament['id'], $tournament['current_round']]);
        $roundStats = $stmt->fetch();

        if ($roundStats['completed_matches'] < $roundStats['total_matches']) {
            throw new Exception('Current round is not completed yet');
        }

        if ($tournament['current_round'] >= $tournament['total_rounds']) {
            throw new Exception('Tournament is already completed');
        }

        // Use tournament manager to advance
        $tournamentManager = new TournamentManager($conn);
        $result = $tournamentManager->advanceToNextRound($tournament['id']);

        if (!$result['success']) {
            throw new Exception($result['message'] ?? 'Failed to advance tournament');
        }

        // Log advancement
        logAdminActivity('MANUAL_ADVANCE_TOURNAMENT', 'tournament_structures', $tournament['id'], null, [
            'from_round' => $tournament['current_round'],
            'to_round' => $tournament['current_round'] + 1
        ]);

        $conn->commit();

        echo json_encode([
            'success' => true,
            'message' => 'Tournament advanced to next round successfully',
            'new_round' => $tournament['current_round'] + 1,
            'tournament_id' => $tournament['id']
        ]);

    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}
