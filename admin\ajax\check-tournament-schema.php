<?php
/**
 * Check Tournament Schema Status
 * SC_IMS Sports Competition and Event Management System
 */

require_once '../../config/database.php';
require_once '../auth.php';

// Require admin authentication
requireAdmin();

header('Content-Type: application/json');

$database = new Database();
$conn = $database->getConnection();

try {
    // Check if tournament_participants table exists
    $sql = "SHOW TABLES LIKE 'tournament_participants'";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $tableExists = $stmt->rowCount() > 0;
    
    $columns = [];
    $foreignKeys = [];
    $hasRegistrationId = false;
    $hasDepartmentId = false;
    
    if ($tableExists) {
        // Get column information
        $sql = "DESCRIBE tournament_participants";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $columnInfo = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($columnInfo as $column) {
            $columns[] = $column['Field'];
            if ($column['Field'] === 'registration_id') {
                $hasRegistrationId = true;
            }
            if ($column['Field'] === 'department_id') {
                $hasDepartmentId = true;
            }
        }
        
        // Get foreign key information
        $sql = "SELECT 
                    CONSTRAINT_NAME,
                    COLUMN_NAME,
                    REFERENCED_TABLE_NAME,
                    REFERENCED_COLUMN_NAME
                FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = 'tournament_participants' 
                AND REFERENCED_TABLE_NAME IS NOT NULL";
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $fkInfo = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($fkInfo as $fk) {
            $foreignKeys[] = $fk['COLUMN_NAME'] . ' -> ' . $fk['REFERENCED_TABLE_NAME'] . '(' . $fk['REFERENCED_COLUMN_NAME'] . ')';
        }
    }
    
    echo json_encode([
        'success' => true,
        'table_exists' => $tableExists,
        'columns' => $columns,
        'foreign_keys' => $foreignKeys,
        'has_registration_id' => $hasRegistrationId,
        'has_department_id' => $hasDepartmentId,
        'needs_simplification' => $hasRegistrationId && !$hasDepartmentId
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
