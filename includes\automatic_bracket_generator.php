<?php
/**
 * Automatic Bracket Generation System
 * SC_IMS Sports Competition and Event Management System
 * 
 * Handles automatic tournament generation with intelligent format selection,
 * participant validation, and consistent bracket creation across all categories.
 */

require_once 'unified_bracket_engine.php';
require_once 'tournament_format_selector.php';

class AutomaticBracketGenerator {
    private $conn;
    private $bracketEngine;
    private $formatSelector;
    
    public function __construct($conn) {
        $this->conn = $conn;
        $this->bracketEngine = new UnifiedBracketEngine($conn);
        $this->formatSelector = new TournamentFormatSelector($conn);
    }
    
    /**
     * Generate tournaments for all eligible sport categories in an event
     */
    public function generateTournamentsForEvent($eventId, $config = []) {
        $results = [];
        
        try {
            // Get all sport categories for the event
            $eventSports = $this->getEventSports($eventId);
            
            foreach ($eventSports as $eventSport) {
                $result = $this->generateTournamentForEventSport($eventSport['id'], $config);
                $results[] = [
                    'event_sport_id' => $eventSport['id'],
                    'sport_name' => $eventSport['sport_name'],
                    'result' => $result
                ];
            }
            
            return [
                'success' => true,
                'event_id' => $eventId,
                'tournaments_generated' => count(array_filter($results, function($r) { return $r['result']['success']; })),
                'total_sports' => count($results),
                'details' => $results
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'event_id' => $eventId
            ];
        }
    }
    
    /**
     * Generate tournament for a specific event sport with intelligent configuration
     */
    public function generateTournamentForEventSport($eventSportId, $config = []) {
        try {
            // Check if tournament generation is needed
            $eligibility = $this->checkTournamentEligibility($eventSportId);
            
            if (!$eligibility['eligible']) {
                return [
                    'success' => false,
                    'message' => $eligibility['reason'],
                    'event_sport_id' => $eventSportId
                ];
            }
            
            // Get participants
            $participants = $this->bracketEngine->getEventParticipants($eventSportId);
            
            // Intelligent format selection
            $format = $this->selectOptimalFormat($eventSportId, count($participants), $config);
            
            // Generate intelligent configuration
            $tournamentConfig = $this->generateIntelligentConfig($format, $participants, $config);
            
            // Generate tournament
            $result = $this->bracketEngine->generateBracketForCategory($eventSportId, $tournamentConfig);
            
            // Log generation details
            $this->logTournamentGeneration($eventSportId, $result, $tournamentConfig);
            
            return $result;
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'event_sport_id' => $eventSportId
            ];
        }
    }
    
    /**
     * Check if tournament generation is eligible for an event sport
     */
    private function checkTournamentEligibility($eventSportId) {
        // Check if tournament already exists
        $sql = "SELECT id, status FROM tournament_structures 
                WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$eventSportId]);
        $existingTournament = $stmt->fetch();
        
        if ($existingTournament) {
            return [
                'eligible' => false,
                'reason' => 'Tournament already exists (Status: ' . $existingTournament['status'] . ')'
            ];
        }
        
        // Check participant count
        $participants = $this->bracketEngine->getEventParticipants($eventSportId);
        if (count($participants) < 2) {
            return [
                'eligible' => false,
                'reason' => 'Insufficient participants. Need at least 2 participants, found ' . count($participants)
            ];
        }
        
        // Check if event sport is active
        $sql = "SELECT status FROM event_sports WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$eventSportId]);
        $eventSport = $stmt->fetch();
        
        if (!$eventSport || $eventSport['status'] === 'completed') {
            return [
                'eligible' => false,
                'reason' => 'Event sport is not active or has been completed'
            ];
        }
        
        return [
            'eligible' => true,
            'reason' => 'All eligibility criteria met'
        ];
    }
    
    /**
     * Select optimal tournament format based on sport type and participant count
     */
    private function selectOptimalFormat($eventSportId, $participantCount, $config) {
        // Get event sport information
        $sql = "SELECT es.*, s.type as sport_type, s.name as sport_name
                FROM event_sports es
                JOIN sports s ON es.sport_id = s.id
                WHERE es.id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$eventSportId]);
        $eventSport = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$eventSport) {
            throw new Exception('Event sport not found');
        }
        
        // Use format selector for intelligent selection
        $format = $this->formatSelector->selectFormat(
            $eventSport['event_id'],
            $eventSport['sport_id'],
            $participantCount
        );
        
        if (!$format) {
            throw new Exception('No suitable tournament format found');
        }
        
        return $format;
    }
    
    /**
     * Generate intelligent tournament configuration based on format and participants
     */
    private function generateIntelligentConfig($format, $participants, $userConfig) {
        $config = array_merge([
            'auto_generated' => true,
            'comprehensive_validation' => true,
            'fallback_enabled' => true
        ], $userConfig);
        
        // Intelligent seeding method selection
        if (!isset($config['seeding_method'])) {
            $config['seeding_method'] = $this->selectOptimalSeedingMethod($participants, $format);
        }
        
        // Format-specific intelligent configuration
        switch ($format['code']) {
            case 'round_robin':
                $config = array_merge($config, [
                    'points_win' => 3,
                    'points_draw' => 1,
                    'points_loss' => 0,
                    'head_to_head_tiebreaker' => true
                ]);
                break;
                
            case 'multi_stage':
                $participantCount = count($participants);
                $optimalGroups = $this->calculateOptimalGroups($participantCount);
                $config = array_merge($config, [
                    'groups_count' => $optimalGroups['groups'],
                    'teams_advance_per_group' => $optimalGroups['advance_per_group'],
                    'group_points_win' => 3,
                    'group_points_draw' => 1,
                    'group_points_loss' => 0
                ]);
                break;
                
            case 'swiss_system':
                $config = array_merge($config, [
                    'avoid_rematches' => true,
                    'points_win' => 1,
                    'points_draw' => 0.5,
                    'points_loss' => 0
                ]);
                break;
                
            case 'single_elimination':
            case 'double_elimination':
                $config = array_merge($config, [
                    'bracket_seeding' => true,
                    'third_place_match' => count($participants) >= 4
                ]);
                break;
        }
        
        return $config;
    }
    
    /**
     * Select optimal seeding method based on participant data
     */
    private function selectOptimalSeedingMethod($participants, $format) {
        // Check if participants have ranking data
        $hasRankingData = false;
        foreach ($participants as $participant) {
            if (isset($participant['points']) || isset($participant['wins']) || isset($participant['ranking'])) {
                $hasRankingData = true;
                break;
            }
        }
        
        // For academic competitions, prefer ranking if available
        if ($format['sport_type_category'] === 'academic' && $hasRankingData) {
            return 'ranking';
        }
        
        // For large tournaments with ranking data, use hybrid
        if (count($participants) > 8 && $hasRankingData) {
            return 'hybrid';
        }
        
        // For small tournaments with ranking data, use ranking
        if (count($participants) <= 8 && $hasRankingData) {
            return 'ranking';
        }
        
        // Default to random for fair play
        return 'random';
    }
    
    /**
     * Calculate optimal group configuration for multi-stage tournaments
     */
    private function calculateOptimalGroups($participantCount) {
        // Optimal group sizes: 3-5 teams per group
        $idealGroupSize = 4;
        $groups = max(2, round($participantCount / $idealGroupSize));
        
        // Ensure even distribution
        while ($participantCount % $groups > $groups / 2 && $groups > 2) {
            $groups--;
        }
        
        // Calculate teams advancing (typically 50% or 2 per group)
        $advancePerGroup = max(1, min(2, floor($idealGroupSize / 2)));
        
        return [
            'groups' => $groups,
            'advance_per_group' => $advancePerGroup,
            'teams_per_group' => ceil($participantCount / $groups)
        ];
    }
    
    /**
     * Get all event sports for an event
     */
    private function getEventSports($eventId) {
        $sql = "SELECT es.*, s.name as sport_name, s.type as sport_type
                FROM event_sports es
                JOIN sports s ON es.sport_id = s.id
                WHERE es.event_id = ? AND es.status != 'completed'
                ORDER BY s.name";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$eventId]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * Log tournament generation details for audit and debugging
     */
    private function logTournamentGeneration($eventSportId, $result, $config) {
        $logData = [
            'event_sport_id' => $eventSportId,
            'success' => $result['success'],
            'tournament_id' => $result['tournament_id'] ?? null,
            'format' => $result['format'] ?? null,
            'participant_count' => $result['participant_count'] ?? 0,
            'config' => $config,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        error_log("Tournament Generation: " . json_encode($logData));
        
        // Store in database if audit table exists
        try {
            $sql = "INSERT INTO tournament_generation_log 
                    (event_sport_id, success, tournament_id, format_name, participant_count, configuration, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, NOW())";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([
                $eventSportId,
                $result['success'] ? 1 : 0,
                $result['tournament_id'] ?? null,
                $result['format'] ?? null,
                $result['participant_count'] ?? 0,
                json_encode($config)
            ]);
        } catch (Exception $e) {
            // Audit table doesn't exist, continue without logging to database
        }
    }
    
    /**
     * Get tournament generation statistics for an event
     */
    public function getTournamentGenerationStats($eventId) {
        $sql = "SELECT 
                    COUNT(*) as total_sports,
                    COUNT(ts.id) as tournaments_generated,
                    COUNT(CASE WHEN ts.status = 'in_progress' THEN 1 END) as active_tournaments,
                    COUNT(CASE WHEN ts.status = 'completed' THEN 1 END) as completed_tournaments
                FROM event_sports es
                LEFT JOIN tournament_structures ts ON es.id = ts.event_sport_id
                WHERE es.event_id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$eventId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
