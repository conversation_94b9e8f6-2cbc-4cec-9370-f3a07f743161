<?php
/**
 * Complete Sport Type Detection Test
 * Tests the entire flow from sport selection to tournament format loading
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get available sports for testing (using event ID 1)
$available_sports = getAvailableSports($conn, 1);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sport Type Detection Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .debug { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 3px; }
        select, button { padding: 10px; margin: 10px 0; }
        .format-list { background: #fff; border: 1px solid #ccc; padding: 10px; margin: 10px 0; }
    </style>
</head>
<body>
    <h1>🔍 Complete Sport Type Detection Test</h1>
    
    <div class="test-section">
        <h2>1. Available Sports Data</h2>
        <p>Testing the sports data returned by getAvailableSports():</p>
        
        <?php if (empty($available_sports)): ?>
            <p class="error">❌ No available sports found!</p>
        <?php else: ?>
            <table border="1" style="border-collapse: collapse; width: 100%;">
                <tr>
                    <th>ID</th>
                    <th>Name</th>
                    <th>Type (Legacy)</th>
                    <th>Sport Type Category</th>
                    <th>Sport Type Name</th>
                    <th>Data-Type Value</th>
                </tr>
                <?php foreach ($available_sports as $sport): ?>
                    <?php $data_type = $sport['sport_type_category'] ?? $sport['type']; ?>
                    <tr>
                        <td><?php echo $sport['id']; ?></td>
                        <td><?php echo htmlspecialchars($sport['name']); ?></td>
                        <td><?php echo $sport['type'] ?? 'NULL'; ?></td>
                        <td><?php echo $sport['sport_type_category'] ?? 'NULL'; ?></td>
                        <td><?php echo $sport['sport_type_name'] ?? 'NULL'; ?></td>
                        <td><strong><?php echo $data_type; ?></strong></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        <?php endif; ?>
    </div>
    
    <div class="test-section">
        <h2>2. Interactive Sport Selection Test</h2>
        <p>Select a sport to test the tournament format loading:</p>
        
        <select id="sport_id" onchange="loadTournamentFormats()">
            <option value="">Select a sport...</option>
            <?php foreach ($available_sports as $sport): ?>
                <option value="<?php echo $sport['id']; ?>"
                        data-type="<?php echo $sport['sport_type_category'] ?? $sport['type']; ?>"
                        data-sport-type-name="<?php echo htmlspecialchars($sport['sport_type_name'] ?? ''); ?>">
                    <?php echo htmlspecialchars($sport['name']); ?>
                    <?php if ($sport['sport_type_name']): ?>
                        (<?php echo htmlspecialchars($sport['sport_type_name']); ?>)
                    <?php else: ?>
                        (<?php echo ucfirst($sport['type'] ?? 'Unknown'); ?>)
                    <?php endif; ?>
                </option>
            <?php endforeach; ?>
        </select>
        
        <div id="sport-info" style="display: none; background: #e6f3ff; padding: 10px; margin: 10px 0; border-radius: 5px;">
            <p><strong>Selected Sport:</strong> <span id="sport-name"></span></p>
            <p><strong>Sport Type:</strong> <span id="sport-type"></span></p>
            <p><strong>Data-Type Attribute:</strong> <span id="data-type"></span></p>
        </div>
        
        <div id="ajax-debug" class="debug" style="display: none;">
            <h4>AJAX Debug Information:</h4>
            <div id="ajax-content"></div>
        </div>
        
        <div id="tournament-formats" style="display: none;">
            <h4>Available Tournament Formats:</h4>
            <select id="tournament_format_id">
                <option value="">Loading...</option>
            </select>
            <div id="format-details" class="format-list"></div>
        </div>
    </div>
    
    <div class="test-section">
        <h2>3. Direct AJAX Tests</h2>
        <p>Testing AJAX endpoint directly with different sport types:</p>
        <button onclick="testAllSportTypes()">Test All Sport Types</button>
        <div id="direct-test-results"></div>
    </div>

    <script>
        function loadTournamentFormats() {
            const sportSelect = document.getElementById('sport_id');
            const formatSelect = document.getElementById('tournament_format_id');
            const sportInfo = document.getElementById('sport-info');
            const ajaxDebug = document.getElementById('ajax-debug');
            const ajaxContent = document.getElementById('ajax-content');
            const tournamentFormats = document.getElementById('tournament-formats');
            const formatDetails = document.getElementById('format-details');
            
            const sportId = sportSelect.value;
            
            if (!sportId) {
                sportInfo.style.display = 'none';
                ajaxDebug.style.display = 'none';
                tournamentFormats.style.display = 'none';
                return;
            }
            
            // Get sport information
            const selectedOption = sportSelect.options[sportSelect.selectedIndex];
            const sportType = selectedOption.dataset.type || 'traditional';
            const sportName = selectedOption.textContent;
            const sportTypeName = selectedOption.dataset.sportTypeName || 'Unknown';
            
            // Display sport info
            document.getElementById('sport-name').textContent = sportName;
            document.getElementById('sport-type').textContent = sportTypeName;
            document.getElementById('data-type').textContent = sportType;
            sportInfo.style.display = 'block';
            
            // Show AJAX debug
            ajaxDebug.style.display = 'block';
            ajaxContent.innerHTML = '<p class="info">Making AJAX request...</p>';
            ajaxContent.innerHTML += `<p><strong>Sport ID:</strong> ${sportId}</p>`;
            ajaxContent.innerHTML += `<strong>Sport Type being sent:</strong> ${sportType}</p>`;
            
            // Reset format selection
            formatSelect.innerHTML = '<option value="">Loading...</option>';
            tournamentFormats.style.display = 'block';
            
            // Make AJAX request
            fetch('ajax/get-tournament-formats.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `sport_type=${encodeURIComponent(sportType)}`
            })
            .then(response => {
                ajaxContent.innerHTML += `<p><strong>Response Status:</strong> ${response.status}</p>`;
                return response.text();
            })
            .then(text => {
                ajaxContent.innerHTML += `<p><strong>Raw Response:</strong></p><pre style="background: #fff; padding: 5px; border: 1px solid #ccc;">${text}</pre>`;
                
                try {
                    const data = JSON.parse(text);
                    
                    if (data.success) {
                        ajaxContent.innerHTML += `<p class="success">✓ AJAX Success: Found ${data.formats.length} formats</p>`;
                        
                        // Populate format dropdown
                        formatSelect.innerHTML = '<option value="">Select tournament format...</option>';
                        let formatDetailsHtml = '<h5>Format Details:</h5><ul>';
                        
                        data.formats.forEach(format => {
                            formatSelect.innerHTML += `<option value="${format.id}">${format.name}</option>`;
                            formatDetailsHtml += `<li><strong>${format.name}</strong> - ${format.description}</li>`;
                        });
                        
                        formatDetailsHtml += '</ul>';
                        formatDetails.innerHTML = formatDetailsHtml;
                        
                        if (data.debug) {
                            ajaxContent.innerHTML += `<p><strong>Debug Info:</strong> ${JSON.stringify(data.debug)}</p>`;
                        }
                    } else {
                        ajaxContent.innerHTML += `<p class="error">❌ AJAX Error: ${data.message}</p>`;
                        formatSelect.innerHTML = '<option value="">Error loading formats</option>';
                        formatDetails.innerHTML = '<p class="error">Failed to load tournament formats</p>';
                    }
                } catch (e) {
                    ajaxContent.innerHTML += `<p class="error">❌ JSON Parse Error: ${e.message}</p>`;
                    formatSelect.innerHTML = '<option value="">Error parsing response</option>';
                }
            })
            .catch(error => {
                ajaxContent.innerHTML += `<p class="error">❌ Fetch Error: ${error.message}</p>`;
                formatSelect.innerHTML = '<option value="">Network error</option>';
            });
        }
        
        function testAllSportTypes() {
            const resultsDiv = document.getElementById('direct-test-results');
            resultsDiv.innerHTML = '<p>Testing all sport types...</p>';
            
            const sportTypes = ['traditional', 'academic', 'judged', 'performance', 'team', 'individual'];
            
            sportTypes.forEach(sportType => {
                resultsDiv.innerHTML += `<h4>Testing: ${sportType}</h4>`;
                
                fetch('ajax/get-tournament-formats.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `sport_type=${encodeURIComponent(sportType)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultsDiv.innerHTML += `<p class="success">✓ ${sportType}: Found ${data.formats.length} formats</p>`;
                        if (data.formats.length > 0) {
                            resultsDiv.innerHTML += '<ul>';
                            data.formats.forEach(format => {
                                resultsDiv.innerHTML += `<li>${format.name}</li>`;
                            });
                            resultsDiv.innerHTML += '</ul>';
                        }
                    } else {
                        resultsDiv.innerHTML += `<p class="error">❌ ${sportType}: ${data.message}</p>`;
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML += `<p class="error">❌ ${sportType}: ${error.message}</p>`;
                });
            });
        }
    </script>
</body>
</html>
