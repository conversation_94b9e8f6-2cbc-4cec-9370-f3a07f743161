<?php
require_once '../config/database.php';
require_once '../includes/advanced_tournament_engine.php';

$database = new Database();
$conn = $database->getConnection();

$tournament_id = 65;
$event_sport_id = 52;

echo "<h2>🔧 Fix Tournament 65 Matches</h2>";

echo "<h3>1. Current State</h3>";
$stmt = $conn->prepare("SELECT COUNT(*) as match_count FROM matches WHERE tournament_structure_id = ?");
$stmt->execute([$tournament_id]);
$current_matches = $stmt->fetch();
echo "<p>Current matches for tournament 65: " . $current_matches['match_count'] . "</p>";

echo "<h3>2. Generate Matches for Tournament 65</h3>";

try {
    // Get tournament details
    $stmt = $conn->prepare("
        SELECT ts.*, tf.name as format_name, tf.algorithm_class
        FROM tournament_structures ts
        LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
        WHERE ts.id = ?
    ");
    $stmt->execute([$tournament_id]);
    $tournament = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tournament) {
        throw new Exception("Tournament not found");
    }
    
    echo "<p>Tournament format: " . $tournament['format_name'] . "</p>";
    echo "<p>Algorithm: " . $tournament['algorithm_class'] . "</p>";
    
    // Get participants
    $stmt = $conn->prepare("
        SELECT DISTINCT
            d.id,
            d.name,
            d.abbreviation
        FROM event_department_registrations edr
        JOIN event_sports es ON edr.event_id = es.event_id
        JOIN departments d ON edr.department_id = d.id
        WHERE es.id = ? AND edr.status IN ('pending', 'approved')
        ORDER BY d.name
    ");
    $stmt->execute([$event_sport_id]);
    $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Participants found: " . count($participants) . "</p>";
    foreach ($participants as $p) {
        echo "<p>- " . $p['name'] . " (" . $p['abbreviation'] . ")</p>";
    }
    
    if (count($participants) < 2) {
        throw new Exception("Not enough participants");
    }
    
    // Generate Round Robin matches manually
    echo "<h3>3. Creating Round Robin Matches</h3>";
    
    $conn->beginTransaction();
    
    // Clear existing matches
    $stmt = $conn->prepare("DELETE FROM matches WHERE tournament_structure_id = ?");
    $stmt->execute([$tournament_id]);
    
    $match_number = 1;
    $round_number = 1;
    
    // Generate all vs all matches
    for ($i = 0; $i < count($participants); $i++) {
        for ($j = $i + 1; $j < count($participants); $j++) {
            $team1 = $participants[$i];
            $team2 = $participants[$j];
            
            $stmt = $conn->prepare("
                INSERT INTO matches 
                (tournament_structure_id, round_number, match_number, team1_id, team2_id, 
                 status, bracket_position, is_bye_match, created_at)
                VALUES (?, ?, ?, ?, ?, 'pending', ?, 0, NOW())
            ");
            
            $bracket_position = "R{$round_number}M{$match_number}";
            
            $stmt->execute([
                $tournament_id,
                $round_number,
                $match_number,
                $team1['id'],
                $team2['id'],
                $bracket_position
            ]);
            
            echo "<p>✅ Created match $match_number: " . $team1['name'] . " vs " . $team2['name'] . "</p>";
            $match_number++;
        }
    }
    
    $conn->commit();
    
    echo "<h3>4. Verification</h3>";
    $stmt = $conn->prepare("SELECT COUNT(*) as match_count FROM matches WHERE tournament_structure_id = ?");
    $stmt->execute([$tournament_id]);
    $new_matches = $stmt->fetch();
    echo "<p>✅ Total matches created: " . $new_matches['match_count'] . "</p>";
    
    echo "<p>🎉 <strong>Tournament 65 matches successfully generated!</strong></p>";
    
} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollback();
    }
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='manage-category.php?event_id=4&sport_id=37&category_id=16'>← Back to Category Management</a></p>";
?>
