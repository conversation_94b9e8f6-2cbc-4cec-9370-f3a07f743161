<?php
/**
 * Test Sport Type Detection
 * Check what sport type is being detected for specific sports
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

// Get available sports for testing
$available_sports = getAvailableSports($conn, 1);

echo "<h2>🔍 Sport Type Detection Test</h2>";

echo "<h3>Available Sports and Their Data Types</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Sport Name</th><th>data-type Value</th><th>Sport Type Category</th><th>Type</th><th>Test AJAX</th></tr>";

foreach ($available_sports as $sport) {
    $data_type = $sport['sport_type_category'] ?? $sport['type'];
    $sport_name = htmlspecialchars($sport['name']);
    
    echo "<tr>";
    echo "<td>{$sport_name}</td>";
    echo "<td style='font-weight: bold; color: blue;'>{$data_type}</td>";
    echo "<td>" . ($sport['sport_type_category'] ?? 'N/A') . "</td>";
    echo "<td>" . ($sport['type'] ?? 'N/A') . "</td>";
    echo "<td><button onclick=\"testSportType('{$data_type}', '{$sport_name}')\">Test</button></td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>AJAX Test Results</h3>";
echo "<div id='test-results'></div>";

?>

<script>
function testSportType(sportType, sportName) {
    const resultsDiv = document.getElementById('test-results');
    
    resultsDiv.innerHTML += `<h4>Testing: ${sportName} (sport_type: ${sportType})</h4>`;
    
    fetch('ajax/get-tournament-formats.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: `sport_type=${encodeURIComponent(sportType)}`
    })
    .then(response => response.json())
    .then(data => {
        let html = `<div style="border: 1px solid #ddd; padding: 10px; margin: 10px 0;">`;
        html += `<strong>Sport:</strong> ${sportName}<br>`;
        html += `<strong>Sport Type Sent:</strong> ${sportType}<br>`;
        html += `<strong>Success:</strong> ${data.success}<br>`;
        
        if (data.success && data.formats) {
            html += `<strong>Formats Found:</strong> ${data.formats.length}<br>`;
            html += `<strong>Formats:</strong><ul>`;
            data.formats.forEach(format => {
                html += `<li>${format.name} (${format.code})</li>`;
            });
            html += `</ul>`;
        } else {
            html += `<strong style="color: red;">Error:</strong> ${data.message || 'No formats found'}<br>`;
        }
        
        if (data.debug) {
            html += `<strong>Debug Info:</strong> ${JSON.stringify(data.debug)}<br>`;
        }
        
        html += `</div>`;
        resultsDiv.innerHTML += html;
    })
    .catch(error => {
        resultsDiv.innerHTML += `<div style="border: 1px solid red; padding: 10px; margin: 10px 0; color: red;">`;
        resultsDiv.innerHTML += `<strong>Error testing ${sportName}:</strong> ${error.message}`;
        resultsDiv.innerHTML += `</div>`;
    });
}

// Auto-test problematic sports
window.onload = function() {
    // Look for judged sports and test them automatically
    const rows = document.querySelectorAll('table tr');
    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 2) {
            const sportName = cells[0].textContent;
            const dataType = cells[1].textContent;
            
            if (dataType === 'judged' || sportName.includes('Mr. and Ms.') || sportName.includes('Dance')) {
                setTimeout(() => {
                    testSportType(dataType, sportName);
                }, 1000);
            }
        }
    });
};
</script>

<style>
table {
    border-collapse: collapse;
    width: 100%;
}
th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}
th {
    background-color: #f2f2f2;
}
button {
    background: #007bff;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
}
button:hover {
    background: #0056b3;
}
</style>
