<?php
/**
 * Debug Navigation Issue
 * Comprehensive debugging to identify why category navigation fails
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Navigation Issue</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🔍 Debug Navigation Issue</h1>
        <p>Let's identify exactly why category navigation is failing</p>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>1. Database Status Check</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    // Check all relevant tables
                    $events_count = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
                    $sports_count = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
                    $event_sports_count = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
                    $categories_count = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
                    
                    echo "<div class='row'>";
                    echo "<div class='col-md-3'><div class='alert alert-info text-center'><h4>$events_count</h4>Events</div></div>";
                    echo "<div class='col-md-3'><div class='alert alert-info text-center'><h4>$sports_count</h4>Sports</div></div>";
                    echo "<div class='col-md-3'><div class='alert alert-info text-center'><h4>$event_sports_count</h4>Event-Sports</div></div>";
                    echo "<div class='col-md-3'><div class='alert alert-info text-center'><h4>$categories_count</h4>Categories</div></div>";
                    echo "</div>";
                    
                    if ($categories_count == 0) {
                        echo "<div class='alert alert-danger'>";
                        echo "<h5>❌ PROBLEM FOUND: No categories exist!</h5>";
                        echo "<p>This is why navigation fails. We need to create categories first.</p>";
                        echo "<button class='btn btn-primary' onclick='createTestData()'>Create Test Categories</button>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-success'>";
                        echo "<h5>✅ Categories exist. Let's check the relationships...</h5>";
                        echo "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database Error: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>2. Category-Event-Sport Relationships</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    $stmt = $conn->prepare("
                        SELECT 
                            sc.id as category_id,
                            sc.category_name,
                            sc.event_sport_id,
                            es.event_id,
                            es.sport_id,
                            e.name as event_name,
                            s.name as sport_name
                        FROM sport_categories sc
                        LEFT JOIN event_sports es ON sc.event_sport_id = es.id
                        LEFT JOIN events e ON es.event_id = e.id
                        LEFT JOIN sports s ON es.sport_id = s.id
                        ORDER BY sc.id
                        LIMIT 10
                    ");
                    $stmt->execute();
                    $relationships = $stmt->fetchAll();
                    
                    if ($relationships) {
                        echo "<div class='table-responsive'>";
                        echo "<table class='table table-striped'>";
                        echo "<thead><tr><th>Category ID</th><th>Category Name</th><th>Event</th><th>Sport</th><th>Test Link</th><th>Status</th></tr></thead>";
                        echo "<tbody>";
                        
                        foreach ($relationships as $rel) {
                            $status = "✅ Good";
                            $link_class = "btn-success";
                            $test_url = "manage-category.php?event_id={$rel['event_id']}&sport_id={$rel['sport_id']}&category_id={$rel['category_id']}";
                            
                            if (!$rel['event_id'] || !$rel['sport_id']) {
                                $status = "❌ Broken";
                                $link_class = "btn-danger";
                                $test_url = "#";
                            }
                            
                            echo "<tr>";
                            echo "<td>{$rel['category_id']}</td>";
                            echo "<td>" . htmlspecialchars($rel['category_name']) . "</td>";
                            echo "<td>" . htmlspecialchars($rel['event_name'] ?? 'NULL') . "</td>";
                            echo "<td>" . htmlspecialchars($rel['sport_name'] ?? 'NULL') . "</td>";
                            echo "<td>";
                            if ($test_url !== "#") {
                                echo "<a href='$test_url' class='btn btn-sm $link_class' target='_blank'>Test</a>";
                            } else {
                                echo "<span class='text-muted'>No link</span>";
                            }
                            echo "</td>";
                            echo "<td>$status</td>";
                            echo "</tr>";
                        }
                        
                        echo "</tbody></table>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-warning'>No category relationships found.</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>3. Test Actual Navigation</h3>
            </div>
            <div class="card-body">
                <p>Let's test the actual navigation flow from sport-categories.php:</p>
                
                <?php
                try {
                    $stmt = $conn->prepare("
                        SELECT DISTINCT es.event_id, es.sport_id, e.name as event_name, s.name as sport_name
                        FROM event_sports es
                        JOIN events e ON es.event_id = e.id
                        JOIN sports s ON es.sport_id = s.id
                        WHERE EXISTS (
                            SELECT 1 FROM sport_categories sc WHERE sc.event_sport_id = es.id
                        )
                        ORDER BY e.name, s.name
                        LIMIT 5
                    ");
                    $stmt->execute();
                    $test_cases = $stmt->fetchAll();
                    
                    if ($test_cases) {
                        echo "<div class='row'>";
                        foreach ($test_cases as $test) {
                            $categories_url = "sport-categories.php?event_id={$test['event_id']}&sport_id={$test['sport_id']}";
                            
                            echo "<div class='col-md-6 mb-3'>";
                            echo "<div class='card'>";
                            echo "<div class='card-body'>";
                            echo "<h6 class='card-title'>" . htmlspecialchars($test['event_name']) . "</h6>";
                            echo "<p class='card-text'>" . htmlspecialchars($test['sport_name']) . "</p>";
                            echo "<a href='$categories_url' class='btn btn-primary' target='_blank'>";
                            echo "<i class='fas fa-external-link-alt'></i> Go to Categories";
                            echo "</a>";
                            echo "<p class='mt-2'><small class='text-muted'>Click above, then click on a category name to test navigation</small></p>";
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                        }
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-warning'>No test cases available. Need to create categories first.</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>4. Direct Test Links</h3>
            </div>
            <div class="card-body">
                <p>Test manage-category.php directly with known parameters:</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <h6>Standard Tests:</h6>
                        <div class="list-group">
                            <a href="manage-category.php?event_id=1&sport_id=1&category_id=1" class="list-group-item list-group-item-action" target="_blank">
                                Test: event_id=1, sport_id=1, category_id=1
                            </a>
                            <a href="manage-category.php?event_id=1&sport_id=1&category_id=1&debug=1" class="list-group-item list-group-item-action" target="_blank">
                                Debug Mode: event_id=1, sport_id=1, category_id=1
                            </a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Invalid Tests (should redirect):</h6>
                        <div class="list-group">
                            <a href="manage-category.php" class="list-group-item list-group-item-action" target="_blank">
                                No parameters (should redirect)
                            </a>
                            <a href="manage-category.php?event_id=999&sport_id=999&category_id=999&debug=1" class="list-group-item list-group-item-action" target="_blank">
                                Invalid IDs (should show debug info)
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3>5. Quick Fix</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5>🔧 Recommended Actions:</h5>
                    <ol>
                        <li>If no categories exist, create test data using the button above</li>
                        <li>Test the direct links to see if manage-category.php works</li>
                        <li>Check the sport-categories.php page to see if categories are listed</li>
                        <li>Use debug mode to see exact error messages</li>
                    </ol>
                </div>
                
                <button class="btn btn-success" onclick="createTestData()">
                    <i class="fas fa-plus"></i> Create Complete Test Data
                </button>
                
                <button class="btn btn-info" onclick="checkErrorLogs()">
                    <i class="fas fa-file-alt"></i> Check Error Logs
                </button>
                
                <div id="result" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script>
        function createTestData() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="alert alert-info">Creating test data...</div>';
            
            fetch('ajax/create-test-categories.php', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                    setTimeout(() => location.reload(), 2000);
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">Error: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">Network error: ${error.message}</div>`;
            });
        }
        
        function checkErrorLogs() {
            window.open('view-error-logs.php', '_blank');
        }
    </script>
</body>
</html>
