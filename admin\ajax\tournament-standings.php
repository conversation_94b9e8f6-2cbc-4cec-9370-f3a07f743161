<?php
/**
 * Tournament Standings Management
 * Handles real-time standings calculation and display
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'get_standings':
            handleGetStandings($conn, $input);
            break;
        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get tournament standings for a category
 */
function handleGetStandings($conn, $input) {
    $eventId = $input['event_id'] ?? '';
    $sportId = $input['sport_id'] ?? '';
    $categoryId = $input['category_id'] ?? '';

    if (empty($eventId) || empty($sportId) || empty($categoryId)) {
        throw new Exception('Missing required parameters');
    }

    // Get tournament structure
    $stmt = $conn->prepare("
        SELECT 
            ts.*,
            tf.name as format_name,
            tf.code as format_code
        FROM tournament_structures ts
        JOIN event_sports es ON ts.event_sport_id = es.id
        LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ? AND ts.status != 'cancelled'
        ORDER BY ts.created_at DESC
        LIMIT 1
    ");
    $stmt->execute([$eventId, $sportId]);
    $tournament = $stmt->fetch();

    // Get all registered participants using the same logic as UnifiedBracketEngine
    require_once '../../includes/unified_bracket_engine.php';
    $bracketEngine = new UnifiedBracketEngine($conn);
    $rawParticipants = $bracketEngine->getEventParticipants($categoryId);

    // Convert to the format expected by standings
    $participants = [];
    foreach ($rawParticipants as $participant) {
        $participants[] = [
            'id' => $participant['department_id'] ?? $participant['id'],
            'name' => $participant['department_name'] ?? $participant['team_name'],
            'abbreviation' => '', // Will be filled if available
            'color_code' => $participant['color_code'] ?? '#007bff',
            'registration_id' => $participant['id']
        ];
    }

    // If no participants found via bracket engine, try fallback
    if (empty($participants)) {
        $stmt = $conn->prepare("
            SELECT
                d.id,
                d.name,
                d.abbreviation,
                d.color_code,
                edr.id as registration_id
            FROM departments d
            JOIN event_department_registrations edr ON d.id = edr.department_id
            WHERE edr.event_id = ? AND edr.status IN ('approved', 'pending')
            ORDER BY d.name
        ");
        $stmt->execute([$eventId]);
        $participants = $stmt->fetchAll();
    }

    // Calculate standings based on tournament format
    $standings = [];

    foreach ($participants as $participant) {
        $stats = ['matches_played' => 0, 'wins' => 0, 'losses' => 0, 'goals_for' => 0, 'goals_against' => 0];

        // Only get match statistics if tournament exists
        if ($tournament) {
            $stmt = $conn->prepare("
                SELECT
                    COUNT(*) as matches_played,
                    SUM(CASE
                        WHEN winner_id = ? THEN 1
                        ELSE 0
                    END) as wins,
                    SUM(CASE
                        WHEN (team1_id = ? OR team2_id = ?) AND winner_id != ? AND status = 'completed' THEN 1
                        ELSE 0
                    END) as losses,
                    SUM(CASE
                        WHEN team1_id = ? THEN COALESCE(team_a_score, 0)
                        WHEN team2_id = ? THEN COALESCE(team_b_score, 0)
                        ELSE 0
                    END) as goals_for,
                    SUM(CASE
                        WHEN team1_id = ? THEN COALESCE(team_b_score, 0)
                        WHEN team2_id = ? THEN COALESCE(team_a_score, 0)
                        ELSE 0
                    END) as goals_against
                FROM matches
                WHERE tournament_structure_id = ?
                AND (team1_id = ? OR team2_id = ?)
                AND status = 'completed'
            ");
            $stmt->execute([
                $participant['id'], // winner_id check
                $participant['id'], $participant['id'], $participant['id'], // losses check
                $participant['id'], $participant['id'], // goals_for
                $participant['id'], $participant['id'], // goals_against
                $tournament['id'], // tournament_structure_id
                $participant['id'], $participant['id'] // team participation
            ]);
            $stats = $stmt->fetch() ?: $stats;
        }

        // Calculate points based on tournament format
        $formatCode = $tournament ? $tournament['format_code'] : 'default';
        $points = calculatePoints($formatCode, $stats);

        // Calculate goal difference
        $goalDifference = ($stats['goals_for'] ?? 0) - ($stats['goals_against'] ?? 0);

        $standings[] = [
            'id' => $participant['id'],
            'name' => $participant['name'],
            'abbreviation' => $participant['abbreviation'] ?? '',
            'color_code' => $participant['color_code'],
            'matches_played' => $stats['matches_played'] ?? 0,
            'wins' => $stats['wins'] ?? 0,
            'losses' => $stats['losses'] ?? 0,
            'draws' => 0, // TODO: Implement draws if needed
            'goals_for' => $stats['goals_for'] ?? 0,
            'goals_against' => $stats['goals_against'] ?? 0,
            'goal_difference' => $goalDifference,
            'points' => $points
        ];
    }

    // Sort standings based on tournament format
    $formatCode = $tournament ? $tournament['format_code'] : 'default';
    $standings = sortStandings($formatCode, $standings);

    echo json_encode([
        'success' => true,
        'standings' => $standings,
        'tournament_format' => $formatCode,
        'tournament_exists' => $tournament ? true : false,
        'total_participants' => count($standings)
    ]);
}

/**
 * Calculate points based on tournament format
 */
function calculatePoints($formatCode, $stats) {
    $wins = $stats['wins'] ?? 0;
    $losses = $stats['losses'] ?? 0;
    $draws = 0; // TODO: Implement if needed

    switch ($formatCode) {
        case 'round_robin':
            // Standard football/soccer scoring: 3 points for win, 1 for draw, 0 for loss
            return ($wins * 3) + ($draws * 1);
            
        case 'swiss_system':
            // Chess-style scoring: 1 point for win, 0.5 for draw, 0 for loss
            return ($wins * 1) + ($draws * 0.5);
            
        case 'single_elimination':
        case 'double_elimination':
            // Elimination tournaments: just count wins
            return $wins;
            
        default:
            // Default: 3-1-0 system
            return ($wins * 3) + ($draws * 1);
    }
}

/**
 * Sort standings based on tournament format
 */
function sortStandings($formatCode, $standings) {
    switch ($formatCode) {
        case 'round_robin':
            // Sort by: Points, Goal Difference, Goals For, Name
            usort($standings, function($a, $b) {
                if ($a['points'] != $b['points']) {
                    return $b['points'] <=> $a['points'];
                }
                if ($a['goal_difference'] != $b['goal_difference']) {
                    return $b['goal_difference'] <=> $a['goal_difference'];
                }
                if ($a['goals_for'] != $b['goals_for']) {
                    return $b['goals_for'] <=> $a['goals_for'];
                }
                return $a['name'] <=> $b['name'];
            });
            break;
            
        case 'swiss_system':
            // Sort by: Points, Wins, Name
            usort($standings, function($a, $b) {
                if ($a['points'] != $b['points']) {
                    return $b['points'] <=> $a['points'];
                }
                if ($a['wins'] != $b['wins']) {
                    return $b['wins'] <=> $a['wins'];
                }
                return $a['name'] <=> $b['name'];
            });
            break;
            
        case 'single_elimination':
        case 'double_elimination':
            // Sort by: Wins (advancement), Name
            usort($standings, function($a, $b) {
                if ($a['wins'] != $b['wins']) {
                    return $b['wins'] <=> $a['wins'];
                }
                return $a['name'] <=> $b['name'];
            });
            break;
            
        default:
            // Default sorting by points
            usort($standings, function($a, $b) {
                if ($a['points'] != $b['points']) {
                    return $b['points'] <=> $a['points'];
                }
                return $a['name'] <=> $b['name'];
            });
    }
    
    return $standings;
}
