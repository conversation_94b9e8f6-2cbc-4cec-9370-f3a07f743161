<?php
/**
 * Verify Categories Created for event_id=4, sport_id=40
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>✅ Verify Categories Created</h1>";

try {
    // Check if event-sport relationship exists
    $stmt = $conn->prepare("
        SELECT es.id as event_sport_id, e.name as event_name, s.name as sport_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE es.event_id = 4 AND es.sport_id = 40
    ");
    $stmt->execute();
    $event_sport = $stmt->fetch();
    
    if (!$event_sport) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ Event-Sport Relationship Missing</h3>";
        echo "<p>No relationship exists between event_id=4 and sport_id=40</p>";
        echo "<p>Need to create this relationship first.</p>";
        echo "</div>";
        
        // Create the relationship
        echo "<h3>Creating Event-Sport Relationship</h3>";
        
        // First check if both event and sport exist
        $event_check = $conn->prepare("SELECT name FROM events WHERE id = 4");
        $event_check->execute();
        $event = $event_check->fetch();
        
        $sport_check = $conn->prepare("SELECT name FROM sports WHERE id = 40");
        $sport_check->execute();
        $sport = $sport_check->fetch();
        
        if ($event && $sport) {
            $stmt = $conn->prepare("INSERT INTO event_sports (event_id, sport_id, max_teams, status) VALUES (4, 40, 8, 'registration')");
            $stmt->execute();
            $event_sport_id = $conn->lastInsertId();
            
            echo "<p style='color: green;'>✅ Created event-sport relationship</p>";
            echo "<p><strong>Event:</strong> {$event['name']}</p>";
            echo "<p><strong>Sport:</strong> {$sport['name']}</p>";
            echo "<p><strong>Event-Sport ID:</strong> $event_sport_id</p>";
            
            $event_sport = ['event_sport_id' => $event_sport_id, 'event_name' => $event['name'], 'sport_name' => $sport['name']];
        } else {
            echo "<p style='color: red;'>❌ Event or Sport doesn't exist</p>";
            if (!$event) echo "<p>Event ID 4 not found</p>";
            if (!$sport) echo "<p>Sport ID 40 not found</p>";
            exit;
        }
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Event-Sport Relationship Found</h3>";
        echo "<p><strong>Event:</strong> {$event_sport['event_name']}</p>";
        echo "<p><strong>Sport:</strong> {$event_sport['sport_name']}</p>";
        echo "<p><strong>Event-Sport ID:</strong> {$event_sport['event_sport_id']}</p>";
        echo "</div>";
    }
    
    // Check categories
    echo "<h2>Categories for this Event-Sport</h2>";
    
    $stmt = $conn->prepare("SELECT * FROM sport_categories WHERE event_sport_id = ? ORDER BY category_name");
    $stmt->execute([$event_sport['event_sport_id']]);
    $categories = $stmt->fetchAll();
    
    if (empty($categories)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<h3>⚠️ No Categories Found</h3>";
        echo "<p>Creating default categories now...</p>";
        echo "</div>";
        
        // Create categories
        $default_categories = [
            ['Men\'s Singles A', 'men', 'John Referee', '<EMAIL>', 'Court 1'],
            ['Women\'s Singles A', 'women', 'Jane Referee', '<EMAIL>', 'Court 2'],
            ['Mixed Doubles A', 'mixed', 'Mike Referee', '<EMAIL>', 'Court 3'],
            ['Men\'s Doubles A', 'men', 'Tom Referee', '<EMAIL>', 'Court 4'],
            ['Women\'s Doubles A', 'women', 'Sarah Referee', '<EMAIL>', 'Court 5']
        ];
        
        $stmt = $conn->prepare("
            INSERT INTO sport_categories (event_sport_id, category_name, category_type, referee_name, referee_email, venue)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        foreach ($default_categories as $cat) {
            $stmt->execute([
                $event_sport['event_sport_id'],
                $cat[0],
                $cat[1],
                $cat[2],
                $cat[3],
                $cat[4]
            ]);
            $category_id = $conn->lastInsertId();
            echo "<p style='color: green;'>✅ Created: {$cat[0]} (ID: $category_id)</p>";
        }
        
        // Re-fetch categories
        $stmt = $conn->prepare("SELECT * FROM sport_categories WHERE event_sport_id = ? ORDER BY category_name");
        $stmt->execute([$event_sport['event_sport_id']]);
        $categories = $stmt->fetchAll();
        
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Categories Found</h3>";
        echo "<p>Found " . count($categories) . " categories</p>";
        echo "</div>";
    }
    
    // Display categories
    echo "<h3>Available Categories:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #007bff; color: white;'>";
    echo "<th>ID</th><th>Category Name</th><th>Type</th><th>Referee</th><th>Email</th><th>Venue</th><th>Test Link</th>";
    echo "</tr>";
    
    foreach ($categories as $cat) {
        $manage_url = "manage-category.php?event_id=4&sport_id=40&category_id={$cat['id']}";
        echo "<tr>";
        echo "<td>{$cat['id']}</td>";
        echo "<td>{$cat['category_name']}</td>";
        echo "<td>{$cat['category_type']}</td>";
        echo "<td>{$cat['referee_name']}</td>";
        echo "<td>{$cat['referee_email']}</td>";
        echo "<td>{$cat['venue']}</td>";
        echo "<td><a href='$manage_url' target='_blank'>Test</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Test the sport-categories page
    echo "<h2>🧪 Test Navigation Now</h2>";
    
    $categories_url = "sport-categories.php?event_id=4&sport_id=40";
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; text-align: center;'>";
    echo "<h3>Ready to Test!</h3>";
    echo "<p>The categories have been created. Now test the navigation:</p>";
    echo "<a href='$categories_url' target='_blank' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 1.1em; display: inline-block; margin: 10px;'>";
    echo "🧪 Open Sport Categories Page";
    echo "</a>";
    echo "<p><small>Click on category names in the table - they should now be clickable and navigate to manage-category.php</small></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
