<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$event_id = $_GET['event_id'] ?? 4;
$sport_id = $_GET['sport_id'] ?? 37;

echo "<h2>Event Participants Debug - Event ID: $event_id, Sport ID: $sport_id</h2>";

try {
    // Get event_sport_id
    $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    
    if (!$event_sport) {
        echo "<p style='color: red;'>No event_sport found for Event ID $event_id and Sport ID $sport_id</p>";
        exit;
    }
    
    $event_sport_id = $event_sport['id'];
    echo "<p>Event Sport ID: $event_sport_id</p>";
    
    // Check event department registrations
    echo "<h3>Event Department Registrations</h3>";
    $stmt = $conn->prepare("
        SELECT edr.*, d.name as department_name
        FROM event_department_registrations edr
        JOIN departments d ON edr.department_id = d.id
        WHERE edr.event_id = ?
    ");
    $stmt->execute([$event_id]);
    $registrations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($registrations)) {
        echo "<p style='color: orange;'>No event department registrations found for Event ID $event_id</p>";
        
        // Let's create some test registrations
        echo "<h4>Creating Test Registrations</h4>";
        
        // Get some departments
        $stmt = $conn->prepare("SELECT id, name FROM departments WHERE status = 'active' LIMIT 5");
        $stmt->execute();
        $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        foreach ($departments as $dept) {
            // Create event department registration
            $stmt = $conn->prepare("
                INSERT INTO event_department_registrations 
                (event_id, department_id, status, registration_date)
                VALUES (?, ?, 'approved', NOW())
            ");
            $stmt->execute([$event_id, $dept['id']]);
            $reg_id = $conn->lastInsertId();
            
            // Create department sport participation
            $stmt = $conn->prepare("
                INSERT INTO department_sport_participations
                (event_department_registration_id, event_sport_id, team_name, status)
                VALUES (?, ?, ?, 'confirmed')
            ");
            $stmt->execute([$reg_id, $event_sport_id, $dept['name']]);
            
            echo "<p style='color: green;'>✓ Created registration for {$dept['name']}</p>";
        }
        
        echo "<p><a href='?event_id=$event_id&sport_id=$sport_id'>Refresh to see results</a></p>";
        
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Department</th><th>Status</th><th>Registration Date</th></tr>";
        foreach ($registrations as $reg) {
            echo "<tr>";
            echo "<td>{$reg['id']}</td>";
            echo "<td>{$reg['department_name']}</td>";
            echo "<td>{$reg['status']}</td>";
            echo "<td>{$reg['registration_date']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // Check sport participations
        echo "<h3>Department Sport Participations for Event Sport ID: $event_sport_id</h3>";
        $stmt = $conn->prepare("
            SELECT dsp.*, d.name as department_name
            FROM department_sport_participations dsp
            JOIN event_department_registrations edr ON dsp.event_department_registration_id = edr.id
            JOIN departments d ON edr.department_id = d.id
            WHERE dsp.event_sport_id = ?
        ");
        $stmt->execute([$event_sport_id]);
        $participations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (empty($participations)) {
            echo "<p style='color: orange;'>No sport participations found for Event Sport ID $event_sport_id</p>";
            
            // Create sport participations for existing registrations
            echo "<h4>Creating Sport Participations</h4>";
            foreach ($registrations as $reg) {
                $stmt = $conn->prepare("
                    INSERT INTO department_sport_participations
                    (event_department_registration_id, event_sport_id, team_name, status)
                    VALUES (?, ?, ?, 'confirmed')
                ");
                $stmt->execute([$reg['id'], $event_sport_id, $reg['department_name']]);
                echo "<p style='color: green;'>✓ Created sport participation for {$reg['department_name']}</p>";
            }
            
        } else {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>ID</th><th>Department</th><th>Team Name</th><th>Status</th></tr>";
            foreach ($participations as $part) {
                echo "<tr>";
                echo "<td>{$part['id']}</td>";
                echo "<td>{$part['department_name']}</td>";
                echo "<td>{$part['team_name']}</td>";
                echo "<td>{$part['status']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // Test the participant query from manage-category.php
    echo "<h3>Participant Query Test</h3>";
    $stmt = $conn->prepare("
        SELECT 
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            edr.status,
            edr.registration_date,
            dsp.team_name,
            dsp.participants as participant_list
        FROM event_department_registrations edr
        JOIN departments d ON edr.department_id = d.id
        JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
        WHERE edr.event_id = ? 
        AND dsp.event_sport_id = ?
        AND edr.status IN ('pending', 'approved')
        AND dsp.status IN ('registered', 'confirmed')
        ORDER BY d.name
    ");
    $stmt->execute([$event_id, $event_sport_id]);
    $test_participants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<p>Found " . count($test_participants) . " participants</p>";
    
    if (!empty($test_participants)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Abbreviation</th><th>Team Name</th><th>Status</th></tr>";
        foreach ($test_participants as $part) {
            echo "<tr>";
            echo "<td>{$part['id']}</td>";
            echo "<td>{$part['name']}</td>";
            echo "<td>{$part['abbreviation']}</td>";
            echo "<td>{$part['team_name']}</td>";
            echo "<td>{$part['status']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
