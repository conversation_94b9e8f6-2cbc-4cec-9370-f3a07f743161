<?php
require_once '../admin/includes/config.php';

// Validate referee token
if (!isset($_GET['token'])) {
    die('Invalid access. Referee token required.');
}

$token = $_GET['token'];

try {
    // Verify token and get match details
    $stmt = $pdo->prepare("
        SELECT 
            rs.*,
            m.*,
            es.sport_id,
            es.event_id,
            s.name as sport_name,
            e.name as event_name,
            t1.name as team1_name,
            d1.name as team1_department,
            t2.name as team2_name,
            d2.name as team2_department,
            es.referee_name
        FROM referee_sessions rs
        JOIN tournament_matches m ON rs.match_id = m.id
        JOIN event_sports es ON m.event_sport_id = es.id
        JOIN sports s ON es.sport_id = s.id
        JOIN events e ON es.event_id = e.id
        LEFT JOIN tournament_participants t1 ON m.team1_id = t1.id
        LEFT JOIN departments d1 ON t1.department_id = d1.id
        LEFT JOIN tournament_participants t2 ON m.team2_id = t2.id
        LEFT JOIN departments d2 ON t2.department_id = d2.id
        WHERE rs.token = ? AND rs.status = 'active' AND rs.expires_at > NOW()
    ");
    
    $stmt->execute([$token]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        die('Invalid or expired referee session.');
    }
    
    // Update last accessed
    $stmt = $pdo->prepare("UPDATE referee_sessions SET last_accessed = NOW() WHERE token = ?");
    $stmt->execute([$token]);
    
} catch (Exception $e) {
    error_log("Error in referee match access: " . $e->getMessage());
    die('Error accessing match. Please contact administrator.');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Referee Interface - <?php echo htmlspecialchars($session['sport_name']); ?></title>
    
    <link rel="stylesheet" href="../admin/assets/css/bracket-styles.css">
    <link rel="stylesheet" href="../admin/assets/css/bracket-modals.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .referee-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .referee-header {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .referee-title {
            color: #343a40;
            margin: 0 0 10px 0;
            font-size: 1.8em;
        }
        
        .referee-subtitle {
            color: #6c757d;
            margin: 0;
            font-size: 1.1em;
        }
        
        .match-info {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .teams-display {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 20px;
            align-items: center;
            margin: 20px 0;
        }
        
        .team-card {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 3px solid transparent;
            transition: all 0.3s ease;
        }
        
        .team-card.winner {
            border-color: #28a745;
            background: linear-gradient(135deg, #d4edda, #c3e6cb);
        }
        
        .team-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #343a40;
            margin-bottom: 5px;
        }
        
        .team-department {
            color: #6c757d;
            font-size: 0.9em;
            margin-bottom: 15px;
        }
        
        .score-input {
            width: 80px;
            height: 60px;
            font-size: 2em;
            font-weight: bold;
            text-align: center;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            background: white;
        }
        
        .score-input:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }
        
        .vs-separator {
            font-size: 2em;
            font-weight: bold;
            color: #6c757d;
            text-align: center;
        }
        
        .referee-actions {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 25px;
        }
        
        .referee-btn {
            padding: 15px 25px;
            border: none;
            border-radius: 10px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 10px;
            font-size: 1em;
        }
        
        .referee-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }
        
        .btn-start {
            background: #28a745;
            color: white;
        }
        
        .btn-pause {
            background: #ffc107;
            color: #212529;
        }
        
        .btn-complete {
            background: #007bff;
            color: white;
        }
        
        .btn-reset {
            background: #6c757d;
            color: white;
        }
        
        .match-status {
            text-align: center;
            margin: 20px 0;
        }
        
        .status-badge {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.9em;
        }
        
        .status-pending {
            background: #6c757d;
            color: white;
        }
        
        .status-in_progress {
            background: #ffc107;
            color: #212529;
        }
        
        .status-completed {
            background: #28a745;
            color: white;
        }
        
        .timer-display {
            background: #343a40;
            color: white;
            border-radius: 10px;
            padding: 15px;
            text-align: center;
            margin: 20px 0;
            font-size: 1.5em;
            font-weight: bold;
        }
        
        @media (max-width: 768px) {
            .teams-display {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .vs-separator {
                order: 2;
                font-size: 1.5em;
            }
            
            .referee-actions {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="referee-container">
        <!-- Header -->
        <div class="referee-header">
            <h1 class="referee-title">
                <i class="fas fa-whistle"></i>
                Referee Interface
            </h1>
            <p class="referee-subtitle">
                <?php echo htmlspecialchars($session['event_name']); ?> - <?php echo htmlspecialchars($session['sport_name']); ?>
            </p>
        </div>
        
        <!-- Match Information -->
        <div class="match-info">
            <div class="match-status">
                <span class="status-badge status-<?php echo $session['status']; ?>">
                    <i class="fas fa-<?php echo $session['status'] === 'completed' ? 'check-circle' : ($session['status'] === 'in_progress' ? 'play-circle' : 'clock'); ?>"></i>
                    <?php echo ucfirst(str_replace('_', ' ', $session['status'])); ?>
                </span>
            </div>
            
            <div class="timer-display" id="matchTimer">
                <i class="fas fa-stopwatch"></i>
                <span id="timerValue">00:00:00</span>
            </div>
            
            <div class="teams-display">
                <!-- Team 1 -->
                <div class="team-card" id="team1Card">
                    <div class="team-name"><?php echo htmlspecialchars($session['team1_name'] ?? 'TBD'); ?></div>
                    <div class="team-department"><?php echo htmlspecialchars($session['team1_department'] ?? ''); ?></div>
                    <input type="number" class="score-input" id="team1Score" value="<?php echo $session['team1_score'] ?? 0; ?>" min="0">
                </div>
                
                <!-- VS Separator -->
                <div class="vs-separator">VS</div>
                
                <!-- Team 2 -->
                <div class="team-card" id="team2Card">
                    <div class="team-name"><?php echo htmlspecialchars($session['team2_name'] ?? 'TBD'); ?></div>
                    <div class="team-department"><?php echo htmlspecialchars($session['team2_department'] ?? ''); ?></div>
                    <input type="number" class="score-input" id="team2Score" value="<?php echo $session['team2_score'] ?? 0; ?>" min="0">
                </div>
            </div>
            
            <!-- Referee Actions -->
            <div class="referee-actions">
                <button class="referee-btn btn-start" onclick="startMatch()" id="startBtn">
                    <i class="fas fa-play"></i>
                    Start Match
                </button>
                
                <button class="referee-btn btn-pause" onclick="pauseMatch()" id="pauseBtn" style="display: none;">
                    <i class="fas fa-pause"></i>
                    Pause Match
                </button>
                
                <button class="referee-btn btn-complete" onclick="completeMatch()" id="completeBtn">
                    <i class="fas fa-flag-checkered"></i>
                    Complete Match
                </button>
                
                <button class="referee-btn btn-reset" onclick="resetMatch()">
                    <i class="fas fa-undo"></i>
                    Reset Scores
                </button>
            </div>
        </div>
    </div>
    
    <script>
        let matchTimer = null;
        let startTime = null;
        let elapsedTime = 0;
        let isRunning = false;
        
        const matchId = '<?php echo $session['match_id']; ?>';
        const token = '<?php echo $token; ?>';
        
        // Initialize
        document.addEventListener('DOMContentLoaded', function() {
            updateWinnerDisplay();
            
            // Auto-save scores on change
            document.getElementById('team1Score').addEventListener('input', saveScores);
            document.getElementById('team2Score').addEventListener('input', saveScores);
        });
        
        function startMatch() {
            if (!isRunning) {
                startTime = Date.now() - elapsedTime;
                matchTimer = setInterval(updateTimer, 1000);
                isRunning = true;
                
                document.getElementById('startBtn').style.display = 'none';
                document.getElementById('pauseBtn').style.display = 'block';
                
                updateMatchStatus('in_progress');
            }
        }
        
        function pauseMatch() {
            if (isRunning) {
                clearInterval(matchTimer);
                isRunning = false;
                
                document.getElementById('startBtn').style.display = 'block';
                document.getElementById('pauseBtn').style.display = 'none';
            }
        }
        
        function updateTimer() {
            if (isRunning) {
                elapsedTime = Date.now() - startTime;
                const totalSeconds = Math.floor(elapsedTime / 1000);
                const hours = Math.floor(totalSeconds / 3600);
                const minutes = Math.floor((totalSeconds % 3600) / 60);
                const seconds = totalSeconds % 60;
                
                document.getElementById('timerValue').textContent = 
                    `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            }
        }
        
        function completeMatch() {
            if (confirm('Are you sure you want to complete this match?')) {
                pauseMatch();
                saveScores();
                updateMatchStatus('completed');
                
                // Disable further editing
                document.getElementById('team1Score').disabled = true;
                document.getElementById('team2Score').disabled = true;
                document.getElementById('completeBtn').disabled = true;
                
                alert('Match completed successfully!');
            }
        }
        
        function resetMatch() {
            if (confirm('Are you sure you want to reset the scores?')) {
                document.getElementById('team1Score').value = 0;
                document.getElementById('team2Score').value = 0;
                saveScores();
                updateWinnerDisplay();
            }
        }
        
        function saveScores() {
            const team1Score = parseInt(document.getElementById('team1Score').value) || 0;
            const team2Score = parseInt(document.getElementById('team2Score').value) || 0;
            
            fetch('save_referee_scores.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    token: token,
                    team1_score: team1Score,
                    team2_score: team2Score
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    updateWinnerDisplay();
                }
            })
            .catch(error => {
                console.error('Error saving scores:', error);
            });
        }
        
        function updateMatchStatus(status) {
            fetch('update_match_status.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    token: token,
                    status: status
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload(); // Refresh to show updated status
                }
            })
            .catch(error => {
                console.error('Error updating status:', error);
            });
        }
        
        function updateWinnerDisplay() {
            const team1Score = parseInt(document.getElementById('team1Score').value) || 0;
            const team2Score = parseInt(document.getElementById('team2Score').value) || 0;
            
            const team1Card = document.getElementById('team1Card');
            const team2Card = document.getElementById('team2Card');
            
            // Remove winner class from both
            team1Card.classList.remove('winner');
            team2Card.classList.remove('winner');
            
            // Add winner class to the leading team
            if (team1Score > team2Score) {
                team1Card.classList.add('winner');
            } else if (team2Score > team1Score) {
                team2Card.classList.add('winner');
            }
        }
    </script>
</body>
</html>
