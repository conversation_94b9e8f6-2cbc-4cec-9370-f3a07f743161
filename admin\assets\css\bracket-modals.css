/* SC_IMS Tournament Bracket Modal Styles */

/* Modal Overlay */
.sc-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    animation: fadeIn 0.3s ease forwards;
}

@keyframes fadeIn {
    to { opacity: 1; }
}

/* Modal Container */
.sc-modal-container {
    background: white;
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 800px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    animation: modalSlideIn 0.3s ease forwards;
}

@keyframes modalSlideIn {
    to {
        transform: scale(1);
    }
}

/* Modal Header */
.sc-modal-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 20px 25px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 20px 20px 0 0;
}

.sc-modal-title {
    margin: 0;
    font-size: 1.4em;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.sc-modal-close {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.sc-modal-close:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* Modal Body */
.sc-modal-body {
    padding: 30px 25px;
    max-height: 60vh;
    overflow-y: auto;
}

/* Form Styles */
.sc-form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.sc-form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.sc-form-label {
    font-weight: 600;
    color: #343a40;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95em;
}

.sc-form-label i {
    color: #007bff;
    width: 16px;
}

.sc-form-input,
.sc-form-select,
.sc-form-textarea {
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1em;
    transition: all 0.3s ease;
    background: white;
}

.sc-form-input:focus,
.sc-form-select:focus,
.sc-form-textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.sc-form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* VS Divider */
.sc-vs-divider {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
}

.sc-vs-circle-modal {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #6c757d, #495057);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.1em;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

/* Winner Controls */
.sc-winner-controls {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 10px;
    margin-top: 10px;
}

.sc-winner-btn {
    padding: 12px 20px;
    border: 2px solid #e9ecef;
    background: white;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.sc-winner-btn:hover {
    border-color: #007bff;
    background: #f8f9fa;
}

.sc-winner-btn.active {
    background: #007bff;
    border-color: #007bff;
    color: white;
}

.sc-winner-btn#team1-winner.active,
.sc-winner-btn#team2-winner.active {
    background: #28a745;
    border-color: #28a745;
}

.sc-winner-btn#no-winner {
    background: #6c757d;
    color: white;
    border-color: #6c757d;
}

.sc-winner-btn#no-winner.active {
    background: #495057;
    border-color: #495057;
}

/* Modal Footer */
.sc-modal-footer {
    background: #f8f9fa;
    padding: 20px 25px;
    border-top: 1px solid #e9ecef;
}

.sc-modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

.sc-btn-primary,
.sc-btn-secondary,
.sc-btn-referee {
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.95em;
}

.sc-btn-primary {
    background: #007bff;
    color: white;
}

.sc-btn-primary:hover {
    background: #0056b3;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.3);
}

.sc-btn-secondary {
    background: #6c757d;
    color: white;
}

.sc-btn-secondary:hover {
    background: #545b62;
    transform: translateY(-2px);
}

.sc-btn-referee {
    background: #28a745;
    color: white;
}

.sc-btn-referee:hover {
    background: #1e7e34;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
}

/* Notifications */
.sc-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 10001;
    transform: translateX(400px);
    transition: transform 0.3s ease;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.15);
}

.sc-notification.show {
    transform: translateX(0);
}

.sc-notification-success {
    background: #28a745;
}

.sc-notification-error {
    background: #dc3545;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sc-modal-container {
        width: 95%;
        margin: 20px;
        max-height: 95vh;
    }
    
    .sc-modal-header {
        padding: 15px 20px;
    }
    
    .sc-modal-title {
        font-size: 1.2em;
    }
    
    .sc-modal-body {
        padding: 20px;
    }
    
    .sc-form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .sc-winner-controls {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .sc-modal-actions {
        flex-direction: column;
        gap: 10px;
    }
    
    .sc-btn-primary,
    .sc-btn-secondary,
    .sc-btn-referee {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .sc-modal-container {
        width: 100%;
        height: 100%;
        border-radius: 0;
        max-height: 100vh;
    }
    
    .sc-modal-header {
        border-radius: 0;
    }
    
    .sc-modal-body {
        padding: 15px;
    }
    
    .sc-modal-footer {
        padding: 15px;
    }
    
    .sc-notification {
        right: 10px;
        left: 10px;
        transform: translateY(-100px);
    }
    
    .sc-notification.show {
        transform: translateY(0);
    }
}

/* Custom Scrollbar for Modal Body */
.sc-modal-body::-webkit-scrollbar {
    width: 6px;
}

.sc-modal-body::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.sc-modal-body::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.sc-modal-body::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading States */
.sc-btn-primary:disabled,
.sc-btn-secondary:disabled,
.sc-btn-referee:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.sc-loading {
    position: relative;
    overflow: hidden;
}

.sc-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}
