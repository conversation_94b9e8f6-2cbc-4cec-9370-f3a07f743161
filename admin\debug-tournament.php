<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Tournament Debug Information</h2>";

// Check event_sport configuration
echo "<h3>Event Sport Configuration (ID: 37)</h3>";
$stmt = $conn->prepare("
    SELECT es.*, tf.name as format_name, tf.code as format_code, tf.min_participants 
    FROM event_sports es 
    LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id 
    WHERE es.id = 37
");
$stmt->execute();
$sport = $stmt->fetch(PDO::FETCH_ASSOC);
echo "<pre>";
print_r($sport);
echo "</pre>";

// Check available tournament formats
echo "<h3>Available Tournament Formats</h3>";
$stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
$stmt->execute();
$formats = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<pre>";
print_r($formats);
echo "</pre>";

// Check participants
echo "<h3>Available Departments</h3>";
$stmt = $conn->prepare("SELECT id, name, abbreviation, status FROM departments WHERE status = 'active' ORDER BY name LIMIT 10");
$stmt->execute();
$departments = $stmt->fetchAll(PDO::FETCH_ASSOC);
echo "<pre>";
print_r($departments);
echo "</pre>";

// Test tournament generation
echo "<h3>Test Tournament Generation</h3>";
try {
    require_once '../includes/advanced_tournament_engine.php';
    require_once '../includes/tournament_algorithms_advanced.php';
    
    $engine = new AdvancedTournamentEngine($conn);
    $result = $engine->generateTournament(37, 16, [
        'seeding_method' => 'random',
        'third_place_playoff' => false,
        'scoring_config' => [
            'points_win' => 3,
            'points_draw' => 1,
            'points_loss' => 0
        ]
    ]);
    
    echo "<h4>Tournament Generation Result:</h4>";
    echo "<pre>";
    print_r($result);
    echo "</pre>";
    
} catch (Exception $e) {
    echo "<h4>Tournament Generation Error:</h4>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
