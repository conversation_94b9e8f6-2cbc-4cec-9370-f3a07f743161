<?php
/**
 * Assign Judged Format to Category 24
 * SC_IMS Sports Competition and Event Management System
 * 
 * This script assigns an appropriate judged sport format to category 24
 */

require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Assigning Judged Format to Category 24</h2>";

try {
    $conn->beginTransaction();
    
    // Get current category info
    $stmt = $conn->prepare("
        SELECT sc.*, es.tournament_format_id, s.name as sport_name, s.type as sport_type
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = 24
    ");
    $stmt->execute();
    $category = $stmt->fetch();
    
    if (!$category) {
        throw new Exception("Category 24 not found");
    }
    
    echo "<h3>Current Category Information:</h3>";
    echo "<p><strong>Category:</strong> " . htmlspecialchars($category['name']) . "</p>";
    echo "<p><strong>Sport:</strong> " . htmlspecialchars($category['sport_name']) . "</p>";
    echo "<p><strong>Sport Type:</strong> " . htmlspecialchars($category['sport_type']) . "</p>";
    echo "<p><strong>Current Format ID:</strong> " . ($category['tournament_format_id'] ?? 'Not Set') . "</p>";
    
    // Get available judged formats
    $stmt = $conn->prepare("
        SELECT id, name, code, description, min_participants
        FROM tournament_formats 
        WHERE sport_type_category IN ('judged', 'performance')
        AND min_participants <= 2
        ORDER BY name
    ");
    $stmt->execute();
    $judged_formats = $stmt->fetchAll();
    
    if (empty($judged_formats)) {
        throw new Exception("No suitable judged formats found with min_participants <= 2");
    }
    
    echo "<h3>Available Judged Formats:</h3>";
    echo "<ul>";
    foreach ($judged_formats as $format) {
        echo "<li><strong>" . htmlspecialchars($format['name']) . "</strong> (ID: " . $format['id'] . ") - Min: " . $format['min_participants'] . " participants</li>";
    }
    echo "</ul>";
    
    // Select the most appropriate format for singing competition
    $selected_format = null;
    foreach ($judged_formats as $format) {
        if (in_array($format['code'], ['individual_performance', 'judged_rounds', 'talent_showcase'])) {
            $selected_format = $format;
            break;
        }
    }
    
    if (!$selected_format) {
        $selected_format = $judged_formats[0]; // Use first available
    }
    
    echo "<h3>Selected Format:</h3>";
    echo "<p><strong>Format:</strong> " . htmlspecialchars($selected_format['name']) . "</p>";
    echo "<p><strong>Code:</strong> " . htmlspecialchars($selected_format['code']) . "</p>";
    echo "<p><strong>Description:</strong> " . htmlspecialchars($selected_format['description']) . "</p>";
    echo "<p><strong>Min Participants:</strong> " . $selected_format['min_participants'] . "</p>";
    
    // Update the event_sports table with the selected format
    $stmt = $conn->prepare("
        UPDATE event_sports 
        SET tournament_format_id = ? 
        WHERE id = ?
    ");
    $stmt->execute([$selected_format['id'], $category['event_sport_id']]);
    
    echo "<p style='color: green;'>✅ Tournament format updated successfully!</p>";
    
    // Verify the update
    $stmt = $conn->prepare("
        SELECT tf.name as format_name, tf.min_participants
        FROM event_sports es
        JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.id = ?
    ");
    $stmt->execute([$category['event_sport_id']]);
    $updated_format = $stmt->fetch();
    
    if ($updated_format) {
        echo "<h3>Verification:</h3>";
        echo "<p><strong>New Format:</strong> " . htmlspecialchars($updated_format['format_name']) . "</p>";
        echo "<p><strong>Min Participants:</strong> " . $updated_format['min_participants'] . "</p>";
    }
    
    $conn->commit();
    echo "<h3 style='color: green;'>🎉 Format assignment completed successfully!</h3>";
    
    echo "<p><a href='manage-category.php?event_id=" . $category['event_id'] . "&sport_id=" . $category['sport_id'] . "&category_id=24' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Updated Category</a></p>";
    
} catch (Exception $e) {
    $conn->rollback();
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
h3 { color: #555; margin-top: 30px; }
ul { background: #f8f9fa; padding: 15px; border-radius: 5px; }
p { margin: 10px 0; }
</style>
