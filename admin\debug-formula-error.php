<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🔍 Debug Formula Calculator Error</h1>";
echo "<p>Investigating the parse error in tournament_formula_calculator.php...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>1. Check Tournament Formats</h2>";
    
    $stmt = $conn->prepare("SELECT id, name, rounds_formula, matches_formula, algorithm_class FROM tournament_formats");
    $stmt->execute();
    $formats = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th>ID</th><th>Name</th><th>Rounds Formula</th><th>Matches Formula</th><th>Algorithm Class</th>";
    echo "</tr>";
    
    foreach ($formats as $format) {
        echo "<tr>";
        echo "<td>{$format['id']}</td>";
        echo "<td>{$format['name']}</td>";
        echo "<td>" . htmlspecialchars($format['rounds_formula'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($format['matches_formula'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($format['algorithm_class'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>2. Check Event Sport Configuration</h2>";
    
    $event_id = 4;
    $sport_id = 37;
    
    $stmt = $conn->prepare("
        SELECT es.*, tf.name as format_name, tf.rounds_formula, tf.matches_formula, tf.algorithm_class
        FROM event_sports es
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    
    if ($event_sport) {
        echo "<p><strong>Event Sport ID:</strong> {$event_sport['id']}</p>";
        echo "<p><strong>Tournament Format:</strong> " . ($event_sport['format_name'] ?? 'Not configured') . "</p>";
        echo "<p><strong>Rounds Formula:</strong> " . htmlspecialchars($event_sport['rounds_formula'] ?? 'NULL') . "</p>";
        echo "<p><strong>Matches Formula:</strong> " . htmlspecialchars($event_sport['matches_formula'] ?? 'NULL') . "</p>";
        echo "<p><strong>Algorithm Class:</strong> " . htmlspecialchars($event_sport['algorithm_class'] ?? 'NULL') . "</p>";
    } else {
        echo "<p style='color: red;'>❌ Event sport not found</p>";
    }
    
    echo "<h2>3. Test Formula Calculator</h2>";
    
    if ($event_sport && $event_sport['rounds_formula']) {
        echo "<h3>Testing Rounds Formula</h3>";
        echo "<p>Formula: " . htmlspecialchars($event_sport['rounds_formula']) . "</p>";
        
        // Test with different participant counts
        $test_participants = [2, 4, 8, 16];
        
        foreach ($test_participants as $count) {
            echo "<h4>Testing with {$count} participants:</h4>";
            
            try {
                // Test the formula manually
                $formula = $event_sport['rounds_formula'];
                
                // Replace 'n' with participant count
                $expression = str_replace('n', $count, $formula);
                echo "<p>Expression: {$expression}</p>";
                
                // Test if it's a valid PHP expression
                $test_result = @eval("return $expression;");
                
                if ($test_result === false) {
                    echo "<p style='color: red;'>❌ Expression evaluation failed</p>";
                } else {
                    echo "<p style='color: green;'>✅ Result: {$test_result}</p>";
                }
                
            } catch (ParseError $e) {
                echo "<p style='color: red;'>❌ Parse Error: " . $e->getMessage() . "</p>";
            } catch (Error $e) {
                echo "<p style='color: red;'>❌ Fatal Error: " . $e->getMessage() . "</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h2>4. Test Tournament Manager</h2>";
    
    try {
        require_once '../includes/tournament_manager.php';
        echo "<p style='color: green;'>✅ TournamentManager loaded successfully</p>";
        
        $tournamentManager = new TournamentManager($conn);
        echo "<p style='color: green;'>✅ TournamentManager instantiated successfully</p>";
        
    } catch (ParseError $e) {
        echo "<p style='color: red;'>❌ Parse Error in TournamentManager: " . $e->getMessage() . "</p>";
        echo "<p>File: " . $e->getFile() . "</p>";
        echo "<p>Line: " . $e->getLine() . "</p>";
    } catch (Error $e) {
        echo "<p style='color: red;'>❌ Fatal Error in TournamentManager: " . $e->getMessage() . "</p>";
        echo "<p>File: " . $e->getFile() . "</p>";
        echo "<p>Line: " . $e->getLine() . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Exception in TournamentManager: " . $e->getMessage() . "</p>";
        echo "<p>File: " . $e->getFile() . "</p>";
        echo "<p>Line: " . $e->getLine() . "</p>";
    }
    
    echo "<h2>5. Test Formula Calculator Directly</h2>";
    
    try {
        require_once '../includes/tournament_formula_calculator.php';
        echo "<p style='color: green;'>✅ TournamentFormulaCalculator loaded successfully</p>";
        
        $calculator = new TournamentFormulaCalculator($conn);
        echo "<p style='color: green;'>✅ TournamentFormulaCalculator instantiated successfully</p>";
        
    } catch (ParseError $e) {
        echo "<p style='color: red;'>❌ Parse Error in TournamentFormulaCalculator: " . $e->getMessage() . "</p>";
        echo "<p>File: " . $e->getFile() . "</p>";
        echo "<p>Line: " . $e->getLine() . "</p>";
    } catch (Error $e) {
        echo "<p style='color: red;'>❌ Fatal Error in TournamentFormulaCalculator: " . $e->getMessage() . "</p>";
        echo "<p>File: " . $e->getFile() . "</p>";
        echo "<p>Line: " . $e->getLine() . "</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Exception in TournamentFormulaCalculator: " . $e->getMessage() . "</p>";
        echo "<p>File: " . $e->getFile() . "</p>";
        echo "<p>Line: " . $e->getLine() . "</p>";
    }
    
    echo "<h2>6. Recommendations</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6; margin: 20px 0;'>";
    echo "<h4>Possible Solutions:</h4>";
    echo "<ol>";
    echo "<li><strong>Check Formula Syntax:</strong> Ensure all formulas use valid PHP mathematical expressions</li>";
    echo "<li><strong>Replace Complex Functions:</strong> Replace functions like log2() with log(n, 2)</li>";
    echo "<li><strong>Add Fallback Logic:</strong> Use simple hardcoded formulas when database formulas fail</li>";
    echo "<li><strong>Validate Expressions:</strong> Add better validation before eval()</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Fatal Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}
?>
