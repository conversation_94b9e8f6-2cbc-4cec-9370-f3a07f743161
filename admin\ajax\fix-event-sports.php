<?php
/**
 * Fix Missing Event Sports Records
 * Creates missing event_sports records for sport categories
 */

// Enable error reporting for debugging
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Start output buffering
ob_start();

// Set JSON response header
header('Content-Type: application/json');

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

try {
    // Check admin authentication
    if (!isAdminLoggedIn()) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'Authentication required'
        ]);
        exit;
    }

    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();

    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'create_event_sport':
            createEventSport($conn, $input);
            break;
        case 'create_all_missing':
            createAllMissingEventSports($conn);
            break;
        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    ob_clean();
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function createEventSport($conn, $input) {
    $eventId = $input['event_id'] ?? '';
    $sportId = $input['sport_id'] ?? '';
    
    if (empty($eventId) || empty($sportId)) {
        throw new Exception('Event ID and Sport ID are required');
    }
    
    // Check if record already exists
    $stmt = $conn->prepare("
        SELECT id FROM event_sports 
        WHERE event_id = ? AND sport_id = ?
    ");
    $stmt->execute([$eventId, $sportId]);
    if ($stmt->fetch()) {
        throw new Exception('Event sport record already exists');
    }
    
    // Get default tournament format (Single Elimination)
    $stmt = $conn->prepare("
        SELECT id FROM tournament_formats 
        WHERE code = 'single_elimination' 
        LIMIT 1
    ");
    $stmt->execute();
    $defaultFormatId = $stmt->fetchColumn();
    
    if (!$defaultFormatId) {
        throw new Exception('No default tournament format found');
    }
    
    // Create event_sports record
    $stmt = $conn->prepare("
        INSERT INTO event_sports (event_id, sport_id, tournament_format_id, bracket_type, status)
        VALUES (?, ?, ?, 'single_elimination', 'registration')
    ");
    $stmt->execute([$eventId, $sportId, $defaultFormatId]);
    
    $eventSportId = $conn->lastInsertId();
    
    // Log admin activity
    if (function_exists('logAdminActivity')) {
        logAdminActivity('CREATE_EVENT_SPORT', 'event_sports', $eventSportId, null, [
            'event_id' => $eventId,
            'sport_id' => $sportId,
            'tournament_format_id' => $defaultFormatId
        ]);
    }
    
    echo json_encode([
        'success' => true,
        'message' => 'Event sport record created successfully',
        'event_sport_id' => $eventSportId
    ]);
}

function createAllMissingEventSports($conn) {
    // Get all sport categories that don't have corresponding event_sports records
    $stmt = $conn->prepare("
        SELECT DISTINCT sc.event_id, sc.sport_id
        FROM sport_categories sc
        LEFT JOIN event_sports es ON sc.event_id = es.event_id AND sc.sport_id = es.sport_id
        WHERE es.id IS NULL
    ");
    $stmt->execute();
    $missing_records = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($missing_records)) {
        echo json_encode([
            'success' => true,
            'message' => 'No missing records found',
            'created_count' => 0
        ]);
        return;
    }
    
    // Get default tournament format
    $stmt = $conn->prepare("
        SELECT id FROM tournament_formats 
        WHERE code = 'single_elimination' 
        LIMIT 1
    ");
    $stmt->execute();
    $defaultFormatId = $stmt->fetchColumn();
    
    if (!$defaultFormatId) {
        throw new Exception('No default tournament format found');
    }
    
    $created_count = 0;
    
    foreach ($missing_records as $record) {
        try {
            // Create event_sports record
            $stmt = $conn->prepare("
                INSERT INTO event_sports (event_id, sport_id, tournament_format_id, bracket_type, status)
                VALUES (?, ?, ?, 'single_elimination', 'registration')
            ");
            $stmt->execute([$record['event_id'], $record['sport_id'], $defaultFormatId]);
            $created_count++;
            
        } catch (Exception $e) {
            // Log error but continue with other records
            error_log("Failed to create event_sport for event {$record['event_id']}, sport {$record['sport_id']}: " . $e->getMessage());
        }
    }
    
    // Log admin activity
    if (function_exists('logAdminActivity')) {
        logAdminActivity('CREATE_ALL_EVENT_SPORTS', 'event_sports', null, null, [
            'created_count' => $created_count,
            'total_missing' => count($missing_records)
        ]);
    }
    
    echo json_encode([
        'success' => true,
        'message' => "Created {$created_count} event sport records",
        'created_count' => $created_count
    ]);
}
?>
