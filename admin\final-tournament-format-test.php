<?php
/**
 * Final Tournament Format Test
 * Comprehensive test to verify Academic and Judged sports show tournament formats
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>🎯 Final Tournament Format Test</h2>";

// 1. Check current state
echo "<h3>1. Current Database State</h3>";

// Check sports categorization
$stmt = $conn->prepare("
    SELECT s.id, s.name, s.type, st.category as sport_type_category, st.name as sport_type_name
    FROM sports s
    LEFT JOIN sport_types st ON s.sport_type_id = st.id
    WHERE st.category IN ('academic', 'judged') OR s.type IN ('academic', 'judged')
    ORDER BY st.category, s.name
");
$stmt->execute();
$categorized_sports = $stmt->fetchAll();

echo "<h4>Academic and Judged Sports:</h4>";
if (empty($categorized_sports)) {
    echo "<p style='color: red;'>❌ No Academic or Judged sports found!</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Name</th><th>Category</th><th>Type</th></tr>";
    foreach ($categorized_sports as $sport) {
        $category = $sport['sport_type_category'] ?? $sport['type'];
        $color = $category === 'academic' ? '#e6f3ff' : '#ffe6e6';
        echo "<tr style='background-color: {$color};'>";
        echo "<td>{$sport['name']}</td>";
        echo "<td>{$category}</td>";
        echo "<td>{$sport['type']}</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// Check tournament formats
$stmt = $conn->prepare("
    SELECT name, code, sport_type_category, sport_types
    FROM tournament_formats
    WHERE sport_type_category IN ('academic', 'judged') OR sport_types LIKE '%academic%' OR sport_types LIKE '%judged%'
    ORDER BY sport_type_category, name
");
$stmt->execute();
$formats = $stmt->fetchAll();

echo "<h4>Academic and Judged Tournament Formats:</h4>";
if (empty($formats)) {
    echo "<p style='color: red;'>❌ No Academic or Judged tournament formats found!</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Name</th><th>Code</th><th>Category</th><th>Types</th></tr>";
    foreach ($formats as $format) {
        $category = $format['sport_type_category'] ?? 'N/A';
        $color = $category === 'academic' ? '#e6f3ff' : '#ffe6e6';
        echo "<tr style='background-color: {$color};'>";
        echo "<td>{$format['name']}</td>";
        echo "<td>{$format['code']}</td>";
        echo "<td>{$category}</td>";
        echo "<td>{$format['sport_types']}</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// 2. Test AJAX endpoints
echo "<h3>2. AJAX Endpoint Tests</h3>";

$test_cases = [
    ['academic', 'Academic Sports'],
    ['judged', 'Judged Sports'],
    ['traditional', 'Traditional Sports (Control)']
];

foreach ($test_cases as $test_case) {
    $sport_type = $test_case[0];
    $label = $test_case[1];
    
    echo "<h4>Testing: {$label}</h4>";
    
    $_POST['sport_type'] = $sport_type;
    
    ob_start();
    try {
        include 'ajax/get-tournament-formats.php';
        $response = ob_get_clean();
        
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            $format_count = count($data['formats']);
            echo "<p style='color: green;'>✅ Success: Found {$format_count} formats</p>";
            
            if ($format_count > 0) {
                echo "<ul>";
                foreach ($data['formats'] as $format) {
                    echo "<li><strong>{$format['name']}</strong> - {$format['description']}</li>";
                }
                echo "</ul>";
            }
            
            if (isset($data['debug'])) {
                echo "<p><small>Debug: " . json_encode($data['debug']) . "</small></p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Failed: " . ($data['message'] ?? 'Unknown error') . "</p>";
            echo "<p><small>Response: " . htmlspecialchars($response) . "</small></p>";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
    }
    
    unset($_POST['sport_type']);
}

// 3. Test specific problematic sports
echo "<h3>3. Specific Sport Tests</h3>";

$problematic_sports = [
    'Mr. and Ms. Intramurals',
    'Dance',
    'Banner Raising',
    'Chess'
];

foreach ($problematic_sports as $sport_name) {
    echo "<h4>Testing: {$sport_name}</h4>";
    
    $stmt = $conn->prepare("
        SELECT s.*, st.category as sport_type_category
        FROM sports s
        LEFT JOIN sport_types st ON s.sport_type_id = st.id
        WHERE s.name LIKE ?
        LIMIT 1
    ");
    $stmt->execute(["%{$sport_name}%"]);
    $sport = $stmt->fetch();
    
    if ($sport) {
        $sport_type = $sport['sport_type_category'] ?? $sport['type'] ?? 'traditional';
        echo "<p><strong>Found:</strong> {$sport['name']} (Type: {$sport_type})</p>";
        
        // Test AJAX for this sport type
        $_POST['sport_type'] = $sport_type;
        
        ob_start();
        try {
            include 'ajax/get-tournament-formats.php';
            $response = ob_get_clean();
            $data = json_decode($response, true);
            
            if ($data && $data['success'] && !empty($data['formats'])) {
                echo "<p style='color: green;'>✅ AJAX Success: {$sport['name']} has " . count($data['formats']) . " tournament formats available</p>";
            } else {
                echo "<p style='color: red;'>❌ AJAX Failed: No formats returned for {$sport['name']}</p>";
            }
        } catch (Exception $e) {
            ob_end_clean();
            echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
        }
        
        unset($_POST['sport_type']);
    } else {
        echo "<p style='color: orange;'>⚠️ Sport '{$sport_name}' not found</p>";
    }
}

// 4. Final assessment
echo "<h3>4. Final Assessment</h3>";

$academic_formats_count = 0;
$judged_formats_count = 0;

foreach ($formats as $format) {
    $category = $format['sport_type_category'] ?? '';
    $types = $format['sport_types'] ?? '';
    
    if ($category === 'academic' || strpos($types, 'academic') !== false) {
        $academic_formats_count++;
    }
    if ($category === 'judged' || strpos($types, 'judged') !== false) {
        $judged_formats_count++;
    }
}

$academic_sports_count = 0;
$judged_sports_count = 0;

foreach ($categorized_sports as $sport) {
    $category = $sport['sport_type_category'] ?? $sport['type'];
    if ($category === 'academic') $academic_sports_count++;
    if ($category === 'judged') $judged_sports_count++;
}

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h4>📊 Summary</h4>";
echo "<ul>";
echo "<li><strong>Academic Sports:</strong> {$academic_sports_count} sports, {$academic_formats_count} tournament formats</li>";
echo "<li><strong>Judged Sports:</strong> {$judged_sports_count} sports, {$judged_formats_count} tournament formats</li>";
echo "</ul>";

if ($academic_sports_count > 0 && $academic_formats_count > 0 && $judged_sports_count > 0 && $judged_formats_count > 0) {
    echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 15px 0;'>";
    echo "<h4>🎉 All Tests Passed!</h4>";
    echo "<p>Both Academic and Judged sports have been properly categorized and have tournament formats available.</p>";
    echo "<p><strong>The tournament format dropdown should now work correctly in the 'Add Sport to Event' modal.</strong></p>";
    echo "</div>";
} else {
    echo "<div style='background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 15px 0;'>";
    echo "<h4>⚠️ Issues Found</h4>";
    echo "<p>Some sports or tournament formats are still missing. Please run the fix scripts again.</p>";
    echo "</div>";
}

echo "</div>";

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='manage-event.php?id=1' style='background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 0 10px;'>";
echo "🎯 Test in Event Management";
echo "</a>";
echo "<a href='fix-sport-categorization.php' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 0 10px;'>";
echo "🔧 Run Fix Script Again";
echo "</a>";
echo "</div>";
?>
