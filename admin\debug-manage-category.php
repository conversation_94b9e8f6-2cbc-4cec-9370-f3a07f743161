<?php
/**
 * Debug Manage Category - Investigate Redirect Issue
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🐛 Debug Manage Category Redirect Issue</h1>";

// Get URL parameters
$event_id = $_GET['event_id'] ?? null;
$sport_id = $_GET['sport_id'] ?? null;
$category_id = $_GET['category_id'] ?? null;

echo "<h2>1. URL Parameters Received</h2>";
echo "<p><strong>event_id:</strong> " . ($event_id ?? 'NULL') . "</p>";
echo "<p><strong>sport_id:</strong> " . ($sport_id ?? 'NULL') . "</p>";
echo "<p><strong>category_id:</strong> " . ($category_id ?? 'NULL') . "</p>";

echo "<h2>2. Parameter Validation</h2>";

// Check if parameters are empty
$empty_checks = [
    'event_id' => empty($event_id),
    'sport_id' => empty($sport_id),
    'category_id' => empty($category_id)
];

echo "<h3>Empty() Checks:</h3>";
foreach ($empty_checks as $param => $is_empty) {
    $status = $is_empty ? '❌ EMPTY' : '✅ NOT EMPTY';
    echo "<p><strong>$param:</strong> $status</p>";
}

// Check if parameters are numeric
$numeric_checks = [
    'event_id' => is_numeric($event_id),
    'sport_id' => is_numeric($sport_id),
    'category_id' => is_numeric($category_id)
];

echo "<h3>is_numeric() Checks:</h3>";
foreach ($numeric_checks as $param => $is_numeric) {
    $status = $is_numeric ? '✅ NUMERIC' : '❌ NOT NUMERIC';
    echo "<p><strong>$param:</strong> $status</p>";
}

// Overall validation result
$validation_failed = false;
foreach ($empty_checks as $is_empty) {
    if ($is_empty) {
        $validation_failed = true;
        break;
    }
}
foreach ($numeric_checks as $is_numeric) {
    if (!$is_numeric) {
        $validation_failed = true;
        break;
    }
}

if ($validation_failed) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Validation Failed</h3>";
    echo "<p>Parameters failed validation. This would cause a redirect to events.php</p>";
    echo "<p><strong>Redirect reason:</strong> Invalid or missing parameters</p>";
    echo "</div>";
} else {
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Validation Passed</h3>";
    echo "<p>All parameters passed validation. Proceeding to database queries...</p>";
    echo "</div>";
    
    // Convert to integers
    $event_id = intval($event_id);
    $sport_id = intval($sport_id);
    $category_id = intval($category_id);
    
    echo "<h2>3. Database Query Test</h2>";
    
    try {
        // Test the exact query from manage-category.php
        $stmt = $conn->prepare("
            SELECT 
                sc.*,
                e.name as event_name,
                e.description as event_description,
                e.start_date,
                e.end_date,
                e.venue as event_venue,
                s.name as sport_name,
                s.type as sport_type,
                s.description as sport_description,
                es.id as event_sport_id
            FROM sport_categories sc
            JOIN event_sports es ON sc.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            WHERE sc.id = ? AND es.event_id = ? AND s.id = ?
        ");
        
        echo "<h3>Query Parameters:</h3>";
        echo "<p><strong>category_id:</strong> $category_id</p>";
        echo "<p><strong>event_id:</strong> $event_id</p>";
        echo "<p><strong>sport_id:</strong> $sport_id</p>";
        
        $stmt->execute([$category_id, $event_id, $sport_id]);
        $category = $stmt->fetch();
        
        if (!$category) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h3>❌ Category Not Found</h3>";
            echo "<p>No category found with the given parameters. This would cause a redirect to events.php</p>";
            echo "<p><strong>Redirect reason:</strong> Category not found in database</p>";
            echo "</div>";
            
            // Let's check what categories exist
            echo "<h3>Available Categories:</h3>";
            $stmt = $conn->prepare("
                SELECT 
                    sc.id as category_id,
                    sc.category_name,
                    es.event_id,
                    es.sport_id,
                    e.name as event_name,
                    s.name as sport_name
                FROM sport_categories sc
                JOIN event_sports es ON sc.event_sport_id = es.id
                JOIN events e ON es.event_id = e.id
                JOIN sports s ON es.sport_id = s.id
                ORDER BY e.name, s.name, sc.category_name
            ");
            $stmt->execute();
            $available_categories = $stmt->fetchAll();
            
            if (!empty($available_categories)) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
                echo "<tr><th>Category ID</th><th>Category Name</th><th>Event ID</th><th>Sport ID</th><th>Event Name</th><th>Sport Name</th><th>Test Link</th></tr>";
                foreach ($available_categories as $cat) {
                    $test_url = "debug-manage-category.php?event_id={$cat['event_id']}&sport_id={$cat['sport_id']}&category_id={$cat['category_id']}";
                    echo "<tr>";
                    echo "<td>{$cat['category_id']}</td>";
                    echo "<td>{$cat['category_name']}</td>";
                    echo "<td>{$cat['event_id']}</td>";
                    echo "<td>{$cat['sport_id']}</td>";
                    echo "<td>{$cat['event_name']}</td>";
                    echo "<td>{$cat['sport_name']}</td>";
                    echo "<td><a href='$test_url'>Test</a></td>";
                    echo "</tr>";
                }
                echo "</table>";
            } else {
                echo "<p>No categories found in database.</p>";
                echo "<p><a href='comprehensive-database-fix.php'>Create Test Data</a></p>";
            }
            
        } else {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h3>✅ Category Found Successfully</h3>";
            echo "<p><strong>Category Name:</strong> {$category['category_name']}</p>";
            echo "<p><strong>Event:</strong> {$category['event_name']}</p>";
            echo "<p><strong>Sport:</strong> {$category['sport_name']}</p>";
            echo "<p><strong>This should NOT redirect to events.php</strong></p>";
            echo "</div>";
            
            echo "<h3>Category Details:</h3>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            foreach ($category as $key => $value) {
                echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value ?? 'NULL') . "</td></tr>";
            }
            echo "</table>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ Database Error</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "<p><strong>This would cause a redirect to events.php</strong></p>";
        echo "</div>";
    }
}

echo "<h2>4. Test Links</h2>";

if (isset($_GET['event_id']) && isset($_GET['sport_id']) && isset($_GET['category_id'])) {
    $normal_url = "manage-category.php?event_id={$_GET['event_id']}&sport_id={$_GET['sport_id']}&category_id={$_GET['category_id']}";
    $debug_url = "manage-category.php?event_id={$_GET['event_id']}&sport_id={$_GET['sport_id']}&category_id={$_GET['category_id']}&debug=1";
    
    echo "<p><a href='$normal_url' target='_blank'>Test Normal manage-category.php</a></p>";
    echo "<p><a href='$debug_url' target='_blank'>Test Debug manage-category.php</a></p>";
}

echo "<h2>5. Summary</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<h4>Possible Redirect Causes:</h4>";
echo "<ul>";
echo "<li><strong>Missing Parameters:</strong> event_id, sport_id, or category_id not provided</li>";
echo "<li><strong>Invalid Parameters:</strong> Parameters are not numeric</li>";
echo "<li><strong>Category Not Found:</strong> No matching category in database</li>";
echo "<li><strong>Database Error:</strong> Exception during query execution</li>";
echo "</ul>";
echo "<p>Use the information above to identify which condition is causing the redirect to events.php</p>";
echo "</div>";
?>
