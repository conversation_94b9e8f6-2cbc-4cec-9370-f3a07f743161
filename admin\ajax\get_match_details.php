<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['match_id'])) {
        throw new Exception('Missing match ID');
    }
    
    $match_id = $input['match_id'];
    
    // Get match details with team information
    $stmt = $pdo->prepare("
        SELECT 
            m.*,
            t1.name as team1_name,
            t1.department_id as team1_department_id,
            d1.name as team1_department,
            t2.name as team2_name,
            t2.department_id as team2_department_id,
            d2.name as team2_department,
            tf.name as format_name,
            tf.description as format_description
        FROM tournament_matches m
        LEFT JOIN tournament_participants t1 ON m.team1_id = t1.id
        LEFT JOIN departments d1 ON t1.department_id = d1.id
        LEFT JOIN tournament_participants t2 ON m.team2_id = t2.id
        LEFT JOIN departments d2 ON t2.department_id = d2.id
        LEFT JOIN event_sports es ON m.event_sport_id = es.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE m.id = ?
    ");
    
    $stmt->execute([$match_id]);
    $match = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$match) {
        throw new Exception('Match not found');
    }
    
    // Format scheduled time for HTML input
    if ($match['scheduled_time']) {
        $match['scheduled_time'] = date('H:i', strtotime($match['scheduled_time']));
    }
    
    // Log admin activity
    logAdminActivity($_SESSION['admin_id'], 'view', 'match_details', $match_id, [
        'match_id' => $match_id,
        'team1' => $match['team1_name'],
        'team2' => $match['team2_name'],
        'status' => $match['status']
    ]);
    
    echo json_encode([
        'success' => true,
        'match' => $match
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_match_details.php: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Error loading match details: ' . $e->getMessage()
    ]);
}
?>
