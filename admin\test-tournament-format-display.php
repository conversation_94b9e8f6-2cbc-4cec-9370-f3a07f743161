<?php
/**
 * Test Tournament Format Display in manage-category.php
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🧪 Test Tournament Format Display</h1>";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; margin-bottom: 20px;'>";
echo "<h2 style='margin-top: 0;'>🏆 Tournament Format Field Test</h2>";
echo "<p>Testing the new Tournament Format field in the manage-category.php Overview tab.</p>";
echo "</div>";

try {
    // Check if tournament tables exist
    $tables_check = $conn->query("SHOW TABLES LIKE 'tournament_%'")->fetchAll();
    $tournament_tables_exist = count($tables_check) > 0;
    
    echo "<h2>1. Database Schema Check</h2>";
    
    if ($tournament_tables_exist) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Tournament Tables Found</h3>";
        echo "<p>Found " . count($tables_check) . " tournament-related tables:</p>";
        echo "<ul>";
        foreach ($tables_check as $table) {
            echo "<li>" . $table[0] . "</li>";
        }
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<h3>⚠️ Tournament Tables Missing</h3>";
        echo "<p>Tournament tables don't exist. The system will fall back to sport bracket_format.</p>";
        echo "</div>";
    }
    
    echo "<h2>2. Test Categories with Tournament Format Display</h2>";
    
    // Get available categories for testing
    $stmt = $conn->prepare("
        SELECT 
            sc.id as category_id,
            sc.category_name,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name,
            s.bracket_format
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        ORDER BY e.name, s.name, sc.category_name
        LIMIT 5
    ");
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
    if (empty($categories)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ No Categories Found</h3>";
        echo "<p>No categories available for testing. Please create some categories first.</p>";
        echo "<a href='verify-categories-created.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create Test Categories</a>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
        echo "<h3>✅ Found " . count($categories) . " Categories for Testing</h3>";
        echo "</div>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #007bff; color: white;'>";
        echo "<th>Category</th><th>Event</th><th>Sport</th><th>Default Format</th><th>Tournament Format Status</th><th>Test Link</th>";
        echo "</tr>";
        
        foreach ($categories as $cat) {
            $manage_url = "manage-category.php?event_id={$cat['event_id']}&sport_id={$cat['sport_id']}&category_id={$cat['category_id']}";
            
            // Check if tournament format exists for this category
            $tournament_format_status = "Not configured";
            $format_color = "#6c757d";
            
            if ($tournament_tables_exist) {
                try {
                    $stmt = $conn->prepare("
                        SELECT tf.name as format_name, ts.status
                        FROM tournament_structures ts
                        JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
                        WHERE ts.event_sport_id = (
                            SELECT es.id FROM event_sports es 
                            WHERE es.event_id = ? AND es.sport_id = ?
                        )
                        ORDER BY ts.created_at DESC
                        LIMIT 1
                    ");
                    $stmt->execute([$cat['event_id'], $cat['sport_id']]);
                    $tournament_format = $stmt->fetch();
                    
                    if ($tournament_format) {
                        $tournament_format_status = $tournament_format['format_name'] . " (" . $tournament_format['status'] . ")";
                        $format_color = "#007bff";
                    }
                } catch (Exception $e) {
                    // Ignore errors
                }
            }
            
            // If no tournament format, show sport default
            if ($tournament_format_status === "Not configured") {
                $tournament_format_status = "Default: " . ucwords(str_replace('_', ' ', $cat['bracket_format']));
                $format_color = "#ffc107";
            }
            
            echo "<tr>";
            echo "<td>{$cat['category_name']}</td>";
            echo "<td>{$cat['event_name']}</td>";
            echo "<td>{$cat['sport_name']}</td>";
            echo "<td>" . ucwords(str_replace('_', ' ', $cat['bracket_format'])) . "</td>";
            echo "<td style='color: $format_color;'>$tournament_format_status</td>";
            echo "<td><a href='$manage_url' target='_blank' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Test</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>3. Tournament Format Display Examples</h2>";
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;'>";
    
    // Example 1: Configured Tournament Format
    echo "<div style='border: 1px solid #28a745; border-radius: 8px; padding: 15px;'>";
    echo "<h4 style='color: #28a745; margin-top: 0;'>✅ Configured Tournament Format</h4>";
    echo "<p><strong>Tournament Format:</strong> <span style='color: #007bff; font-weight: 600;'>Single Elimination</span></p>";
    echo "<p><small style='color: #6c757d;'>Traditional knockout tournament format</small></p>";
    echo "<p><small style='color: #28a745;'>Status: In Progress</small></p>";
    echo "<p><small>This is how it appears when a tournament format is properly configured.</small></p>";
    echo "</div>";
    
    // Example 2: Not Configured
    echo "<div style='border: 1px solid #ffc107; border-radius: 8px; padding: 15px;'>";
    echo "<h4 style='color: #ffc107; margin-top: 0;'>⚠️ Not Configured</h4>";
    echo "<p><strong>Tournament Format:</strong> <span style='color: #6c757d;'>Not configured</span></p>";
    echo "<p><small style='color: #ffc107;'>⚠️ Tournament format needs to be set up</small></p>";
    echo "<p><small>This is how it appears when no tournament format is configured.</small></p>";
    echo "</div>";
    
    // Example 3: Default from Sport
    echo "<div style='border: 1px solid #007bff; border-radius: 8px; padding: 15px;'>";
    echo "<h4 style='color: #007bff; margin-top: 0;'>🔧 Default Format</h4>";
    echo "<p><strong>Tournament Format:</strong> <span style='color: #007bff; font-weight: 600;'>Round Robin</span></p>";
    echo "<p><small style='color: #6c757d;'>Default format for this sport</small></p>";
    echo "<p><small style='color: #28a745;'>Status: Setup</small></p>";
    echo "<p><small>This is how it appears when using the sport's default format.</small></p>";
    echo "</div>";
    echo "</div>";
    
    echo "<h2>4. Testing Instructions</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px;'>";
    echo "<h4>How to Test the Tournament Format Field:</h4>";
    echo "<ol>";
    echo "<li><strong>Click any 'Test' link above</strong> to open a category management page</li>";
    echo "<li><strong>Look for the 'Tournament Format' field</strong> in the Basic Details section of the Overview tab</li>";
    echo "<li><strong>Verify the display shows:</strong>";
    echo "<ul>";
    echo "<li>Tournament format name (if configured)</li>";
    echo "<li>Format description (if available)</li>";
    echo "<li>Tournament status (if not in setup)</li>";
    echo "<li>Warning message (if not configured)</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Test different categories</strong> to see various format configurations</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>5. Summary</h2>";

echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;'>";
echo "<h3>✅ Tournament Format Field Added Successfully</h3>";
echo "<ul>";
echo "<li><strong>Location:</strong> manage-category.php → Overview tab → Category Information → Basic Details</li>";
echo "<li><strong>Displays:</strong> Tournament format name, description, and status</li>";
echo "<li><strong>Fallback:</strong> Shows sport's default bracket format if no tournament configured</li>";
echo "<li><strong>Visual Indicators:</strong> Color-coded status and warning messages</li>";
echo "<li><strong>Responsive:</strong> Works with existing grid layout</li>";
echo "</ul>";
echo "<p><strong>The Tournament Format field is now available for administrators to see what competition structure is set up for each category.</strong></p>";
echo "</div>";
?>
