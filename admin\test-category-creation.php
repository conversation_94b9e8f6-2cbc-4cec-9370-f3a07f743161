<?php
/**
 * Test Category Creation Functionality
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🧪 Test Category Creation</h1>";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; margin-bottom: 20px;'>";
echo "<h2 style='margin-top: 0;'>🏆 Category Creation Test</h2>";
echo "<p>Testing the Add New Category functionality in sport-categories.php</p>";
echo "</div>";

try {
    // Get available event-sport combinations for testing
    $stmt = $conn->prepare("
        SELECT 
            es.id as event_sport_id,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name,
            COUNT(sc.id) as existing_categories
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN sport_categories sc ON es.id = sc.event_sport_id
        GROUP BY es.id, es.event_id, es.sport_id, e.name, s.name
        ORDER BY e.name, s.name
        LIMIT 10
    ");
    $stmt->execute();
    $event_sports = $stmt->fetchAll();
    
    if (empty($event_sports)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ No Event-Sport Combinations Found</h3>";
        echo "<p>No event-sport combinations available for testing. Please create events and add sports first.</p>";
        echo "</div>";
        exit;
    }
    
    echo "<h2>1. Available Event-Sport Combinations</h2>";
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
    echo "<tr style='background: #007bff; color: white;'>";
    echo "<th>Event</th><th>Sport</th><th>Existing Categories</th><th>Test Category Creation</th>";
    echo "</tr>";
    
    foreach ($event_sports as $es) {
        $test_url = "sport-categories.php?event_id={$es['event_id']}&sport_id={$es['sport_id']}";
        
        echo "<tr>";
        echo "<td>{$es['event_name']}</td>";
        echo "<td>{$es['sport_name']}</td>";
        echo "<td style='text-align: center;'>{$es['existing_categories']}</td>";
        echo "<td><a href='$test_url' target='_blank' style='background: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Test Add Category</a></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>2. Test Category Creation via AJAX</h2>";
    
    // Select first event-sport for testing
    $test_es = $event_sports[0];
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
    echo "<h4>Testing with: {$test_es['event_name']} - {$test_es['sport_name']}</h4>";
    echo "<p>Event Sport ID: {$test_es['event_sport_id']}</p>";
    
    echo "<form id='testCategoryForm' style='margin-top: 15px;'>";
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;'>";
    
    echo "<div>";
    echo "<label>Category Name:</label>";
    echo "<input type='text' id='test_category_name' value='Test Category " . date('His') . "' style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;'>";
    echo "</div>";
    
    echo "<div>";
    echo "<label>Category Type:</label>";
    echo "<select id='test_category_type' style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;'>";
    echo "<option value='men'>Men's</option>";
    echo "<option value='women'>Women's</option>";
    echo "<option value='mixed'>Mixed</option>";
    echo "<option value='open'>Open</option>";
    echo "</select>";
    echo "</div>";
    
    echo "<div>";
    echo "<label>Referee Name:</label>";
    echo "<input type='text' id='test_referee_name' value='Test Referee' style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;'>";
    echo "</div>";
    
    echo "<div>";
    echo "<label>Referee Email:</label>";
    echo "<input type='email' id='test_referee_email' value='<EMAIL>' style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;'>";
    echo "</div>";
    
    echo "<div>";
    echo "<label>Venue:</label>";
    echo "<input type='text' id='test_venue' value='Test Venue' style='width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;'>";
    echo "</div>";
    
    echo "</div>";
    
    echo "<button type='button' onclick='testCategoryCreation({$test_es['event_sport_id']})' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin-top: 15px; cursor: pointer;'>Test Create Category</button>";
    echo "</form>";
    
    echo "<div id='testResult' style='margin-top: 15px;'></div>";
    echo "</div>";
    
    echo "<h2>3. Manual Testing Instructions</h2>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px;'>";
    echo "<h4>How to Test Category Creation:</h4>";
    echo "<ol>";
    echo "<li><strong>Click 'Test Add Category' link</strong> for any event-sport combination above</li>";
    echo "<li><strong>Click 'Add New Category' button</strong> on the sport-categories page</li>";
    echo "<li><strong>Fill out the modal form:</strong>";
    echo "<ul>";
    echo "<li>Category Name (required)</li>";
    echo "<li>Category Type (required)</li>";
    echo "<li>Referee Name (optional)</li>";
    echo "<li>Referee Email (optional)</li>";
    echo "<li>Venue (optional)</li>";
    echo "</ul>";
    echo "</li>";
    echo "<li><strong>Click 'Create Category'</strong> to submit</li>";
    echo "<li><strong>Verify:</strong> Page should reload and show the new category in the list</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>4. Expected Behavior</h2>";
    
    echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;'>";
    
    echo "<div style='border: 1px solid #28a745; border-radius: 8px; padding: 15px;'>";
    echo "<h4 style='color: #28a745; margin-top: 0;'>✅ Success Case</h4>";
    echo "<ul>";
    echo "<li>Modal opens when button is clicked</li>";
    echo "<li>Form validation works (required fields)</li>";
    echo "<li>AJAX submission succeeds</li>";
    echo "<li>Success notification appears</li>";
    echo "<li>Page reloads showing new category</li>";
    echo "<li>Category appears in the table</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<div style='border: 1px solid #dc3545; border-radius: 8px; padding: 15px;'>";
    echo "<h4 style='color: #dc3545; margin-top: 0;'>❌ Error Cases</h4>";
    echo "<ul>";
    echo "<li>Empty required fields show validation</li>";
    echo "<li>Duplicate category names are rejected</li>";
    echo "<li>Invalid email format is rejected</li>";
    echo "<li>Database errors show error message</li>";
    echo "<li>Network errors are handled gracefully</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<script>
function testCategoryCreation(eventSportId) {
    const resultDiv = document.getElementById('testResult');
    resultDiv.innerHTML = '<p style="color: #007bff;">Testing category creation...</p>';
    
    // Get form data
    const formData = new FormData();
    formData.append('entity', 'sport_category');
    formData.append('action', 'create');
    formData.append('event_sport_id', eventSportId);
    formData.append('category_name', document.getElementById('test_category_name').value);
    formData.append('category_type', document.getElementById('test_category_type').value);
    formData.append('referee_name', document.getElementById('test_referee_name').value);
    formData.append('referee_email', document.getElementById('test_referee_email').value);
    formData.append('venue', document.getElementById('test_venue').value);
    formData.append('csrf_token', 'test_token');
    
    fetch('ajax/modal-handler.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            resultDiv.innerHTML = `
                <div style="background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;">
                    <h4 style="color: #155724; margin-top: 0;">✅ Success!</h4>
                    <p><strong>Message:</strong> ${data.message}</p>
                    <p><strong>Category ID:</strong> ${data.id}</p>
                    <p>The category was created successfully. You can now test the UI by visiting the sport-categories page.</p>
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div style="background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545;">
                    <h4 style="color: #721c24; margin-top: 0;">❌ Error</h4>
                    <p><strong>Message:</strong> ${data.message}</p>
                    <p>The category creation failed. Check the error message above.</p>
                </div>
            `;
        }
    })
    .catch(error => {
        resultDiv.innerHTML = `
            <div style="background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545;">
                <h4 style="color: #721c24; margin-top: 0;">❌ Network Error</h4>
                <p><strong>Error:</strong> ${error.message}</p>
                <p>There was a network error while testing category creation.</p>
            </div>
        `;
    });
}
</script>
