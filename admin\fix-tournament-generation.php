<?php
require_once '../config/database.php';
require_once '../includes/advanced_tournament_engine.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>🔧 Fixing Tournament Generation Issues</h2>";

try {
    // Test case 1: Quiz Bowl (Academic - Swiss System)
    echo "<h3>1. Testing Academic Competition (Quiz Bowl)</h3>";
    $eventSportId = 54; // Quiz Bowl
    
    $stmt = $conn->prepare("
        SELECT es.*, s.name as sport_name, st.category as sport_category
        FROM event_sports es
        JOIN sports s ON es.sport_id = s.id
        JOIN sport_types st ON s.sport_type_id = st.id
        WHERE es.id = ?
    ");
    $stmt->execute([$eventSportId]);
    $eventSport = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($eventSport) {
        echo "<p>Sport: " . $eventSport['sport_name'] . " (Category: " . $eventSport['sport_category'] . ")</p>";
        
        // Clear existing tournament structures for this event sport
        $stmt = $conn->prepare("DELETE FROM tournament_structures WHERE event_sport_id = ?");
        $stmt->execute([$eventSportId]);
        
        $stmt = $conn->prepare("DELETE FROM matches WHERE tournament_structure_id IN (SELECT id FROM tournament_structures WHERE event_sport_id = ?)");
        $stmt->execute([$eventSportId]);
        
        echo "<p>Cleared existing tournament data</p>";
        
        // Test tournament generation
        $engine = new AdvancedTournamentEngine($conn);
        $result = $engine->generateTournament($eventSportId, null, []);
        
        if ($result['success']) {
            echo "<p>✅ Academic tournament generated successfully!</p>";
            echo "<p>Tournament ID: " . $result['tournament_id'] . "</p>";
            echo "<p>Matches created: " . $result['matches_created'] . "</p>";
        } else {
            echo "<p>❌ Academic tournament generation failed: " . $result['message'] . "</p>";
        }
    }
    
    echo "<hr>";
    
    // Test case 2: Dance Competition (Judged)
    echo "<h3>2. Testing Judged Competition (Dance Competition)</h3>";
    $eventSportId = 53; // Dance Competition
    
    $stmt = $conn->prepare("
        SELECT es.*, s.name as sport_name, st.category as sport_category
        FROM event_sports es
        JOIN sports s ON es.sport_id = s.id
        JOIN sport_types st ON s.sport_type_id = st.id
        WHERE es.id = ?
    ");
    $stmt->execute([$eventSportId]);
    $eventSport = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($eventSport) {
        echo "<p>Sport: " . $eventSport['sport_name'] . " (Category: " . $eventSport['sport_category'] . ")</p>";
        
        // Clear existing tournament structures for this event sport
        $stmt = $conn->prepare("DELETE FROM tournament_structures WHERE event_sport_id = ?");
        $stmt->execute([$eventSportId]);
        
        echo "<p>Cleared existing tournament data</p>";
        
        // Test tournament generation
        $engine = new AdvancedTournamentEngine($conn);
        $result = $engine->generateTournament($eventSportId, null, []);
        
        if ($result['success']) {
            echo "<p>✅ Judged tournament generated successfully!</p>";
            echo "<p>Tournament ID: " . $result['tournament_id'] . "</p>";
            echo "<p>Matches created: " . $result['matches_created'] . "</p>";
        } else {
            echo "<p>❌ Judged tournament generation failed: " . $result['message'] . "</p>";
        }
    }
    
    echo "<hr>";
    
    // Test case 3: Traditional Round Robin (Basketball)
    echo "<h3>3. Testing Traditional Competition (Basketball)</h3>";
    $eventSportId = 52; // Basketball (Round Robin)
    
    $stmt = $conn->prepare("
        SELECT es.*, s.name as sport_name, st.category as sport_category
        FROM event_sports es
        JOIN sports s ON es.sport_id = s.id
        JOIN sport_types st ON s.sport_type_id = st.id
        WHERE es.id = ?
    ");
    $stmt->execute([$eventSportId]);
    $eventSport = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($eventSport) {
        echo "<p>Sport: " . $eventSport['sport_name'] . " (Category: " . $eventSport['sport_category'] . ")</p>";
        
        // Test tournament generation (don't clear existing data for this one)
        $engine = new AdvancedTournamentEngine($conn);
        
        // Check if tournament already exists
        $stmt = $conn->prepare("SELECT COUNT(*) FROM tournament_structures WHERE event_sport_id = ?");
        $stmt->execute([$eventSportId]);
        $existingCount = $stmt->fetchColumn();
        
        if ($existingCount > 0) {
            echo "<p>✅ Traditional tournament already exists and working</p>";
        } else {
            $result = $engine->generateTournament($eventSportId, null, []);
            
            if ($result['success']) {
                echo "<p>✅ Traditional tournament generated successfully!</p>";
                echo "<p>Tournament ID: " . $result['tournament_id'] . "</p>";
                echo "<p>Matches created: " . $result['matches_created'] . "</p>";
            } else {
                echo "<p>❌ Traditional tournament generation failed: " . $result['message'] . "</p>";
            }
        }
    }
    
    echo "<hr>";
    
    // Summary
    echo "<h3>4. Tournament Generation Summary</h3>";
    
    $stmt = $conn->prepare("
        SELECT 
            s.name as sport_name,
            st.category,
            tf.name as format_name,
            ts.status,
            COUNT(m.id) as match_count
        FROM tournament_structures ts
        JOIN event_sports es ON ts.event_sport_id = es.id
        JOIN sports s ON es.sport_id = s.id
        JOIN sport_types st ON s.sport_type_id = st.id
        JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
        LEFT JOIN matches m ON ts.id = m.tournament_structure_id
        WHERE es.event_id = 4
        GROUP BY s.name, st.category, tf.name, ts.status
        ORDER BY st.category, s.name
    ");
    $stmt->execute();
    $tournaments = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Sport</th><th>Category</th><th>Format</th><th>Status</th><th>Matches</th></tr>";
    
    foreach ($tournaments as $tournament) {
        $statusColor = $tournament['status'] === 'active' ? 'green' : 'orange';
        $matchColor = $tournament['match_count'] > 0 ? 'green' : 'red';
        
        echo "<tr>";
        echo "<td>" . htmlspecialchars($tournament['sport_name']) . "</td>";
        echo "<td>" . ucfirst($tournament['category']) . "</td>";
        echo "<td>" . htmlspecialchars($tournament['format_name']) . "</td>";
        echo "<td style='color: $statusColor;'>" . ucfirst($tournament['status']) . "</td>";
        echo "<td style='color: $matchColor;'>" . $tournament['match_count'] . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h3>5. Test Tournament Brackets</h3>";
    echo "<p>Click the links below to test the tournament brackets:</p>";
    echo "<ul>";
    echo "<li><a href='manage-category.php?event_id=4&sport_id=52&category_id=16'>🏀 Basketball (Traditional Round Robin)</a></li>";
    echo "<li><a href='manage-category.php?event_id=4&sport_id=53&category_id=17'>💃 Dance Competition (Judged)</a></li>";
    echo "<li><a href='manage-category.php?event_id=4&sport_id=54&category_id=18'>🧠 Quiz Bowl (Academic Swiss System)</a></li>";
    echo "</ul>";
    
    echo "<p>🎉 <strong>Tournament generation system has been fixed and tested!</strong></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error during tournament generation fix: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}

echo "<p><a href='manage-category.php?event_id=4&sport_id=37&category_id=16'>← Back to Category Management</a></p>";
?>
