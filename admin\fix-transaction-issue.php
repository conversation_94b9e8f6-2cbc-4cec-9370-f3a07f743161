<?php
/**
 * Fix Active Transaction Issue
 * This script will check for and close any active transactions
 */

require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Transaction Status Check and Fix</h2>";

try {
    // Check if there's an active transaction
    $inTransaction = $conn->inTransaction();
    echo "<p><strong>Transaction Status:</strong> " . ($inTransaction ? "ACTIVE" : "NONE") . "</p>";
    
    if ($inTransaction) {
        echo "<p style='color: orange;'>⚠️ Found active transaction. Attempting to close it...</p>";
        
        try {
            // Try to rollback the active transaction
            $conn->rollBack();
            echo "<p style='color: green;'>✅ Successfully rolled back active transaction</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to rollback: " . $e->getMessage() . "</p>";
            
            // Try to commit instead
            try {
                $conn->commit();
                echo "<p style='color: green;'>✅ Successfully committed active transaction</p>";
            } catch (Exception $e2) {
                echo "<p style='color: red;'>❌ Failed to commit: " . $e2->getMessage() . "</p>";
            }
        }
        
        // Check again
        $inTransaction = $conn->inTransaction();
        echo "<p><strong>Transaction Status After Fix:</strong> " . ($inTransaction ? "STILL ACTIVE" : "CLOSED") . "</p>";
    } else {
        echo "<p style='color: green;'>✅ No active transaction found</p>";
    }
    
    // Test a simple transaction to ensure everything works
    echo "<h3>Testing Transaction Functionality</h3>";
    
    $conn->beginTransaction();
    echo "<p>✅ Started test transaction</p>";
    
    $conn->rollBack();
    echo "<p>✅ Rolled back test transaction</p>";
    
    echo "<p style='color: green;'><strong>✅ Transaction system is working properly!</strong></p>";
    
    echo "<h3>Next Steps</h3>";
    echo "<p>1. Go back to your category management page</p>";
    echo "<p>2. Try generating the tournament again</p>";
    echo "<p>3. The 'active transaction' error should be resolved</p>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='manage-category.php?event_id=4&sport_id=37&category_id=16' class='btn btn-primary' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Return to Category Management</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
