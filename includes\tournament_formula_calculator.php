<?php
/**
 * Database-Driven Tournament Formula Calculator
 * SC_IMS Sports Competition and Event Management System
 * 
 * Calculates tournament parameters using database-stored formulas
 * instead of hardcoded values for complete flexibility
 */

class TournamentFormulaCalculator {
    private $conn;
    
    public function __construct($conn) {
        $this->conn = $conn;
    }
    
    /**
     * Calculate number of rounds using database formula
     */
    public function calculateRounds($format, $participantCount) {
        try {
            $formula = $format['rounds_formula'] ?? 'ceil(log2(n))';
            return $this->evaluateFormula($formula, $participantCount, $format);
        } catch (Exception $e) {
            error_log("Error calculating rounds: " . $e->getMessage());
            // Fallback to single elimination formula
            return ceil(log($participantCount, 2));
        }
    }
    
    /**
     * Calculate number of matches using database formula
     */
    public function calculateMatches($format, $participantCount) {
        try {
            $formula = $format['matches_formula'] ?? 'n-1';
            return $this->evaluateFormula($formula, $participantCount, $format);
        } catch (Exception $e) {
            error_log("Error calculating matches: " . $e->getMessage());
            // Fallback to single elimination formula
            return $participantCount - 1;
        }
    }
    
    /**
     * Evaluate mathematical formula with participant count
     */
    private function evaluateFormula($formula, $participantCount, $format) {
        // Handle special cases
        if ($formula === 'variable') {
            return $this->calculateVariableFormula($format, $participantCount);
        }
        
        // Replace 'n' with actual participant count
        $expression = str_replace('n', $participantCount, $formula);
        
        // Evaluate mathematical expression safely
        return $this->safeEvaluate($expression, $participantCount, $format);
    }
    
    /**
     * Handle variable formulas that depend on format configuration
     */
    private function calculateVariableFormula($format, $participantCount) {
        $config = json_decode($format['configuration'] ?? '{}', true);
        
        switch ($format['code']) {
            case 'multi_stage':
                return $this->calculateMultiStageFormula($participantCount, $config);
                
            case 'league_format':
                return $this->calculateLeagueFormula($participantCount, $config);
                
            default:
                // Default to single elimination
                return ceil(log($participantCount, 2));
        }
    }
    
    /**
     * Calculate multi-stage tournament parameters
     */
    private function calculateMultiStageFormula($participantCount, $config) {
        $groupsCount = $config['groups_count'] ?? 4;
        $advancePerGroup = $config['advance_per_group'] ?? 2;
        
        // Group stage rounds (round robin within groups)
        $participantsPerGroup = ceil($participantCount / $groupsCount);
        $groupStageRounds = max(1, $participantsPerGroup - 1);
        
        // Knockout stage rounds
        $advancingParticipants = $groupsCount * $advancePerGroup;
        $knockoutRounds = ceil(log($advancingParticipants, 2));
        
        return $groupStageRounds + $knockoutRounds;
    }
    
    /**
     * Calculate league format parameters
     */
    private function calculateLeagueFormula($participantCount, $config) {
        $homeAway = $config['home_away'] ?? false;
        $multiplier = $homeAway ? 2 : 1;
        
        // Each team plays each other team once (or twice for home/away)
        return ($participantCount - 1) * $multiplier;
    }
    
    /**
     * Safely evaluate mathematical expressions using pattern matching instead of eval
     */
    private function safeEvaluate($expression, $participantCount, $format) {
        try {
            // Use pattern-based evaluation instead of eval for safety
            $result = $this->evaluateFormulaPattern($expression, $participantCount);

            // Ensure result is a positive integer
            return max(1, (int)round($result));

        } catch (Exception $e) {
            error_log("Formula evaluation error: $expression - " . $e->getMessage());
            // Return fallback value based on participant count
            return $this->getFallbackValue($participantCount);
        }
    }

    /**
     * Evaluate formula using pattern matching (safer than eval)
     */
    private function evaluateFormulaPattern($formula, $n) {
        $formula = strtolower(trim($formula));

        // Single elimination rounds: ceil(log2(n))
        if (preg_match('/ceil\s*\(\s*log2?\s*\(\s*n\s*\)\s*\)/', $formula)) {
            return ceil(log($n, 2));
        }

        // Single elimination matches: n - 1
        if (preg_match('/^n\s*-\s*1$/', $formula)) {
            return $n - 1;
        }

        // Round robin rounds: n - 1
        if (preg_match('/^n\s*-\s*1$/', $formula)) {
            return $n - 1;
        }

        // Round robin matches: n * (n - 1) / 2
        if (preg_match('/n\s*\*\s*\(\s*n\s*-\s*1\s*\)\s*\/\s*2/', $formula)) {
            return ($n * ($n - 1)) / 2;
        }

        // Double elimination: 2 * ceil(log2(n)) - 1
        if (preg_match('/2\s*\*\s*ceil\s*\(\s*log2?\s*\(\s*n\s*\)\s*\)\s*-\s*1/', $formula)) {
            return 2 * ceil(log($n, 2)) - 1;
        }

        // Swiss system rounds: ceil(log2(n))
        if (preg_match('/ceil\s*\(\s*log2?\s*\(\s*n\s*\)\s*\)/', $formula)) {
            return ceil(log($n, 2));
        }

        // Simple numeric value
        if (is_numeric($formula)) {
            return (float)$formula;
        }

        // If no pattern matches, use fallback
        throw new Exception("Unrecognized formula pattern: $formula");
    }

    /**
     * Get fallback value when formula evaluation fails
     */
    private function getFallbackValue($participantCount) {
        // Default to single elimination formula
        return ceil(log($participantCount, 2));
    }
    
    /**
     * Replace mathematical functions with PHP equivalents
     * NOTE: Currently unused - using pattern-based evaluation instead
     */
    private function replaceMathFunctions($expression) {
        $replacements = [
            'ceil(' => 'ceil(',
            'floor(' => 'floor(',
            'round(' => 'round(',
            'log2(' => 'log(',
            'log(' => 'log(',
            'sqrt(' => 'sqrt(',
            'pow(' => 'pow(',
            'abs(' => 'abs(',
            'min(' => 'min(',
            'max(' => 'max('
        ];
        
        foreach ($replacements as $search => $replace) {
            $expression = str_replace($search, $replace, $expression);
        }
        
        // Handle log2 specifically
        $expression = preg_replace('/log\(([^,]+)\)/', 'log($1, 2)', $expression);
        
        return $expression;
    }
    
    /**
     * Validate mathematical expression for safety
     */
    private function isValidExpression($expression) {
        // Check for balanced parentheses
        if (substr_count($expression, '(') !== substr_count($expression, ')')) {
            return false;
        }
        
        // Check for valid characters only
        if (!preg_match('/^[0-9+\-*\/\(\)\.\s]+$/', $expression)) {
            return false;
        }
        
        // Check for division by zero
        if (preg_match('/\/\s*0/', $expression)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Calculate bracket structure parameters
     */
    public function calculateBracketStructure($format, $participantCount) {
        $rounds = $this->calculateRounds($format, $participantCount);
        $matches = $this->calculateMatches($format, $participantCount);
        
        $structure = [
            'total_rounds' => $rounds,
            'total_matches' => $matches,
            'participants_per_round' => $this->calculateParticipantsPerRound($format, $participantCount, $rounds),
            'matches_per_round' => $this->calculateMatchesPerRound($format, $participantCount, $rounds),
            'bye_participants' => $this->calculateByeParticipants($format, $participantCount),
            'advancement_rules' => $this->getAdvancementRules($format)
        ];
        
        return $structure;
    }
    
    /**
     * Calculate participants per round
     */
    private function calculateParticipantsPerRound($format, $participantCount, $totalRounds) {
        $participants = [];
        $current = $participantCount;
        
        switch ($format['advancement_type']) {
            case 'elimination':
                for ($round = 1; $round <= $totalRounds; $round++) {
                    $participants[$round] = $current;
                    $current = ceil($current / 2);
                }
                break;
                
            case 'points':
            case 'ranking':
                // All participants play in all rounds
                for ($round = 1; $round <= $totalRounds; $round++) {
                    $participants[$round] = $participantCount;
                }
                break;
                
            case 'hybrid':
                // Complex calculation based on format configuration
                $participants = $this->calculateHybridParticipants($format, $participantCount, $totalRounds);
                break;
        }
        
        return $participants;
    }
    
    /**
     * Calculate matches per round
     */
    private function calculateMatchesPerRound($format, $participantCount, $totalRounds) {
        $matches = [];
        $participantsPerRound = $this->calculateParticipantsPerRound($format, $participantCount, $totalRounds);
        
        foreach ($participantsPerRound as $round => $participants) {
            switch ($format['advancement_type']) {
                case 'elimination':
                    $matches[$round] = floor($participants / 2);
                    break;
                    
                case 'points':
                    if ($format['code'] === 'round_robin') {
                        $matches[$round] = $participants * ($participants - 1) / 2;
                    } else {
                        $matches[$round] = floor($participants / 2);
                    }
                    break;
                    
                default:
                    $matches[$round] = floor($participants / 2);
            }
        }
        
        return $matches;
    }
    
    /**
     * Calculate bye participants (participants who advance without playing)
     */
    private function calculateByeParticipants($format, $participantCount) {
        if (!$format['supports_byes']) {
            return 0;
        }
        
        // Calculate next power of 2
        $nextPowerOf2 = pow(2, ceil(log($participantCount, 2)));
        return $nextPowerOf2 - $participantCount;
    }
    
    /**
     * Get advancement rules from format configuration
     */
    private function getAdvancementRules($format) {
        $config = json_decode($format['configuration'] ?? '{}', true);
        
        $rules = [
            'type' => $format['advancement_type'],
            'elimination_type' => $format['code'],
            'seeding_required' => $format['requires_seeding'],
            'bye_handling' => $config['bye_handling'] ?? 'automatic'
        ];
        
        // Add format-specific rules
        switch ($format['advancement_type']) {
            case 'points':
                $rules['points_win'] = $config['points_win'] ?? 3;
                $rules['points_draw'] = $config['points_draw'] ?? 1;
                $rules['points_loss'] = $config['points_loss'] ?? 0;
                break;
                
            case 'elimination':
                $rules['third_place_match'] = $config['third_place_match'] ?? false;
                $rules['bracket_seeding'] = $config['bracket_seeding'] ?? true;
                break;
        }
        
        return $rules;
    }
    
    /**
     * Calculate hybrid tournament participants (multi-stage, etc.)
     */
    private function calculateHybridParticipants($format, $participantCount, $totalRounds) {
        $config = json_decode($format['configuration'] ?? '{}', true);
        $participants = [];
        
        if ($format['code'] === 'multi_stage') {
            $groupsCount = $config['groups_count'] ?? 4;
            $advancePerGroup = $config['advance_per_group'] ?? 2;
            $groupStageRounds = max(1, ceil($participantCount / $groupsCount) - 1);
            
            // Group stage
            for ($round = 1; $round <= $groupStageRounds; $round++) {
                $participants[$round] = $participantCount;
            }
            
            // Knockout stage
            $advancingParticipants = $groupsCount * $advancePerGroup;
            $current = $advancingParticipants;
            for ($round = $groupStageRounds + 1; $round <= $totalRounds; $round++) {
                $participants[$round] = $current;
                $current = ceil($current / 2);
            }
        }
        
        return $participants;
    }
}
?>
