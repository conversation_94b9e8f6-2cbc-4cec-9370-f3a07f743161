<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🏆 Tournament Management Status Dashboard</h1>";
echo "<p>Complete overview of tournament system status and quick fixes...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $event_id = $_GET['event_id'] ?? 4;
    $sport_id = $_GET['sport_id'] ?? 37;
    $category_id = $_GET['category_id'] ?? 15;
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎯 Current Context</h3>";
    echo "<p><strong>Event ID:</strong> {$event_id} | <strong>Sport ID:</strong> {$sport_id} | <strong>Category ID:</strong> {$category_id}</p>";
    echo "</div>";
    
    $status_summary = [];
    $quick_fixes = [];
    
    // ===== 1. CATEGORY AND EVENT SPORT STATUS =====
    echo "<h2>1. 📋 Category and Event Sport Status</h2>";
    
    $stmt = $conn->prepare("
        SELECT
            sc.*,
            e.name as event_name,
            s.name as sport_name,
            s.type as sport_type,
            es.id as event_sport_id,
            es.tournament_format_id,
            es.bracket_type
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ? AND es.event_id = ? AND s.id = ?
    ");
    $stmt->execute([$category_id, $event_id, $sport_id]);
    $category = $stmt->fetch();
    
    if ($category) {
        echo "<p style='color: green;'>✅ Category found: {$category['category_name']}</p>";
        echo "<p style='color: green;'>✅ Event Sport ID: {$category['event_sport_id']}</p>";
        $status_summary['category_found'] = true;
    } else {
        echo "<p style='color: red;'>❌ Category not found with given parameters</p>";
        $status_summary['category_found'] = false;
        $quick_fixes[] = "Verify category ID, event ID, and sport ID parameters";
    }
    
    // ===== 2. TOURNAMENT FORMAT STATUS =====
    echo "<h2>2. 🎮 Tournament Format Status</h2>";
    
    if ($category) {
        if ($category['tournament_format_id']) {
            $stmt = $conn->prepare("SELECT name, description FROM tournament_formats WHERE id = ?");
            $stmt->execute([$category['tournament_format_id']]);
            $format = $stmt->fetch();
            
            if ($format) {
                echo "<p style='color: green;'>✅ Tournament format configured: {$format['name']}</p>";
                echo "<p style='color: green;'>✅ Format ID: {$category['tournament_format_id']}</p>";
                $status_summary['format_configured'] = true;
            } else {
                echo "<p style='color: red;'>❌ Tournament format ID exists but format not found in database</p>";
                $status_summary['format_configured'] = false;
                $quick_fixes[] = "Fix tournament format reference or configure new format";
            }
        } else {
            echo "<p style='color: red;'>❌ No tournament format configured</p>";
            if ($category['bracket_type']) {
                echo "<p style='color: orange;'>⚠️ Legacy bracket type found: {$category['bracket_type']}</p>";
            }
            $status_summary['format_configured'] = false;
            $quick_fixes[] = "Configure tournament format for event sport";
        }
    }
    
    // ===== 3. PARTICIPANT STATUS =====
    echo "<h2>3. 👥 Participant Status</h2>";
    
    if ($category) {
        $stmt = $conn->prepare("
            SELECT COUNT(*) as count
            FROM registrations r
            WHERE r.event_sport_id = ? AND r.status IN ('confirmed', 'registered', 'approved')
        ");
        $stmt->execute([$category['event_sport_id']]);
        $participant_count = $stmt->fetch()['count'];
        
        echo "<p><strong>Registered Participants:</strong> {$participant_count}</p>";
        
        if ($participant_count >= 2) {
            echo "<p style='color: green;'>✅ Sufficient participants for tournament</p>";
            $status_summary['sufficient_participants'] = true;
        } else {
            echo "<p style='color: red;'>❌ Insufficient participants (need at least 2)</p>";
            $status_summary['sufficient_participants'] = false;
            $quick_fixes[] = "Add more participants to the event sport";
        }
    }
    
    // ===== 4. DATABASE STRUCTURE STATUS =====
    echo "<h2>4. 🗄️ Database Structure Status</h2>";
    
    $required_tables = [
        'tournament_formats' => ['id', 'name', 'code', 'algorithm_class'],
        'tournament_structures' => ['id', 'event_sport_id', 'tournament_format_id', 'name'],
        'tournament_participants' => ['id', 'tournament_structure_id', 'registration_id'],
        'tournament_rounds' => ['id', 'tournament_structure_id', 'round_number']
    ];
    
    $database_ok = true;
    foreach ($required_tables as $table => $columns) {
        try {
            $stmt = $conn->query("DESCRIBE {$table}");
            $existing_columns = array_column($stmt->fetchAll(), 'Field');
            
            $missing_columns = array_diff($columns, $existing_columns);
            if (empty($missing_columns)) {
                echo "<p style='color: green;'>✅ Table {$table} is complete</p>";
            } else {
                echo "<p style='color: red;'>❌ Table {$table} missing columns: " . implode(', ', $missing_columns) . "</p>";
                $database_ok = false;
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Table {$table} does not exist</p>";
            $database_ok = false;
        }
    }
    
    // Check matches table for tournament columns
    try {
        $stmt = $conn->query("DESCRIBE matches");
        $match_columns = array_column($stmt->fetchAll(), 'Field');
        
        if (in_array('tournament_structure_id', $match_columns)) {
            echo "<p style='color: green;'>✅ Matches table has tournament_structure_id column</p>";
        } else {
            echo "<p style='color: red;'>❌ Matches table missing tournament_structure_id column</p>";
            $database_ok = false;
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Cannot check matches table structure</p>";
        $database_ok = false;
    }
    
    $status_summary['database_structure'] = $database_ok;
    if (!$database_ok) {
        $quick_fixes[] = "Fix database structure using table checker tool";
    }
    
    // ===== 5. EXISTING TOURNAMENT CHECK =====
    echo "<h2>5. 🏆 Existing Tournament Check</h2>";
    
    if ($category) {
        $stmt = $conn->prepare("
            SELECT id, name, status, created_at
            FROM tournament_structures 
            WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')
            ORDER BY created_at DESC
        ");
        $stmt->execute([$category['event_sport_id']]);
        $active_tournaments = $stmt->fetchAll();
        
        if (empty($active_tournaments)) {
            echo "<p style='color: green;'>✅ No active tournaments - ready for auto-generation</p>";
            $status_summary['no_active_tournaments'] = true;
        } else {
            echo "<p style='color: red;'>❌ Active tournaments exist:</p>";
            foreach ($active_tournaments as $t) {
                echo "<p style='margin-left: 20px;'>- {$t['name']} (Status: {$t['status']}, ID: {$t['id']})</p>";
            }
            $status_summary['no_active_tournaments'] = false;
            $quick_fixes[] = "Cancel or complete existing tournaments before auto-generation";
        }
    }
    
    // ===== 6. OVERALL STATUS SUMMARY =====
    echo "<h2>6. 📊 Overall Status Summary</h2>";
    
    $all_requirements_met = true;
    $requirements = [
        'Category Found' => $status_summary['category_found'] ?? false,
        'Tournament Format Configured' => $status_summary['format_configured'] ?? false,
        'Sufficient Participants' => $status_summary['sufficient_participants'] ?? false,
        'Database Structure Complete' => $status_summary['database_structure'] ?? false,
        'No Active Tournaments' => $status_summary['no_active_tournaments'] ?? false
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th style='padding: 10px;'>Requirement</th><th style='padding: 10px;'>Status</th>";
    echo "</tr>";
    
    foreach ($requirements as $requirement => $status) {
        echo "<tr>";
        echo "<td style='padding: 10px;'>{$requirement}</td>";
        if ($status) {
            echo "<td style='padding: 10px; color: green; text-align: center; font-weight: bold;'>✅ PASS</td>";
        } else {
            echo "<td style='padding: 10px; color: red; text-align: center; font-weight: bold;'>❌ FAIL</td>";
            $all_requirements_met = false;
        }
        echo "</tr>";
    }
    echo "</table>";
    
    // ===== 7. ACTION PLAN =====
    echo "<h2>7. 🎯 Action Plan</h2>";
    
    if ($all_requirements_met) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 3px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🎉 ALL REQUIREMENTS MET!</h3>";
        echo "<p style='font-size: 18px;'>Auto-generation should work perfectly now.</p>";
        echo "<p><a href='manage-category.php?category_id={$category_id}&event_id={$event_id}&sport_id={$sport_id}' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 18px; font-weight: bold;'>🚀 TEST AUTO-GENERATION</a></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border: 3px solid #dc3545; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ REQUIREMENTS NOT MET</h3>";
        echo "<p>Complete the following fixes in order:</p>";
        echo "<ol style='font-size: 16px;'>";
        
        if (!$status_summary['database_structure']) {
            echo "<li><a href='check-tournament-tables.php' style='background: #dc3545; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>🔧 Fix Database Structure</a></li>";
        }
        
        if (!$status_summary['format_configured']) {
            echo "<li><a href='fix-tournament-format.php?event_id={$event_id}&sport_id={$sport_id}' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>⚙️ Configure Tournament Format</a></li>";
        }
        
        if (!$status_summary['sufficient_participants']) {
            echo "<li><a href='add-test-participants.php' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>👥 Add More Participants</a></li>";
        }
        
        if (!$status_summary['no_active_tournaments']) {
            echo "<li><a href='tournament-diagnostic-tool.php?event_id={$event_id}&sport_id={$sport_id}&category_id={$category_id}' style='background: #ffc107; color: black; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>🗑️ Cancel Active Tournaments</a></li>";
        }
        
        echo "</ol>";
        echo "</div>";
    }
    
    echo "<h3>🔍 Additional Diagnostic Tools:</h3>";
    echo "<p>";
    echo "<a href='debug-format-display.php?event_id={$event_id}&sport_id={$sport_id}&category_id={$category_id}' style='background: #6c757d; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; margin-right: 10px;'>🔍 Debug Format Display</a>";
    echo "<a href='tournament-diagnostic-tool.php?event_id={$event_id}&sport_id={$sport_id}&category_id={$category_id}' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>📊 Full Diagnostic Report</a>";
    echo "</p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Dashboard Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
