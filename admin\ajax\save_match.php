<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

header('Content-Type: application/json');

try {
    if (!isset($_POST['match_id'])) {
        throw new Exception('Missing match ID');
    }
    
    $match_id = $_POST['match_id'];
    $team1_id = !empty($_POST['team1_id']) ? (int)$_POST['team1_id'] : null;
    $team2_id = !empty($_POST['team2_id']) ? (int)$_POST['team2_id'] : null;
    $team1_score = !empty($_POST['team1_score']) ? (int)$_POST['team1_score'] : null;
    $team2_score = !empty($_POST['team2_score']) ? (int)$_POST['team2_score'] : null;
    $status = $_POST['status'] ?? 'pending';
    $scheduled_time = !empty($_POST['scheduled_time']) ? $_POST['scheduled_time'] : null;
    $winner_id = !empty($_POST['winner_id']) ? (int)$_POST['winner_id'] : null;
    $notes = $_POST['notes'] ?? '';
    
    // Validate status
    $valid_statuses = ['pending', 'in_progress', 'completed'];
    if (!in_array($status, $valid_statuses)) {
        throw new Exception('Invalid match status');
    }
    
    // Validate teams are different
    if ($team1_id && $team2_id && $team1_id === $team2_id) {
        throw new Exception('Teams must be different');
    }
    
    // Auto-determine winner if not set but scores are provided
    if (!$winner_id && $team1_score !== null && $team2_score !== null) {
        if ($team1_score > $team2_score) {
            $winner_id = $team1_id;
        } elseif ($team2_score > $team1_score) {
            $winner_id = $team2_id;
        }
        // If scores are equal, winner_id remains null (tie)
    }
    
    // If match is completed, ensure we have scores and teams
    if ($status === 'completed') {
        if (!$team1_id || !$team2_id) {
            throw new Exception('Both teams must be selected for completed matches');
        }
        if ($team1_score === null || $team2_score === null) {
            throw new Exception('Scores must be provided for completed matches');
        }
    }
    
    $pdo->beginTransaction();
    
    try {
        // Update match
        $stmt = $pdo->prepare("
            UPDATE tournament_matches 
            SET team1_id = ?, 
                team2_id = ?, 
                team1_score = ?, 
                team2_score = ?, 
                status = ?, 
                scheduled_time = ?, 
                winner_id = ?, 
                notes = ?,
                updated_at = NOW()
            WHERE id = ?
        ");
        
        $stmt->execute([
            $team1_id,
            $team2_id,
            $team1_score,
            $team2_score,
            $status,
            $scheduled_time,
            $winner_id,
            $notes,
            $match_id
        ]);
        
        if ($stmt->rowCount() === 0) {
            throw new Exception('Match not found or no changes made');
        }
        
        // If match is completed and has a winner, update tournament progression
        if ($status === 'completed' && $winner_id) {
            updateTournamentProgression($pdo, $match_id, $winner_id);
        }
        
        // Update tournament standings if needed
        updateTournamentStandings($pdo, $match_id);
        
        $pdo->commit();
        
        // Log admin activity
        logAdminActivity($_SESSION['admin_id'], 'update', 'tournament_match', $match_id, [
            'match_id' => $match_id,
            'team1_id' => $team1_id,
            'team2_id' => $team2_id,
            'team1_score' => $team1_score,
            'team2_score' => $team2_score,
            'status' => $status,
            'winner_id' => $winner_id
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => 'Match updated successfully'
        ]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
    
} catch (Exception $e) {
    error_log("Error in save_match.php: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Update tournament progression when a match is completed
 */
function updateTournamentProgression($pdo, $match_id, $winner_id) {
    // Get match details
    $stmt = $pdo->prepare("
        SELECT event_sport_id, round_number, match_number 
        FROM tournament_matches 
        WHERE id = ?
    ");
    $stmt->execute([$match_id]);
    $match = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$match) return;
    
    // Check if there's a next round match for this winner
    $next_round = $match['round_number'] + 1;
    $next_match_number = ceil($match['match_number'] / 2);
    
    $stmt = $pdo->prepare("
        SELECT id, team1_id, team2_id 
        FROM tournament_matches 
        WHERE event_sport_id = ? 
        AND round_number = ? 
        AND match_number = ?
    ");
    $stmt->execute([$match['event_sport_id'], $next_round, $next_match_number]);
    $next_match = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($next_match) {
        // Determine if winner goes to team1 or team2 slot
        $is_odd_match = ($match['match_number'] % 2 === 1);
        $team_slot = $is_odd_match ? 'team1_id' : 'team2_id';
        
        $stmt = $pdo->prepare("
            UPDATE tournament_matches 
            SET {$team_slot} = ? 
            WHERE id = ?
        ");
        $stmt->execute([$winner_id, $next_match['id']]);
    }
}

/**
 * Update tournament standings for round robin or point-based tournaments
 */
function updateTournamentStandings($pdo, $match_id) {
    // Get tournament format
    $stmt = $pdo->prepare("
        SELECT tf.type, es.id as event_sport_id
        FROM tournament_matches tm
        JOIN event_sports es ON tm.event_sport_id = es.id
        JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE tm.id = ?
    ");
    $stmt->execute([$match_id]);
    $format = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$format || $format['type'] !== 'round_robin') {
        return; // Only update standings for round robin tournaments
    }
    
    // Get match details
    $stmt = $pdo->prepare("
        SELECT team1_id, team2_id, team1_score, team2_score, winner_id, status
        FROM tournament_matches 
        WHERE id = ? AND status = 'completed'
    ");
    $stmt->execute([$match_id]);
    $match = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$match) return;
    
    // Update standings for both teams
    foreach (['team1_id', 'team2_id'] as $team_key) {
        $team_id = $match[$team_key];
        if (!$team_id) continue;
        
        $is_team1 = ($team_key === 'team1_id');
        $team_score = $is_team1 ? $match['team1_score'] : $match['team2_score'];
        $opponent_score = $is_team1 ? $match['team2_score'] : $match['team1_score'];
        
        $points = 0;
        $wins = 0;
        $losses = 0;
        $draws = 0;
        
        if ($match['winner_id'] == $team_id) {
            $wins = 1;
            $points = 3; // 3 points for win
        } elseif ($match['winner_id'] === null) {
            $draws = 1;
            $points = 1; // 1 point for draw
        } else {
            $losses = 1;
            $points = 0; // 0 points for loss
        }
        
        // Update or insert standings
        $stmt = $pdo->prepare("
            INSERT INTO tournament_standings 
            (event_sport_id, team_id, matches_played, wins, losses, draws, points_for, points_against, points)
            VALUES (?, ?, 1, ?, ?, ?, ?, ?, ?)
            ON DUPLICATE KEY UPDATE
            matches_played = matches_played + 1,
            wins = wins + VALUES(wins),
            losses = losses + VALUES(losses),
            draws = draws + VALUES(draws),
            points_for = points_for + VALUES(points_for),
            points_against = points_against + VALUES(points_against),
            points = points + VALUES(points)
        ");
        
        $stmt->execute([
            $format['event_sport_id'],
            $team_id,
            $wins,
            $losses,
            $draws,
            $team_score,
            $opponent_score,
            $points
        ]);
    }
}
?>
