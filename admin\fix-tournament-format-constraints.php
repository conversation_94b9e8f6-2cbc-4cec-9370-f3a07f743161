<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🔧 Fix Tournament Format Database Constraints</h1>";
echo "<p>Resolving foreign key constraints and ensuring proper tournament format references...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>1. Analyzing Current Database State</h2>";
    
    // Check if event_sports has tournament_format_id column
    $stmt = $conn->prepare("SHOW COLUMNS FROM event_sports LIKE 'tournament_format_id'");
    $stmt->execute();
    $has_tournament_format_id = $stmt->fetch();
    
    if (!$has_tournament_format_id) {
        echo "<p style='color: orange;'>⚠️ event_sports table missing tournament_format_id column. Adding...</p>";
        $conn->exec("ALTER TABLE event_sports ADD COLUMN tournament_format_id INT NULL AFTER sport_id");
        echo "<p style='color: green;'>✅ Added tournament_format_id column to event_sports</p>";
    } else {
        echo "<p style='color: green;'>✅ event_sports.tournament_format_id column exists</p>";
    }
    
    echo "<h2>2. Handling Foreign Key Constraints</h2>";
    
    // Check for existing foreign key constraints that might prevent deletion
    $stmt = $conn->prepare("
        SELECT CONSTRAINT_NAME, TABLE_NAME, COLUMN_NAME, REFERENCED_TABLE_NAME, REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_SCHEMA = DATABASE() 
        AND REFERENCED_TABLE_NAME = 'tournament_formats'
    ");
    $stmt->execute();
    $constraints = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Existing Foreign Key Constraints:</h3>";
    if (empty($constraints)) {
        echo "<p style='color: blue;'>ℹ️ No foreign key constraints found referencing tournament_formats</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>Constraint</th><th>Table</th><th>Column</th><th>References</th>";
        echo "</tr>";
        foreach ($constraints as $constraint) {
            echo "<tr>";
            echo "<td>{$constraint['CONSTRAINT_NAME']}</td>";
            echo "<td>{$constraint['TABLE_NAME']}</td>";
            echo "<td>{$constraint['COLUMN_NAME']}</td>";
            echo "<td>{$constraint['REFERENCED_TABLE_NAME']}.{$constraint['REFERENCED_COLUMN_NAME']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>3. Safely Updating Tournament Formats</h2>";
    
    // Instead of deleting all formats, update existing ones and add missing ones
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats");
    $stmt->execute();
    $formatCount = $stmt->fetch()['count'];
    
    if ($formatCount > 0) {
        echo "<p style='color: blue;'>ℹ️ Found {$formatCount} existing tournament formats. Updating instead of deleting...</p>";
        
        // Update existing formats with new schema
        $updates = [
            "ALTER TABLE tournament_formats ADD COLUMN IF NOT EXISTS sport_type_category ENUM('traditional', 'individual', 'academic', 'judged', 'performance', 'all') DEFAULT 'all' AFTER description",
            "ALTER TABLE tournament_formats ADD COLUMN IF NOT EXISTS advancement_type ENUM('elimination', 'points', 'ranking', 'hybrid') DEFAULT 'elimination' AFTER sport_type_category",
            "ALTER TABLE tournament_formats ADD COLUMN IF NOT EXISTS requires_seeding BOOLEAN DEFAULT FALSE AFTER advancement_type",
            "ALTER TABLE tournament_formats ADD COLUMN IF NOT EXISTS supports_byes BOOLEAN DEFAULT TRUE AFTER requires_seeding",
            "ALTER TABLE tournament_formats ADD COLUMN IF NOT EXISTS rounds_formula VARCHAR(255) AFTER supports_byes",
            "ALTER TABLE tournament_formats ADD COLUMN IF NOT EXISTS matches_formula VARCHAR(255) AFTER rounds_formula",
            "ALTER TABLE tournament_formats ADD COLUMN IF NOT EXISTS algorithm_class VARCHAR(100) DEFAULT 'SingleEliminationAlgorithm' AFTER matches_formula",
            "ALTER TABLE tournament_formats ADD COLUMN IF NOT EXISTS configuration JSON AFTER algorithm_class"
        ];
        
        foreach ($updates as $sql) {
            try {
                $conn->exec($sql);
                echo "<p style='color: green;'>✅ Schema update applied</p>";
            } catch (Exception $e) {
                if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
                    echo "<p style='color: blue;'>ℹ️ Column already exists, skipping</p>";
                } else {
                    echo "<p style='color: orange;'>⚠️ Update warning: " . $e->getMessage() . "</p>";
                }
            }
        }
        
        // Update existing formats with proper data
        $formatUpdates = [
            [
                'code' => 'single_elimination',
                'rounds_formula' => 'ceil(log2(n))',
                'matches_formula' => 'n-1',
                'algorithm_class' => 'SingleEliminationAlgorithm',
                'configuration' => '{"bracket_seeding": true, "third_place_match": false}'
            ],
            [
                'code' => 'double_elimination', 
                'rounds_formula' => 'ceil(log2(n))*2-1',
                'matches_formula' => '2*n-2',
                'algorithm_class' => 'DoubleEliminationAlgorithm',
                'configuration' => '{"winners_bracket": true, "losers_bracket": true}'
            ],
            [
                'code' => 'round_robin',
                'rounds_formula' => '1',
                'matches_formula' => 'n*(n-1)/2',
                'algorithm_class' => 'RoundRobinAlgorithm',
                'configuration' => '{"points_win": 3, "points_draw": 1, "points_loss": 0}'
            ]
        ];
        
        foreach ($formatUpdates as $update) {
            $stmt = $conn->prepare("
                UPDATE tournament_formats 
                SET rounds_formula = ?, matches_formula = ?, algorithm_class = ?, configuration = ?
                WHERE code = ?
            ");
            $stmt->execute([
                $update['rounds_formula'],
                $update['matches_formula'], 
                $update['algorithm_class'],
                $update['configuration'],
                $update['code']
            ]);
            
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✅ Updated {$update['code']} format</p>";
            }
        }
        
    } else {
        echo "<p style='color: blue;'>ℹ️ No existing formats found. Creating new ones...</p>";
        
        // Insert new formats
        $formats = [
            [
                'name' => 'Single Elimination',
                'code' => 'single_elimination',
                'description' => 'Traditional knockout tournament',
                'sport_type_category' => 'all',
                'advancement_type' => 'elimination',
                'min_participants' => 2,
                'requires_seeding' => true,
                'supports_byes' => true,
                'rounds_formula' => 'ceil(log2(n))',
                'matches_formula' => 'n-1',
                'algorithm_class' => 'SingleEliminationAlgorithm',
                'configuration' => '{"bracket_seeding": true, "third_place_match": false}'
            ]
        ];
        
        foreach ($formats as $format) {
            $stmt = $conn->prepare("
                INSERT INTO tournament_formats (
                    name, code, description, sport_type_category, advancement_type,
                    min_participants, requires_seeding, supports_byes,
                    rounds_formula, matches_formula, algorithm_class, configuration
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $format['name'], $format['code'], $format['description'],
                $format['sport_type_category'], $format['advancement_type'],
                $format['min_participants'], $format['requires_seeding'], $format['supports_byes'],
                $format['rounds_formula'], $format['matches_formula'], 
                $format['algorithm_class'], $format['configuration']
            ]);
            
            echo "<p style='color: green;'>✅ Created {$format['name']} format</p>";
        }
    }
    
    echo "<h2>4. Ensuring Event Sports Have Valid Tournament Format References</h2>";
    
    // Get the first available tournament format ID
    $stmt = $conn->prepare("SELECT id FROM tournament_formats ORDER BY id LIMIT 1");
    $stmt->execute();
    $defaultFormatId = $stmt->fetchColumn();
    
    if ($defaultFormatId) {
        // Update event_sports records that have NULL or invalid tournament_format_id
        $stmt = $conn->prepare("
            UPDATE event_sports 
            SET tournament_format_id = ? 
            WHERE tournament_format_id IS NULL 
            OR tournament_format_id NOT IN (SELECT id FROM tournament_formats)
        ");
        $stmt->execute([$defaultFormatId]);
        $updated = $stmt->rowCount();
        
        if ($updated > 0) {
            echo "<p style='color: green;'>✅ Updated {$updated} event_sports records with valid tournament format ID</p>";
        } else {
            echo "<p style='color: blue;'>ℹ️ All event_sports records already have valid tournament format references</p>";
        }
        
        // Specifically check our test case
        $stmt = $conn->prepare("
            SELECT es.*, tf.name as format_name 
            FROM event_sports es 
            LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
            WHERE es.event_id = 4 AND es.sport_id = 37
        ");
        $stmt->execute();
        $testEventSport = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($testEventSport) {
            echo "<h3>Test Event Sport Status:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><td><strong>Event ID</strong></td><td>{$testEventSport['event_id']}</td></tr>";
            echo "<tr><td><strong>Sport ID</strong></td><td>{$testEventSport['sport_id']}</td></tr>";
            echo "<tr><td><strong>Tournament Format ID</strong></td><td>{$testEventSport['tournament_format_id']}</td></tr>";
            echo "<tr><td><strong>Format Name</strong></td><td>{$testEventSport['format_name']}</td></tr>";
            echo "</table>";
            
            if ($testEventSport['tournament_format_id'] && $testEventSport['format_name']) {
                echo "<p style='color: green;'>✅ Test event sport has valid tournament format reference!</p>";
            } else {
                echo "<p style='color: red;'>❌ Test event sport still has invalid tournament format reference</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Test event sport (Event 4, Sport 37) not found!</p>";
        }
        
    } else {
        echo "<p style='color: red;'>❌ No tournament formats available!</p>";
    }
    
    echo "<h2>5. Database Constraint Fix Complete!</h2>";
    echo "<p style='color: green; font-weight: bold;'>✅ Tournament format database constraints have been resolved!</p>";
    echo "<p><a href='test-database-driven-tournament.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Test Tournament Generation</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}
?>
