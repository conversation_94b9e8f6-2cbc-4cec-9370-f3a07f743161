<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Registration System Analysis</h2>";

try {
    // Check event_department_registrations table
    echo "<h3>Event Department Registrations</h3>";
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM event_department_registrations");
    $stmt->execute();
    $edr_count = $stmt->fetch()['count'];
    echo "<p>Total event department registrations: $edr_count</p>";
    
    if ($edr_count > 0) {
        $stmt = $conn->prepare("
            SELECT edr.*, e.name as event_name, d.name as department_name 
            FROM event_department_registrations edr
            JOIN events e ON edr.event_id = e.id
            JOIN departments d ON edr.department_id = d.id
            LIMIT 5
        ");
        $stmt->execute();
        $sample_edr = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Event</th><th>Department</th><th>Status</th><th>Registration Date</th></tr>";
        foreach ($sample_edr as $row) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['event_name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['department_name']) . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "<td>" . $row['registration_date'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check department_sport_participations table
    echo "<h3>Department Sport Participations</h3>";
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM department_sport_participations");
    $stmt->execute();
    $dsp_count = $stmt->fetch()['count'];
    echo "<p>Total department sport participations: $dsp_count</p>";
    
    if ($dsp_count > 0) {
        $stmt = $conn->prepare("
            SELECT dsp.*, s.name as sport_name, d.name as department_name 
            FROM department_sport_participations dsp
            JOIN event_sports es ON dsp.event_sport_id = es.id
            JOIN sports s ON es.sport_id = s.id
            JOIN event_department_registrations edr ON dsp.event_department_registration_id = edr.id
            JOIN departments d ON edr.department_id = d.id
            LIMIT 5
        ");
        $stmt->execute();
        $sample_dsp = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>ID</th><th>Department</th><th>Sport</th><th>Team Name</th><th>Status</th></tr>";
        foreach ($sample_dsp as $row) {
            echo "<tr>";
            echo "<td>" . $row['id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['department_name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['sport_name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['team_name'] ?? 'N/A') . "</td>";
            echo "<td>" . $row['status'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Check for specific event (Event ID 4)
    echo "<h3>Event ID 4 Registration Analysis</h3>";
    $stmt = $conn->prepare("
        SELECT 
            edr.id as reg_id,
            d.name as department_name,
            d.abbreviation,
            COUNT(dsp.id) as sports_count
        FROM event_department_registrations edr
        JOIN departments d ON edr.department_id = d.id
        LEFT JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
        WHERE edr.event_id = 4
        GROUP BY edr.id, d.name, d.abbreviation
        ORDER BY d.name
    ");
    $stmt->execute();
    $event4_registrations = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($event4_registrations)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Registration ID</th><th>Department</th><th>Abbreviation</th><th>Sports Count</th></tr>";
        foreach ($event4_registrations as $row) {
            echo "<tr>";
            echo "<td>" . $row['reg_id'] . "</td>";
            echo "<td>" . htmlspecialchars($row['department_name']) . "</td>";
            echo "<td>" . htmlspecialchars($row['abbreviation']) . "</td>";
            echo "<td>" . $row['sports_count'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No registrations found for Event ID 4</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
