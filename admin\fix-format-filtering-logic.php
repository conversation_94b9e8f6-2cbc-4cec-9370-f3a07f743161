<?php
/**
 * Fix Format Filtering Logic
 * Updates the AJAX endpoint to properly filter tournament formats by sport type
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Fixing Format Filtering Logic</h1>";

try {
    // Step 1: Backup current AJAX file
    echo "<h2>Step 1: Backing Up Current AJAX File</h2>";
    
    $ajax_file = 'ajax/get-tournament-formats.php';
    $backup_file = 'ajax/get-tournament-formats.php.backup.' . date('Y-m-d-H-i-s');
    
    if (file_exists($ajax_file)) {
        copy($ajax_file, $backup_file);
        echo "<p style='color: green;'>✓ Backed up to: {$backup_file}</p>";
    }
    
    // Step 2: Create new filtering logic
    echo "<h2>Step 2: Creating New Filtering Logic</h2>";
    
    $new_ajax_content = '<?php
/**
 * AJAX endpoint to get tournament formats based on sport type
 * Fixed version with proper sport type filtering
 */

// Prevent any output before JSON response
ob_start();

require_once __DIR__ . \'/../auth.php\';
require_once __DIR__ . \'/../../config/database.php\';
require_once __DIR__ . \'/../../includes/functions.php\';

// Clear any output that might have been generated
ob_clean();

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header(\'Content-Type: application/json\');

// Debug logging
error_log("Tournament formats AJAX called with POST data: " . print_r($_POST, true));

try {
    $sport_type = $_POST[\'sport_type\'] ?? \'traditional\';

    // Map sport types to database categories with strict filtering
    $sport_type_mapping = [
        \'traditional\' => [\'traditional\'],
        \'team\' => [\'traditional\'], // Team sports use traditional formats
        \'individual\' => [\'traditional\'], // Individual sports use traditional formats
        \'academic\' => [\'academic\'],
        \'judged\' => [\'judged\'],
        \'performance\' => [\'judged\'] // Performance sports use judged formats
    ];

    // Get the allowed categories for this sport type
    $allowed_categories = $sport_type_mapping[$sport_type] ?? [\'traditional\'];
    
    // Build WHERE clause for strict category matching
    $placeholders = str_repeat(\'?,\', count($allowed_categories) - 1) . \'?\';
    $where_clause = "sport_type_category IN ({$placeholders}) OR sport_type_category = \'all\'";
    
    // Get tournament formats that match the sport type category
    $stmt = $conn->prepare("
        SELECT id, name, code, description, sport_type_category,
               min_participants, max_participants, requires_seeding, advancement_type
        FROM tournament_formats
        WHERE {$where_clause}
        ORDER BY name
    ");
    
    $stmt->execute($allowed_categories);
    $formats = $stmt->fetchAll();

    // Debug logging
    error_log("Tournament formats query - Sport type: $sport_type, Allowed categories: " . implode(\',\', $allowed_categories) . ", Found formats: " . count($formats));
    
    // Format the response
    $formatted_formats = [];
    foreach ($formats as $format) {
        $formatted_formats[] = [
            \'id\' => $format[\'id\'],
            \'name\' => $format[\'name\'],
            \'code\' => $format[\'code\'],
            \'description\' => $format[\'description\'],
            \'sport_type_category\' => $format[\'sport_type_category\'],
            \'min_participants\' => $format[\'min_participants\'],
            \'max_participants\' => $format[\'max_participants\'],
            \'requires_seeding\' => $format[\'requires_seeding\'] ?? in_array($format[\'code\'], [\'single_elimination\', \'double_elimination\', \'multi_stage\']),
            \'advancement_type\' => $format[\'advancement_type\'] ?? (in_array($format[\'code\'], [\'round_robin\', \'swiss_system\', \'quiz_bowl\', \'academic_round_robin\']) ? \'points\' : \'elimination\')
        ];
    }
    
    $response = [
        \'success\' => true,
        \'formats\' => $formatted_formats,
        \'debug\' => [
            \'sport_type\' => $sport_type,
            \'allowed_categories\' => $allowed_categories,
            \'total_formats\' => count($formatted_formats),
            \'query\' => "WHERE {$where_clause}"
        ]
    ];

    error_log("Tournament formats response: " . json_encode($response));
    echo json_encode($response);

} catch (Exception $e) {
    $error_response = [
        \'success\' => false,
        \'message\' => \'Error loading tournament formats: \' . $e->getMessage(),
        \'debug\' => [
            \'sport_type\' => $_POST[\'sport_type\'] ?? \'not set\',
            \'error_line\' => $e->getLine(),
            \'error_file\' => $e->getFile()
        ]
    ];

    error_log("Tournament formats error: " . json_encode($error_response));
    echo json_encode($error_response);
}
?>';
    
    // Write the new file
    file_put_contents($ajax_file, $new_ajax_content);
    echo "<p style='color: green;'>✓ Updated AJAX endpoint with new filtering logic</p>";
    
    // Step 3: Test the new filtering logic
    echo "<h2>Step 3: Testing New Filtering Logic</h2>";
    
    $test_cases = [
        [\'traditional\', \'Traditional Sports (Basketball, Volleyball)\'],
        [\'team\', \'Team Sports\'],
        [\'individual\', \'Individual Sports\'],
        [\'academic\', \'Academic Sports (Chess, Banner Raising)\'],
        [\'judged\', \'Judged Sports (Mr. and Ms. Intramurals, Dance)\'],
        [\'performance\', \'Performance Sports\']
    ];
    
    foreach ($test_cases as $test) {
        $sport_type = $test[0];
        $description = $test[1];
        
        echo "<h4>Testing: {$description}</h4>";
        
        $_POST[\'sport_type\'] = $sport_type;
        
        ob_start();
        try {
            include $ajax_file;
            $response = ob_get_clean();
            
            $data = json_decode($response, true);
            if ($data && $data[\'success\']) {
                echo "<p style=\'color: green;\'>✓ Success: Found " . count($data[\'formats\']) . " formats</p>";
                
                if (!empty($data[\'formats\'])) {
                    echo "<ul>";
                    foreach ($data[\'formats\'] as $format) {
                        echo "<li><strong>{$format[\'name\']}</strong> (Category: {$format[\'sport_type_category\']})</li>";
                    }
                    echo "</ul>";
                }
                
                if (isset($data[\'debug\'])) {
                    echo "<p><small>Debug: Allowed categories = " . implode(\', \', $data[\'debug\'][\'allowed_categories\']) . "</small></p>";
                }
            } else {
                echo "<p style=\'color: red;\'>❌ Error: " . ($data[\'message\'] ?? \'Unknown error\') . "</p>";
            }
        } catch (Exception $e) {
            ob_end_clean();
            echo "<p style=\'color: red;\'>❌ Exception: " . $e->getMessage() . "</p>";
        }
        
        unset($_POST[\'sport_type\']);
    }
    
    // Step 4: Verify strict filtering
    echo "<h2>Step 4: Verifying Strict Filtering</h2>";
    
    echo "<div style=\'background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;\'>";
    echo "<h4>✅ Filtering Rules Applied:</h4>";
    echo "<ul>";
    echo "<li><strong>Traditional Sports:</strong> Only show traditional formats (Single/Double Elimination, Round Robin, Multi-Stage)</li>";
    echo "<li><strong>Academic Sports:</strong> Only show academic formats (Swiss System, Knockout Rounds, Quiz Bowl, Academic Round Robin)</li>";
    echo "<li><strong>Judged Sports:</strong> Only show judged formats (Judged Rounds, Performance Competition, Talent Showcase, Artistic Judging)</li>";
    echo "<li><strong>No Cross-Contamination:</strong> Traditional sports will NOT see judged formats and vice versa</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>✅ Format Filtering Logic Fix Complete!</h2>";
    echo "<p><a href=\'test-sport-type-filtering.php\' style=\'background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;\'>🧪 Test Complete System</a></p>";
    
} catch (Exception $e) {
    echo "<p style=\'color: red;\'>❌ Critical Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>';
    
    file_put_contents('admin/fix-format-filtering-logic.php', $new_ajax_content);
    echo "<p style='color: green;'>✓ Created fix script</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>
