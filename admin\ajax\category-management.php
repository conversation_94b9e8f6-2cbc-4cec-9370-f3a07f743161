<?php
/**
 * Category Management AJAX Endpoint
 * SC_IMS Sports Competition and Event Management System
 *
 * Handles AJAX requests for category management operations
 */

// Clean output buffer to prevent any unwanted output
if (ob_get_level()) {
    ob_clean();
}

require_once '../../config/database.php';
require_once '../../includes/auth.php';
require_once '../../includes/functions.php';

// Ensure admin authentication
requireAdmin();

// Set headers
header('Content-Type: application/json');
header('Cache-Control: no-cache, must-revalidate');

// Initialize database connection
$database = new Database();
$conn = $database->getConnection();

try {
    $action = $_POST['action'] ?? $_GET['action'] ?? '';

    if (empty($action)) {
        throw new Exception('No action specified');
    }

    switch ($action) {
        case 'get_current_format':
            handleGetCurrentFormat();
            break;

        case 'update_tournament_format':
            handleUpdateTournamentFormat();
            break;

        case 'update_category':
            handleUpdateCategory();
            break;

        case 'get_category':
            handleGetCategory();
            break;

        default:
            throw new Exception('Invalid action: ' . $action);
    }

} catch (Exception $e) {
    // Clear any output that might have been generated
    if (ob_get_level()) {
        ob_clean();
    }

    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'action' => $action ?? 'unknown'
    ]);
} catch (Error $e) {
    // Handle fatal errors
    if (ob_get_level()) {
        ob_clean();
    }

    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => 'Server error: ' . $e->getMessage(),
        'action' => $action ?? 'unknown'
    ]);
}

/**
 * Get current tournament format for an event sport
 */
function handleGetCurrentFormat() {
    global $conn;
    
    $eventSportId = $_POST['event_sport_id'] ?? $_GET['event_sport_id'] ?? '';
    
    if (empty($eventSportId)) {
        throw new Exception('Event sport ID is required');
    }
    
    // Get current tournament format from event_sports table
    $stmt = $conn->prepare("
        SELECT es.tournament_format_id, es.max_teams, es.bracket_type,
               tf.name as format_name, tf.description as format_description
        FROM event_sports es
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.id = ?
    ");
    $stmt->execute([$eventSportId]);
    $format = $stmt->fetch();
    
    if ($format) {
        echo json_encode([
            'success' => true,
            'format' => $format
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'No format found for this event sport'
        ]);
    }
}

/**
 * Update tournament format for an event sport
 */
function handleUpdateTournamentFormat() {
    global $conn;
    
    $eventSportId = $_POST['event_sport_id'] ?? '';
    $tournamentFormatId = $_POST['tournament_format_id'] ?? '';
    $seedingMethod = $_POST['seeding_method'] ?? 'random';
    $maxTeams = $_POST['max_teams'] ?? null;
    
    if (empty($eventSportId) || empty($tournamentFormatId)) {
        throw new Exception('Event sport ID and tournament format ID are required');
    }
    
    // Validate tournament format exists
    $stmt = $conn->prepare("SELECT id, name, code FROM tournament_formats WHERE id = ?");
    $stmt->execute([$tournamentFormatId]);
    $format = $stmt->fetch();
    
    if (!$format) {
        throw new Exception('Invalid tournament format selected');
    }
    
    // Update event_sports table with new tournament format
    $stmt = $conn->prepare("
        UPDATE event_sports 
        SET tournament_format_id = ?, bracket_type = ?, max_teams = ?
        WHERE id = ?
    ");
    $stmt->execute([
        $tournamentFormatId,
        $format['code'],
        $maxTeams ?: null,
        $eventSportId
    ]);
    
    // Check if there's an existing tournament structure that needs updating
    $stmt = $conn->prepare("
        SELECT id FROM tournament_structures 
        WHERE event_sport_id = ?
    ");
    $stmt->execute([$eventSportId]);
    $existingTournament = $stmt->fetch();
    
    if ($existingTournament) {
        // Update existing tournament structure
        $stmt = $conn->prepare("
            UPDATE tournament_structures 
            SET tournament_format_id = ?, seeding_method = ?
            WHERE event_sport_id = ?
        ");
        $stmt->execute([
            $tournamentFormatId,
            $seedingMethod,
            $eventSportId
        ]);
    }
    
    // Log admin activity
    logAdminActivity($conn, $_SESSION['admin_id'], 'update_tournament_format', 
        "Updated tournament format for event_sport_id: {$eventSportId} to format: {$format['name']}");
    
    echo json_encode([
        'success' => true,
        'message' => 'Tournament format updated successfully',
        'format' => [
            'id' => $tournamentFormatId,
            'name' => $format['name'],
            'code' => $format['code']
        ]
    ]);
}

/**
 * Handle category update
 */
function handleUpdateCategory() {
    global $conn;

    $category_id = intval($_POST['category_id']);
    $category_name = trim($_POST['category_name']);
    $category_type = $_POST['category_type'];
    $referee_name = trim($_POST['referee_name']);
    $referee_email = trim($_POST['referee_email']);
    $venue = trim($_POST['venue']);
    $status = $_POST['status'];

    // Validate required fields
    if (empty($category_name)) {
        throw new Exception('Category name is required');
    }

    if (empty($category_type)) {
        throw new Exception('Category type is required');
    }

    if (!in_array($category_type, ['men', 'women', 'mixed', 'open', 'youth', 'senior', 'other'])) {
        throw new Exception('Invalid category type');
    }

    if (!in_array($status, ['registration', 'ongoing', 'completed', 'cancelled'])) {
        throw new Exception('Invalid status');
    }

    // Validate email if provided
    if (!empty($referee_email) && !filter_var($referee_email, FILTER_VALIDATE_EMAIL)) {
        throw new Exception('Invalid referee email format');
    }

    // Check if category exists
    $stmt = $conn->prepare("SELECT id FROM sport_categories WHERE id = ?");
    $stmt->execute([$category_id]);
    if (!$stmt->fetch()) {
        throw new Exception('Category not found');
    }

    // Update category
    $stmt = $conn->prepare("
        UPDATE sport_categories
        SET
            category_name = ?,
            category_type = ?,
            referee_name = ?,
            referee_email = ?,
            venue = ?,
            status = ?,
            updated_at = NOW()
        WHERE id = ?
    ");

    $stmt->execute([
        $category_name,
        $category_type,
        $referee_name ?: null,
        $referee_email ?: null,
        $venue ?: null,
        $status,
        $category_id
    ]);

    // Log the activity
    logAdminActivity($conn, $_SESSION['admin_id'], 'update_category',
        "Updated category: {$category_name} (Type: {$category_type}, Status: {$status})");

    echo json_encode([
        'success' => true,
        'message' => 'Category updated successfully!',
        'data' => [
            'category_id' => $category_id,
            'category_name' => $category_name,
            'category_type' => $category_type,
            'status' => $status
        ]
    ]);
}

/**
 * Handle get category details
 */
function handleGetCategory() {
    global $conn;

    $category_id = intval($_POST['category_id']);

    $stmt = $conn->prepare("
        SELECT
            sc.*,
            e.name as event_name,
            s.name as sport_name
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ?
    ");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();

    if (!$category) {
        throw new Exception('Category not found');
    }

    echo json_encode([
        'success' => true,
        'data' => $category
    ]);
}
?>
