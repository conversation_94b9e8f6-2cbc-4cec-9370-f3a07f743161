<?php
/**
 * Debug Tournament Format Issues
 * Investigate the sport_type_category and filtering problems
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔍 Tournament Format Issues Investigation</h1>";

// 1. Check current tournament_formats table structure
echo "<h2>1. Tournament Formats Table Structure</h2>";
$stmt = $conn->prepare("DESCRIBE tournament_formats");
$stmt->execute();
$columns = $stmt->fetchAll();

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
foreach ($columns as $col) {
    echo "<tr>";
    echo "<td>{$col['Field']}</td>";
    echo "<td>{$col['Type']}</td>";
    echo "<td>{$col['Null']}</td>";
    echo "<td>{$col['Key']}</td>";
    echo "<td>{$col['Default']}</td>";
    echo "<td>{$col['Extra']}</td>";
    echo "</tr>";
}
echo "</table>";

// 2. Check current tournament formats data
echo "<h2>2. Current Tournament Formats Data</h2>";
$stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
$stmt->execute();
$formats = $stmt->fetchAll();

if (empty($formats)) {
    echo "<p style='color: red;'>❌ No tournament formats found in database!</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Sport Type Category</th><th>Sport Types</th><th>Description</th></tr>";
    foreach ($formats as $format) {
        $row_color = '';
        if (($format['sport_type_category'] ?? '') === 'individual') {
            $row_color = 'background-color: #ffe6e6;'; // Red highlight for incorrect 'individual'
        }
        
        echo "<tr style='{$row_color}'>";
        echo "<td>{$format['id']}</td>";
        echo "<td>{$format['name']}</td>";
        echo "<td>{$format['code']}</td>";
        echo "<td>" . ($format['sport_type_category'] ?? 'NULL') . "</td>";
        echo "<td>" . ($format['sport_types'] ?? 'NULL') . "</td>";
        echo "<td>" . substr($format['description'] ?? '', 0, 50) . "...</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Count by category
    echo "<h3>Count by Sport Type Category:</h3>";
    $stmt = $conn->prepare("SELECT sport_type_category, COUNT(*) as count FROM tournament_formats GROUP BY sport_type_category");
    $stmt->execute();
    $counts = $stmt->fetchAll();
    
    echo "<ul>";
    foreach ($counts as $count) {
        $category = $count['sport_type_category'] ?? 'NULL';
        echo "<li><strong>{$category}:</strong> {$count['count']} formats</li>";
    }
    echo "</ul>";
}

// 3. Check sports and their types
echo "<h2>3. Sports and Their Types</h2>";
$stmt = $conn->prepare("
    SELECT s.id, s.name, s.type, st.category as sport_type_category, st.name as sport_type_name
    FROM sports s
    LEFT JOIN sport_types st ON s.sport_type_id = st.id
    ORDER BY st.category, s.name
");
$stmt->execute();
$sports = $stmt->fetchAll();

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Sport Name</th><th>Legacy Type</th><th>Sport Type Category</th><th>Sport Type Name</th></tr>";
foreach ($sports as $sport) {
    $category = $sport['sport_type_category'] ?? $sport['type'] ?? 'NULL';
    $row_color = '';
    if ($category === 'judged') $row_color = 'background-color: #ffe6f0;';
    if ($category === 'academic') $row_color = 'background-color: #e6f3ff;';
    if ($category === 'traditional') $row_color = 'background-color: #e6ffe6;';
    
    echo "<tr style='{$row_color}'>";
    echo "<td>{$sport['id']}</td>";
    echo "<td>{$sport['name']}</td>";
    echo "<td>" . ($sport['type'] ?? 'NULL') . "</td>";
    echo "<td>" . ($sport['sport_type_category'] ?? 'NULL') . "</td>";
    echo "<td>" . ($sport['sport_type_name'] ?? 'NULL') . "</td>";
    echo "</tr>";
}
echo "</table>";

// 4. Test current filtering logic
echo "<h2>4. Current Filtering Logic Test</h2>";

$test_sport_types = ['traditional', 'academic', 'judged', 'performance'];

foreach ($test_sport_types as $sport_type) {
    echo "<h3>Testing sport type: {$sport_type}</h3>";
    
    // Simulate the current AJAX call
    $_POST['sport_type'] = $sport_type;
    
    ob_start();
    try {
        include 'ajax/get-tournament-formats.php';
        $response = ob_get_clean();
        
        $data = json_decode($response, true);
        if ($data && $data['success']) {
            echo "<p style='color: green;'>✓ Found " . count($data['formats']) . " formats for {$sport_type}</p>";
            if (!empty($data['formats'])) {
                echo "<ul>";
                foreach ($data['formats'] as $format) {
                    echo "<li><strong>{$format['name']}</strong> (Code: {$format['code']})</li>";
                }
                echo "</ul>";
            }
            
            if (isset($data['debug'])) {
                echo "<p><strong>Debug info:</strong> " . json_encode($data['debug']) . "</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Error for {$sport_type}: " . ($data['message'] ?? 'Unknown error') . "</p>";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>❌ Exception for {$sport_type}: " . $e->getMessage() . "</p>";
    }
    
    unset($_POST['sport_type']);
}

// 5. Identify the problems
echo "<h2>5. Problem Analysis</h2>";

echo "<div style='background: #fff3cd; padding: 15px; border: 1px solid #ffeaa7; border-radius: 5px;'>";
echo "<h3>🚨 Issues Identified:</h3>";

// Check if all formats have 'individual' category
$stmt = $conn->prepare("SELECT COUNT(*) as total, 
                               SUM(CASE WHEN sport_type_category = 'individual' THEN 1 ELSE 0 END) as individual_count
                        FROM tournament_formats");
$stmt->execute();
$analysis = $stmt->fetch();

if ($analysis['individual_count'] == $analysis['total'] && $analysis['total'] > 0) {
    echo "<p style='color: red;'><strong>Problem 1 CONFIRMED:</strong> All {$analysis['total']} tournament formats have sport_type_category = 'individual'</p>";
}

// Check if traditional sports show judged formats
echo "<p><strong>Problem 2:</strong> Need to verify if traditional sports show judged formats...</p>";

echo "</div>";

echo "<h2>6. Recommended Fix</h2>";
echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Update tournament_formats table with correct sport_type_category values</li>";
echo "<li>Fix the filtering logic in get-tournament-formats.php</li>";
echo "<li>Test that each sport type only shows appropriate formats</li>";
echo "</ol>";
echo "<p><a href='fix-tournament-format-categories.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🔧 Apply Fix</a></p>";
echo "</div>";
?>
