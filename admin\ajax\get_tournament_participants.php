<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['event_id']) || !isset($input['sport_id'])) {
        throw new Exception('Missing required parameters');
    }
    
    $event_id = (int)$input['event_id'];
    $sport_id = (int)$input['sport_id'];
    
    // Get participants from unified registration system
    $stmt = $pdo->prepare("
        SELECT 
            d.id,
            d.name,
            d.department_name as department,
            d.contact_person,
            d.contact_email,
            d.contact_phone
        FROM departments d
        INNER JOIN event_registrations er ON d.id = er.department_id
        WHERE er.event_id = ? AND er.status = 'active'
        ORDER BY d.name ASC
    ");
    
    $stmt->execute([$event_id]);
    $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Log admin activity
    logAdminActivity($_SESSION['admin_id'], 'view', 'tournament_participants', $event_id, [
        'event_id' => $event_id,
        'sport_id' => $sport_id,
        'participant_count' => count($participants)
    ]);
    
    echo json_encode([
        'success' => true,
        'participants' => $participants,
        'count' => count($participants)
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_tournament_participants.php: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Error loading participants: ' . $e->getMessage()
    ]);
}
?>
