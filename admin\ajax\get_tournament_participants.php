<?php
require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is admin
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!isset($input['event_id']) || !isset($input['sport_id'])) {
        throw new Exception('Missing required parameters');
    }

    $event_id = (int)$input['event_id'];
    $sport_id = (int)$input['sport_id'];

    // Get participants from unified registration system
    $stmt = $conn->prepare("
        SELECT DISTINCT
            d.id,
            d.name,
            d.abbreviation as department,
            d.contact_person,
            d.contact_email,
            d.contact_phone
        FROM event_department_registrations edr
        JOIN event_sports es ON edr.event_id = es.event_id
        JOIN departments d ON edr.department_id = d.id
        WHERE es.id = ? AND edr.status IN ('pending', 'approved')
        ORDER BY d.name ASC
    ");

    // Use event_sport_id instead of event_id for the query
    $stmt->execute([$sport_id]);
    $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Log admin activity (if function exists)
    if (function_exists('logAdminActivity')) {
        logAdminActivity($_SESSION['admin_id'], 'view', 'tournament_participants', $event_id, [
            'event_id' => $event_id,
            'sport_id' => $sport_id,
            'participant_count' => count($participants)
        ]);
    }
    
    echo json_encode([
        'success' => true,
        'participants' => $participants,
        'count' => count($participants)
    ]);
    
} catch (Exception $e) {
    error_log("Error in get_tournament_participants.php: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Error loading participants: ' . $e->getMessage()
    ]);
}
?>
