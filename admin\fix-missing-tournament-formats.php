<?php
/**
 * Fix Missing Tournament Formats
 * Add comprehensive tournament formats for Academic and Judged sports
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Adding Missing Tournament Formats</h2>";

try {
    // First, check the table structure to determine which field to use
    $stmt = $conn->prepare("SHOW COLUMNS FROM tournament_formats LIKE 'sport_type%'");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    $has_category_field = false;
    $has_types_field = false;
    
    foreach ($columns as $col) {
        if ($col['Field'] === 'sport_type_category') {
            $has_category_field = true;
        }
        if ($col['Field'] === 'sport_types') {
            $has_types_field = true;
        }
    }
    
    echo "<p>Table structure: ";
    echo $has_category_field ? "✓ sport_type_category " : "❌ sport_type_category ";
    echo $has_types_field ? "✓ sport_types" : "❌ sport_types";
    echo "</p>";
    
    // Define comprehensive tournament formats
    $tournament_formats = [];
    
    if ($has_category_field) {
        // Use sport_type_category field (ENUM)
        $tournament_formats = [
            // Academic Sports Formats
            [
                'name' => 'Swiss System',
                'code' => 'swiss_system',
                'description' => 'Pairing system where participants with similar records compete against each other. No elimination, all participants play multiple rounds.',
                'sport_type_category' => 'academic',
                'min_participants' => 4,
                'max_participants' => null,
                'requires_seeding' => false,
                'supports_byes' => true,
                'advancement_type' => 'points',
                'rounds_formula' => 'ceil(log2(n))',
                'matches_formula' => 'n*rounds/2',
                'algorithm_class' => 'SwissSystemAlgorithm',
                'configuration' => '{"pairing_algorithm": "swiss", "avoid_rematches": true, "points_win": 1, "points_draw": 0.5, "points_loss": 0}'
            ],
            [
                'name' => 'Knockout Rounds',
                'code' => 'knockout_rounds',
                'description' => 'Academic elimination tournament with question pools and time limits. Single elimination format for knowledge competitions.',
                'sport_type_category' => 'academic',
                'min_participants' => 4,
                'max_participants' => null,
                'requires_seeding' => true,
                'supports_byes' => true,
                'advancement_type' => 'elimination',
                'rounds_formula' => 'ceil(log2(n))',
                'matches_formula' => 'n-1',
                'algorithm_class' => 'KnockoutRoundsAlgorithm',
                'configuration' => '{"question_pools": true, "time_limits": true, "sudden_death": false}'
            ],
            [
                'name' => 'Quiz Bowl Format',
                'code' => 'quiz_bowl',
                'description' => 'Round robin format specifically designed for quiz bowl competitions with toss-up and bonus questions.',
                'sport_type_category' => 'academic',
                'min_participants' => 3,
                'max_participants' => 12,
                'requires_seeding' => false,
                'supports_byes' => false,
                'advancement_type' => 'points',
                'rounds_formula' => '1',
                'matches_formula' => 'n*(n-1)/2',
                'algorithm_class' => 'QuizBowlAlgorithm',
                'configuration' => '{"tossup_questions": 20, "bonus_questions": 20, "points_tossup": 10, "points_bonus": 30}'
            ],
            [
                'name' => 'Academic Round Robin',
                'code' => 'academic_round_robin',
                'description' => 'Every participant competes against every other participant in academic challenges.',
                'sport_type_category' => 'academic',
                'min_participants' => 3,
                'max_participants' => 16,
                'requires_seeding' => false,
                'supports_byes' => false,
                'advancement_type' => 'points',
                'rounds_formula' => '1',
                'matches_formula' => 'n*(n-1)/2',
                'algorithm_class' => 'AcademicRoundRobinAlgorithm',
                'configuration' => '{"points_win": 3, "points_draw": 1, "points_loss": 0, "academic_scoring": true}'
            ],
            
            // Judged Sports Formats
            [
                'name' => 'Judged Rounds',
                'code' => 'judged_rounds',
                'description' => 'Multiple judged rounds with scoring criteria. Participants advance based on cumulative judge scores.',
                'sport_type_category' => 'judged',
                'min_participants' => 3,
                'max_participants' => null,
                'requires_seeding' => false,
                'supports_byes' => false,
                'advancement_type' => 'points',
                'rounds_formula' => '3',
                'matches_formula' => 'n*3',
                'algorithm_class' => 'JudgedRoundsAlgorithm',
                'configuration' => '{"rounds": ["preliminary", "semifinal", "final"], "criteria_based": true, "judge_count": 5}'
            ],
            [
                'name' => 'Performance Competition',
                'code' => 'performance_competition',
                'description' => 'Structured performance competition with multiple rounds and detailed judging criteria.',
                'sport_type_category' => 'judged',
                'min_participants' => 3,
                'max_participants' => 50,
                'requires_seeding' => false,
                'supports_byes' => false,
                'advancement_type' => 'points',
                'rounds_formula' => 'variable',
                'matches_formula' => 'n*rounds',
                'algorithm_class' => 'PerformanceCompetitionAlgorithm',
                'configuration' => '{"performance_rounds": 2, "technical_score": true, "artistic_score": true, "audience_choice": false}'
            ],
            [
                'name' => 'Talent Showcase',
                'code' => 'talent_showcase',
                'description' => 'Showcase format with multiple performance rounds, combining judge scores and audience voting.',
                'sport_type_category' => 'judged',
                'min_participants' => 3,
                'max_participants' => 50,
                'requires_seeding' => false,
                'supports_byes' => false,
                'advancement_type' => 'points',
                'rounds_formula' => 'variable',
                'matches_formula' => 'n*rounds',
                'algorithm_class' => 'TalentShowcaseAlgorithm',
                'configuration' => '{"showcase_rounds": 3, "audience_voting": true, "judge_weight": 0.7, "audience_weight": 0.3}'
            ],
            [
                'name' => 'Artistic Judging',
                'code' => 'artistic_judging',
                'description' => 'Comprehensive artistic competition with technical and artistic components, similar to figure skating or gymnastics.',
                'sport_type_category' => 'judged',
                'min_participants' => 3,
                'max_participants' => 30,
                'requires_seeding' => false,
                'supports_byes' => false,
                'advancement_type' => 'points',
                'rounds_formula' => '2',
                'matches_formula' => 'n*2',
                'algorithm_class' => 'ArtisticJudgingAlgorithm',
                'configuration' => '{"technical_component": 0.5, "artistic_component": 0.5, "difficulty_bonus": true, "deduction_system": true}'
            ]
        ];
    } else if ($has_types_field) {
        // Use sport_types field (TEXT/VARCHAR)
        $tournament_formats = [
            // Academic Sports Formats
            [
                'name' => 'Swiss System',
                'code' => 'swiss_system',
                'description' => 'Pairing system where participants with similar records compete against each other. No elimination, all participants play multiple rounds.',
                'sport_types' => 'academic',
                'min_participants' => 4,
                'max_participants' => null
            ],
            [
                'name' => 'Knockout Rounds',
                'code' => 'knockout_rounds',
                'description' => 'Academic elimination tournament with question pools and time limits. Single elimination format for knowledge competitions.',
                'sport_types' => 'academic',
                'min_participants' => 4,
                'max_participants' => null
            ],
            [
                'name' => 'Quiz Bowl Format',
                'code' => 'quiz_bowl',
                'description' => 'Round robin format specifically designed for quiz bowl competitions with toss-up and bonus questions.',
                'sport_types' => 'academic',
                'min_participants' => 3,
                'max_participants' => 12
            ],
            [
                'name' => 'Academic Round Robin',
                'code' => 'academic_round_robin',
                'description' => 'Every participant competes against every other participant in academic challenges.',
                'sport_types' => 'academic',
                'min_participants' => 3,
                'max_participants' => 16
            ],
            
            // Judged Sports Formats
            [
                'name' => 'Judged Rounds',
                'code' => 'judged_rounds',
                'description' => 'Multiple judged rounds with scoring criteria. Participants advance based on cumulative judge scores.',
                'sport_types' => 'judged,performance',
                'min_participants' => 3,
                'max_participants' => null
            ],
            [
                'name' => 'Performance Competition',
                'code' => 'performance_competition',
                'description' => 'Structured performance competition with multiple rounds and detailed judging criteria.',
                'sport_types' => 'judged,performance',
                'min_participants' => 3,
                'max_participants' => 50
            ],
            [
                'name' => 'Talent Showcase',
                'code' => 'talent_showcase',
                'description' => 'Showcase format with multiple performance rounds, combining judge scores and audience voting.',
                'sport_types' => 'judged,performance',
                'min_participants' => 3,
                'max_participants' => 50
            ],
            [
                'name' => 'Artistic Judging',
                'code' => 'artistic_judging',
                'description' => 'Comprehensive artistic competition with technical and artistic components, similar to figure skating or gymnastics.',
                'sport_types' => 'judged,performance',
                'min_participants' => 3,
                'max_participants' => 30
            ]
        ];
    }
    
    if (empty($tournament_formats)) {
        echo "<p style='color: red;'>❌ Cannot determine table structure. Please check tournament_formats table.</p>";
        exit;
    }
    
    echo "<h3>Adding Tournament Formats</h3>";
    
    $added_count = 0;
    $skipped_count = 0;
    
    foreach ($tournament_formats as $format) {
        // Check if format already exists
        $stmt = $conn->prepare("SELECT id FROM tournament_formats WHERE code = ?");
        $stmt->execute([$format['code']]);
        
        if ($stmt->fetch()) {
            echo "<p style='color: orange;'>⚠️ Skipped: {$format['name']} (already exists)</p>";
            $skipped_count++;
            continue;
        }
        
        // Build INSERT query based on available fields
        if ($has_category_field) {
            $sql = "INSERT INTO tournament_formats (
                name, code, description, sport_type_category, min_participants, max_participants,
                requires_seeding, supports_byes, advancement_type, rounds_formula, matches_formula,
                algorithm_class, configuration
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $params = [
                $format['name'],
                $format['code'],
                $format['description'],
                $format['sport_type_category'],
                $format['min_participants'],
                $format['max_participants'],
                $format['requires_seeding'] ?? false,
                $format['supports_byes'] ?? true,
                $format['advancement_type'] ?? 'points',
                $format['rounds_formula'] ?? '1',
                $format['matches_formula'] ?? 'n',
                $format['algorithm_class'] ?? 'DefaultAlgorithm',
                $format['configuration'] ?? '{}'
            ];
        } else {
            $sql = "INSERT INTO tournament_formats (
                name, code, description, sport_types, min_participants, max_participants
            ) VALUES (?, ?, ?, ?, ?, ?)";
            
            $params = [
                $format['name'],
                $format['code'],
                $format['description'],
                $format['sport_types'],
                $format['min_participants'],
                $format['max_participants']
            ];
        }
        
        try {
            $stmt = $conn->prepare($sql);
            $stmt->execute($params);
            echo "<p style='color: green;'>✓ Added: {$format['name']}</p>";
            $added_count++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error adding {$format['name']}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h3>Summary</h3>";
    echo "<p><strong>Added:</strong> {$added_count} tournament formats</p>";
    echo "<p><strong>Skipped:</strong> {$skipped_count} tournament formats (already existed)</p>";
    
    if ($added_count > 0) {
        echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>";
        echo "<h4>✅ Success!</h4>";
        echo "<p>Tournament formats have been added successfully. Academic and Judged sports should now show appropriate tournament format options.</p>";
        echo "<p><strong>Next steps:</strong></p>";
        echo "<ul>";
        echo "<li>Test the 'Add Sport to Event' modal with Academic sports (e.g., Chess, Quiz Bowl)</li>";
        echo "<li>Test the 'Add Sport to Event' modal with Judged sports (e.g., Dance, Singing Contest)</li>";
        echo "<li>Verify that tournament format dropdown populates correctly</li>";
        echo "</ul>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='manage-event.php?id=1' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test in Event Management</a></p>";
?>
