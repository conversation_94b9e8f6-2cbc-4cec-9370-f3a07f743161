<?php
/**
 * Final Navigation Test
 * Comprehensive test to identify and fix the category navigation issue
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Create test data if needed
$action = $_POST['action'] ?? '';
if ($action === 'create_test_data') {
    try {
        // Ensure we have at least one event
        $event_count = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
        if ($event_count == 0) {
            $conn->exec("INSERT INTO events (name, description, start_date, end_date, venue, status) 
                        VALUES ('Test Event', 'Test event for navigation', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY), 'Test Venue', 'active')");
        }
        
        // Ensure we have at least one sport
        $sport_count = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
        if ($sport_count == 0) {
            $conn->exec("INSERT INTO sports (name, type, description) 
                        VALUES ('Test Sport', 'traditional', 'Test sport for navigation')");
        }
        
        // Ensure we have event_sports relationship
        $es_count = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
        if ($es_count == 0) {
            $conn->exec("INSERT INTO event_sports (event_id, sport_id) 
                        SELECT e.id, s.id FROM events e, sports s LIMIT 1");
        }
        
        // Create test categories
        $cat_count = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
        if ($cat_count == 0) {
            $stmt = $conn->prepare("
                INSERT INTO sport_categories (event_sport_id, category_name, category_type, referee_name, venue)
                SELECT es.id, CONCAT('Test Category ', es.id), 'men', 'Test Referee', 'Test Venue'
                FROM event_sports es
                LIMIT 3
            ");
            $stmt->execute();
        }
        
        echo json_encode(['success' => true, 'message' => 'Test data created successfully']);
        exit;
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        exit;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Final Navigation Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🎯 Final Navigation Test</h1>
        <p>Let's definitively fix the category navigation issue</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4>1. Database Status</h4>
                    </div>
                    <div class="card-body" id="database-status">
                        <p>Checking database...</p>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4>2. Create Test Data</h4>
                    </div>
                    <div class="card-body">
                        <p>If no data exists, create test data:</p>
                        <button class="btn btn-primary" onclick="createTestData()">
                            <i class="fas fa-plus"></i> Create Test Data
                        </button>
                        <div id="create-result" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h4>3. Working Navigation Links</h4>
            </div>
            <div class="card-body" id="navigation-links">
                <p>Loading navigation links...</p>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h4>4. Step-by-Step Test</h4>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5>🧪 Follow these steps to test navigation:</h5>
                    <ol>
                        <li><strong>Step 1:</strong> Click "Go to Categories Page" below</li>
                        <li><strong>Step 2:</strong> On the categories page, click on a category name</li>
                        <li><strong>Step 3:</strong> You should see the three-tab interface (Overview, Fixtures, Standings)</li>
                        <li><strong>Step 4:</strong> If you get redirected to events.php, there's still an issue</li>
                    </ol>
                </div>
                
                <div id="step-by-step-links"></div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h4>5. Debug Information</h4>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Direct Test Links:</h6>
                        <div id="direct-links"></div>
                    </div>
                    <div class="col-md-6">
                        <h6>Debug Mode Links:</h6>
                        <div id="debug-links"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            checkDatabase();
            loadNavigationLinks();
        });
        
        function checkDatabase() {
            const statusDiv = document.getElementById('database-status');
            
            fetch('quick-category-check.php')
                .then(response => response.text())
                .then(html => {
                    // Extract key information
                    const categoriesMatch = html.match(/Total categories:<\/strong> (\d+)/);
                    const categoriesCount = categoriesMatch ? parseInt(categoriesMatch[1]) : 0;
                    
                    let statusHtml = `<p><strong>Categories in database:</strong> ${categoriesCount}</p>`;
                    
                    if (categoriesCount === 0) {
                        statusHtml += '<div class="alert alert-danger">❌ No categories found! This is why navigation fails.</div>';
                    } else {
                        statusHtml += '<div class="alert alert-success">✅ Categories exist. Navigation should work.</div>';
                    }
                    
                    statusDiv.innerHTML = statusHtml;
                })
                .catch(error => {
                    statusDiv.innerHTML = `<div class="alert alert-danger">Error checking database: ${error.message}</div>`;
                });
        }
        
        function createTestData() {
            const resultDiv = document.getElementById('create-result');
            resultDiv.innerHTML = '<p class="text-info">Creating test data...</p>';
            
            fetch('final-navigation-test.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=create_test_data'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                    setTimeout(() => {
                        checkDatabase();
                        loadNavigationLinks();
                    }, 1000);
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">Error: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">Network error: ${error.message}</div>`;
            });
        }
        
        function loadNavigationLinks() {
            const linksDiv = document.getElementById('navigation-links');
            const stepDiv = document.getElementById('step-by-step-links');
            const directDiv = document.getElementById('direct-links');
            const debugDiv = document.getElementById('debug-links');
            
            fetch('ajax/get-test-data.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.event_sports.length > 0) {
                        const es = data.event_sports[0]; // Use first available
                        
                        // Main navigation links
                        const categoriesUrl = `sport-categories.php?event_id=${es.event_id}&sport_id=${es.sport_id}`;
                        const manageUrl = `manage-category.php?event_id=${es.event_id}&sport_id=${es.sport_id}&category_id=1`;
                        const debugUrl = `manage-category.php?event_id=${es.event_id}&sport_id=${es.sport_id}&category_id=1&debug=1`;
                        
                        linksDiv.innerHTML = `
                            <div class="alert alert-success">
                                <h6>✅ Navigation links available for: ${es.event_name} - ${es.sport_name}</h6>
                                <div class="d-flex gap-2 mt-2">
                                    <a href="${categoriesUrl}" class="btn btn-primary" target="_blank">
                                        <i class="fas fa-list"></i> Go to Categories Page
                                    </a>
                                    <a href="${manageUrl}" class="btn btn-success" target="_blank">
                                        <i class="fas fa-cog"></i> Direct Category Management
                                    </a>
                                </div>
                            </div>
                        `;
                        
                        stepDiv.innerHTML = `
                            <div class="d-flex gap-2">
                                <a href="${categoriesUrl}" class="btn btn-lg btn-primary" target="_blank">
                                    <i class="fas fa-arrow-right"></i> Step 1: Go to Categories Page
                                </a>
                            </div>
                            <p class="mt-2"><small class="text-muted">After clicking above, look for category names in the table and click on one.</small></p>
                        `;
                        
                        directDiv.innerHTML = `
                            <div class="list-group">
                                <a href="${manageUrl}" class="list-group-item list-group-item-action" target="_blank">
                                    Test Direct Link
                                </a>
                            </div>
                        `;
                        
                        debugDiv.innerHTML = `
                            <div class="list-group">
                                <a href="${debugUrl}" class="list-group-item list-group-item-action" target="_blank">
                                    Debug Mode Test
                                </a>
                            </div>
                        `;
                        
                    } else {
                        linksDiv.innerHTML = '<div class="alert alert-warning">No event-sport combinations available. Create test data first.</div>';
                    }
                })
                .catch(error => {
                    linksDiv.innerHTML = `<div class="alert alert-danger">Error loading links: ${error.message}</div>`;
                });
        }
    </script>
</body>
</html>
