<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🔧 Fix Missing Database Columns</h1>";
echo "<p>Adding missing columns to existing tournament tables...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $fixes_applied = [];
    $errors = [];
    
    echo "<h2>1. Checking Current Table Structures</h2>";
    
    // Function to check if column exists
    function columnExists($conn, $table, $column) {
        try {
            $stmt = $conn->query("DESCRIBE {$table}");
            $columns = $stmt->fetchAll();
            $existing_columns = array_column($columns, 'Field');
            return in_array($column, $existing_columns);
        } catch (Exception $e) {
            return false;
        }
    }
    
    // Function to add column safely
    function addColumnSafely($conn, $table, $column, $definition, &$fixes_applied, &$errors) {
        if (!columnExists($conn, $table, $column)) {
            echo "<p style='color: orange;'>⚠️ Adding missing column: {$table}.{$column}</p>";
            try {
                $sql = "ALTER TABLE {$table} ADD COLUMN {$column} {$definition}";
                $conn->exec($sql);
                echo "<p style='color: green;'>✅ Successfully added {$table}.{$column}</p>";
                $fixes_applied[] = "Added {$column} to {$table}";
                return true;
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Failed to add {$table}.{$column}: " . $e->getMessage() . "</p>";
                $errors[] = "Failed to add {$table}.{$column}: " . $e->getMessage();
                return false;
            }
        } else {
            echo "<p style='color: green;'>✅ Column {$table}.{$column} already exists</p>";
            return true;
        }
    }
    
    // ===== FIX TOURNAMENT_PARTICIPANTS TABLE =====
    echo "<h3>Fixing tournament_participants table...</h3>";
    
    // Check if table exists first
    try {
        $conn->query("SELECT 1 FROM tournament_participants LIMIT 1");
        echo "<p style='color: green;'>✅ tournament_participants table exists</p>";
        
        // Add missing columns
        addColumnSafely($conn, 'tournament_participants', 'tournament_structure_id', 'INT NOT NULL', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_participants', 'registration_id', 'INT NOT NULL', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_participants', 'seed_number', 'INT', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_participants', 'group_assignment', 'VARCHAR(10)', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_participants', 'current_status', "ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active'", $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_participants', 'points', 'DECIMAL(10,2) DEFAULT 0', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_participants', 'wins', 'INT DEFAULT 0', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_participants', 'losses', 'INT DEFAULT 0', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_participants', 'draws', 'INT DEFAULT 0', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_participants', 'performance_data', 'JSON', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_participants', 'created_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP', $fixes_applied, $errors);
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ tournament_participants table does not exist or has issues</p>";
        $errors[] = "tournament_participants table issue: " . $e->getMessage();
    }
    
    // ===== FIX TOURNAMENT_ROUNDS TABLE =====
    echo "<h3>Fixing tournament_rounds table...</h3>";
    
    try {
        $conn->query("SELECT 1 FROM tournament_rounds LIMIT 1");
        echo "<p style='color: green;'>✅ tournament_rounds table exists</p>";
        
        // Add missing columns
        addColumnSafely($conn, 'tournament_rounds', 'tournament_structure_id', 'INT NOT NULL', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_rounds', 'round_number', 'INT NOT NULL', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_rounds', 'round_name', 'VARCHAR(100) NOT NULL', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_rounds', 'round_type', "ENUM('group', 'elimination', 'final', 'consolation') DEFAULT 'elimination'", $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_rounds', 'status', "ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending'", $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_rounds', 'start_date', 'DATETIME', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_rounds', 'end_date', 'DATETIME', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_rounds', 'matches_count', 'INT DEFAULT 0', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_rounds', 'completed_matches', 'INT DEFAULT 0', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_rounds', 'advancement_criteria', 'JSON', $fixes_applied, $errors);
        addColumnSafely($conn, 'tournament_rounds', 'created_at', 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP', $fixes_applied, $errors);
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ tournament_rounds table does not exist or has issues</p>";
        $errors[] = "tournament_rounds table issue: " . $e->getMessage();
    }
    
    // ===== FIX MATCHES TABLE =====
    echo "<h3>Fixing matches table...</h3>";
    
    try {
        $conn->query("SELECT 1 FROM matches LIMIT 1");
        echo "<p style='color: green;'>✅ matches table exists</p>";
        
        // Add missing columns
        addColumnSafely($conn, 'matches', 'tournament_structure_id', 'INT', $fixes_applied, $errors);
        addColumnSafely($conn, 'matches', 'tournament_round_id', 'INT', $fixes_applied, $errors);
        addColumnSafely($conn, 'matches', 'bracket_position', 'VARCHAR(50)', $fixes_applied, $errors);
        addColumnSafely($conn, 'matches', 'is_bye_match', 'BOOLEAN DEFAULT FALSE', $fixes_applied, $errors);
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ matches table does not exist or has issues</p>";
        $errors[] = "matches table issue: " . $e->getMessage();
    }
    
    // ===== ADD FOREIGN KEY CONSTRAINTS (SAFELY) =====
    echo "<h2>2. Adding Foreign Key Constraints</h2>";
    
    // Function to add foreign key safely
    function addForeignKeySafely($conn, $table, $constraint_name, $column, $ref_table, $ref_column, &$fixes_applied, &$errors) {
        try {
            // Check if constraint already exists
            $stmt = $conn->prepare("
                SELECT CONSTRAINT_NAME 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = DATABASE() 
                AND TABLE_NAME = ? 
                AND CONSTRAINT_NAME = ?
            ");
            $stmt->execute([$table, $constraint_name]);
            
            if ($stmt->fetch()) {
                echo "<p style='color: green;'>✅ Foreign key {$constraint_name} already exists</p>";
                return true;
            }
            
            // Add the constraint
            $sql = "ALTER TABLE {$table} ADD CONSTRAINT {$constraint_name} 
                    FOREIGN KEY ({$column}) REFERENCES {$ref_table}({$ref_column}) ON DELETE CASCADE";
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Added foreign key constraint: {$constraint_name}</p>";
            $fixes_applied[] = "Added foreign key constraint: {$constraint_name}";
            return true;
            
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Foreign key {$constraint_name} issue: " . $e->getMessage() . "</p>";
            // Don't treat foreign key issues as critical errors
            return false;
        }
    }
    
    // Add foreign key constraints
    addForeignKeySafely($conn, 'tournament_participants', 'fk_tp_tournament_structure', 'tournament_structure_id', 'tournament_structures', 'id', $fixes_applied, $errors);
    addForeignKeySafely($conn, 'tournament_participants', 'fk_tp_registration', 'registration_id', 'registrations', 'id', $fixes_applied, $errors);
    addForeignKeySafely($conn, 'tournament_rounds', 'fk_tr_tournament_structure', 'tournament_structure_id', 'tournament_structures', 'id', $fixes_applied, $errors);
    addForeignKeySafely($conn, 'matches', 'fk_matches_tournament_structure', 'tournament_structure_id', 'tournament_structures', 'id', $fixes_applied, $errors);
    addForeignKeySafely($conn, 'matches', 'fk_matches_tournament_round', 'tournament_round_id', 'tournament_rounds', 'id', $fixes_applied, $errors);
    
    // ===== CREATE INDEXES (SAFELY) =====
    echo "<h2>3. Creating Performance Indexes</h2>";
    
    function addIndexSafely($conn, $index_name, $sql, &$fixes_applied, &$errors) {
        try {
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Created index: {$index_name}</p>";
            $fixes_applied[] = "Created index: {$index_name}";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<p style='color: green;'>✅ Index {$index_name} already exists</p>";
            } elseif (strpos($e->getMessage(), "doesn't exist in table") !== false) {
                echo "<p style='color: orange;'>⚠️ Cannot create index {$index_name}: Required column missing</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Index {$index_name} issue: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Only create indexes if the columns exist
    if (columnExists($conn, 'tournament_participants', 'tournament_structure_id')) {
        addIndexSafely($conn, 'idx_tournament_participants_tournament', 'CREATE INDEX idx_tournament_participants_tournament ON tournament_participants(tournament_structure_id)', $fixes_applied, $errors);
    }
    
    if (columnExists($conn, 'tournament_rounds', 'tournament_structure_id')) {
        addIndexSafely($conn, 'idx_tournament_rounds_tournament', 'CREATE INDEX idx_tournament_rounds_tournament ON tournament_rounds(tournament_structure_id)', $fixes_applied, $errors);
    }
    
    addIndexSafely($conn, 'idx_tournament_structures_event_sport', 'CREATE INDEX idx_tournament_structures_event_sport ON tournament_structures(event_sport_id)', $fixes_applied, $errors);
    addIndexSafely($conn, 'idx_tournament_structures_status', 'CREATE INDEX idx_tournament_structures_status ON tournament_structures(status)', $fixes_applied, $errors);
    addIndexSafely($conn, 'idx_matches_tournament_structure', 'CREATE INDEX idx_matches_tournament_structure ON matches(tournament_structure_id)', $fixes_applied, $errors);
    addIndexSafely($conn, 'idx_matches_tournament_round', 'CREATE INDEX idx_matches_tournament_round ON matches(tournament_round_id)', $fixes_applied, $errors);
    
    // ===== SUMMARY =====
    echo "<h2>4. Summary</h2>";
    
    if (!empty($fixes_applied)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ Fixes Applied Successfully</h3>";
        echo "<ul>";
        foreach ($fixes_applied as $fix) {
            echo "<li>{$fix}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($errors)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Errors Encountered</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>{$error}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (empty($fixes_applied) && empty($errors)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ All Columns Present</h3>";
        echo "<p>No missing columns were found. Database structure is complete.</p>";
        echo "</div>";
    }
    
    echo "<h3>Next Steps:</h3>";
    echo "<p><a href='tournament-status-dashboard.php?event_id=4&sport_id=37&category_id=15' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔍 Check Overall Status</a></p>";
    echo "<p><a href='manage-category.php?category_id=15&event_id=4&sport_id=37' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🚀 Test Tournament Generation</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Critical Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
