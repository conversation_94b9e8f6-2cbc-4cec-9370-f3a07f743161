<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🔍 Debug Tournament Format Issue</h1>";
echo "<p>Investigating the 'Configured tournament format not found in database' error...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>1. Check Tournament Formats Table</h2>";
    
    $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
    $stmt->execute();
    $formats = $stmt->fetchAll();
    
    if (empty($formats)) {
        echo "<p style='color: red;'>❌ No tournament formats found in database!</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($formats) . " tournament formats:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>ID</th><th>Name</th><th>Code</th><th>Algorithm Class</th>";
        echo "</tr>";
        foreach ($formats as $format) {
            echo "<tr>";
            echo "<td>{$format['id']}</td>";
            echo "<td>{$format['name']}</td>";
            echo "<td>{$format['code']}</td>";
            echo "<td>" . ($format['algorithm_class'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>2. Check Event Sports Configuration</h2>";
    
    // Check all event sports and their tournament format references
    $stmt = $conn->prepare("
        SELECT es.id, es.event_id, es.sport_id, es.tournament_format_id,
               e.name as event_name, s.name as sport_name,
               tf.name as format_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        ORDER BY es.id
    ");
    $stmt->execute();
    $event_sports = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th>Event Sport ID</th><th>Event</th><th>Sport</th><th>Format ID</th><th>Format Name</th><th>Status</th>";
    echo "</tr>";
    
    $invalid_references = [];
    
    foreach ($event_sports as $es) {
        $status = 'OK';
        $status_color = 'green';
        
        if ($es['tournament_format_id'] && !$es['format_name']) {
            $status = 'INVALID REFERENCE';
            $status_color = 'red';
            $invalid_references[] = $es;
        } elseif (!$es['tournament_format_id']) {
            $status = 'NO FORMAT SET';
            $status_color = 'orange';
        }
        
        echo "<tr>";
        echo "<td>{$es['id']}</td>";
        echo "<td>{$es['event_name']}</td>";
        echo "<td>{$es['sport_name']}</td>";
        echo "<td>" . ($es['tournament_format_id'] ?? 'NULL') . "</td>";
        echo "<td>" . ($es['format_name'] ?? 'NULL') . "</td>";
        echo "<td style='color: {$status_color};'><strong>{$status}</strong></td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>3. Fix Invalid References</h2>";
    
    if (!empty($invalid_references)) {
        echo "<p style='color: red;'>❌ Found " . count($invalid_references) . " invalid tournament format references!</p>";
        
        // Get the first available tournament format ID
        $stmt = $conn->prepare("SELECT id FROM tournament_formats ORDER BY id LIMIT 1");
        $stmt->execute();
        $defaultFormatId = $stmt->fetchColumn();
        
        if ($defaultFormatId) {
            echo "<p style='color: blue;'>ℹ️ Using tournament format ID {$defaultFormatId} as default...</p>";
            
            foreach ($invalid_references as $invalid) {
                $stmt = $conn->prepare("UPDATE event_sports SET tournament_format_id = ? WHERE id = ?");
                $stmt->execute([$defaultFormatId, $invalid['id']]);
                
                echo "<p style='color: green;'>✅ Fixed Event Sport ID {$invalid['id']} ({$invalid['event_name']} - {$invalid['sport_name']})</p>";
            }
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
            echo "<h3 style='color: #155724; margin-top: 0;'>🎉 <strong>FIXED!</strong></h3>";
            echo "<p>All invalid tournament format references have been corrected!</p>";
            echo "</div>";
            
        } else {
            echo "<p style='color: red;'>❌ No tournament formats available to use as default!</p>";
            echo "<p><a href='fix-tournament-format-constraints.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Create Tournament Formats</a></p>";
        }
        
    } else {
        echo "<p style='color: green;'>✅ All event sports have valid tournament format references!</p>";
    }
    
    echo "<h2>4. Test Specific Event Sport (Event 4, Sport 37)</h2>";
    
    $stmt = $conn->prepare("
        SELECT es.*, tf.name as format_name, tf.code as format_code
        FROM event_sports es
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.event_id = 4 AND es.sport_id = 37
    ");
    $stmt->execute();
    $testEventSport = $stmt->fetch();
    
    if ($testEventSport) {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><td><strong>Event Sport ID</strong></td><td>{$testEventSport['id']}</td></tr>";
        echo "<tr><td><strong>Tournament Format ID</strong></td><td>{$testEventSport['tournament_format_id']}</td></tr>";
        echo "<tr><td><strong>Format Name</strong></td><td>" . ($testEventSport['format_name'] ?? 'NULL') . "</td></tr>";
        echo "<tr><td><strong>Format Code</strong></td><td>" . ($testEventSport['format_code'] ?? 'NULL') . "</td></tr>";
        echo "</table>";
        
        if ($testEventSport['tournament_format_id'] && $testEventSport['format_name']) {
            echo "<p style='color: green;'>✅ Test event sport has valid tournament format!</p>";
        } else {
            echo "<p style='color: red;'>❌ Test event sport has invalid tournament format reference!</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Test event sport (Event 4, Sport 37) not found!</p>";
    }
    
    echo "<h2>5. Verification</h2>";
    echo "<p><a href='complete-tournament-test.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🧪 Run Tournament Test</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}
?>
