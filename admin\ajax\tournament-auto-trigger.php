<?php
/**
 * Tournament Auto-Trigger System
 * Automatically generates tournament brackets when conditions are met
 */

// Enable error reporting for debugging but prevent output
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Start output buffering to catch any unexpected output
ob_start();

// Set JSON response header early
header('Content-Type: application/json');

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

try {
    // Check admin authentication for AJAX request
    if (!isAdminLoggedIn()) {
        http_response_code(401);
        echo json_encode([
            'success' => false,
            'message' => 'Authentication required'
        ]);
        exit;
    }

    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();

    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'check_conditions':
            handleCheckConditions($conn, $input);
            break;
        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    // Clear any output buffer to prevent HTML errors
    ob_clean();

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'debug' => [
            'file' => __FILE__,
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ]
    ]);
} catch (Error $e) {
    // Clear any output buffer to prevent HTML errors
    ob_clean();

    echo json_encode([
        'success' => false,
        'message' => 'Fatal error: ' . $e->getMessage(),
        'debug' => [
            'file' => __FILE__,
            'line' => $e->getLine()
        ]
    ]);
}

// Clean up output buffer
ob_end_flush();

/**
 * Check if auto-trigger conditions are met for tournament generation
 */
function handleCheckConditions($conn, $input) {
    $eventId = $input['event_id'] ?? '';
    $sportId = $input['sport_id'] ?? '';
    $categoryId = $input['category_id'] ?? '';

    if (empty($eventId) || empty($sportId) || empty($categoryId)) {
        throw new Exception('Missing required parameters');
    }

    // Get category and tournament information
    // First, get the category by ID
    $stmt = $conn->prepare("
        SELECT
            sc.*,
            es.event_id,
            es.sport_id,
            es.tournament_format_id,
            es.status as event_sport_status,
            tf.name as format_name,
            tf.min_participants,
            tf.algorithm_class
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE sc.id = ?
    ");
    $stmt->execute([$categoryId]);
    $category = $stmt->fetch();

    if (!$category) {
        throw new Exception('Category not found');
    }

    // Verify the category belongs to the correct event and sport
    if ($category['event_id'] != $eventId || $category['sport_id'] != $sportId) {
        throw new Exception("Category belongs to different event/sport. Expected Event: $eventId, Sport: $sportId. Found Event: {$category['event_id']}, Sport: {$category['sport_id']}");
    }

    // Check if tournament already exists
    $stmt = $conn->prepare("
        SELECT COUNT(*) as tournament_count
        FROM tournament_structures ts
        JOIN event_sports es ON ts.event_sport_id = es.id
        WHERE es.event_id = ? AND es.sport_id = ? AND ts.status != 'cancelled'
    ");
    $stmt->execute([$eventId, $sportId]);
    $tournamentExists = $stmt->fetchColumn() > 0;

    // Get registered participants count
    $stmt = $conn->prepare("
        SELECT COUNT(*) as participant_count
        FROM event_department_registrations edr
        WHERE edr.event_id = ? AND edr.status = 'approved'
    ");
    $stmt->execute([$eventId]);
    $participantCount = $stmt->fetchColumn();

    // Check auto-trigger conditions
    $conditions = [
        'tournament_format_configured' => !empty($category['tournament_format_id']),
        'sufficient_participants' => $participantCount >= ($category['min_participants'] ?? 2),
        'no_existing_tournament' => !$tournamentExists,
        'category_status_ready' => in_array($category['status'] ?? 'draft', ['registration', 'ongoing']),
        'event_sport_active' => ($category['event_sport_status'] ?? 'draft') === 'registration'
    ];

    $autoGenerate = array_reduce($conditions, function($carry, $condition) {
        return $carry && $condition;
    }, true);

    // Log the check (only if logAdminActivity function exists)
    if (function_exists('logAdminActivity')) {
        logAdminActivity('AUTO_TRIGGER_CHECK', 'sport_categories', $categoryId, null, [
            'result' => $autoGenerate ? 'TRIGGERED' : 'NOT_TRIGGERED',
            'conditions' => $conditions,
            'participant_count' => $participantCount
        ]);
    }

    echo json_encode([
        'success' => true,
        'auto_generate' => $autoGenerate,
        'conditions' => $conditions,
        'participant_count' => $participantCount,
        'min_participants' => $category['min_participants'] ?? 2,
        'tournament_format' => $category['format_name'] ?? 'Not configured',
        'message' => $autoGenerate ?
            'Auto-generating tournament brackets...' :
            'Conditions not met for auto-generation'
    ]);
}
