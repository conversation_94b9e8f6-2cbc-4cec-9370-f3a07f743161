<?php
/**
 * Complete Navigation Fix
 * This will definitively fix the category navigation issue
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$action = $_POST['action'] ?? '';
$message = '';
$message_type = '';

if ($action === 'fix_all') {
    try {
        $conn->beginTransaction();
        
        // Step 1: Check if we have events and sports
        $events_count = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
        $sports_count = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
        
        if ($events_count == 0 || $sports_count == 0) {
            throw new Exception("Need events and sports first. Events: $events_count, Sports: $sports_count");
        }
        
        // Step 2: Ensure we have event_sports relationships
        $event_sports_count = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
        
        if ($event_sports_count == 0) {
            // Create some event_sports relationships
            $stmt = $conn->prepare("
                INSERT INTO event_sports (event_id, sport_id) 
                SELECT e.id, s.id 
                FROM events e 
                CROSS JOIN sports s 
                LIMIT 10
            ");
            $stmt->execute();
            $message .= "Created event-sport relationships. ";
        }
        
        // Step 3: Create categories
        $categories_count = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
        
        if ($categories_count == 0) {
            // Get event_sports to create categories for
            $stmt = $conn->prepare("
                SELECT es.id as event_sport_id, e.name as event_name, s.name as sport_name
                FROM event_sports es
                JOIN events e ON es.event_id = e.id
                JOIN sports s ON es.sport_id = s.id
                LIMIT 5
            ");
            $stmt->execute();
            $event_sports = $stmt->fetchAll();
            
            $created = 0;
            foreach ($event_sports as $es) {
                // Create Men's and Women's categories
                $categories = [
                    ['name' => "Men's " . $es['sport_name'], 'type' => 'men'],
                    ['name' => "Women's " . $es['sport_name'], 'type' => 'women']
                ];
                
                foreach ($categories as $cat) {
                    $stmt = $conn->prepare("
                        INSERT INTO sport_categories 
                        (event_sport_id, category_name, category_type, referee_name, referee_email, venue)
                        VALUES (?, ?, ?, ?, ?, ?)
                    ");
                    
                    $stmt->execute([
                        $es['event_sport_id'],
                        $cat['name'],
                        $cat['type'],
                        'Test Referee',
                        '<EMAIL>',
                        'Main Venue'
                    ]);
                    $created++;
                }
            }
            $message .= "Created $created categories. ";
        }
        
        $conn->commit();
        $message .= "Navigation fix completed successfully!";
        $message_type = 'success';
        
    } catch (Exception $e) {
        $conn->rollback();
        $message = "Error: " . $e->getMessage();
        $message_type = 'danger';
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Navigation Fix</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🔧 Complete Navigation Fix</h1>
        <p>This will definitively fix the category navigation issue</p>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>Current Database Status</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    $events_count = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
                    $sports_count = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
                    $event_sports_count = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
                    $categories_count = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
                    
                    echo "<div class='row'>";
                    echo "<div class='col-md-3'><div class='card text-center'><div class='card-body'><h5>$events_count</h5><p>Events</p></div></div></div>";
                    echo "<div class='col-md-3'><div class='card text-center'><div class='card-body'><h5>$sports_count</h5><p>Sports</p></div></div></div>";
                    echo "<div class='col-md-3'><div class='card text-center'><div class='card-body'><h5>$event_sports_count</h5><p>Event-Sports</p></div></div></div>";
                    echo "<div class='col-md-3'><div class='card text-center'><div class='card-body'><h5>$categories_count</h5><p>Categories</p></div></div></div>";
                    echo "</div>";
                    
                    $needs_fix = ($events_count == 0 || $sports_count == 0 || $event_sports_count == 0 || $categories_count == 0);
                    
                    if ($needs_fix) {
                        echo "<div class='alert alert-warning mt-3'>";
                        echo "<strong>⚠️ Issues Found:</strong><br>";
                        if ($events_count == 0) echo "- No events exist<br>";
                        if ($sports_count == 0) echo "- No sports exist<br>";
                        if ($event_sports_count == 0) echo "- No event-sport relationships<br>";
                        if ($categories_count == 0) echo "- No categories exist<br>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-success mt-3'>";
                        echo "<strong>✅ Database looks good!</strong> Navigation should work.";
                        echo "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database Error: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
        
        <?php if ($needs_fix ?? false): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h3>Fix Navigation</h3>
            </div>
            <div class="card-body">
                <p>Click the button below to automatically fix all navigation issues:</p>
                <form method="POST">
                    <input type="hidden" name="action" value="fix_all">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-wrench"></i> Fix All Navigation Issues
                    </button>
                </form>
            </div>
        </div>
        <?php endif; ?>
        
        <div class="card">
            <div class="card-header">
                <h3>Test Navigation</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    // Get working navigation links
                    $stmt = $conn->prepare("
                        SELECT sc.id as category_id, sc.category_name,
                               es.event_id, es.sport_id,
                               e.name as event_name, s.name as sport_name
                        FROM sport_categories sc
                        JOIN event_sports es ON sc.event_sport_id = es.id
                        JOIN events e ON es.event_id = e.id
                        JOIN sports s ON es.sport_id = s.id
                        ORDER BY e.name, s.name, sc.category_name
                        LIMIT 10
                    ");
                    $stmt->execute();
                    $working_links = $stmt->fetchAll();
                    
                    if ($working_links) {
                        echo "<h5>✅ Working Navigation Links:</h5>";
                        echo "<div class='row'>";
                        
                        foreach ($working_links as $link) {
                            $categories_url = "sport-categories.php?event_id={$link['event_id']}&sport_id={$link['sport_id']}";
                            $manage_url = "manage-category.php?event_id={$link['event_id']}&sport_id={$link['sport_id']}&category_id={$link['category_id']}";
                            $debug_url = "manage-category.php?event_id={$link['event_id']}&sport_id={$link['sport_id']}&category_id={$link['category_id']}&debug=1";
                            
                            echo "<div class='col-md-6 mb-3'>";
                            echo "<div class='card'>";
                            echo "<div class='card-body'>";
                            echo "<h6 class='card-title'>" . htmlspecialchars($link['category_name']) . "</h6>";
                            echo "<p class='card-text'>";
                            echo "<small class='text-muted'>" . htmlspecialchars($link['event_name']) . " - " . htmlspecialchars($link['sport_name']) . "</small>";
                            echo "</p>";
                            echo "<div class='d-flex gap-2 flex-wrap'>";
                            echo "<a href='$categories_url' class='btn btn-sm btn-outline-primary' target='_blank'>Categories Page</a>";
                            echo "<a href='$manage_url' class='btn btn-sm btn-success' target='_blank'>Direct Link</a>";
                            echo "<a href='$debug_url' class='btn btn-sm btn-warning' target='_blank'>Debug Mode</a>";
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                        }
                        
                        echo "</div>";
                        
                        echo "<div class='alert alert-info mt-3'>";
                        echo "<h6>🧪 Testing Instructions:</h6>";
                        echo "<ol>";
                        echo "<li><strong>Categories Page:</strong> Go to the sport categories listing</li>";
                        echo "<li><strong>Direct Link:</strong> Test the manage-category.php page directly</li>";
                        echo "<li><strong>Debug Mode:</strong> See detailed error information if something fails</li>";
                        echo "</ol>";
                        echo "<p><strong>Expected behavior:</strong> Clicking a category name should take you to the three-tab management interface (Overview, Fixtures, Standings).</p>";
                        echo "</div>";
                        
                    } else {
                        echo "<div class='alert alert-warning'>No working links available. Please fix the database issues first.</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Error loading test links: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
    </div>
</body>
</html>
