<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

// Get the tournament
$stmt = $conn->prepare("
    SELECT ts.*, tf.name as format_name
    FROM tournament_structures ts
    LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
    WHERE ts.event_sport_id = 52
    ORDER BY ts.created_at DESC
    LIMIT 1
");
$stmt->execute();
$tournament = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$tournament) {
    die("No tournament found");
}

$tournament_id = $tournament['id'];
?>
<!DOCTYPE html>
<html>
<head>
    <title>Bracket Test</title>
    <link rel="stylesheet" href="assets/css/bracket-styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; border: 1px solid #ccc; }
    </style>
</head>
<body>
    <h1>🏆 Tournament Bracket Test</h1>
    
    <div class="debug">
        <strong>Tournament Info:</strong><br>
        ID: <?php echo $tournament['id']; ?><br>
        Format: <?php echo $tournament['format_name']; ?><br>
        Event Sport ID: <?php echo $tournament['event_sport_id']; ?>
    </div>

    <?php
    // Test matches query directly
    $stmt = $conn->prepare("
        SELECT
            m.*,
            d1.name as team1_name,
            d1.abbreviation as team1_department,
            d2.name as team2_name,
            d2.abbreviation as team2_department
        FROM matches m
        LEFT JOIN departments d1 ON m.team1_id = d1.id
        LEFT JOIN departments d2 ON m.team2_id = d2.id
        WHERE m.tournament_structure_id = ?
        ORDER BY m.round_number, m.match_number
    ");
    $stmt->execute([$tournament_id]);
    $matches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    ?>

    <div class="debug">
        <strong>Matches Found:</strong> <?php echo count($matches); ?>
        <?php if ($matches): ?>
            <ul>
                <?php foreach ($matches as $match): ?>
                    <li>
                        Round <?php echo $match['round_number']; ?>, Match <?php echo $match['match_number']; ?>: 
                        <?php echo $match['team1_name'] ?? 'TBD'; ?> vs <?php echo $match['team2_name'] ?? 'TBD'; ?>
                        (Status: <?php echo $match['status']; ?>)
                    </li>
                <?php endforeach; ?>
            </ul>
        <?php endif; ?>
    </div>

    <h2>Bracket Display:</h2>
    <?php
    require_once 'includes/bracket_display.php';
    $bracketDisplay = new BracketDisplay($conn, $tournament_id, 52);
    echo $bracketDisplay->renderBracket();
    ?>

    <p><a href="manage-category.php?event_id=4&sport_id=37&category_id=16">← Back to Category Management</a></p>
</body>
</html>
