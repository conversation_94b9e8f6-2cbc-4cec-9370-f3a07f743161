<?php
require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Enhanced Bracket System Test</title>
    <link rel='stylesheet' href='assets/css/bracket-styles.css'>
    <link rel='stylesheet' href='assets/css/bracket-modals.css'>
    <link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .test-section { background: white; padding: 20px; margin: 20px 0; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-title { color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
        .status { padding: 5px 10px; border-radius: 5px; margin: 5px 0; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>";

echo "<h1>Enhanced Bracket System Test</h1>";

// Test 1: Check CSS files
echo "<div class='test-section'>
<h2 class='test-title'>1. CSS Files Test</h2>";

$css_files = [
    'assets/css/bracket-styles.css',
    'assets/css/bracket-modals.css'
];

foreach ($css_files as $file) {
    if (file_exists($file)) {
        echo "<div class='status success'>✅ $file exists</div>";
    } else {
        echo "<div class='status error'>❌ $file missing</div>";
    }
}
echo "</div>";

// Test 2: Check JavaScript files
echo "<div class='test-section'>
<h2 class='test-title'>2. JavaScript Files Test</h2>";

$js_files = [
    'assets/js/bracket-modals.js'
];

foreach ($js_files as $file) {
    if (file_exists($file)) {
        echo "<div class='status success'>✅ $file exists</div>";
    } else {
        echo "<div class='status error'>❌ $file missing</div>";
    }
}
echo "</div>";

// Test 3: Database tables
echo "<div class='test-section'>
<h2 class='test-title'>3. Database Tables Test</h2>";

$tables = [
    'tournament_structures',
    'tournament_matches',
    'departments',
    'tournament_formats'
];

foreach ($tables as $table) {
    try {
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "<div class='status success'>✅ Table '$table' exists</div>";
        } else {
            echo "<div class='status error'>❌ Table '$table' missing</div>";
        }
    } catch (Exception $e) {
        echo "<div class='status error'>❌ Error checking table '$table': " . $e->getMessage() . "</div>";
    }
}
echo "</div>";

// Test 4: Enhanced Bracket Display
echo "<div class='test-section'>
<h2 class='test-title'>4. Enhanced Bracket Display Test</h2>";

// Create sample bracket data to test the enhanced display
echo "<div class='status info'>Testing enhanced bracket styling with sample data...</div>";

// Sample matches for demonstration
$sample_matches = [
    [
        'id' => 'test_1',
        'round_number' => 1,
        'match_number' => 1,
        'team1_name' => 'Computer Science',
        'team1_department' => 'CS',
        'team1_score' => 15,
        'team2_name' => 'Information Technology',
        'team2_department' => 'IT',
        'team2_score' => 12,
        'status' => 'completed',
        'winner_id' => 1,
        'team1_id' => 1,
        'team2_id' => 2,
        'format_name' => 'Single Elimination',
        'scheduled_time' => '2024-01-15 14:00:00'
    ],
    [
        'id' => 'test_2',
        'round_number' => 1,
        'match_number' => 2,
        'team1_name' => 'Business Administration',
        'team1_department' => 'BA',
        'team1_score' => null,
        'team2_name' => 'Engineering',
        'team2_department' => 'ENG',
        'team2_score' => null,
        'status' => 'pending',
        'winner_id' => null,
        'team1_id' => 3,
        'team2_id' => 4,
        'format_name' => 'Single Elimination',
        'scheduled_time' => '2024-01-15 14:30:00'
    ]
];

// Render enhanced bracket with sample data
echo "<div class='sc-bracket-container'>
    <div class='sc-bracket-notice'>
        <i class='fas fa-trophy'></i>
        Enhanced Bracket Display Test - Sample Tournament Data
    </div>";

// Group by rounds
$rounds = [];
foreach ($sample_matches as $match) {
    $rounds[$match['round_number']][] = $match;
}

foreach ($rounds as $round_number => $round_matches) {
    echo "<div class='sc-round'>
        <div class='sc-round-header'>Round $round_number</div>
        <div class='sc-round-matches'>";
    
    foreach ($round_matches as $match) {
        $statusClass = 'sc-match-' . $match['status'];
        $isTeam1Winner = isset($match['winner_id']) && $match['winner_id'] == $match['team1_id'];
        $isTeam2Winner = isset($match['winner_id']) && $match['winner_id'] == $match['team2_id'];
        
        echo "<div class='sc-match-card $statusClass' data-match-id='{$match['id']}'>
            <!-- Match Header -->
            <div class='sc-match-header'>
                <div class='sc-match-info'>
                    <span class='sc-match-number'>R{$match['round_number']}M{$match['match_number']}</span>
                    <span class='sc-match-format'>{$match['format_name']}</span>
                </div>
                <div class='sc-match-actions'>
                    <button class='sc-btn sc-btn-edit' onclick='testEditModal(\"{$match['id']}\")' title='Edit Match'>
                        <i class='fas fa-edit'></i>
                    </button>
                    <button class='sc-btn sc-btn-referee' onclick='testRefereeFunction(\"{$match['id']}\")' title='Send to Referee'>
                        <i class='fas fa-whistle'></i>
                    </button>
                </div>
            </div>
            
            <!-- Match Content -->
            <div class='sc-match-content'>
                <!-- Team 1 -->
                <div class='sc-team-container team1 " . ($isTeam1Winner ? 'winner' : '') . "'>
                    <div class='sc-team-avatar'>
                        <i class='fas fa-users'></i>
                    </div>
                    <div class='sc-team-details'>
                        <div class='sc-team-name'>{$match['team1_name']}</div>
                        <div class='sc-team-department'>{$match['team1_department']}</div>
                    </div>
                    <div class='sc-team-score'>
                        <span class='sc-score-value'>" . ($match['team1_score'] ?? '-') . "</span>
                    </div>
                </div>
                
                <!-- VS Separator -->
                <div class='sc-match-vs'>
                    <div class='sc-vs-circle'>
                        <span>VS</span>
                    </div>
                </div>
                
                <!-- Team 2 -->
                <div class='sc-team-container team2 " . ($isTeam2Winner ? 'winner' : '') . "'>
                    <div class='sc-team-avatar'>
                        <i class='fas fa-users'></i>
                    </div>
                    <div class='sc-team-details'>
                        <div class='sc-team-name'>{$match['team2_name']}</div>
                        <div class='sc-team-department'>{$match['team2_department']}</div>
                    </div>
                    <div class='sc-team-score'>
                        <span class='sc-score-value'>" . ($match['team2_score'] ?? '-') . "</span>
                    </div>
                </div>
            </div>
            
            <!-- Match Footer -->
            <div class='sc-match-footer'>
                <div class='sc-match-status'>
                    <span class='sc-status-badge sc-status-{$match['status']}'>
                        <i class='fas fa-" . ($match['status'] === 'completed' ? 'check-circle' : ($match['status'] === 'in_progress' ? 'play-circle' : 'clock')) . "'></i>
                        " . ucfirst(str_replace('_', ' ', $match['status'])) . "
                    </span>
                </div>
                <div class='sc-match-time'>
                    <i class='fas fa-clock'></i>
                    " . date('H:i', strtotime($match['scheduled_time'])) . "
                </div>
            </div>
        </div>";
    }
    
    echo "</div></div>";
}

echo "</div>";

echo "<div class='status success'>✅ Enhanced bracket display rendered successfully!</div>";
echo "</div>";

// Test 5: Modal System Test
echo "<div class='test-section'>
<h2 class='test-title'>5. Modal System Test</h2>
<button onclick='testModal()' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>
    <i class='fas fa-edit'></i> Test Modal Interface
</button>
<div class='status info'>Click the button above to test the modal interface</div>
</div>";

echo "<script>
function testEditModal(matchId) {
    alert('Edit modal would open for match: ' + matchId + '\\n\\nThis demonstrates the enhanced bracket interface with clickable edit buttons.');
}

function testRefereeFunction(matchId) {
    alert('Referee function called for match: ' + matchId + '\\n\\nThis would send the match to a referee with a secure access link.');
}

function testModal() {
    alert('Modal system is working!\\n\\nThe enhanced bracket interface includes:\\n\\n• Modal-based match editing\\n• Unique SC_IMS visual design\\n• Referee integration\\n• Responsive layout\\n• Professional animations');
}

// Test CSS loading
document.addEventListener('DOMContentLoaded', function() {
    const testElement = document.querySelector('.sc-bracket-container');
    if (testElement) {
        const styles = window.getComputedStyle(testElement);
        const hasGradient = styles.background.includes('gradient') || styles.backgroundImage.includes('gradient');
        
        if (hasGradient) {
            console.log('✅ Enhanced CSS styles are loading correctly');
        } else {
            console.log('❌ Enhanced CSS styles may not be loading properly');
        }
    }
});
</script>";

echo "</body></html>";
?>
