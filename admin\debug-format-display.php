<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🔍 Debug Tournament Format Display</h1>";
echo "<p>Investigating why tournament format is not displaying correctly...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $event_id = $_GET['event_id'] ?? 4;
    $sport_id = $_GET['sport_id'] ?? 37;
    $category_id = $_GET['category_id'] ?? 15;
    
    echo "<h2>1. Category and Event Sport Information</h2>";
    
    // Get category information (same query as manage-category.php)
    $stmt = $conn->prepare("
        SELECT
            sc.*,
            e.name as event_name,
            e.description as event_description,
            e.start_date,
            e.end_date,
            s.name as sport_name,
            s.type as sport_type,
            s.description as sport_description,
            es.id as event_sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ? AND es.event_id = ? AND s.id = ?
    ");
    $stmt->execute([$category_id, $event_id, $sport_id]);
    $category = $stmt->fetch();
    
    if (!$category) {
        echo "<p style='color: red;'>❌ Category not found</p>";
        exit;
    }
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<p><strong>Category ID:</strong> {$category['id']}</p>";
    echo "<p><strong>Category Name:</strong> {$category['category_name']}</p>";
    echo "<p><strong>Event Sport ID:</strong> {$category['event_sport_id']}</p>";
    echo "<p><strong>Sport:</strong> {$category['sport_name']}</p>";
    echo "<p><strong>Event:</strong> {$category['event_name']}</p>";
    echo "</div>";
    
    echo "<h2>2. Event Sport Configuration (Step by Step)</h2>";
    
    // Step 1: Get event sport configuration (exact same query as manage-category.php)
    echo "<h3>Step 1: Event Sport Tournament Format Query</h3>";
    $stmt = $conn->prepare("
        SELECT
            es.tournament_format_id,
            es.bracket_type,
            tf.name as format_name,
            tf.description as format_description,
            tf.code as format_code
        FROM event_sports es
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.id = ?
    ");
    $stmt->execute([$category['event_sport_id']]);
    $event_sport_config = $stmt->fetch();
    
    if ($event_sport_config) {
        echo "<p style='color: green;'>✅ Event sport configuration found</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>Field</th><th>Value</th>";
        echo "</tr>";
        echo "<tr><td>tournament_format_id</td><td>" . ($event_sport_config['tournament_format_id'] ?? 'NULL') . "</td></tr>";
        echo "<tr><td>bracket_type</td><td>" . ($event_sport_config['bracket_type'] ?? 'NULL') . "</td></tr>";
        echo "<tr><td>format_name</td><td>" . ($event_sport_config['format_name'] ?? 'NULL') . "</td></tr>";
        echo "<tr><td>format_description</td><td>" . ($event_sport_config['format_description'] ?? 'NULL') . "</td></tr>";
        echo "<tr><td>format_code</td><td>" . ($event_sport_config['format_code'] ?? 'NULL') . "</td></tr>";
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No event sport configuration found</p>";
    }
    
    echo "<h3>Step 2: Tournament Format Logic (Replicate manage-category.php)</h3>";
    
    $tournament_format = null;
    
    if ($event_sport_config) {
        if ($event_sport_config['format_name']) {
            // Use the configured tournament format from event sports
            $tournament_format = [
                'format_name' => $event_sport_config['format_name'],
                'format_description' => $event_sport_config['format_description'] ?? 'Tournament format configured for this event',
                'format_code' => $event_sport_config['format_code'],
                'tournament_status' => 'configured',
                'participant_count' => 0,
                'total_rounds' => 0,
                'current_round' => 0,
                'seeding_method' => 'random'
            ];
            echo "<p style='color: green;'>✅ Using configured tournament format: {$event_sport_config['format_name']}</p>";
        } elseif ($event_sport_config['bracket_type']) {
            // Use legacy bracket_type from event sports
            $tournament_format = [
                'format_name' => ucwords(str_replace('_', ' ', $event_sport_config['bracket_type'])),
                'format_description' => 'Tournament format configured for this event',
                'format_code' => $event_sport_config['bracket_type'],
                'tournament_status' => 'configured',
                'participant_count' => 0,
                'total_rounds' => 0,
                'current_round' => 0,
                'seeding_method' => 'random'
            ];
            echo "<p style='color: orange;'>⚠️ Using legacy bracket type: {$event_sport_config['bracket_type']}</p>";
        } else {
            echo "<p style='color: red;'>❌ No tournament format or bracket type configured</p>";
        }
    }
    
    echo "<h3>Step 3: Fallback to Sport Default</h3>";
    
    // If no event sport format found, fall back to sport default
    if (!$tournament_format) {
        $stmt = $conn->prepare("SELECT bracket_format FROM sports WHERE id = ?");
        $stmt->execute([$sport_id]);
        $sport_format = $stmt->fetch();
        
        if ($sport_format && $sport_format['bracket_format']) {
            $tournament_format = [
                'format_name' => ucwords(str_replace('_', ' ', $sport_format['bracket_format'])),
                'format_description' => 'Default format for this sport (not configured for event)',
                'format_code' => $sport_format['bracket_format'],
                'tournament_status' => 'default',
                'participant_count' => 0,
                'total_rounds' => 0,
                'current_round' => 0,
                'seeding_method' => 'random'
            ];
            echo "<p style='color: orange;'>⚠️ Using sport default format: {$sport_format['bracket_format']}</p>";
        } else {
            echo "<p style='color: red;'>❌ No sport default format found</p>";
        }
    }
    
    echo "<h2>3. Final Tournament Format Result</h2>";
    
    if ($tournament_format) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>✅ Tournament Format Found</h4>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>Property</th><th>Value</th>";
        echo "</tr>";
        foreach ($tournament_format as $key => $value) {
            echo "<tr>";
            echo "<td>{$key}</td>";
            echo "<td>" . htmlspecialchars($value) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        echo "<h3>Display Preview (as it should appear in manage-category.php):</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 8px;'>";
        echo "<p><strong>Tournament Format:</strong> ";
        echo '<span style="color: #007bff; font-weight: 600;">' . htmlspecialchars($tournament_format['format_name']) . '</span>';
        if (!empty($tournament_format['format_description'])) {
            echo '<br><small style="color: #6c757d;">' . htmlspecialchars($tournament_format['format_description']) . '</small>';
        }
        if ($tournament_format['tournament_status'] !== 'setup') {
            echo '<br><small style="color: #28a745;">Status: ' . ucfirst($tournament_format['tournament_status']) . '</small>';
        }
        echo "</p>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
        echo "<h4 style='color: #721c24; margin-top: 0;'>❌ No Tournament Format Found</h4>";
        echo "<p>This is why the format is not displaying in manage-category.php</p>";
        echo "</div>";
        
        echo "<h3>Display Preview (as it appears in manage-category.php):</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border: 1px solid #dee2e6; border-radius: 8px;'>";
        echo "<p><strong>Tournament Format:</strong> ";
        echo '<span style="color: #6c757d;">Not configured</span>';
        echo '<br><small style="color: #ffc107;">⚠️ Tournament format needs to be set up</small>';
        echo "</p>";
        echo "</div>";
    }
    
    echo "<h2>4. Auto-Generation Condition Check</h2>";
    
    // Check participants
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM registrations r
        WHERE r.event_sport_id = ? AND r.status IN ('confirmed', 'registered', 'approved')
    ");
    $stmt->execute([$category['event_sport_id']]);
    $participant_count = $stmt->fetch()['count'];
    
    echo "<p><strong>Participants:</strong> {$participant_count}</p>";
    echo "<p><strong>Tournament Format:</strong> " . ($tournament_format ? "✅ Found" : "❌ Not found") . "</p>";
    echo "<p><strong>Format Status:</strong> " . ($tournament_format ? $tournament_format['tournament_status'] : "N/A") . "</p>";
    
    $auto_generation_possible = false;
    if ($participant_count >= 2 && $tournament_format && $tournament_format['tournament_status'] === 'configured') {
        echo "<p style='color: green;'>✅ Auto-generation conditions met</p>";
        $auto_generation_possible = true;
    } else {
        echo "<p style='color: red;'>❌ Auto-generation conditions NOT met</p>";
        if ($participant_count < 2) {
            echo "<p style='color: red;'>  - Insufficient participants ({$participant_count} < 2)</p>";
        }
        if (!$tournament_format) {
            echo "<p style='color: red;'>  - No tournament format found</p>";
        } elseif ($tournament_format['tournament_status'] !== 'configured') {
            echo "<p style='color: red;'>  - Tournament format status is '{$tournament_format['tournament_status']}' (needs 'configured')</p>";
        }
    }
    
    echo "<h2>5. Quick Fixes</h2>";
    
    if (!$tournament_format || $tournament_format['tournament_status'] !== 'configured') {
        echo "<p><a href='fix-tournament-format.php?event_id={$event_id}&sport_id={$sport_id}' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔧 Configure Tournament Format</a></p>";
    }
    
    if ($participant_count < 2) {
        echo "<p><a href='add-test-participants.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>👥 Add More Participants</a></p>";
    }
    
    if ($auto_generation_possible) {
        echo "<p><a href='manage-category.php?category_id={$category_id}&event_id={$event_id}&sport_id={$sport_id}' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🚀 Test Auto-Generation</a></p>";
    }
    
    echo "<p><a href='tournament-diagnostic-tool.php?event_id={$event_id}&sport_id={$sport_id}&category_id={$category_id}' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔍 Full Diagnostic</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Debug Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
