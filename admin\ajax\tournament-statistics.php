<?php
/**
 * Tournament Statistics
 * Provides real-time tournament statistics and metrics
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'get_statistics':
            handleGetStatistics($conn, $input);
            break;
        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get comprehensive tournament statistics
 */
function handleGetStatistics($conn, $input) {
    $eventId = $input['event_id'] ?? '';
    $sportId = $input['sport_id'] ?? '';
    $categoryId = $input['category_id'] ?? '';

    if (empty($eventId) || empty($sportId) || empty($categoryId)) {
        throw new Exception('Missing required parameters');
    }

    // Get tournament structure
    $stmt = $conn->prepare("
        SELECT 
            ts.*,
            tf.name as format_name,
            tf.code as format_code
        FROM tournament_structures ts
        JOIN event_sports es ON ts.event_sport_id = es.id
        LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ? AND ts.status != 'cancelled'
        ORDER BY ts.created_at DESC
        LIMIT 1
    ");
    $stmt->execute([$eventId, $sportId]);
    $tournament = $stmt->fetch();

    // Get participant count
    $stmt = $conn->prepare("
        SELECT COUNT(*) as participant_count
        FROM event_department_registrations edr
        WHERE edr.event_id = ? AND edr.status = 'approved'
    ");
    $stmt->execute([$eventId]);
    $participantCount = $stmt->fetchColumn();

    $statistics = [
        'participants' => $participantCount,
        'total_matches' => 0,
        'completed_matches' => 0,
        'pending_matches' => 0,
        'ongoing_matches' => 0,
        'cancelled_matches' => 0,
        'tournament_status' => 'not_started',
        'current_round' => 0,
        'total_rounds' => 0,
        'completion_percentage' => 0
    ];

    if ($tournament) {
        // Get match statistics
        $stmt = $conn->prepare("
            SELECT 
                COUNT(*) as total_matches,
                SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_matches,
                SUM(CASE WHEN status = 'ongoing' THEN 1 ELSE 0 END) as ongoing_matches,
                SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_matches,
                SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as pending_matches
            FROM matches 
            WHERE tournament_structure_id = ?
        ");
        $stmt->execute([$tournament['id']]);
        $matchStats = $stmt->fetch();

        // Calculate completion percentage
        $completionPercentage = 0;
        if ($matchStats['total_matches'] > 0) {
            $completionPercentage = round(
                ($matchStats['completed_matches'] / $matchStats['total_matches']) * 100, 
                1
            );
        }

        $statistics = [
            'participants' => $tournament['participant_count'],
            'total_matches' => $matchStats['total_matches'],
            'completed_matches' => $matchStats['completed_matches'],
            'pending_matches' => $matchStats['pending_matches'],
            'ongoing_matches' => $matchStats['ongoing_matches'],
            'cancelled_matches' => $matchStats['cancelled_matches'],
            'tournament_status' => $tournament['status'],
            'current_round' => $tournament['current_round'],
            'total_rounds' => $tournament['total_rounds'],
            'completion_percentage' => $completionPercentage,
            'tournament_format' => $tournament['format_name'],
            'tournament_id' => $tournament['id']
        ];

        // Get additional round-specific statistics
        if ($tournament['current_round'] > 0) {
            $stmt = $conn->prepare("
                SELECT 
                    round_number,
                    COUNT(*) as round_matches,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as round_completed
                FROM matches 
                WHERE tournament_structure_id = ?
                GROUP BY round_number
                ORDER BY round_number
            ");
            $stmt->execute([$tournament['id']]);
            $roundStats = $stmt->fetchAll();

            $statistics['round_breakdown'] = $roundStats;
        }

        // Get top performers (if matches have been played)
        if ($matchStats['completed_matches'] > 0) {
            $stmt = $conn->prepare("
                SELECT 
                    d.name,
                    d.abbreviation,
                    COUNT(m.id) as matches_played,
                    SUM(CASE WHEN m.winner_id = d.id THEN 1 ELSE 0 END) as wins,
                    SUM(CASE 
                        WHEN m.team1_id = d.id THEN COALESCE(m.team_a_score, 0)
                        WHEN m.team2_id = d.id THEN COALESCE(m.team_b_score, 0)
                        ELSE 0
                    END) as total_score
                FROM departments d
                JOIN event_department_registrations edr ON d.id = edr.department_id
                LEFT JOIN matches m ON (m.team1_id = d.id OR m.team2_id = d.id) 
                    AND m.tournament_structure_id = ? AND m.status = 'completed'
                WHERE edr.event_id = ? AND edr.status = 'approved'
                GROUP BY d.id, d.name, d.abbreviation
                HAVING matches_played > 0
                ORDER BY wins DESC, total_score DESC
                LIMIT 3
            ");
            $stmt->execute([$tournament['id'], $eventId]);
            $topPerformers = $stmt->fetchAll();

            $statistics['top_performers'] = $topPerformers;
        }
    }

    echo json_encode([
        'success' => true,
        'statistics' => $statistics,
        'last_updated' => date('Y-m-d H:i:s')
    ]);
}
