<?php
/**
 * Test Tournament Format Fix
 * Verify that Academic and Judged sports show appropriate tournament format options
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get available sports for testing
$available_sports = getAvailableSports($conn, 1); // Use event ID 1 for testing

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Tournament Format Fix - SC_IMS</title>
    <link rel="stylesheet" href="../assets/css/admin-styles.css">
    <style>
        .test-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        .debug-output {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 Tournament Format Fix Test</h1>
        <p>This page tests whether Academic and Judged sports now show appropriate tournament format options.</p>
        
        <div class="test-section">
            <h3>Test 1: Academic Sports</h3>
            <div class="form-group">
                <label class="form-label" for="academic_sport">Select an Academic Sport:</label>
                <select name="academic_sport" id="academic_sport" class="form-control" onchange="testTournamentFormats('academic_sport', 'academic_formats')">
                    <option value="">Select an academic sport...</option>
                    <?php foreach ($available_sports as $sport): ?>
                        <?php if (($sport['sport_type_category'] ?? $sport['type']) === 'academic'): ?>
                            <option value="<?php echo $sport['id']; ?>" 
                                    data-type="<?php echo $sport['sport_type_category'] ?? $sport['type']; ?>">
                                <?php echo htmlspecialchars($sport['name']); ?>
                                (<?php echo htmlspecialchars($sport['sport_type_name'] ?? ucfirst($sport['type'])); ?>)
                            </option>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="academic_formats">Tournament Formats:</label>
                <select name="academic_formats" id="academic_formats" class="form-control">
                    <option value="">Select a sport first...</option>
                </select>
            </div>
            
            <div id="academic_debug" class="debug-output" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>Test 2: Judged Sports</h3>
            <div class="form-group">
                <label class="form-label" for="judged_sport">Select a Judged Sport:</label>
                <select name="judged_sport" id="judged_sport" class="form-control" onchange="testTournamentFormats('judged_sport', 'judged_formats')">
                    <option value="">Select a judged sport...</option>
                    <?php foreach ($available_sports as $sport): ?>
                        <?php if (($sport['sport_type_category'] ?? $sport['type']) === 'judged'): ?>
                            <option value="<?php echo $sport['id']; ?>" 
                                    data-type="<?php echo $sport['sport_type_category'] ?? $sport['type']; ?>">
                                <?php echo htmlspecialchars($sport['name']); ?>
                                (<?php echo htmlspecialchars($sport['sport_type_name'] ?? ucfirst($sport['type'])); ?>)
                            </option>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="judged_formats">Tournament Formats:</label>
                <select name="judged_formats" id="judged_formats" class="form-control">
                    <option value="">Select a sport first...</option>
                </select>
            </div>
            
            <div id="judged_debug" class="debug-output" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>Test 3: Traditional Sports (Control Test)</h3>
            <div class="form-group">
                <label class="form-label" for="traditional_sport">Select a Traditional Sport:</label>
                <select name="traditional_sport" id="traditional_sport" class="form-control" onchange="testTournamentFormats('traditional_sport', 'traditional_formats')">
                    <option value="">Select a traditional sport...</option>
                    <?php foreach ($available_sports as $sport): ?>
                        <?php if (($sport['sport_type_category'] ?? $sport['type']) === 'traditional'): ?>
                            <option value="<?php echo $sport['id']; ?>" 
                                    data-type="<?php echo $sport['sport_type_category'] ?? $sport['type']; ?>">
                                <?php echo htmlspecialchars($sport['name']); ?>
                                (<?php echo htmlspecialchars($sport['sport_type_name'] ?? ucfirst($sport['type'])); ?>)
                            </option>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label class="form-label" for="traditional_formats">Tournament Formats:</label>
                <select name="traditional_formats" id="traditional_formats" class="form-control">
                    <option value="">Select a sport first...</option>
                </select>
            </div>
            
            <div id="traditional_debug" class="debug-output" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h3>📊 Test Results Summary</h3>
            <div id="test_summary">
                <p class="info">Run tests above to see results...</p>
            </div>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="manage-event.php?id=1" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 0 10px;">
                🎯 Test in Real Event Management
            </a>
            <a href="debug-tournament-formats-issue.php" style="background: #6c757d; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; margin: 0 10px;">
                🔍 View Debug Report
            </a>
        </div>
    </div>

    <script>
        let testResults = {
            academic: null,
            judged: null,
            traditional: null
        };

        function testTournamentFormats(sportSelectId, formatSelectId) {
            const sportSelect = document.getElementById(sportSelectId);
            const formatSelect = document.getElementById(formatSelectId);
            const debugDiv = document.getElementById(sportSelectId.replace('_sport', '_debug'));
            
            const sportId = sportSelect.value;
            const sportType = sportSelect.options[sportSelect.selectedIndex].dataset.type || 'traditional';
            
            // Show debug output
            debugDiv.style.display = 'block';
            debugDiv.innerHTML = '<p class="info">🔄 Loading tournament formats...</p>';
            
            // Reset format selection
            formatSelect.innerHTML = '<option value="">Loading...</option>';
            
            if (!sportId) {
                formatSelect.innerHTML = '<option value="">Select a sport first...</option>';
                debugDiv.style.display = 'none';
                return;
            }
            
            // Log test start
            console.log(`Testing sport type: ${sportType}, Sport ID: ${sportId}`);
            
            // Fetch tournament formats
            fetch('ajax/get-tournament-formats.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `sport_type=${encodeURIComponent(sportType)}`
            })
            .then(response => response.json())
            .then(data => {
                let debugHtml = `<strong>🔍 Debug Info:</strong><br>`;
                debugHtml += `Sport Type: ${sportType}<br>`;
                debugHtml += `Sport ID: ${sportId}<br>`;
                debugHtml += `Response: ${JSON.stringify(data, null, 2)}<br>`;
                
                formatSelect.innerHTML = '<option value="">Select tournament format...</option>';
                
                if (data.success && data.formats && data.formats.length > 0) {
                    debugHtml += `<span class="success">✅ Success: Found ${data.formats.length} formats</span><br>`;
                    
                    data.formats.forEach(format => {
                        const option = document.createElement('option');
                        option.value = format.id;
                        option.textContent = format.name;
                        option.title = format.description;
                        formatSelect.appendChild(option);
                        
                        debugHtml += `- ${format.name} (${format.code})<br>`;
                    });
                    
                    // Record test result
                    const testType = sportSelectId.replace('_sport', '');
                    testResults[testType] = {
                        success: true,
                        formatCount: data.formats.length,
                        formats: data.formats.map(f => f.name)
                    };
                } else {
                    debugHtml += `<span class="error">❌ No formats found</span><br>`;
                    formatSelect.innerHTML = '<option value="">No formats available</option>';
                    
                    // Record test result
                    const testType = sportSelectId.replace('_sport', '');
                    testResults[testType] = {
                        success: false,
                        formatCount: 0,
                        error: data.message || 'No formats returned'
                    };
                }
                
                debugDiv.innerHTML = debugHtml;
                updateTestSummary();
            })
            .catch(error => {
                console.error('Error loading tournament formats:', error);
                debugDiv.innerHTML = `<span class="error">❌ Error: ${error.message}</span>`;
                formatSelect.innerHTML = '<option value="">Error loading formats</option>';
                
                // Record test result
                const testType = sportSelectId.replace('_sport', '');
                testResults[testType] = {
                    success: false,
                    formatCount: 0,
                    error: error.message
                };
                updateTestSummary();
            });
        }
        
        function updateTestSummary() {
            const summaryDiv = document.getElementById('test_summary');
            let html = '<h4>Test Results:</h4>';
            
            Object.keys(testResults).forEach(testType => {
                const result = testResults[testType];
                if (result === null) {
                    html += `<p class="info">⏳ ${testType.charAt(0).toUpperCase() + testType.slice(1)}: Not tested yet</p>`;
                } else if (result.success) {
                    html += `<p class="success">✅ ${testType.charAt(0).toUpperCase() + testType.slice(1)}: ${result.formatCount} formats found</p>`;
                    html += `<ul style="margin-left: 20px;">`;
                    result.formats.forEach(format => {
                        html += `<li>${format}</li>`;
                    });
                    html += `</ul>`;
                } else {
                    html += `<p class="error">❌ ${testType.charAt(0).toUpperCase() + testType.slice(1)}: Failed - ${result.error}</p>`;
                }
            });
            
            // Overall assessment
            const testedResults = Object.values(testResults).filter(r => r !== null);
            if (testedResults.length > 0) {
                const successCount = testedResults.filter(r => r.success).length;
                const totalCount = testedResults.length;
                
                if (successCount === totalCount) {
                    html += `<div style="background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;">`;
                    html += `<h4>🎉 All Tests Passed!</h4>`;
                    html += `<p>Tournament format loading is working correctly for all tested sport types.</p>`;
                    html += `</div>`;
                } else {
                    html += `<div style="background: #f8d7da; padding: 15px; border-left: 4px solid #dc3545; margin: 20px 0;">`;
                    html += `<h4>⚠️ Some Tests Failed</h4>`;
                    html += `<p>${successCount} out of ${totalCount} tests passed. Please check the failed tests above.</p>`;
                    html += `</div>`;
                }
            }
            
            summaryDiv.innerHTML = html;
        }
    </script>
</body>
</html>
