<?php
/**
 * Simplify Tournament Participants Schema
 * Remove registration_id dependency and create direct department-tournament relationship
 * SC_IMS Sports Competition and Event Management System
 */

require_once '../config/database.php';
require_once 'auth.php';

// Require admin authentication
requireAdmin();

$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simplify Tournament Schema - SC_IMS Admin</title>
    <link rel="stylesheet" href="../assets/css/admin-styles.css">
    <style>
        .schema-container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 20px;
        }
        .schema-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .current-schema, .new-schema {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
        }
        .current-schema h4 {
            color: #dc3545;
            margin-top: 0;
        }
        .new-schema h4 {
            color: #28a745;
            margin-top: 0;
        }
        .sql-code {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .action-buttons {
            text-align: center;
            margin: 30px 0;
        }
        .btn-primary {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        .btn-primary:hover {
            background: #0056b3;
        }
        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 0 10px;
        }
        .btn-secondary:hover {
            background: #545b62;
        }
        .result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="admin-header">
        <h1>🔧 Simplify Tournament Schema</h1>
        <p>Remove registration dependencies and create direct department-tournament relationships</p>
    </div>

    <div class="schema-container">
        <div class="schema-section">
            <h2>📋 Schema Simplification Overview</h2>
            <p>This tool will simplify the tournament participants system by removing complex registration dependencies and creating direct relationships between departments and tournaments.</p>
            
            <div class="current-schema">
                <h4>❌ Current Complex Schema</h4>
                <div class="sql-code">
CREATE TABLE tournament_participants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tournament_structure_id INT NOT NULL,
    registration_id INT NOT NULL,  -- ❌ Complex dependency
    seed_number INT,
    current_status ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active',
    points DECIMAL(10,2) DEFAULT 0,
    wins INT DEFAULT 0,
    losses INT DEFAULT 0,
    draws INT DEFAULT 0,
    performance_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE  -- ❌ Problematic
);
                </div>
                <p><strong>Problems:</strong></p>
                <ul>
                    <li>Requires complex registration system for tournament participation</li>
                    <li>Foreign key constraint errors when registrations don't exist</li>
                    <li>Prevents automatic tournament generation</li>
                    <li>Creates unnecessary barriers for department participation</li>
                </ul>
            </div>

            <div class="new-schema">
                <h4>✅ New Simplified Schema</h4>
                <div class="sql-code">
CREATE TABLE tournament_participants (
    id INT AUTO_INCREMENT PRIMARY KEY,
    tournament_structure_id INT NOT NULL,
    department_id INT NOT NULL,  -- ✅ Direct department link
    team_name VARCHAR(255),      -- ✅ Optional custom team name
    seed_number INT,
    current_status ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active',
    points DECIMAL(10,2) DEFAULT 0,
    wins INT DEFAULT 0,
    losses INT DEFAULT 0,
    draws INT DEFAULT 0,
    performance_data JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,  -- ✅ Simple & reliable
    UNIQUE KEY unique_tournament_department (tournament_structure_id, department_id)
);
                </div>
                <p><strong>Benefits:</strong></p>
                <ul>
                    <li>Direct department-tournament relationship</li>
                    <li>No registration barriers for tournament participation</li>
                    <li>Automatic tournament generation works seamlessly</li>
                    <li>Simplified data integrity with reliable foreign keys</li>
                    <li>Departments automatically eligible when added to events</li>
                </ul>
            </div>
        </div>

        <div class="action-buttons">
            <button type="button" class="btn-primary" onclick="simplifySchema()">
                🔧 Simplify Tournament Schema
            </button>
            <button type="button" class="btn-secondary" onclick="checkCurrentSchema()">
                📊 Check Current Schema
            </button>
            <a href="manage-events.php" class="btn-secondary">← Back to Events</a>
        </div>

        <div id="result"></div>
    </div>

    <script>
        function checkCurrentSchema() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">🔍 Checking current schema...</div>';
            
            fetch('ajax/check-tournament-schema.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h4>✅ Current Schema Status</h4>
                                <p><strong>Table Exists:</strong> ${data.table_exists ? 'Yes' : 'No'}</p>
                                <p><strong>Columns:</strong> ${data.columns.join(', ')}</p>
                                <p><strong>Foreign Keys:</strong> ${data.foreign_keys.join(', ')}</p>
                                <p><strong>Needs Simplification:</strong> ${data.has_registration_id ? 'Yes' : 'No'}</p>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Error: ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="error">❌ Network Error: ${error.message}</div>`;
                });
        }

        function simplifySchema() {
            if (!confirm('This will recreate the tournament_participants table with simplified schema. Any existing tournament participant data will be lost. Continue?')) {
                return;
            }
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="info">🔧 Simplifying tournament schema...</div>';
            
            fetch('ajax/simplify-tournament-schema.php', {
                method: 'POST'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultDiv.innerHTML = `
                            <div class="success">
                                <h4>✅ Schema Simplified Successfully!</h4>
                                <p>${data.message}</p>
                                <p><strong>Changes Applied:</strong></p>
                                <ul>
                                    <li>Removed registration_id foreign key dependency</li>
                                    <li>Added direct department_id foreign key</li>
                                    <li>Added team_name field for custom team names</li>
                                    <li>Added updated_at timestamp</li>
                                    <li>Added unique constraint for tournament-department pairs</li>
                                </ul>
                                <p><strong>Next Steps:</strong> Tournament generation should now work without registration barriers!</p>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML = `<div class="error">❌ Error: ${data.message}</div>`;
                    }
                })
                .catch(error => {
                    resultDiv.innerHTML = `<div class="error">❌ Network Error: ${error.message}</div>`;
                });
        }

        // Check schema on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkCurrentSchema();
        });
    </script>
</body>
</html>
