<?php
/**
 * Live Debug Tournament Formats
 * Real-time debugging to see what's happening with tournament format loading
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>🔍 Live Tournament Format Debug</h2>";

// 1. Check what's actually in the database right now
echo "<h3>1. Current Database State</h3>";

echo "<h4>Tournament Formats Table:</h4>";
$stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
$stmt->execute();
$all_formats = $stmt->fetchAll();

if (empty($all_formats)) {
    echo "<p style='color: red;'>❌ NO TOURNAMENT FORMATS FOUND IN DATABASE!</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Sport Type Category</th><th>Sport Types</th></tr>";
    foreach ($all_formats as $format) {
        echo "<tr>";
        echo "<td>{$format['id']}</td>";
        echo "<td>{$format['name']}</td>";
        echo "<td>{$format['code']}</td>";
        echo "<td>" . ($format['sport_type_category'] ?? 'NULL') . "</td>";
        echo "<td>" . ($format['sport_types'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h4>Sports with Categories:</h4>";
$stmt = $conn->prepare("
    SELECT s.id, s.name, s.type, st.category as sport_type_category, st.name as sport_type_name
    FROM sports s
    LEFT JOIN sport_types st ON s.sport_type_id = st.id
    ORDER BY s.name
");
$stmt->execute();
$all_sports = $stmt->fetchAll();

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Name</th><th>Type</th><th>Sport Type Category</th><th>Sport Type Name</th></tr>";
foreach ($all_sports as $sport) {
    $category = $sport['sport_type_category'] ?? $sport['type'] ?? 'NULL';
    $row_color = '';
    if ($category === 'judged') $row_color = 'background-color: #ffe6e6;';
    if ($category === 'academic') $row_color = 'background-color: #e6f3ff;';
    
    echo "<tr style='{$row_color}'>";
    echo "<td>{$sport['id']}</td>";
    echo "<td>{$sport['name']}</td>";
    echo "<td>" . ($sport['type'] ?? 'NULL') . "</td>";
    echo "<td>" . ($sport['sport_type_category'] ?? 'NULL') . "</td>";
    echo "<td>" . ($sport['sport_type_name'] ?? 'NULL') . "</td>";
    echo "</tr>";
}
echo "</table>";

// 2. Test the exact AJAX calls that would be made
echo "<h3>2. Live AJAX Tests</h3>";

$test_sports = [
    'Mr. and Ms. Intramurals',
    'Dance',
    'Banner Raising',
    'Chess'
];

foreach ($test_sports as $sport_name) {
    echo "<h4>Testing: {$sport_name}</h4>";
    
    // Find the sport
    $stmt = $conn->prepare("
        SELECT s.*, st.category as sport_type_category
        FROM sports s
        LEFT JOIN sport_types st ON s.sport_type_id = st.id
        WHERE s.name LIKE ?
        LIMIT 1
    ");
    $stmt->execute(["%{$sport_name}%"]);
    $sport = $stmt->fetch();
    
    if (!$sport) {
        echo "<p style='color: red;'>❌ Sport '{$sport_name}' not found!</p>";
        continue;
    }
    
    $sport_type = $sport['sport_type_category'] ?? $sport['type'] ?? 'traditional';
    echo "<p><strong>Sport found:</strong> {$sport['name']}</p>";
    echo "<p><strong>Sport type being sent to AJAX:</strong> <code>{$sport_type}</code></p>";
    
    // Simulate the exact AJAX call
    $_POST['sport_type'] = $sport_type;
    
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0; background: #f9f9f9;'>";
    echo "<strong>AJAX Response:</strong><br>";
    
    ob_start();
    try {
        include 'ajax/get-tournament-formats.php';
        $response = ob_get_clean();
        
        echo "<pre style='background: #fff; padding: 10px; border: 1px solid #ccc;'>";
        echo htmlspecialchars($response);
        echo "</pre>";
        
        $data = json_decode($response, true);
        if ($data) {
            echo "<strong>Parsed Response:</strong><br>";
            echo "Success: " . ($data['success'] ? 'YES' : 'NO') . "<br>";
            if (isset($data['formats'])) {
                echo "Formats found: " . count($data['formats']) . "<br>";
                if (!empty($data['formats'])) {
                    echo "<ul>";
                    foreach ($data['formats'] as $format) {
                        echo "<li><strong>{$format['name']}</strong> (ID: {$format['id']}, Code: {$format['code']})</li>";
                    }
                    echo "</ul>";
                }
            }
            if (isset($data['debug'])) {
                echo "<strong>Debug info:</strong> " . json_encode($data['debug']) . "<br>";
            }
            if (isset($data['message'])) {
                echo "<strong>Error message:</strong> {$data['message']}<br>";
            }
        } else {
            echo "<p style='color: red;'>❌ Failed to parse JSON response</p>";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
    }
    
    echo "</div>";
    unset($_POST['sport_type']);
}

// 3. Manual query tests
echo "<h3>3. Manual Query Tests</h3>";

$manual_tests = [
    ['academic', 'Academic formats'],
    ['judged', 'Judged formats']
];

foreach ($manual_tests as $test) {
    $sport_type = $test[0];
    $label = $test[1];
    
    echo "<h4>{$label} (sport_type = '{$sport_type}'):</h4>";
    
    // Test with sport_type_category field
    try {
        $stmt = $conn->prepare("
            SELECT id, name, code, sport_type_category
            FROM tournament_formats
            WHERE sport_type_category = ? OR sport_type_category = 'all'
            ORDER BY name
        ");
        $stmt->execute([$sport_type]);
        $formats = $stmt->fetchAll();
        
        echo "<p><strong>Using sport_type_category field:</strong> Found " . count($formats) . " formats</p>";
        if (!empty($formats)) {
            echo "<ul>";
            foreach ($formats as $format) {
                echo "<li>{$format['name']} (Category: {$format['sport_type_category']})</li>";
            }
            echo "</ul>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error with sport_type_category: " . $e->getMessage() . "</p>";
    }
    
    // Test with sport_types field
    try {
        $stmt = $conn->prepare("
            SELECT id, name, code, sport_types
            FROM tournament_formats
            WHERE sport_types LIKE ? OR sport_types LIKE ? OR sport_types LIKE ? OR sport_types = ?
            ORDER BY name
        ");
        $params = [
            $sport_type . ',%',
            '%,' . $sport_type . ',%',
            '%,' . $sport_type,
            $sport_type
        ];
        $stmt->execute($params);
        $formats = $stmt->fetchAll();
        
        echo "<p><strong>Using sport_types field:</strong> Found " . count($formats) . " formats</p>";
        if (!empty($formats)) {
            echo "<ul>";
            foreach ($formats as $format) {
                echo "<li>{$format['name']} (Types: {$format['sport_types']})</li>";
            }
            echo "</ul>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error with sport_types: " . $e->getMessage() . "</p>";
    }
}

echo "<h3>4. Immediate Fix</h3>";
echo "<p>If no formats are found above, we need to create them immediately:</p>";
echo "<p><a href='create-tournament-formats-now.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🚨 CREATE FORMATS NOW</a></p>";
?>
