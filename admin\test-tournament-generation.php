<?php
/**
 * Test Tournament Generation
 * Direct test of the tournament generation functionality
 */

require_once '../config/database.php';
require_once '../includes/unified_bracket_engine.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Tournament Generation Test</h2>";

// Test parameters
$eventSportId = 52; // Basketball 5v5 category

try {
    echo "<h3>Step 1: Check Transaction Status</h3>";
    $inTransaction = $conn->inTransaction();
    echo "<p>Transaction Status: " . ($inTransaction ? "ACTIVE" : "NONE") . "</p>";
    
    if ($inTransaction) {
        echo "<p style='color: orange;'>Closing active transaction...</p>";
        $conn->rollBack();
    }
    
    echo "<h3>Step 2: Initialize Bracket Engine</h3>";
    $bracketEngine = new UnifiedBracketEngine($conn);
    echo "<p>✅ Bracket engine initialized</p>";
    
    echo "<h3>Step 3: Get Participants</h3>";
    $participants = $bracketEngine->getEventParticipants($eventSportId);
    echo "<p>Found " . count($participants) . " participants:</p>";
    
    if (!empty($participants)) {
        echo "<ul>";
        foreach ($participants as $participant) {
            echo "<li>" . htmlspecialchars($participant['department_name'] ?? $participant['team_name'] ?? 'Unknown') . "</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'>❌ No participants found!</p>";
        exit;
    }
    
    echo "<h3>Step 4: Test Tournament Generation</h3>";
    
    // Configuration for tournament
    $config = [
        'tournament_format_id' => 1, // Single Elimination
        'seeding_method' => 'random'
    ];
    
    echo "<p>Attempting to generate tournament...</p>";
    
    $result = $bracketEngine->generateBracketForCategory($eventSportId, $config);
    
    if ($result['success']) {
        echo "<p style='color: green;'>✅ Tournament generated successfully!</p>";
        echo "<p><strong>Tournament ID:</strong> " . $result['tournament_id'] . "</p>";
        echo "<p><strong>Participants:</strong> " . $result['participant_count'] . "</p>";
        echo "<p><strong>Matches Created:</strong> " . count($result['bracket_data']['matches'] ?? []) . "</p>";
        
        echo "<h4>Tournament Structure:</h4>";
        echo "<pre>" . json_encode($result['bracket_data'], JSON_PRETTY_PRINT) . "</pre>";
        
    } else {
        echo "<p style='color: red;'>❌ Tournament generation failed: " . $result['message'] . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    echo "<p>File: " . $e->getFile() . "</p>";
    echo "<p>Line: " . $e->getLine() . "</p>";
    
    // Clean up any transaction
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
}

echo "<div style='margin-top: 20px;'>";
echo "<a href='manage-category.php?event_id=4&sport_id=37&category_id=16' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Return to Category Management</a>";
echo "</div>";
?>
