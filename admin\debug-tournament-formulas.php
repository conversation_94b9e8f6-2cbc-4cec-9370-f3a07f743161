<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>🔍 Tournament Formula Debug</h2>";

// Check tournament formats and their formulas
$stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
$stmt->execute();
$formats = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<h3>Tournament Formats and Formulas</h3>";
echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Rounds Formula</th><th>Matches Formula</th></tr>";

foreach ($formats as $format) {
    echo "<tr>";
    echo "<td>" . $format['id'] . "</td>";
    echo "<td>" . htmlspecialchars($format['name']) . "</td>";
    echo "<td>" . htmlspecialchars($format['code']) . "</td>";
    echo "<td>" . htmlspecialchars($format['rounds_formula'] ?? 'NULL') . "</td>";
    echo "<td>" . htmlspecialchars($format['matches_formula'] ?? 'NULL') . "</td>";
    echo "</tr>";
}
echo "</table>";

echo "<h3>Test Formula Evaluation</h3>";
$participantCount = 5;

foreach ($formats as $format) {
    echo "<h4>" . htmlspecialchars($format['name']) . "</h4>";
    
    if ($format['rounds_formula']) {
        $formula = str_replace('n', $participantCount, $format['rounds_formula']);
        echo "<p>Original formula: " . htmlspecialchars($format['rounds_formula']) . "</p>";
        echo "<p>Substituted formula: " . htmlspecialchars($formula) . "</p>";
        
        try {
            // Check for problematic functions
            if (strpos($formula, 'lg2') !== false) {
                echo "<p>❌ Error: Formula contains 'lg2' which is not a valid PHP function</p>";
                echo "<p>🔧 Should be: " . str_replace('lg2', 'log', $formula) . "</p>";
            } elseif (strpos($formula, 'log2') !== false) {
                echo "<p>❌ Error: Formula contains 'log2' which needs to be log(n, 2)</p>";
                echo "<p>🔧 Should be: " . str_replace('log2(n)', 'log(n, 2)', $formula) . "</p>";
            } else {
                $result = eval("return $formula;");
                echo "<p>✅ Result: " . $result . "</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ Error evaluating formula: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p>No formula defined</p>";
    }
    echo "<hr>";
}

echo "<h3>Fix Problematic Formulas</h3>";
echo "<p><a href='fix-tournament-formulas.php'>🔧 Fix Tournament Formulas</a></p>";
echo "<p><a href='manage-category.php?event_id=4&sport_id=37&category_id=16'>← Back to Category Management</a></p>";
?>
