<?php
require_once 'config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "Checking unified registration tables...\n";

$tables = ['event_department_registrations', 'department_sport_participations'];
foreach ($tables as $table) {
    $stmt = $conn->prepare("SHOW TABLES LIKE ?");
    $stmt->execute([$table]);
    if ($stmt->fetch()) {
        echo "✅ Table $table exists\n";
    } else {
        echo "❌ Table $table missing\n";
    }
}

// Check if we have any data
echo "\nChecking data...\n";
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM event_department_registrations");
$stmt->execute();
$result = $stmt->fetch();
echo "Event department registrations: " . $result['count'] . "\n";

$stmt = $conn->prepare("SELECT COUNT(*) as count FROM department_sport_participations");
$stmt->execute();
$result = $stmt->fetch();
echo "Department sport participations: " . $result['count'] . "\n";
?>
