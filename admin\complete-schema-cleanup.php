<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🔧 Complete Tournament Schema Cleanup</h1>";
echo "<p>Comprehensive cleanup and rebuild of tournament database schema...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $fixes_applied = [];
    $errors = [];
    
    echo "<h2>1. Dropping and Recreating tournament_participants Table</h2>";
    
    // Step 1: Get current participants data before dropping table
    $existing_data = [];
    try {
        $stmt = $conn->query("SELECT * FROM tournament_participants");
        $existing_data = $stmt->fetchAll();
        echo "<p>Found " . count($existing_data) . " existing tournament participants records</p>";
    } catch (Exception $e) {
        echo "<p>No existing tournament_participants data found</p>";
    }
    
    // Step 2: Drop the entire tournament_participants table to clean up all constraints
    try {
        $conn->exec("DROP TABLE IF EXISTS tournament_participants");
        echo "<p style='color: green;'>✅ Dropped old tournament_participants table</p>";
        $fixes_applied[] = "Dropped old tournament_participants table";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Failed to drop tournament_participants: " . $e->getMessage() . "</p>";
        $errors[] = "Failed to drop tournament_participants: " . $e->getMessage();
    }
    
    // Step 3: Create new tournament_participants table with correct schema
    try {
        $sql = "CREATE TABLE tournament_participants (
            id INT AUTO_INCREMENT PRIMARY KEY,
            tournament_structure_id INT NOT NULL,
            registration_id INT NOT NULL,
            seed_number INT,
            group_assignment VARCHAR(10),
            current_status ENUM('active', 'eliminated', 'bye', 'withdrawn') DEFAULT 'active',
            points DECIMAL(10,2) DEFAULT 0,
            wins INT DEFAULT 0,
            losses INT DEFAULT 0,
            draws INT DEFAULT 0,
            performance_data JSON,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_tournament_participant (tournament_structure_id, registration_id)
        )";
        $conn->exec($sql);
        echo "<p style='color: green;'>✅ Created new tournament_participants table with correct schema</p>";
        $fixes_applied[] = "Created new tournament_participants table";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Failed to create tournament_participants: " . $e->getMessage() . "</p>";
        $errors[] = "Failed to create tournament_participants: " . $e->getMessage();
    }
    
    echo "<h2>2. Adding Foreign Key Constraints Safely</h2>";
    
    // Add foreign key constraints only if the referenced tables exist
    try {
        // Check if tournament_structures exists
        $conn->query("SELECT 1 FROM tournament_structures LIMIT 1");
        
        try {
            $sql = "ALTER TABLE tournament_participants 
                    ADD CONSTRAINT fk_tp_tournament_structure 
                    FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE";
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Added foreign key constraint for tournament_structure_id</p>";
            $fixes_applied[] = "Added foreign key constraint for tournament_structure_id";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Foreign key constraint issue: " . $e->getMessage() . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ tournament_structures table does not exist</p>";
        $errors[] = "tournament_structures table missing";
    }
    
    try {
        // Check if registrations exists
        $conn->query("SELECT 1 FROM registrations LIMIT 1");
        
        try {
            $sql = "ALTER TABLE tournament_participants 
                    ADD CONSTRAINT fk_tp_registration 
                    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE";
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Added foreign key constraint for registration_id</p>";
            $fixes_applied[] = "Added foreign key constraint for registration_id";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Foreign key constraint issue: " . $e->getMessage() . "</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ registrations table does not exist</p>";
        $errors[] = "registrations table missing";
    }
    
    echo "<h2>3. Ensuring All Required Tournament Tables Exist</h2>";
    
    // Check and create tournament_structures if missing
    try {
        $conn->query("SELECT 1 FROM tournament_structures LIMIT 1");
        echo "<p style='color: green;'>✅ tournament_structures table exists</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ tournament_structures table missing - creating...</p>";
        try {
            $sql = "CREATE TABLE tournament_structures (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_sport_id INT NOT NULL,
                tournament_format_id INT NOT NULL,
                name VARCHAR(200) NOT NULL,
                participant_count INT DEFAULT 0,
                seeding_method ENUM('random', 'ranked', 'manual') DEFAULT 'random',
                scoring_config JSON,
                bracket_data JSON,
                total_rounds INT DEFAULT 0,
                current_round INT DEFAULT 1,
                status ENUM('setup', 'in_progress', 'completed', 'cancelled') DEFAULT 'setup',
                start_date DATETIME,
                end_date DATETIME,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE
            )";
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Created tournament_structures table</p>";
            $fixes_applied[] = "Created tournament_structures table";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to create tournament_structures: " . $e->getMessage() . "</p>";
            $errors[] = "Failed to create tournament_structures: " . $e->getMessage();
        }
    }
    
    // Check and create tournament_rounds if missing
    try {
        $conn->query("SELECT 1 FROM tournament_rounds LIMIT 1");
        echo "<p style='color: green;'>✅ tournament_rounds table exists</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ tournament_rounds table missing - creating...</p>";
        try {
            $sql = "CREATE TABLE tournament_rounds (
                id INT AUTO_INCREMENT PRIMARY KEY,
                tournament_structure_id INT NOT NULL,
                round_number INT NOT NULL,
                round_name VARCHAR(100) NOT NULL,
                round_type ENUM('group', 'elimination', 'final', 'consolation') DEFAULT 'elimination',
                status ENUM('pending', 'in_progress', 'completed') DEFAULT 'pending',
                start_date DATETIME,
                end_date DATETIME,
                matches_count INT DEFAULT 0,
                completed_matches INT DEFAULT 0,
                advancement_criteria JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE,
                UNIQUE KEY unique_tournament_round (tournament_structure_id, round_number)
            )";
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Created tournament_rounds table</p>";
            $fixes_applied[] = "Created tournament_rounds table";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to create tournament_rounds: " . $e->getMessage() . "</p>";
            $errors[] = "Failed to create tournament_rounds: " . $e->getMessage();
        }
    }
    
    echo "<h2>4. Updating matches Table for Tournament Support</h2>";
    
    // Add tournament columns to matches table if missing
    try {
        $stmt = $conn->query("DESCRIBE matches");
        $columns = $stmt->fetchAll();
        $existing_columns = array_column($columns, 'Field');
        
        $required_columns = [
            'tournament_structure_id' => 'INT',
            'tournament_round_id' => 'INT',
            'bracket_position' => 'VARCHAR(50)',
            'is_bye_match' => 'BOOLEAN DEFAULT FALSE'
        ];
        
        foreach ($required_columns as $column => $definition) {
            if (!in_array($column, $existing_columns)) {
                try {
                    $sql = "ALTER TABLE matches ADD COLUMN {$column} {$definition}";
                    $conn->exec($sql);
                    echo "<p style='color: green;'>✅ Added {$column} column to matches table</p>";
                    $fixes_applied[] = "Added {$column} column to matches table";
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Failed to add {$column}: " . $e->getMessage() . "</p>";
                    $errors[] = "Failed to add {$column}: " . $e->getMessage();
                }
            } else {
                echo "<p style='color: green;'>✅ matches table already has {$column} column</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error checking matches table: " . $e->getMessage() . "</p>";
        $errors[] = "Error checking matches table: " . $e->getMessage();
    }
    
    echo "<h2>5. Verifying Current Event Data</h2>";
    
    // Get current event sport and participants for Basketball 5v5
    $event_id = 4;
    $sport_id = 37;
    $category_id = 15;
    
    try {
        // Get event sport ID
        $stmt = $conn->prepare("
            SELECT 
                sc.id as category_id,
                sc.category_name,
                es.id as event_sport_id,
                es.tournament_format_id,
                e.name as event_name,
                s.name as sport_name
            FROM sport_categories sc
            JOIN event_sports es ON sc.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            WHERE sc.id = ? AND es.event_id = ? AND es.sport_id = ?
        ");
        $stmt->execute([$category_id, $event_id, $sport_id]);
        $category = $stmt->fetch();
        
        if ($category) {
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
            echo "<h4>Event Information:</h4>";
            echo "<p><strong>Event:</strong> {$category['event_name']}</p>";
            echo "<p><strong>Sport:</strong> {$category['sport_name']}</p>";
            echo "<p><strong>Category:</strong> {$category['category_name']}</p>";
            echo "<p><strong>Event Sport ID:</strong> {$category['event_sport_id']}</p>";
            echo "<p><strong>Tournament Format ID:</strong> " . ($category['tournament_format_id'] ?? 'Not configured') . "</p>";
            echo "</div>";
            
            // Get participants from registrations
            $stmt = $conn->prepare("
                SELECT 
                    r.id,
                    r.department_id,
                    d.name as department_name,
                    r.status,
                    JSON_LENGTH(r.participants) as participant_count
                FROM registrations r
                JOIN departments d ON r.department_id = d.id
                WHERE r.event_sport_id = ? AND r.status IN ('confirmed', 'registered', 'approved')
                ORDER BY d.name
            ");
            $stmt->execute([$category['event_sport_id']]);
            $participants = $stmt->fetchAll();
            
            echo "<h4>Available Participants: " . count($participants) . "</h4>";
            
            if (!empty($participants)) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
                echo "<tr style='background: #f5f5f5;'>";
                echo "<th>Registration ID</th><th>Department</th><th>Status</th><th>Participants</th>";
                echo "</tr>";
                foreach ($participants as $p) {
                    echo "<tr>";
                    echo "<td>{$p['id']}</td>";
                    echo "<td>{$p['department_name']}</td>";
                    echo "<td>{$p['status']}</td>";
                    echo "<td>{$p['participant_count']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } else {
            echo "<p style='color: red;'>❌ Category not found with given parameters</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error getting event data: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>6. Summary</h2>";
    
    if (!empty($fixes_applied)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ Schema Cleanup Complete</h3>";
        echo "<ul>";
        foreach ($fixes_applied as $fix) {
            echo "<li>{$fix}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($errors)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Issues Encountered</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>{$error}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (empty($errors)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🎉 Database Schema is Ready!</h3>";
        echo "<p>The tournament database schema has been completely cleaned up and rebuilt.</p>";
        echo "<p>You can now test the auto-generation with your real Basketball 5v5 data.</p>";
        echo "</div>";
    }
    
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li><a href='fix-tournament-format.php?event_id=4&sport_id=37' style='background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>⚙️ Configure Tournament Format</a></li>";
    echo "<li><a href='tournament-status-dashboard.php?event_id=4&sport_id=37&category_id=15' style='background: #17a2b8; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>🔍 Check Overall Status</a></li>";
    echo "<li><a href='manage-category.php?category_id=15&event_id=4&sport_id=37' style='background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px;'>🚀 Test Auto-Generation</a></li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Critical Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
