<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$tournament_id = 65;
$event_sport_id = 52;

echo "<h2>🔍 Tournament ID 65 Debug</h2>";

echo "<h3>1. Tournament Structure Details</h3>";
$stmt = $conn->prepare("
    SELECT ts.*, tf.name as format_name
    FROM tournament_structures ts
    LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
    WHERE ts.id = ?
");
$stmt->execute([$tournament_id]);
$tournament = $stmt->fetch(PDO::FETCH_ASSOC);

if ($tournament) {
    echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
    echo "<p><strong>Tournament ID:</strong> " . $tournament['id'] . "</p>";
    echo "<p><strong>Event Sport ID:</strong> " . $tournament['event_sport_id'] . "</p>";
    echo "<p><strong>Status:</strong> " . $tournament['status'] . "</p>";
    echo "<p><strong>Format:</strong> " . $tournament['format_name'] . "</p>";
    echo "<p><strong>Created:</strong> " . $tournament['created_at'] . "</p>";
    echo "<p><strong>Participants:</strong> " . $tournament['total_participants'] . "</p>";
    echo "</div>";
} else {
    echo "<p>❌ Tournament ID 65 not found</p>";
}

echo "<h3>2. Matches for Tournament ID 65</h3>";
$stmt = $conn->prepare("
    SELECT
        m.*,
        d1.name as team1_name,
        d2.name as team2_name
    FROM matches m
    LEFT JOIN departments d1 ON m.team1_id = d1.id
    LEFT JOIN departments d2 ON m.team2_id = d2.id
    WHERE m.tournament_structure_id = ?
    ORDER BY m.round_number, m.match_number
");
$stmt->execute([$tournament_id]);
$matches = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p><strong>Matches found:</strong> " . count($matches) . "</p>";

if ($matches) {
    foreach ($matches as $match) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
        echo "<p><strong>Match " . $match['match_number'] . " (Round " . $match['round_number'] . "):</strong></p>";
        echo "<p>" . ($match['team1_name'] ?? 'TBD') . " vs " . ($match['team2_name'] ?? 'TBD') . "</p>";
        echo "<p>Status: " . $match['status'] . "</p>";
        echo "</div>";
    }
} else {
    echo "<p>❌ No matches found for tournament ID 65</p>";
}

echo "<h3>3. All Tournament Structures for Event Sport ID $event_sport_id</h3>";
$stmt = $conn->prepare("
    SELECT ts.id, ts.status, ts.created_at, tf.name as format_name
    FROM tournament_structures ts
    LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
    WHERE ts.event_sport_id = ?
    ORDER BY ts.created_at DESC
");
$stmt->execute([$event_sport_id]);
$all_tournaments = $stmt->fetchAll(PDO::FETCH_ASSOC);

echo "<p><strong>All tournaments for event sport ID $event_sport_id:</strong></p>";
foreach ($all_tournaments as $t) {
    echo "<div style='border: 1px solid #ddd; padding: 5px; margin: 2px 0;'>";
    echo "<p>ID: " . $t['id'] . " | Status: " . $t['status'] . " | Format: " . $t['format_name'] . " | Created: " . $t['created_at'] . "</p>";
    
    // Check matches for each tournament
    $stmt2 = $conn->prepare("SELECT COUNT(*) as match_count FROM matches WHERE tournament_structure_id = ?");
    $stmt2->execute([$t['id']]);
    $match_count = $stmt2->fetch();
    echo "<p>Matches: " . $match_count['match_count'] . "</p>";
    echo "</div>";
}

echo "<h3>4. Check if Tournament 65 needs match generation</h3>";
if ($tournament && count($matches) == 0) {
    echo "<p>🔧 Tournament 65 exists but has no matches. Attempting to generate matches...</p>";
    
    try {
        require_once '../includes/advanced_tournament_engine.php';
        $engine = new AdvancedTournamentEngine($conn);
        
        $result = $engine->generateTournament($event_sport_id, null, []);
        
        if ($result['success']) {
            echo "<p>✅ Tournament generation successful!</p>";
            echo "<p>Tournament ID: " . $result['tournament_id'] . "</p>";
            echo "<p>Matches created: " . $result['matches_created'] . "</p>";
        } else {
            echo "<p>❌ Tournament generation failed: " . $result['message'] . "</p>";
        }
    } catch (Exception $e) {
        echo "<p>❌ Error generating tournament: " . $e->getMessage() . "</p>";
    }
}

echo "<p><a href='manage-category.php?event_id=4&sport_id=37&category_id=16'>← Back to Category Management</a></p>";
?>
