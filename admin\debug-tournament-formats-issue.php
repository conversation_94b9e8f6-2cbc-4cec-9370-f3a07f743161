<?php
/**
 * Debug Tournament Formats Issue
 * Investigate why Academic and Judged sports don't show tournament formats
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Tournament Formats Debug Report</h2>";

// 1. Check tournament_formats table structure
echo "<h3>1. Tournament Formats Table Structure</h3>";
$stmt = $conn->prepare("DESCRIBE tournament_formats");
$stmt->execute();
$columns = $stmt->fetchAll();

echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
foreach ($columns as $col) {
    echo "<tr>";
    echo "<td>{$col['Field']}</td>";
    echo "<td>{$col['Type']}</td>";
    echo "<td>{$col['Null']}</td>";
    echo "<td>{$col['Key']}</td>";
    echo "<td>{$col['Default']}</td>";
    echo "</tr>";
}
echo "</table>";

// 2. Check existing tournament formats
echo "<h3>2. Existing Tournament Formats</h3>";
$stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY id");
$stmt->execute();
$formats = $stmt->fetchAll();

if (empty($formats)) {
    echo "<p style='color: red;'>❌ No tournament formats found in database!</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Sport Type Category</th><th>Sport Types</th><th>Min Participants</th></tr>";
    foreach ($formats as $format) {
        echo "<tr>";
        echo "<td>{$format['id']}</td>";
        echo "<td>{$format['name']}</td>";
        echo "<td>{$format['code']}</td>";
        echo "<td>" . ($format['sport_type_category'] ?? 'N/A') . "</td>";
        echo "<td>" . ($format['sport_types'] ?? 'N/A') . "</td>";
        echo "<td>{$format['min_participants']}</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// 3. Check Academic and Judged sports
echo "<h3>3. Academic and Judged Sports</h3>";
$stmt = $conn->prepare("
    SELECT s.id, s.name, s.type, 
           st.category as sport_type_category,
           st.name as sport_type_name
    FROM sports s
    LEFT JOIN sport_types st ON s.sport_type_id = st.id
    WHERE st.category IN ('academic', 'judged') OR s.type IN ('academic', 'judged')
    ORDER BY st.category, s.name
");
$stmt->execute();
$academic_judged_sports = $stmt->fetchAll();

if (empty($academic_judged_sports)) {
    echo "<p style='color: orange;'>⚠️ No Academic or Judged sports found!</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Type</th><th>Sport Type Category</th><th>Sport Type Name</th></tr>";
    foreach ($academic_judged_sports as $sport) {
        echo "<tr>";
        echo "<td>{$sport['id']}</td>";
        echo "<td>{$sport['name']}</td>";
        echo "<td>{$sport['type']}</td>";
        echo "<td>" . ($sport['sport_type_category'] ?? 'N/A') . "</td>";
        echo "<td>" . ($sport['sport_type_name'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// 4. Test tournament format queries for Academic and Judged
echo "<h3>4. Tournament Format Query Tests</h3>";

$test_types = ['academic', 'judged'];
foreach ($test_types as $sport_type) {
    echo "<h4>Testing for sport type: {$sport_type}</h4>";
    
    // Check if sport_type_category field exists
    $has_category_field = false;
    foreach ($columns as $col) {
        if ($col['Field'] === 'sport_type_category') {
            $has_category_field = true;
            break;
        }
    }
    
    if ($has_category_field) {
        // Query using sport_type_category
        $stmt = $conn->prepare("
            SELECT id, name, code, sport_type_category
            FROM tournament_formats
            WHERE sport_type_category = ? OR sport_type_category = 'all'
            ORDER BY name
        ");
        $stmt->execute([$sport_type]);
        $results = $stmt->fetchAll();
        
        echo "<p>Query using sport_type_category field: Found " . count($results) . " formats</p>";
        if (!empty($results)) {
            foreach ($results as $result) {
                echo "<li>{$result['name']} (Category: {$result['sport_type_category']})</li>";
            }
        }
    } else {
        // Query using sport_types field
        $stmt = $conn->prepare("
            SELECT id, name, code, sport_types
            FROM tournament_formats
            WHERE sport_types LIKE ? OR sport_types LIKE ? OR sport_types LIKE ? OR sport_types = ?
            ORDER BY name
        ");
        $params = [
            $sport_type . ',%',
            '%,' . $sport_type . ',%',
            '%,' . $sport_type,
            $sport_type
        ];
        $stmt->execute($params);
        $results = $stmt->fetchAll();
        
        echo "<p>Query using sport_types field: Found " . count($results) . " formats</p>";
        if (!empty($results)) {
            foreach ($results as $result) {
                echo "<li>{$result['name']} (Types: {$result['sport_types']})</li>";
            }
        }
    }
}

// 5. Test AJAX endpoint
echo "<h3>5. AJAX Endpoint Test</h3>";
echo "<p>Testing get-tournament-formats.php for academic and judged sports...</p>";

foreach (['academic', 'judged'] as $test_type) {
    echo "<h4>Testing AJAX for: {$test_type}</h4>";
    
    // Simulate POST request
    $_POST['sport_type'] = $test_type;
    
    ob_start();
    try {
        include 'ajax/get-tournament-formats.php';
        $response = ob_get_clean();
        
        $data = json_decode($response, true);
        if ($data && isset($data['success'])) {
            if ($data['success']) {
                echo "<p style='color: green;'>✓ AJAX Success: Found " . count($data['formats']) . " formats</p>";
                if (!empty($data['formats'])) {
                    echo "<ul>";
                    foreach ($data['formats'] as $format) {
                        echo "<li>{$format['name']}</li>";
                    }
                    echo "</ul>";
                }
            } else {
                echo "<p style='color: red;'>❌ AJAX Error: " . ($data['message'] ?? 'Unknown error') . "</p>";
            }
        } else {
            echo "<p style='color: red;'>❌ Invalid AJAX response: " . htmlspecialchars($response) . "</p>";
        }
    } catch (Exception $e) {
        ob_end_clean();
        echo "<p style='color: red;'>❌ AJAX Exception: " . $e->getMessage() . "</p>";
    }
    
    unset($_POST['sport_type']);
}

echo "<h3>6. Recommendations</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #007bff;'>";

if (empty($formats)) {
    echo "<p><strong>Issue:</strong> No tournament formats exist in the database.</p>";
    echo "<p><strong>Solution:</strong> Run the tournament format initialization script.</p>";
} else {
    $academic_formats = array_filter($formats, function($f) {
        return (isset($f['sport_type_category']) && $f['sport_type_category'] === 'academic') ||
               (isset($f['sport_types']) && strpos($f['sport_types'], 'academic') !== false);
    });
    
    $judged_formats = array_filter($formats, function($f) {
        return (isset($f['sport_type_category']) && $f['sport_type_category'] === 'judged') ||
               (isset($f['sport_types']) && strpos($f['sport_types'], 'judged') !== false);
    });
    
    if (empty($academic_formats)) {
        echo "<p><strong>Issue:</strong> No tournament formats for Academic sports.</p>";
    }
    
    if (empty($judged_formats)) {
        echo "<p><strong>Issue:</strong> No tournament formats for Judged sports.</p>";
    }
    
    if (empty($academic_formats) || empty($judged_formats)) {
        echo "<p><strong>Solution:</strong> Add missing tournament formats for Academic and Judged sports.</p>";
    }
}

echo "</div>";
?>
