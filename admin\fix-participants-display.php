<?php
/**
 * Fix Participants Display Issue
 * Ensure departments are properly registered and visible in category
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Fix Participants Display Issue</h1>";

$event_id = 4;
$sport_id = 40;
$category_id = 14;

try {
    $conn->beginTransaction();
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
    echo "<h2>Fixing Participants Display for Category</h2>";
    echo "<p>This will ensure departments are properly registered and visible in the category view.</p>";
    echo "</div>";
    
    echo "<h2>1. Check Current State</h2>";
    
    // Check current registrations
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM event_department_registrations 
        WHERE event_id = ?
    ");
    $stmt->execute([$event_id]);
    $current_registrations = $stmt->fetch()['count'];
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Current registrations for event $event_id:</strong> $current_registrations</p>";
    echo "</div>";
    
    if ($current_registrations == 0) {
        echo "<h2>2. Create Department Registrations</h2>";
        
        // First, ensure we have departments
        $stmt = $conn->query("SELECT COUNT(*) as count FROM departments");
        $dept_count = $stmt->fetch()['count'];
        
        if ($dept_count == 0) {
            echo "<p>Creating sample departments...</p>";
            
            $sample_departments = [
                ['College of Engineering', 'COE', '#007bff'],
                ['College of Business', 'COB', '#28a745'],
                ['College of Arts and Sciences', 'CAS', '#dc3545'],
                ['College of Education', 'COEd', '#ffc107'],
                ['College of Medicine', 'COM', '#6f42c1'],
                ['College of Law', 'COL', '#fd7e14'],
                ['College of Agriculture', 'COA', '#20c997'],
                ['College of Nursing', 'CON', '#e83e8c']
            ];
            
            foreach ($sample_departments as $dept) {
                $stmt = $conn->prepare("
                    INSERT INTO departments (name, abbreviation, color_code, description, status) 
                    VALUES (?, ?, ?, ?, 'active')
                ");
                $stmt->execute([$dept[0], $dept[1], $dept[2], "Sample department for testing"]);
            }
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<p>✅ <strong>Created " . count($sample_departments) . " sample departments</strong></p>";
            echo "</div>";
        }
        
        // Get all departments
        $stmt = $conn->query("SELECT id, name, abbreviation FROM departments ORDER BY name");
        $departments = $stmt->fetchAll();
        
        echo "<p>Registering departments for event...</p>";
        
        $registered_count = 0;
        foreach ($departments as $dept) {
            try {
                $stmt = $conn->prepare("
                    INSERT INTO event_department_registrations 
                    (event_id, department_id, status, contact_person, contact_email, total_participants, registration_date) 
                    VALUES (?, ?, 'approved', 'Test Contact', '<EMAIL>', 1, NOW())
                ");
                $stmt->execute([$event_id, $dept['id']]);
                $registered_count++;
            } catch (Exception $e) {
                // Skip if already exists
                if (strpos($e->getMessage(), 'Duplicate') === false) {
                    echo "<p style='color: #dc3545;'>Error registering {$dept['name']}: " . $e->getMessage() . "</p>";
                }
            }
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<p>✅ <strong>Registered $registered_count departments for event</strong></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<p>✅ <strong>Departments already registered for this event</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>3. Test the Category Query</h2>";
    
    // Test the exact query from manage-category.php
    $stmt = $conn->prepare("
        SELECT 
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            edr.id as registration_id,
            edr.registration_date,
            edr.status,
            COUNT(DISTINCT m.id) as matches_played,
            COUNT(DISTINCT CASE WHEN m.winner_id = d.id THEN m.id END) as wins,
            COUNT(DISTINCT CASE WHEN m.loser_id = d.id THEN m.id END) as losses,
            COALESCE(SUM(CASE WHEN m.winner_id = d.id THEN 3 WHEN m.loser_id = d.id THEN 1 ELSE 0 END), 0) as points
        FROM departments d
        JOIN event_department_registrations edr ON d.id = edr.department_id
        LEFT JOIN matches m ON (m.team_a_id = d.id OR m.team_b_id = d.id) 
                            AND m.sport_category_id = ?
        WHERE edr.event_id = ?
        GROUP BY d.id, d.name, d.abbreviation, d.color_code, edr.id, edr.registration_date, edr.status
        ORDER BY points DESC, wins DESC, d.name
    ");
    $stmt->execute([$category_id, $event_id]);
    $participants = $stmt->fetchAll();
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🧪 Category Query Test Results</h3>";
    echo "<p><strong>Participants found:</strong> " . count($participants) . "</p>";
    
    if (!empty($participants)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Department</th>";
        echo "<th style='padding: 8px;'>Abbreviation</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Registration Date</th>";
        echo "</tr>";
        
        foreach ($participants as $participant) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$participant['name']}</td>";
            echo "<td style='padding: 8px;'>{$participant['abbreviation']}</td>";
            echo "<td style='padding: 8px;'>{$participant['status']}</td>";
            echo "<td style='padding: 8px;'>{$participant['registration_date']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>SUCCESS!</strong> The query now returns " . count($participants) . " participants.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Still no participants found.</strong> There may be an issue with the query or data.</p>";
        echo "</div>";
        
        // Try a simpler query
        echo "<h4>Testing Simplified Query:</h4>";
        $stmt = $conn->prepare("
            SELECT 
                d.name,
                d.abbreviation,
                edr.status,
                edr.registration_date
            FROM departments d
            JOIN event_department_registrations edr ON d.id = edr.department_id
            WHERE edr.event_id = ?
            ORDER BY d.name
        ");
        $stmt->execute([$event_id]);
        $simple_participants = $stmt->fetchAll();
        
        echo "<p><strong>Simple query results:</strong> " . count($simple_participants) . " participants</p>";
        
        if (!empty($simple_participants)) {
            echo "<ul>";
            foreach ($simple_participants as $p) {
                echo "<li>{$p['name']} ({$p['abbreviation']}) - {$p['status']}</li>";
            }
            echo "</ul>";
        }
    }
    echo "</div>";
    
    echo "<h2>4. Check manage-category.php Logic</h2>";
    
    // Let's also check if there's an issue with the manage-category.php file itself
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>⚠️ Potential Issues to Check</h3>";
    echo "<ul>";
    echo "<li><strong>Query Parameters:</strong> Make sure category_id and event_id are correct</li>";
    echo "<li><strong>Table Joins:</strong> Verify the JOIN conditions are working</li>";
    echo "<li><strong>WHERE Conditions:</strong> Check if status filtering is too restrictive</li>";
    echo "<li><strong>GROUP BY:</strong> Ensure grouping isn't eliminating results</li>";
    echo "</ul>";
    echo "</div>";
    
    $conn->commit();
    
    echo "<h2>5. Test the Category Page</h2>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='manage-category.php?event_id=$event_id&sport_id=$sport_id&category_id=$category_id' target='_blank' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "🎯 Test Category Page Now";
    echo "</a>";
    
    echo "<a href='debug-participants-issue.php' target='_blank' style='background: #6c757d; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "🔍 Debug Again";
    echo "</a>";
    echo "</div>";
    
    if (!empty($participants)) {
        echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745; text-align: center;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🎉 Participants Should Now Be Visible!</h3>";
        echo "<p>The category page should now show " . count($participants) . " registered departments.</p>";
        echo "<p>If they're still not showing, there may be a caching issue or a problem with the display logic in manage-category.php.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border-left: 4px solid #dc3545;'>";
        echo "<h3 style='color: #721c24;'>⚠️ Participants Still Not Found</h3>";
        echo "<p>The query is still not returning participants. This suggests a deeper issue with the database structure or query logic.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    $conn->rollBack();
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
