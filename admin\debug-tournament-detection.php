<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$event_sport_id = 52;

echo "<h2>🔍 Tournament Detection Debug</h2>";

echo "<h3>1. All Tournament Structures for Event Sport ID $event_sport_id</h3>";
$stmt = $conn->prepare("
    SELECT ts.*, tf.name as format_name
    FROM tournament_structures ts
    LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
    WHERE ts.event_sport_id = ?
    ORDER BY ts.created_at DESC
");
$stmt->execute([$event_sport_id]);
$tournaments = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($tournaments) {
    foreach ($tournaments as $tournament) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
        echo "<p><strong>Tournament ID:</strong> " . $tournament['id'] . "</p>";
        echo "<p><strong>Status:</strong> " . $tournament['status'] . "</p>";
        echo "<p><strong>Format:</strong> " . $tournament['format_name'] . "</p>";
        echo "<p><strong>Created:</strong> " . $tournament['created_at'] . "</p>";
        echo "<p><strong>Event Sport ID:</strong> " . $tournament['event_sport_id'] . "</p>";
        echo "</div>";
    }
} else {
    echo "<p>❌ No tournaments found</p>";
}

echo "<h3>2. Current Detection Query (used in manage-category.php)</h3>";
$stmt = $conn->prepare("
    SELECT id, status FROM tournament_structures
    WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')
    ORDER BY created_at DESC LIMIT 1
");
$stmt->execute([$event_sport_id]);
$detected_tournament = $stmt->fetch();

if ($detected_tournament) {
    echo "<p>✅ <strong>Detected Tournament ID:</strong> " . $detected_tournament['id'] . "</p>";
    echo "<p><strong>Status:</strong> " . $detected_tournament['status'] . "</p>";
} else {
    echo "<p>❌ No tournament detected by current query</p>";
}

echo "<h3>3. Alternative Detection Query (without status filter)</h3>";
$stmt = $conn->prepare("
    SELECT id, status FROM tournament_structures
    WHERE event_sport_id = ?
    ORDER BY created_at DESC LIMIT 1
");
$stmt->execute([$event_sport_id]);
$alt_tournament = $stmt->fetch();

if ($alt_tournament) {
    echo "<p>✅ <strong>Alternative Tournament ID:</strong> " . $alt_tournament['id'] . "</p>";
    echo "<p><strong>Status:</strong> " . $alt_tournament['status'] . "</p>";
} else {
    echo "<p>❌ No tournament found with alternative query</p>";
}

echo "<h3>4. Matches for Tournament</h3>";
if ($alt_tournament) {
    $tournament_id = $alt_tournament['id'];
    $stmt = $conn->prepare("
        SELECT COUNT(*) as match_count
        FROM matches
        WHERE tournament_structure_id = ?
    ");
    $stmt->execute([$tournament_id]);
    $match_count = $stmt->fetch();
    
    echo "<p>✅ <strong>Matches found:</strong> " . $match_count['match_count'] . "</p>";
}

echo "<p><a href='manage-category.php?event_id=4&sport_id=37&category_id=16'>← Back to Category Management</a></p>";
?>
