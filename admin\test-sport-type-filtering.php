<?php
/**
 * Test Sport Type Filtering
 * Comprehensive test to verify that tournament format filtering is working correctly
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get available sports for testing
$available_sports = getAvailableSports($conn, 1);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sport Type Filtering Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
        .format-card { background: #f8f9fa; padding: 10px; margin: 5px 0; border-radius: 3px; border-left: 4px solid #007bff; }
        .traditional { border-left-color: #28a745; }
        .academic { border-left-color: #17a2b8; }
        .judged { border-left-color: #dc3545; }
        .cross-contamination { background: #ffe6e6; border-left-color: #dc3545; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 Sport Type Filtering Test</h1>
        <p>Testing that each sport type only shows appropriate tournament formats</p>
        
        <div class="test-section">
            <h2>1. Database State Verification</h2>
            <div id="database-state">
                <p>Checking tournament formats in database...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>2. Interactive Sport Selection Test</h2>
            <p>Select different sports to verify filtering:</p>
            
            <div class="row">
                <div class="col-md-6">
                    <label for="sport_id" class="form-label">Select Sport:</label>
                    <select id="sport_id" class="form-control" onchange="testSportFiltering()">
                        <option value="">Select a sport...</option>
                        <?php foreach ($available_sports as $sport): ?>
                            <option value="<?php echo $sport['id']; ?>"
                                    data-type="<?php echo $sport['sport_type_category'] ?? $sport['type']; ?>"
                                    data-sport-type-name="<?php echo htmlspecialchars($sport['sport_type_name'] ?? ''); ?>">
                                <?php echo htmlspecialchars($sport['name']); ?>
                                (<?php echo $sport['sport_type_category'] ?? $sport['type'] ?? 'Unknown'; ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-6">
                    <label for="tournament_format_id" class="form-label">Tournament Formats:</label>
                    <select id="tournament_format_id" class="form-control">
                        <option value="">Select a sport first...</option>
                    </select>
                </div>
            </div>
            
            <div id="filtering-results" class="mt-3"></div>
        </div>
        
        <div class="test-section">
            <h2>3. Automated Filtering Tests</h2>
            <button onclick="runAutomatedTests()" class="btn btn-primary">Run All Tests</button>
            <div id="automated-results" class="mt-3"></div>
        </div>
        
        <div class="test-section">
            <h2>4. Cross-Contamination Check</h2>
            <p>Verifying that traditional sports don't show judged formats and vice versa:</p>
            <button onclick="checkCrossContamination()" class="btn btn-warning">Check Cross-Contamination</button>
            <div id="contamination-results" class="mt-3"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Check database state on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkDatabaseState();
        });
        
        function checkDatabaseState() {
            const stateDiv = document.getElementById('database-state');
            
            fetch('ajax/get-tournament-formats.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'sport_type=traditional'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    stateDiv.innerHTML = `
                        <p class="success">✓ Database connection successful</p>
                        <p>Found ${data.formats.length} traditional formats</p>
                    `;
                } else {
                    stateDiv.innerHTML = `<p class="error">❌ Database error: ${data.message}</p>`;
                }
            })
            .catch(error => {
                stateDiv.innerHTML = `<p class="error">❌ Connection error: ${error.message}</p>`;
            });
        }
        
        function testSportFiltering() {
            const sportSelect = document.getElementById('sport_id');
            const formatSelect = document.getElementById('tournament_format_id');
            const resultsDiv = document.getElementById('filtering-results');
            
            const sportId = sportSelect.value;
            
            if (!sportId) {
                formatSelect.innerHTML = '<option value="">Select a sport first...</option>';
                resultsDiv.innerHTML = '';
                return;
            }
            
            const selectedOption = sportSelect.options[sportSelect.selectedIndex];
            const sportType = selectedOption.dataset.type || 'traditional';
            const sportName = selectedOption.textContent;
            
            resultsDiv.innerHTML = `
                <div class="info">
                    <strong>Testing:</strong> ${sportName}<br>
                    <strong>Sport Type:</strong> ${sportType}<br>
                    <strong>Expected Formats:</strong> ${getExpectedFormats(sportType)}
                </div>
            `;
            
            formatSelect.innerHTML = '<option value="">Loading...</option>';
            
            fetch('ajax/get-tournament-formats.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: `sport_type=${encodeURIComponent(sportType)}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    formatSelect.innerHTML = '<option value="">Select tournament format...</option>';
                    
                    let formatsHtml = '<h5>Found Formats:</h5>';
                    let hasCorrectFormats = true;
                    let hasIncorrectFormats = false;
                    
                    data.formats.forEach(format => {
                        formatSelect.innerHTML += `<option value="${format.id}">${format.name}</option>`;
                        
                        const isCorrectCategory = isFormatCorrectForSportType(format.sport_type_category, sportType);
                        const cardClass = isCorrectCategory ? format.sport_type_category : 'cross-contamination';
                        
                        if (!isCorrectCategory) {
                            hasIncorrectFormats = true;
                        }
                        
                        formatsHtml += `
                            <div class="format-card ${cardClass}">
                                <strong>${format.name}</strong> (Category: ${format.sport_type_category})
                                ${isCorrectCategory ? '' : ' ⚠️ INCORRECT CATEGORY!'}
                            </div>
                        `;
                    });
                    
                    const testResult = hasIncorrectFormats ? 'error' : 'success';
                    const testIcon = hasIncorrectFormats ? '❌' : '✅';
                    const testMessage = hasIncorrectFormats ? 'FAILED - Cross-contamination detected!' : 'PASSED - Correct filtering';
                    
                    resultsDiv.innerHTML += `
                        <div class="${testResult}">
                            <h5>${testIcon} Test Result: ${testMessage}</h5>
                        </div>
                        ${formatsHtml}
                    `;
                } else {
                    resultsDiv.innerHTML += `<p class="error">❌ Error: ${data.message}</p>`;
                    formatSelect.innerHTML = '<option value="">Error loading formats</option>';
                }
            })
            .catch(error => {
                resultsDiv.innerHTML += `<p class="error">❌ Fetch Error: ${error.message}</p>`;
                formatSelect.innerHTML = '<option value="">Network error</option>';
            });
        }
        
        function getExpectedFormats(sportType) {
            const expectations = {
                'traditional': 'Single/Double Elimination, Round Robin, Multi-Stage',
                'academic': 'Swiss System, Knockout Rounds, Quiz Bowl, Academic Round Robin',
                'judged': 'Judged Rounds, Performance Competition, Talent Showcase, Artistic Judging',
                'performance': 'Judged Rounds, Performance Competition, Talent Showcase, Artistic Judging'
            };
            return expectations[sportType] || 'Traditional formats';
        }
        
        function isFormatCorrectForSportType(formatCategory, sportType) {
            const correctMappings = {
                'traditional': ['traditional'],
                'team': ['traditional'],
                'individual': ['traditional'],
                'academic': ['academic'],
                'judged': ['judged'],
                'performance': ['judged']
            };
            
            const allowedCategories = correctMappings[sportType] || ['traditional'];
            return allowedCategories.includes(formatCategory) || formatCategory === 'all';
        }
        
        function runAutomatedTests() {
            const resultsDiv = document.getElementById('automated-results');
            resultsDiv.innerHTML = '<p>Running automated tests...</p>';
            
            const testCases = [
                { sportType: 'traditional', expectedCategory: 'traditional' },
                { sportType: 'academic', expectedCategory: 'academic' },
                { sportType: 'judged', expectedCategory: 'judged' },
                { sportType: 'performance', expectedCategory: 'judged' }
            ];
            
            let testResults = '';
            let testsCompleted = 0;
            
            testCases.forEach(testCase => {
                fetch('ajax/get-tournament-formats.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                    body: `sport_type=${testCase.sportType}`
                })
                .then(response => response.json())
                .then(data => {
                    testsCompleted++;
                    
                    if (data.success) {
                        const hasOnlyCorrectFormats = data.formats.every(format => 
                            format.sport_type_category === testCase.expectedCategory || format.sport_type_category === 'all'
                        );
                        
                        const testIcon = hasOnlyCorrectFormats ? '✅' : '❌';
                        const testStatus = hasOnlyCorrectFormats ? 'PASSED' : 'FAILED';
                        const testClass = hasOnlyCorrectFormats ? 'success' : 'error';
                        
                        testResults += `
                            <div class="${testClass}">
                                <strong>${testIcon} ${testCase.sportType.toUpperCase()}:</strong> ${testStatus}
                                (Found ${data.formats.length} formats, Expected category: ${testCase.expectedCategory})
                            </div>
                        `;
                    } else {
                        testResults += `
                            <div class="error">
                                <strong>❌ ${testCase.sportType.toUpperCase()}:</strong> ERROR - ${data.message}
                            </div>
                        `;
                    }
                    
                    if (testsCompleted === testCases.length) {
                        resultsDiv.innerHTML = '<h5>Automated Test Results:</h5>' + testResults;
                    }
                })
                .catch(error => {
                    testsCompleted++;
                    testResults += `
                        <div class="error">
                            <strong>❌ ${testCase.sportType.toUpperCase()}:</strong> NETWORK ERROR - ${error.message}
                        </div>
                    `;
                    
                    if (testsCompleted === testCases.length) {
                        resultsDiv.innerHTML = '<h5>Automated Test Results:</h5>' + testResults;
                    }
                });
            });
        }
        
        function checkCrossContamination() {
            const resultsDiv = document.getElementById('contamination-results');
            resultsDiv.innerHTML = '<p>Checking for cross-contamination...</p>';
            
            // Test that traditional sports don't get judged formats
            fetch('ajax/get-tournament-formats.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'sport_type=traditional'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const hasJudgedFormats = data.formats.some(format => format.sport_type_category === 'judged');
                    const hasAcademicFormats = data.formats.some(format => format.sport_type_category === 'academic');
                    
                    let contaminationResults = '<h5>Cross-Contamination Check Results:</h5>';
                    
                    if (hasJudgedFormats) {
                        contaminationResults += '<div class="error">❌ CONTAMINATION: Traditional sports showing judged formats!</div>';
                    } else {
                        contaminationResults += '<div class="success">✅ CLEAN: Traditional sports not showing judged formats</div>';
                    }
                    
                    if (hasAcademicFormats) {
                        contaminationResults += '<div class="error">❌ CONTAMINATION: Traditional sports showing academic formats!</div>';
                    } else {
                        contaminationResults += '<div class="success">✅ CLEAN: Traditional sports not showing academic formats</div>';
                    }
                    
                    resultsDiv.innerHTML = contaminationResults;
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ Error checking contamination: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
            });
        }
    </script>
</body>
</html>
