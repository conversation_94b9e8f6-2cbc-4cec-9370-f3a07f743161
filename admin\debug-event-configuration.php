<?php
/**
 * Debug Event Configuration
 * Check what tournament format and participants were actually configured for this event
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔍 Debug Event Configuration</h1>";

$event_id = 4;
$sport_id = 40;
$category_id = 14;

try {
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
    echo "<h2>Checking Event-Level Configuration</h2>";
    echo "<p>This will show what tournament format and participants were configured when the sport was added to the event.</p>";
    echo "</div>";
    
    echo "<h2>1. Event Sports Configuration</h2>";
    
    // Get event sport configuration
    $stmt = $conn->prepare("
        SELECT 
            es.*,
            e.name as event_name,
            s.name as sport_name,
            tf.name as tournament_format_name,
            tf.code as tournament_format_code,
            tf.description as tournament_format_description
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    
    if ($event_sport) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Event Sport Configuration Found</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px; text-align: left;'>Field</th>";
        echo "<th style='padding: 8px; text-align: left;'>Value</th>";
        echo "</tr>";
        
        $fields = [
            'Event' => $event_sport['event_name'],
            'Sport' => $event_sport['sport_name'],
            'Event Sport ID' => $event_sport['id'],
            'Tournament Format ID' => $event_sport['tournament_format_id'],
            'Tournament Format Name' => $event_sport['tournament_format_name'] ?? 'Not set',
            'Tournament Format Code' => $event_sport['tournament_format_code'] ?? 'Not set',
            'Bracket Type (Legacy)' => $event_sport['bracket_type'] ?? 'Not set',
            'Max Teams' => $event_sport['max_teams'] ?? 'Not set',
            'Registration Deadline' => $event_sport['registration_deadline'] ?? 'Not set',
            'Status' => $event_sport['status'] ?? 'Not set'
        ];
        
        foreach ($fields as $field => $value) {
            echo "<tr>";
            echo "<td style='padding: 8px; font-weight: bold;'>$field</td>";
            echo "<td style='padding: 8px;'>$value</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        // Check what format should be displayed
        $display_format = 'Not configured';
        if ($event_sport['tournament_format_name']) {
            $display_format = $event_sport['tournament_format_name'];
        } elseif ($event_sport['bracket_type']) {
            $display_format = ucwords(str_replace('_', ' ', $event_sport['bracket_type']));
        }
        
        echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Expected Tournament Format Display:</strong> $display_format</p>";
        echo "</div>";
        
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ No Event Sport Configuration Found</h3>";
        echo "<p>The sport may not have been properly added to this event.</p>";
        echo "</div>";
    }
    
    echo "<h2>2. Event Department Registration</h2>";
    
    // Check event department registrations
    $stmt = $conn->prepare("
        SELECT 
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            edr.registration_date,
            edr.status,
            edr.total_participants,
            edr.contact_person
        FROM departments d
        JOIN event_department_registrations edr ON d.id = edr.department_id
        WHERE edr.event_id = ?
        ORDER BY d.name
    ");
    $stmt->execute([$event_id]);
    $event_participants = $stmt->fetchAll();
    
    if (!empty($event_participants)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Event Department Registrations Found</h3>";
        echo "<p><strong>Total Registered Departments:</strong> " . count($event_participants) . "</p>";
        
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f8f9fa;'>";
        echo "<th style='padding: 8px;'>Department</th>";
        echo "<th style='padding: 8px;'>Abbreviation</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Participants</th>";
        echo "<th style='padding: 8px;'>Registration Date</th>";
        echo "</tr>";
        
        foreach ($event_participants as $participant) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$participant['name']}</td>";
            echo "<td style='padding: 8px;'>{$participant['abbreviation']}</td>";
            echo "<td style='padding: 8px;'>{$participant['status']}</td>";
            echo "<td style='padding: 8px;'>{$participant['total_participants']}</td>";
            echo "<td style='padding: 8px;'>{$participant['registration_date']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ No Event Department Registrations Found</h3>";
        echo "<p>No departments are registered for this event.</p>";
        echo "</div>";
    }
    
    echo "<h2>3. Sport-Specific Participations</h2>";
    
    // Check department sport participations for this specific sport
    if ($event_sport) {
        $stmt = $conn->prepare("
            SELECT 
                d.name as department_name,
                d.abbreviation,
                dsp.team_name,
                dsp.status,
                dsp.participants,
                dsp.notes
            FROM department_sport_participations dsp
            JOIN event_department_registrations edr ON dsp.event_department_registration_id = edr.id
            JOIN departments d ON edr.department_id = d.id
            WHERE dsp.event_sport_id = ?
            ORDER BY d.name
        ");
        $stmt->execute([$event_sport['id']]);
        $sport_participations = $stmt->fetchAll();
        
        if (!empty($sport_participations)) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h3>✅ Sport-Specific Participations Found</h3>";
            echo "<p><strong>Departments participating in this sport:</strong> " . count($sport_participations) . "</p>";
            
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f8f9fa;'>";
            echo "<th style='padding: 8px;'>Department</th>";
            echo "<th style='padding: 8px;'>Team Name</th>";
            echo "<th style='padding: 8px;'>Status</th>";
            echo "<th style='padding: 8px;'>Participants</th>";
            echo "<th style='padding: 8px;'>Notes</th>";
            echo "</tr>";
            
            foreach ($sport_participations as $participation) {
                $participants_data = json_decode($participation['participants'], true);
                $participant_count = is_array($participants_data) ? count($participants_data) : 0;
                
                echo "<tr>";
                echo "<td style='padding: 8px;'>{$participation['department_name']} ({$participation['abbreviation']})</td>";
                echo "<td style='padding: 8px;'>" . ($participation['team_name'] ?: 'Not set') . "</td>";
                echo "<td style='padding: 8px;'>{$participation['status']}</td>";
                echo "<td style='padding: 8px;'>$participant_count</td>";
                echo "<td style='padding: 8px;'>" . ($participation['notes'] ?: 'None') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
            echo "</div>";
        } else {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
            echo "<h3>⚠️ No Sport-Specific Participations Found</h3>";
            echo "<p>Departments may be registered for the event but not specifically for this sport.</p>";
            echo "</div>";
        }
    }
    
    echo "<h2>4. Current manage-category.php Logic Analysis</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
    echo "<h3>🔍 What manage-category.php Should Display</h3>";
    
    if ($event_sport) {
        echo "<h4>Tournament Format Logic:</h4>";
        echo "<ol>";
        echo "<li><strong>First Priority:</strong> tournament_formats.name from event_sports.tournament_format_id</li>";
        echo "<li><strong>Second Priority:</strong> event_sports.bracket_type (legacy)</li>";
        echo "<li><strong>Third Priority:</strong> sports.bracket_format (fallback)</li>";
        echo "</ol>";
        
        echo "<h4>Participants Logic:</h4>";
        echo "<ol>";
        echo "<li><strong>Primary Source:</strong> event_department_registrations for this event</li>";
        echo "<li><strong>Sport-Specific:</strong> department_sport_participations for this event_sport</li>";
        echo "</ol>";
        
        // Show what the current logic would produce
        $expected_format = 'Unknown';
        if ($event_sport['tournament_format_name']) {
            $expected_format = $event_sport['tournament_format_name'];
        } elseif ($event_sport['bracket_type']) {
            $expected_format = ucwords(str_replace('_', ' ', $event_sport['bracket_type']));
        }
        
        $expected_participants = count($event_participants);
        
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>Expected Display Values:</h4>";
        echo "<ul>";
        echo "<li><strong>Tournament Format:</strong> $expected_format</li>";
        echo "<li><strong>Participants:</strong> $expected_participants departments</li>";
        echo "</ul>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<h2>5. Fix Recommendations</h2>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;'>";
    echo "<h3>🔧 Issues & Solutions</h3>";
    
    if (!$event_sport) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Critical Issue:</strong> Sport not properly added to event</p>";
        echo "<p><strong>Solution:</strong> Re-add the sport to the event with proper tournament format</p>";
        echo "</div>";
    } else {
        if (!$event_sport['tournament_format_id'] && !$event_sport['bracket_type']) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p><strong>Tournament Format Issue:</strong> No format configured for this event sport</p>";
            echo "<p><strong>Solution:</strong> Update event_sports table with proper tournament_format_id</p>";
            echo "</div>";
        }
        
        if (empty($event_participants)) {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p><strong>Participants Issue:</strong> No departments registered for this event</p>";
            echo "<p><strong>Solution:</strong> Register departments for the event (not just the sport)</p>";
            echo "</div>";
        }
    }
    echo "</div>";
    
    echo "<h2>6. Test Links</h2>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='manage-category.php?event_id=$event_id&sport_id=$sport_id&category_id=$category_id' target='_blank' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "🎯 View Category Page";
    echo "</a>";
    
    echo "<a href='event-sports.php?event_id=$event_id' target='_blank' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "⚙️ Manage Event Sports";
    echo "</a>";
    
    echo "<a href='manage-event.php?id=$event_id' target='_blank' style='background: #6c757d; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "📋 Event Management";
    echo "</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Debug Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
