<?php
/**
 * Match Results Management AJAX Handler
 * SC_IMS Sports Competition and Event Management System
 * 
 * Handles match result entry, validation, and tournament progression.
 */

require_once '../../config/database.php';
require_once '../../includes/match_scheduler.php';

header('Content-Type: application/json');

try {
    $action = $_POST['action'] ?? $_GET['action'] ?? '';
    
    switch ($action) {
        case 'update_match_result':
            handleUpdateMatchResult();
            break;
        case 'get_match_details':
            handleGetMatchDetails();
            break;
        case 'advance_tournament':
            handleAdvanceTournament();
            break;
        case 'get_tournament_progress':
            handleGetTournamentProgress();
            break;
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Update match result
 */
function handleUpdateMatchResult() {
    global $conn;
    
    $matchId = $_POST['match_id'] ?? '';
    $winnerId = $_POST['winner_id'] ?? '';
    $team1Score = $_POST['team1_score'] ?? null;
    $team2Score = $_POST['team2_score'] ?? null;
    $notes = $_POST['notes'] ?? '';
    
    if (empty($matchId)) {
        throw new Exception('Match ID is required');
    }
    
    try {
        $conn->beginTransaction();
        
        // Get match details
        $sql = "SELECT * FROM matches WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$matchId]);
        $match = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$match) {
            throw new Exception('Match not found');
        }
        
        // Validate winner
        if (!empty($winnerId) && $winnerId !== $match['team1_id'] && $winnerId !== $match['team2_id']) {
            throw new Exception('Invalid winner selection');
        }
        
        // Update match result
        $sql = "UPDATE matches 
                SET winner_id = ?, team1_score = ?, team2_score = ?, 
                    status = 'completed', actual_end_time = NOW(), referee_notes = ?
                WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$winnerId, $team1Score, $team2Score, $notes, $matchId]);
        
        // Update tournament participants statistics
        updateParticipantStats($conn, $match, $winnerId, $team1Score, $team2Score);
        
        // Check if round is complete and advance tournament if needed
        $roundComplete = checkRoundComplete($conn, $match['tournament_structure_id'], $match['round_number']);
        
        $response = [
            'success' => true,
            'message' => 'Match result updated successfully',
            'match_id' => $matchId,
            'round_complete' => $roundComplete
        ];
        
        if ($roundComplete) {
            $response['message'] .= '. Round completed!';
            
            // Auto-advance to next round if configured
            $autoAdvance = checkAutoAdvance($conn, $match['tournament_structure_id']);
            if ($autoAdvance) {
                $matchScheduler = new MatchScheduler($conn, $match['tournament_structure_id']);
                $matchScheduler->advanceToNextRound($match['round_number']);
                $response['advanced'] = true;
                $response['message'] .= ' Advanced to next round.';
            }
        }
        
        $conn->commit();
        echo json_encode($response);
        
    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}

/**
 * Get match details for editing
 */
function handleGetMatchDetails() {
    global $conn;
    
    $matchId = $_GET['match_id'] ?? '';
    
    if (empty($matchId)) {
        throw new Exception('Match ID is required');
    }
    
    $sql = "SELECT m.*, 
                   t1.name as team1_name, t1.abbreviation as team1_abbr, t1.color_code as team1_color,
                   t2.name as team2_name, t2.abbreviation as team2_abbr, t2.color_code as team2_color,
                   w.name as winner_name
            FROM matches m
            LEFT JOIN departments t1 ON m.team1_id = t1.id
            LEFT JOIN departments t2 ON m.team2_id = t2.id
            LEFT JOIN departments w ON m.winner_id = w.id
            WHERE m.id = ?";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$matchId]);
    $match = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$match) {
        throw new Exception('Match not found');
    }
    
    echo json_encode([
        'success' => true,
        'match' => $match
    ]);
}

/**
 * Manually advance tournament to next round
 */
function handleAdvanceTournament() {
    global $conn;
    
    $tournamentId = $_POST['tournament_id'] ?? '';
    $currentRound = $_POST['current_round'] ?? '';
    
    if (empty($tournamentId) || empty($currentRound)) {
        throw new Exception('Tournament ID and current round are required');
    }
    
    try {
        $conn->beginTransaction();
        
        // Verify all matches in current round are completed
        $sql = "SELECT COUNT(*) as incomplete_matches
                FROM matches 
                WHERE tournament_structure_id = ? AND round_number = ? AND status != 'completed'";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$tournamentId, $currentRound]);
        $result = $stmt->fetch();
        
        if ($result['incomplete_matches'] > 0) {
            throw new Exception('Cannot advance: ' . $result['incomplete_matches'] . ' matches still incomplete');
        }
        
        // Advance tournament
        $matchScheduler = new MatchScheduler($conn, $tournamentId);
        $matchScheduler->advanceToNextRound($currentRound);
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Tournament advanced to round ' . ($currentRound + 1),
            'new_round' => $currentRound + 1
        ]);
        
    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}

/**
 * Get tournament progress information
 */
function handleGetTournamentProgress() {
    global $conn;
    
    $tournamentId = $_GET['tournament_id'] ?? '';
    
    if (empty($tournamentId)) {
        throw new Exception('Tournament ID is required');
    }
    
    // Get tournament info
    $sql = "SELECT * FROM tournament_structures WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$tournamentId]);
    $tournament = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tournament) {
        throw new Exception('Tournament not found');
    }
    
    // Get match statistics by round
    $sql = "SELECT 
                round_number,
                COUNT(*) as total_matches,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed_matches,
                COUNT(CASE WHEN status = 'ongoing' THEN 1 END) as ongoing_matches,
                COUNT(CASE WHEN status = 'scheduled' THEN 1 END) as scheduled_matches
            FROM matches 
            WHERE tournament_structure_id = ?
            GROUP BY round_number
            ORDER BY round_number";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$tournamentId]);
    $roundStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Calculate overall progress
    $totalMatches = 0;
    $completedMatches = 0;
    
    foreach ($roundStats as $round) {
        $totalMatches += $round['total_matches'];
        $completedMatches += $round['completed_matches'];
    }
    
    $progressPercentage = $totalMatches > 0 ? round(($completedMatches / $totalMatches) * 100, 1) : 0;
    
    echo json_encode([
        'success' => true,
        'tournament' => $tournament,
        'round_stats' => $roundStats,
        'overall_progress' => [
            'total_matches' => $totalMatches,
            'completed_matches' => $completedMatches,
            'progress_percentage' => $progressPercentage
        ]
    ]);
}

/**
 * Update participant statistics after match completion
 */
function updateParticipantStats($conn, $match, $winnerId, $team1Score, $team2Score) {
    $tournamentId = $match['tournament_structure_id'];
    $team1Id = $match['team1_id'];
    $team2Id = $match['team2_id'];
    
    // Update team 1 stats
    if ($team1Id) {
        $team1Win = ($winnerId == $team1Id) ? 1 : 0;
        $team1Loss = ($winnerId && $winnerId != $team1Id) ? 1 : 0;
        $team1Draw = (!$winnerId) ? 1 : 0;
        
        updateTeamStats($conn, $tournamentId, $team1Id, $team1Win, $team1Loss, $team1Draw, $team1Score, $team2Score);
    }
    
    // Update team 2 stats
    if ($team2Id) {
        $team2Win = ($winnerId == $team2Id) ? 1 : 0;
        $team2Loss = ($winnerId && $winnerId != $team2Id) ? 1 : 0;
        $team2Draw = (!$winnerId) ? 1 : 0;
        
        updateTeamStats($conn, $tournamentId, $team2Id, $team2Win, $team2Loss, $team2Draw, $team2Score, $team1Score);
    }
}

/**
 * Update individual team statistics
 */
function updateTeamStats($conn, $tournamentId, $teamId, $wins, $losses, $draws, $goalsFor, $goalsAgainst) {
    // Get current stats
    $sql = "SELECT * FROM tournament_participants 
            WHERE tournament_structure_id = ? AND registration_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$tournamentId, $teamId]);
    $participant = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($participant) {
        // Calculate points (3 for win, 1 for draw, 0 for loss)
        $points = ($wins * 3) + ($draws * 1);
        
        // Calculate goal difference
        $goalDifference = ($goalsFor ?? 0) - ($goalsAgainst ?? 0);
        
        // Update stats
        $sql = "UPDATE tournament_participants 
                SET wins = wins + ?, losses = losses + ?, draws = draws + ?,
                    points = points + ?, goals_for = goals_for + ?, goals_against = goals_against + ?,
                    goal_difference = goal_difference + ?
                WHERE tournament_structure_id = ? AND registration_id = ?";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute([
            $wins, $losses, $draws, $points,
            $goalsFor ?? 0, $goalsAgainst ?? 0, $goalDifference,
            $tournamentId, $teamId
        ]);
    }
}

/**
 * Check if all matches in a round are completed
 */
function checkRoundComplete($conn, $tournamentId, $round) {
    $sql = "SELECT COUNT(*) as incomplete_matches
            FROM matches 
            WHERE tournament_structure_id = ? AND round_number = ? AND status != 'completed'";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$tournamentId, $round]);
    $result = $stmt->fetch();
    
    return $result['incomplete_matches'] == 0;
}

/**
 * Check if tournament is configured for auto-advance
 */
function checkAutoAdvance($conn, $tournamentId) {
    $sql = "SELECT scoring_config FROM tournament_structures WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$tournamentId]);
    $result = $stmt->fetch();
    
    if ($result) {
        $config = json_decode($result['scoring_config'], true);
        return $config['auto_advance'] ?? false;
    }
    
    return false;
}
?>
