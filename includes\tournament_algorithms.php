<?php
/**
 * Tournament Algorithm Classes
 * SC_IMS Sports Competition and Event Management System
 * 
 * Comprehensive tournament management algorithms for all tournament formats
 */

// Base Tournament Algorithm Interface
interface TournamentAlgorithmInterface {
    public function generateBracket($participants, $config = []);
    public function calculateRounds($participantCount);
    public function calculateMatches($participantCount);
    public function advanceParticipants($roundResults);
    public function generateNextRound($currentRound, $participants);
    public function isComplete($tournament);
    public function getRankings($tournament);
}

// Abstract Base Tournament Algorithm
abstract class BaseTournamentAlgorithm implements TournamentAlgorithmInterface {
    protected $conn;
    protected $tournamentId;
    protected $config;
    protected $format;
    protected $calculator;

    public function __construct($conn, $tournamentId, $config = []) {
        $this->conn = $conn;
        $this->tournamentId = $tournamentId;
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Set formula calculator for database-driven calculations
     */
    public function setFormulaCalculator($calculator) {
        $this->calculator = $calculator;
    }

    /**
     * Set tournament format data
     */
    public function setFormat($format) {
        $this->format = $format;
    }

    /**
     * Configure algorithm with settings
     */
    public function configure($config) {
        $this->config = array_merge($this->config, $config);
    }
    
    abstract protected function getDefaultConfig();
    
    protected function seedParticipants($participants, $method = 'random') {
        switch ($method) {
            case 'ranking':
                return $this->seedByRanking($participants);
            case 'manual':
                return $this->seedManually($participants);
            case 'hybrid':
                return $this->seedHybrid($participants);
            default:
                return $this->seedRandomly($participants);
        }
    }
    
    protected function seedRandomly($participants) {
        // Fisher-Yates shuffle for true randomization
        $count = count($participants);
        for ($i = $count - 1; $i > 0; $i--) {
            $j = mt_rand(0, $i);
            $temp = $participants[$i];
            $participants[$i] = $participants[$j];
            $participants[$j] = $temp;
        }

        // Assign seed numbers
        foreach ($participants as $index => $participant) {
            $participants[$index]['seed'] = $index + 1;
        }
        return $participants;
    }
    
    protected function seedByRanking($participants) {
        // Sort by multiple criteria: points, wins, goal difference, etc.
        usort($participants, function($a, $b) {
            // Primary: Points
            $pointsA = $a['points'] ?? $a['total_points'] ?? 0;
            $pointsB = $b['points'] ?? $b['total_points'] ?? 0;
            if ($pointsA !== $pointsB) {
                return $pointsB <=> $pointsA;
            }

            // Secondary: Wins
            $winsA = $a['wins'] ?? $a['total_wins'] ?? 0;
            $winsB = $b['wins'] ?? $b['total_wins'] ?? 0;
            if ($winsA !== $winsB) {
                return $winsB <=> $winsA;
            }

            // Tertiary: Goal difference or performance rating
            $gdA = $a['goal_difference'] ?? $a['performance_rating'] ?? 0;
            $gdB = $b['goal_difference'] ?? $b['performance_rating'] ?? 0;
            return $gdB <=> $gdA;
        });

        // Assign seed numbers based on ranking
        foreach ($participants as $index => $participant) {
            $participants[$index]['seed'] = $index + 1;
            $participants[$index]['ranking_position'] = $index + 1;
        }

        return $participants;
    }
    
    protected function seedManually($participants) {
        // Manual seeding should be handled by admin interface
        return $participants;
    }
    
    protected function seedHybrid($participants) {
        $totalCount = count($participants);

        // Determine how many top seeds to rank (25% or minimum 4, maximum 8)
        $topCount = max(4, min(8, ceil($totalCount * 0.25)));
        $topCount = min($topCount, $totalCount);

        // First, sort all participants by ranking to identify top performers
        $rankedParticipants = $this->seedByRanking($participants);

        // Split into top seeds and rest
        $topSeeds = array_slice($rankedParticipants, 0, $topCount);
        $restParticipants = array_slice($rankedParticipants, $topCount);

        // Keep top seeds in ranking order
        foreach ($topSeeds as $index => $participant) {
            $topSeeds[$index]['seed'] = $index + 1;
            $topSeeds[$index]['seeding_method'] = 'ranking';
        }

        // Randomize the rest
        $restParticipants = $this->seedRandomly($restParticipants);
        foreach ($restParticipants as $index => $participant) {
            $restParticipants[$index]['seed'] = $topCount + $index + 1;
            $restParticipants[$index]['seeding_method'] = 'random';
        }

        return array_merge($topSeeds, $restParticipants);
    }
    
    protected function handleByes($participants) {
        $count = count($participants);
        $nextPowerOf2 = pow(2, ceil(log($count, 2)));
        $byesNeeded = $nextPowerOf2 - $count;
        
        if ($byesNeeded > 0) {
            // Add bye participants (highest seeds get byes)
            for ($i = 0; $i < $byesNeeded; $i++) {
                $participants[] = [
                    'id' => 'bye_' . ($i + 1),
                    'name' => 'BYE',
                    'is_bye' => true,
                    'seed' => $count + $i + 1
                ];
            }
        }
        
        return $participants;
    }
    
    protected function createMatch($participant1, $participant2, $round, $position) {
        return [
            'tournament_structure_id' => $this->tournamentId,
            'tournament_round_id' => $round['id'],
            'team1_id' => $participant1['is_bye'] ?? false ? null : $participant1['id'],
            'team2_id' => $participant2['is_bye'] ?? false ? null : $participant2['id'],
            'round_number' => $round['round_number'],
            'bracket_position' => $position,
            'is_bye_match' => ($participant1['is_bye'] ?? false) || ($participant2['is_bye'] ?? false),
            'status' => 'scheduled'
        ];
    }
}

// Single Elimination Algorithm
class SingleEliminationAlgorithm extends BaseTournamentAlgorithm {
    protected function getDefaultConfig() {
        return [
            'bracket_seeding' => true,
            'third_place_match' => false,
            'consolation_rounds' => false
        ];
    }
    
    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);
        
        // Seed participants
        $seededParticipants = $this->seedParticipants($participants, $config['seeding_method'] ?? 'random');
        
        // Handle byes if needed
        if ($this->config['bracket_seeding']) {
            $seededParticipants = $this->handleByes($seededParticipants);
        }
        
        $totalRounds = $this->calculateRounds(count($participants));
        $bracket = [
            'format' => 'single_elimination',
            'participants' => $seededParticipants,
            'rounds' => $totalRounds,
            'structure' => $this->generateBracketStructure($seededParticipants, $totalRounds)
        ];
        
        return $bracket;
    }
    
    public function calculateRounds($participantCount) {
        // Use database-driven calculation if available
        if ($this->calculator && $this->format) {
            return $this->calculator->calculateRounds($this->format, $participantCount);
        }
        // Fallback to hardcoded formula
        return ceil(log($participantCount, 2));
    }

    public function calculateMatches($participantCount) {
        // Use database-driven calculation if available
        if ($this->calculator && $this->format) {
            return $this->calculator->calculateMatches($this->format, $participantCount);
        }
        // Fallback to hardcoded formula
        return $participantCount - 1;
    }
    
    private function generateBracketStructure($participants, $totalRounds) {
        $structure = [];
        $currentParticipants = $participants;
        
        for ($round = 1; $round <= $totalRounds; $round++) {
            $roundMatches = [];
            $nextRoundParticipants = [];
            
            // Pair participants for matches
            for ($i = 0; $i < count($currentParticipants); $i += 2) {
                $participant1 = $currentParticipants[$i];
                $participant2 = $currentParticipants[$i + 1] ?? null;
                
                if ($participant2 === null) {
                    // Odd number, automatic advancement
                    $nextRoundParticipants[] = $participant1;
                } else {
                    $matchPosition = 'R' . $round . 'M' . (count($roundMatches) + 1);
                    $roundMatches[] = [
                        'position' => $matchPosition,
                        'participant1' => $participant1,
                        'participant2' => $participant2,
                        'winner_advances_to' => $round < $totalRounds ? 'R' . ($round + 1) . 'M' . ceil((count($roundMatches) + 1) / 2) : 'champion'
                    ];
                    
                    // Placeholder for winner
                    $nextRoundParticipants[] = [
                        'id' => 'winner_' . $matchPosition,
                        'name' => 'Winner of ' . $matchPosition,
                        'is_placeholder' => true
                    ];
                }
            }
            
            $structure['round_' . $round] = [
                'round_number' => $round,
                'round_name' => $this->getRoundName($round, $totalRounds),
                'matches' => $roundMatches
            ];
            
            $currentParticipants = $nextRoundParticipants;
        }
        
        return $structure;
    }
    
    private function getRoundName($round, $totalRounds) {
        $remaining = $totalRounds - $round + 1;
        
        switch ($remaining) {
            case 1: return 'Final';
            case 2: return 'Semifinal';
            case 3: return 'Quarterfinal';
            case 4: return 'Round of 16';
            case 5: return 'Round of 32';
            default: return 'Round ' . $round;
        }
    }
    
    public function advanceParticipants($roundResults) {
        // Implementation for advancing winners to next round
        $winners = [];
        foreach ($roundResults as $matchResult) {
            if (isset($matchResult['winner_id'])) {
                $winners[] = $matchResult['winner_id'];
            }
        }
        return $winners;
    }
    
    public function generateNextRound($currentRound, $participants) {
        // Generate matches for the next round based on current round results
        $nextRoundNumber = $currentRound['round_number'] + 1;
        $matches = [];
        
        for ($i = 0; $i < count($participants); $i += 2) {
            if (isset($participants[$i + 1])) {
                $matches[] = $this->createMatch(
                    $participants[$i],
                    $participants[$i + 1],
                    ['id' => null, 'round_number' => $nextRoundNumber],
                    'R' . $nextRoundNumber . 'M' . (count($matches) + 1)
                );
            }
        }
        
        return $matches;
    }
    
    public function isComplete($tournament) {
        // Check if tournament is complete (final match played)
        $sql = "SELECT COUNT(*) as remaining FROM matches 
                WHERE tournament_structure_id = ? AND status != 'completed'";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        $result = $stmt->fetch();
        
        return $result['remaining'] == 0;
    }
    
    public function getRankings($tournament) {
        // Get final rankings for single elimination - simplified schema
        $sql = "SELECT tp.*, tp.team_name, tp.department_id, d.name as department_name
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                WHERE tp.tournament_structure_id = ?
                ORDER BY tp.current_status = 'active' DESC, tp.points DESC, tp.wins DESC";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        return $stmt->fetchAll();
    }
}

// Double Elimination Algorithm
class DoubleEliminationAlgorithm extends BaseTournamentAlgorithm {
    protected function getDefaultConfig() {
        return [
            'winners_bracket' => true,
            'losers_bracket' => true,
            'grand_final_advantage' => true,
            'bracket_seeding' => true
        ];
    }
    
    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);
        
        $seededParticipants = $this->seedParticipants($participants, $config['seeding_method'] ?? 'random');
        $seededParticipants = $this->handleByes($seededParticipants);
        
        $totalRounds = $this->calculateRounds(count($participants));
        
        $bracket = [
            'format' => 'double_elimination',
            'participants' => $seededParticipants,
            'rounds' => $totalRounds,
            'structure' => $this->generateDoubleBracketStructure($seededParticipants, $totalRounds)
        ];
        
        return $bracket;
    }
    
    public function calculateRounds($participantCount) {
        return ceil(log($participantCount, 2)) * 2 - 1;
    }
    
    public function calculateMatches($participantCount) {
        return 2 * $participantCount - 2;
    }
    
    private function generateDoubleBracketStructure($participants, $totalRounds) {
        // Implementation for double elimination bracket structure
        // This is more complex as it involves winners and losers brackets
        return [
            'winners_bracket' => $this->generateWinnersBracket($participants),
            'losers_bracket' => $this->generateLosersBracket($participants),
            'grand_final' => $this->generateGrandFinal()
        ];
    }
    
    private function generateWinnersBracket($participants) {
        // Similar to single elimination but losers go to losers bracket
        return [];
    }
    
    private function generateLosersBracket($participants) {
        // Complex bracket for eliminated participants
        return [];
    }
    
    private function generateGrandFinal() {
        // Final match between winners bracket champion and losers bracket champion
        return [];
    }
    
    public function advanceParticipants($roundResults) {
        // Handle advancement in both brackets
        return [];
    }
    
    public function generateNextRound($currentRound, $participants) {
        // Generate next round considering both brackets
        return [];
    }
    
    public function isComplete($tournament) {
        // Check if grand final is complete
        return false;
    }
    
    public function getRankings($tournament) {
        // Get rankings considering both brackets
        return [];
    }
}

// Round Robin Algorithm
class RoundRobinAlgorithm extends BaseTournamentAlgorithm {
    protected function getDefaultConfig() {
        return [
            'points_win' => 3,
            'points_draw' => 1,
            'points_loss' => 0,
            'head_to_head_tiebreaker' => true,
            'goal_difference_tiebreaker' => true
        ];
    }

    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);

        $seededParticipants = $this->seedParticipants($participants, $config['seeding_method'] ?? 'random');

        $bracket = [
            'format' => 'round_robin',
            'participants' => $seededParticipants,
            'rounds' => 1, // Single round robin
            'structure' => $this->generateRoundRobinStructure($seededParticipants)
        ];

        return $bracket;
    }

    public function calculateRounds($participantCount) {
        return 1; // Single round robin
    }

    public function calculateMatches($participantCount) {
        return $participantCount * ($participantCount - 1) / 2;
    }

    private function generateRoundRobinStructure($participants) {
        $matches = [];
        $participantCount = count($participants);

        // Generate all possible pairings
        for ($i = 0; $i < $participantCount; $i++) {
            for ($j = $i + 1; $j < $participantCount; $j++) {
                $matches[] = [
                    'position' => 'RR_' . ($i + 1) . '_' . ($j + 1),
                    'participant1' => $participants[$i],
                    'participant2' => $participants[$j],
                    'round_number' => 1
                ];
            }
        }

        return [
            'round_1' => [
                'round_number' => 1,
                'round_name' => 'Round Robin',
                'matches' => $matches
            ]
        ];
    }

    public function advanceParticipants($roundResults) {
        // In round robin, all participants continue until all matches are played
        return [];
    }

    public function generateNextRound($currentRound, $participants) {
        // Round robin typically has only one round
        return [];
    }

    public function isComplete($tournament) {
        // Check if all matches are completed
        $sql = "SELECT COUNT(*) as total,
                       SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
                FROM matches WHERE tournament_structure_id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        $result = $stmt->fetch();

        return $result['total'] > 0 && $result['total'] == $result['completed'];
    }

    public function getRankings($tournament) {
        // Calculate round robin standings - simplified schema
        $sql = "SELECT tp.*, tp.team_name, tp.department_id, d.name as department_name,
                       tp.points, tp.wins, tp.losses, tp.draws,
                       (tp.wins * {$this->config['points_win']} +
                        tp.draws * {$this->config['points_draw']} +
                        tp.losses * {$this->config['points_loss']}) as total_points
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                WHERE tp.tournament_structure_id = ?
                ORDER BY total_points DESC, tp.wins DESC, tp.points DESC";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        return $stmt->fetchAll();
    }
}

// Swiss System Algorithm
class SwissSystemAlgorithm extends BaseTournamentAlgorithm {
    protected function getDefaultConfig() {
        return [
            'pairing_algorithm' => 'swiss',
            'avoid_rematches' => true,
            'color_balancing' => false, // For chess-like games
            'points_win' => 1,
            'points_draw' => 0.5,
            'points_loss' => 0
        ];
    }

    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);

        $seededParticipants = $this->seedParticipants($participants, $config['seeding_method'] ?? 'random');
        $totalRounds = $this->calculateRounds(count($participants));

        $bracket = [
            'format' => 'swiss_system',
            'participants' => $seededParticipants,
            'rounds' => $totalRounds,
            'structure' => $this->generateSwissStructure($seededParticipants, $totalRounds)
        ];

        return $bracket;
    }

    public function calculateRounds($participantCount) {
        return ceil(log($participantCount, 2));
    }

    public function calculateMatches($participantCount) {
        $rounds = $this->calculateRounds($participantCount);
        return $rounds * floor($participantCount / 2);
    }

    private function generateSwissStructure($participants, $totalRounds) {
        $structure = [];

        // First round: pair by initial seeding
        $firstRoundMatches = $this->generateFirstRoundPairings($participants);

        $structure['round_1'] = [
            'round_number' => 1,
            'round_name' => 'Round 1',
            'matches' => $firstRoundMatches
        ];

        // Subsequent rounds will be generated dynamically based on standings
        for ($round = 2; $round <= $totalRounds; $round++) {
            $structure['round_' . $round] = [
                'round_number' => $round,
                'round_name' => 'Round ' . $round,
                'matches' => [] // To be generated after previous round
            ];
        }

        return $structure;
    }

    private function generateFirstRoundPairings($participants) {
        $matches = [];
        $count = count($participants);
        $half = floor($count / 2);

        // Swiss system first round: top half vs bottom half
        for ($i = 0; $i < $half; $i++) {
            $matches[] = [
                'position' => 'S1M' . ($i + 1),
                'participant1' => $participants[$i],
                'participant2' => $participants[$i + $half],
                'round_number' => 1
            ];
        }

        // Handle odd number of participants
        if ($count % 2 == 1) {
            $matches[] = [
                'position' => 'S1M' . ($half + 1),
                'participant1' => $participants[$count - 1],
                'participant2' => ['id' => 'bye', 'name' => 'BYE', 'is_bye' => true],
                'round_number' => 1
            ];
        }

        return $matches;
    }

    public function generateSwissPairings($round, $standings) {
        // Swiss pairing algorithm based on current standings
        $matches = [];
        $availableParticipants = array_filter($standings, function($p) {
            return $p['current_status'] == 'active';
        });

        // Sort by points, then by tiebreakers
        usort($availableParticipants, function($a, $b) {
            if ($a['points'] == $b['points']) {
                return $b['wins'] <=> $a['wins'];
            }
            return $b['points'] <=> $a['points'];
        });

        $paired = [];

        foreach ($availableParticipants as $participant) {
            if (in_array($participant['id'], $paired)) continue;

            // Find best opponent
            $opponent = $this->findBestOpponent($participant, $availableParticipants, $paired);

            if ($opponent) {
                $matches[] = [
                    'position' => 'S' . $round . 'M' . (count($matches) + 1),
                    'participant1' => $participant,
                    'participant2' => $opponent,
                    'round_number' => $round
                ];

                $paired[] = $participant['id'];
                $paired[] = $opponent['id'];
            }
        }

        return $matches;
    }

    private function findBestOpponent($participant, $availableParticipants, $paired) {
        foreach ($availableParticipants as $potential) {
            if ($potential['id'] == $participant['id']) continue;
            if (in_array($potential['id'], $paired)) continue;

            // Check if they haven't played before (if avoid_rematches is enabled)
            if ($this->config['avoid_rematches'] && $this->havePlayedBefore($participant, $potential)) {
                continue;
            }

            // Prefer opponents with similar points
            return $potential;
        }

        return null;
    }

    private function havePlayedBefore($participant1, $participant2) {
        $sql = "SELECT COUNT(*) as count FROM matches
                WHERE tournament_structure_id = ?
                AND ((team1_id = ? AND team2_id = ?) OR (team1_id = ? AND team2_id = ?))";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([
            $this->tournamentId,
            $participant1['id'], $participant2['id'],
            $participant2['id'], $participant1['id']
        ]);
        $result = $stmt->fetch();

        return $result['count'] > 0;
    }

    public function advanceParticipants($roundResults) {
        // Update participant standings after each round
        foreach ($roundResults as $matchResult) {
            $this->updateParticipantStandings($matchResult);
        }
        return [];
    }

    private function updateParticipantStandings($matchResult) {
        // Update wins, losses, draws, and points for participants - simplified schema
        if (isset($matchResult['winner_id'])) {
            // Update winner
            $sql = "UPDATE tournament_participants
                    SET wins = wins + 1, points = points + ?
                    WHERE tournament_structure_id = ? AND id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$this->config['points_win'], $this->tournamentId, $matchResult['winner_id']]);

            // Update loser
            $loserId = $matchResult['team1_id'] == $matchResult['winner_id'] ? $matchResult['team2_id'] : $matchResult['team1_id'];
            $sql = "UPDATE tournament_participants
                    SET losses = losses + 1, points = points + ?
                    WHERE tournament_structure_id = ? AND id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$this->config['points_loss'], $this->tournamentId, $loserId]);
        } else {
            // Draw
            $sql = "UPDATE tournament_participants
                    SET draws = draws + 1, points = points + ?
                    WHERE tournament_structure_id = ? AND id IN (?, ?)";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([
                $this->config['points_draw'],
                $this->tournamentId,
                $matchResult['team1_id'],
                $matchResult['team2_id']
            ]);
        }
    }

    public function generateNextRound($currentRound, $participants) {
        $nextRound = $currentRound['round_number'] + 1;
        return $this->generateSwissPairings($nextRound, $participants);
    }

    public function isComplete($tournament) {
        // Swiss system is complete when all planned rounds are finished
        $sql = "SELECT current_round, total_rounds FROM tournament_structures WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        $result = $stmt->fetch();

        return $result && $result['current_round'] >= $result['total_rounds'];
    }

    public function getRankings($tournament) {
        // Get Swiss system standings - simplified schema
        $sql = "SELECT tp.*, tp.team_name, tp.department_id, d.name as department_name
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                WHERE tp.tournament_structure_id = ?
                ORDER BY tp.points DESC, tp.wins DESC, tp.draws DESC";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        return $stmt->fetchAll();
    }
}

// Multi-Stage Tournament Algorithm (Group Stage + Knockout)
class MultiStageAlgorithm extends BaseTournamentAlgorithm {
    protected function getDefaultConfig() {
        return [
            'groups_count' => 4,
            'teams_per_group' => 4,
            'teams_advance_per_group' => 2,
            'group_stage_format' => 'round_robin',
            'knockout_stage_format' => 'single_elimination',
            'group_points_win' => 3,
            'group_points_draw' => 1,
            'group_points_loss' => 0,
            'third_place_match' => true
        ];
    }

    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);

        $participantCount = count($participants);
        $groupsCount = $this->config['groups_count'];
        $teamsPerGroup = ceil($participantCount / $groupsCount);

        // Seed participants
        $seededParticipants = $this->seedParticipants($participants, $config['seeding_method'] ?? 'random');

        // Create groups
        $groups = $this->createGroups($seededParticipants, $groupsCount, $teamsPerGroup);

        // Calculate total rounds (group stage + knockout stage)
        $groupRounds = $teamsPerGroup - 1; // Round robin within groups
        $knockoutRounds = ceil(log($this->config['teams_advance_per_group'] * $groupsCount, 2));
        $totalRounds = $groupRounds + $knockoutRounds;

        $bracket = [
            'format' => 'multi_stage',
            'participants' => $seededParticipants,
            'rounds' => $totalRounds,
            'structure' => [
                'group_stage' => [
                    'groups' => $groups,
                    'rounds' => $groupRounds,
                    'format' => $this->config['group_stage_format']
                ],
                'knockout_stage' => [
                    'rounds' => $knockoutRounds,
                    'format' => $this->config['knockout_stage_format'],
                    'teams_advance' => $this->config['teams_advance_per_group'] * $groupsCount
                ]
            ]
        ];

        return $bracket;
    }

    public function calculateRounds($participantCount) {
        $groupsCount = $this->config['groups_count'];
        $teamsPerGroup = ceil($participantCount / $groupsCount);
        $groupRounds = $teamsPerGroup - 1;
        $knockoutRounds = ceil(log($this->config['teams_advance_per_group'] * $groupsCount, 2));
        return $groupRounds + $knockoutRounds;
    }

    public function calculateMatches($participantCount) {
        $groupsCount = $this->config['groups_count'];
        $teamsPerGroup = ceil($participantCount / $groupsCount);

        // Group stage matches (round robin within each group)
        $groupMatches = $groupsCount * ($teamsPerGroup * ($teamsPerGroup - 1) / 2);

        // Knockout stage matches
        $advancingTeams = $this->config['teams_advance_per_group'] * $groupsCount;
        $knockoutMatches = $advancingTeams - 1;
        if ($this->config['third_place_match']) {
            $knockoutMatches += 1;
        }

        return $groupMatches + $knockoutMatches;
    }

    private function createGroups($participants, $groupsCount, $teamsPerGroup) {
        $groups = [];
        $participantIndex = 0;

        for ($g = 0; $g < $groupsCount; $g++) {
            $groups["Group " . chr(65 + $g)] = [];

            for ($t = 0; $t < $teamsPerGroup && $participantIndex < count($participants); $t++) {
                $groups["Group " . chr(65 + $g)][] = $participants[$participantIndex];
                $participantIndex++;
            }
        }

        return $groups;
    }

    public function advanceParticipants($roundResults) {
        // Implementation for advancing participants from group stage to knockout
        return [];
    }

    public function generateNextRound($currentRound, $participants) {
        // Implementation for generating next round matches
        return [];
    }

    public function isComplete($tournament) {
        // Check if both group stage and knockout stage are complete
        return false;
    }

    public function getRankings($tournament) {
        // Get rankings considering both group stage and knockout stage performance - simplified schema
        $sql = "SELECT tp.*, tp.team_name, tp.department_id, d.name as department_name
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                WHERE tp.tournament_structure_id = ?
                ORDER BY tp.points DESC, tp.wins DESC";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        return $stmt->fetchAll();
    }
}

// =====================================================
// JUDGED SPORT ALGORITHMS
// =====================================================

/**
 * Judged Rounds Algorithm
 * For competitions with multiple judged rounds like singing, dancing, talent shows
 */
class JudgedRoundsAlgorithm extends BaseTournamentAlgorithm {

    protected function getDefaultConfig() {
        return [
            'rounds' => ['preliminary', 'semifinal', 'final'],
            'criteria_based' => true,
            'judge_scoring' => true,
            'audience_voting' => false,
            'advancement_percentage' => 0.5, // 50% advance to next round
            'min_finalists' => 3,
            'max_finalists' => 10
        ];
    }

    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);
        $rounds = $this->config['rounds'];
        $bracket = [];

        foreach ($rounds as $index => $roundName) {
            $roundParticipants = ($index === 0) ? $participants : $this->calculateAdvancingParticipants($participants, $index);

            $bracket[$roundName] = [
                'round_number' => $index + 1,
                'round_name' => ucfirst($roundName),
                'participants' => $roundParticipants,
                'matches' => $this->generateJudgedMatches($roundParticipants, $roundName),
                'advancement_count' => $this->calculateAdvancementCount($roundParticipants, $index, count($rounds))
            ];
        }

        return [
            'success' => true,
            'bracket' => $bracket,
            'format' => 'Judged Rounds',
            'total_rounds' => count($rounds),
            'scoring_type' => 'judged'
        ];
    }

    private function generateJudgedMatches($participants, $roundName) {
        $matches = [];
        foreach ($participants as $index => $participant) {
            $matches[] = [
                'match_number' => $index + 1,
                'participant_id' => $participant['id'],
                'participant_name' => $participant['name'],
                'performance_order' => $index + 1,
                'status' => 'pending',
                'scores' => [],
                'total_score' => 0,
                'rank' => null
            ];
        }
        return $matches;
    }

    private function calculateAdvancingParticipants($participants, $roundIndex) {
        $totalParticipants = count($participants);
        $advancementRate = $this->config['advancement_percentage'];

        if ($roundIndex === 1) { // Semifinal
            return max($this->config['min_finalists'], min($this->config['max_finalists'],
                       floor($totalParticipants * $advancementRate)));
        } elseif ($roundIndex === 2) { // Final
            return $this->config['min_finalists'];
        }

        return $totalParticipants;
    }

    private function calculateAdvancementCount($participants, $roundIndex, $totalRounds) {
        if ($roundIndex >= $totalRounds - 1) {
            return 0; // Final round, no advancement
        }
        return $this->calculateAdvancingParticipants($participants, $roundIndex + 1);
    }

    public function calculateRounds($participantCount) {
        return count($this->config['rounds']);
    }

    public function calculateMatches($participantCount) {
        return $participantCount * count($this->config['rounds']);
    }

    public function advanceParticipants($roundResults) {
        // Sort participants by score and advance top performers
        usort($roundResults, function($a, $b) {
            return $b['total_score'] <=> $a['total_score'];
        });

        $advancementCount = $this->calculateAdvancementCount($roundResults, 0, count($this->config['rounds']));
        return array_slice($roundResults, 0, $advancementCount);
    }

    public function generateNextRound($currentRound, $participants) {
        $nextRoundIndex = $currentRound + 1;
        if ($nextRoundIndex >= count($this->config['rounds'])) {
            return null; // No more rounds
        }

        $roundName = $this->config['rounds'][$nextRoundIndex];
        return $this->generateJudgedMatches($participants, $roundName);
    }

    public function isComplete($tournament) {
        // Check if final round is completed
        $finalRound = end($this->config['rounds']);
        return $this->isRoundComplete($tournament, $finalRound);
    }

    private function isRoundComplete($tournament, $roundName) {
        // Implementation to check if a specific round is complete
        return false; // Simplified for now
    }

    public function getRankings($tournament) {
        // Get final rankings based on judged scores
        $sql = "SELECT tp.*, tp.team_name, tp.department_id, d.name as department_name,
                       COALESCE(SUM(js.total_score), 0) as final_score
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                LEFT JOIN judged_scores js ON tp.id = js.participant_id
                WHERE tp.tournament_structure_id = ?
                GROUP BY tp.id
                ORDER BY final_score DESC, tp.team_name ASC";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        return $stmt->fetchAll();
    }
}

/**
 * Talent Showcase Algorithm
 * For talent shows, pageants, and showcase competitions with audience voting
 */
class TalentShowcaseAlgorithm extends BaseTournamentAlgorithm {

    protected function getDefaultConfig() {
        return [
            'showcase_rounds' => 3,
            'audience_voting' => true,
            'judge_scoring' => true,
            'performance_time_limits' => true,
            'audience_weight' => 0.3,
            'judge_weight' => 0.7,
            'categories' => ['talent', 'presentation', 'audience_appeal']
        ];
    }

    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);
        $rounds = $this->config['showcase_rounds'];
        $bracket = [];

        for ($round = 1; $round <= $rounds; $round++) {
            $roundName = $this->getRoundName($round, $rounds);
            $bracket[$roundName] = [
                'round_number' => $round,
                'round_name' => $roundName,
                'participants' => $participants,
                'performances' => $this->generatePerformanceSlots($participants, $round),
                'voting_enabled' => $this->config['audience_voting'],
                'judging_enabled' => $this->config['judge_scoring']
            ];
        }

        return [
            'success' => true,
            'bracket' => $bracket,
            'format' => 'Talent Showcase',
            'total_rounds' => $rounds,
            'scoring_type' => 'showcase'
        ];
    }

    private function getRoundName($round, $totalRounds) {
        if ($totalRounds === 1) return 'Showcase';
        if ($round === 1) return 'Opening Round';
        if ($round === $totalRounds) return 'Final Showcase';
        return "Round $round";
    }

    private function generatePerformanceSlots($participants, $round) {
        $performances = [];
        foreach ($participants as $index => $participant) {
            $performances[] = [
                'slot_number' => $index + 1,
                'participant_id' => $participant['id'],
                'participant_name' => $participant['name'],
                'performance_time' => $this->calculatePerformanceTime($round),
                'categories' => $this->config['categories'],
                'judge_scores' => [],
                'audience_score' => 0,
                'total_score' => 0,
                'status' => 'pending'
            ];
        }
        return $performances;
    }

    private function calculatePerformanceTime($round) {
        $baseTimes = [3, 4, 5]; // minutes per round
        return $baseTimes[min($round - 1, count($baseTimes) - 1)];
    }

    public function calculateRounds($participantCount) {
        return $this->config['showcase_rounds'];
    }

    public function calculateMatches($participantCount) {
        return $participantCount * $this->config['showcase_rounds'];
    }

    public function advanceParticipants($roundResults) {
        // In showcase format, all participants continue through all rounds
        return $roundResults;
    }

    public function generateNextRound($currentRound, $participants) {
        if ($currentRound >= $this->config['showcase_rounds']) {
            return null;
        }
        return $this->generatePerformanceSlots($participants, $currentRound + 1);
    }

    public function isComplete($tournament) {
        // Check if all showcase rounds are completed
        return $this->getAllRoundsCompleted($tournament);
    }

    private function getAllRoundsCompleted($tournament) {
        // Implementation to check if all rounds are completed
        return false; // Simplified for now
    }

    public function getRankings($tournament) {
        // Calculate final rankings based on combined judge and audience scores
        $sql = "SELECT tp.*, tp.team_name, tp.department_id, d.name as department_name,
                       COALESCE(AVG(js.judge_score * :judge_weight + js.audience_score * :audience_weight), 0) as final_score
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                LEFT JOIN showcase_scores js ON tp.id = js.participant_id
                WHERE tp.tournament_structure_id = ?
                GROUP BY tp.id
                ORDER BY final_score DESC, tp.team_name ASC";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([
            'judge_weight' => $this->config['judge_weight'],
            'audience_weight' => $this->config['audience_weight'],
            $this->tournamentId
        ]);
        return $stmt->fetchAll();
    }
}

/**
 * Performance Competition Algorithm
 * For structured performance competitions with elimination rounds
 */
class PerformanceCompetitionAlgorithm extends BaseTournamentAlgorithm {

    protected function getDefaultConfig() {
        return [
            'performance_time_limits' => true,
            'technical_scoring' => true,
            'artistic_scoring' => true,
            'elimination_rounds' => true,
            'advancement_rate' => 0.5,
            'scoring_criteria' => ['technical', 'artistic', 'presentation', 'overall']
        ];
    }

    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);

        if ($this->config['elimination_rounds']) {
            return $this->generateEliminationBracket($participants);
        } else {
            return $this->generateSingleRoundBracket($participants);
        }
    }

    private function generateEliminationBracket($participants) {
        $totalRounds = ceil(log($participants, 2));
        $bracket = [];

        for ($round = 1; $round <= $totalRounds; $round++) {
            $roundParticipants = $this->calculateRoundParticipants($participants, $round);
            $bracket["Round $round"] = [
                'round_number' => $round,
                'participants' => $roundParticipants,
                'performances' => $this->generatePerformanceMatches($roundParticipants, $round),
                'advancement_count' => $this->calculateAdvancement($roundParticipants)
            ];
        }

        return [
            'success' => true,
            'bracket' => $bracket,
            'format' => 'Performance Competition',
            'total_rounds' => $totalRounds,
            'scoring_type' => 'performance'
        ];
    }

    private function generateSingleRoundBracket($participants) {
        return [
            'success' => true,
            'bracket' => [
                'Performance Round' => [
                    'round_number' => 1,
                    'participants' => $participants,
                    'performances' => $this->generatePerformanceMatches($participants, 1),
                    'advancement_count' => 0
                ]
            ],
            'format' => 'Performance Competition',
            'total_rounds' => 1,
            'scoring_type' => 'performance'
        ];
    }

    private function generatePerformanceMatches($participants, $round) {
        $matches = [];
        foreach ($participants as $index => $participant) {
            $matches[] = [
                'performance_number' => $index + 1,
                'participant_id' => $participant['id'],
                'participant_name' => $participant['name'],
                'round' => $round,
                'criteria_scores' => array_fill_keys($this->config['scoring_criteria'], 0),
                'total_score' => 0,
                'rank' => null,
                'advanced' => false,
                'status' => 'pending'
            ];
        }
        return $matches;
    }

    private function calculateRoundParticipants($totalParticipants, $round) {
        return max(1, floor($totalParticipants / pow(2, $round - 1)));
    }

    private function calculateAdvancement($participants) {
        return max(1, floor(count($participants) * $this->config['advancement_rate']));
    }

    public function calculateRounds($participantCount) {
        return $this->config['elimination_rounds'] ? ceil(log($participantCount, 2)) : 1;
    }

    public function calculateMatches($participantCount) {
        return $this->config['elimination_rounds'] ? ($participantCount - 1) : $participantCount;
    }

    public function advanceParticipants($roundResults) {
        usort($roundResults, function($a, $b) {
            return $b['total_score'] <=> $a['total_score'];
        });

        $advancementCount = $this->calculateAdvancement($roundResults);
        return array_slice($roundResults, 0, $advancementCount);
    }

    public function generateNextRound($currentRound, $participants) {
        if (!$this->config['elimination_rounds'] || empty($participants)) {
            return null;
        }
        return $this->generatePerformanceMatches($participants, $currentRound + 1);
    }

    public function isComplete($tournament) {
        return $this->isFinalRoundComplete($tournament);
    }

    private function isFinalRoundComplete($tournament) {
        // Implementation to check if final round is complete
        return false; // Simplified for now
    }

    public function getRankings($tournament) {
        $sql = "SELECT tp.*, tp.team_name, tp.department_id, d.name as department_name,
                       COALESCE(MAX(ps.total_score), 0) as best_score,
                       COALESCE(AVG(ps.total_score), 0) as average_score
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                LEFT JOIN performance_scores ps ON tp.id = ps.participant_id
                WHERE tp.tournament_structure_id = ?
                GROUP BY tp.id
                ORDER BY best_score DESC, average_score DESC, tp.team_name ASC";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        return $stmt->fetchAll();
    }
}

/**
 * Artistic Judging Algorithm
 * For artistic performances judged on multiple criteria
 */
class ArtisticJudgingAlgorithm extends BaseTournamentAlgorithm {

    protected function getDefaultConfig() {
        return [
            'criteria' => ['technique', 'creativity', 'presentation', 'overall_impact'],
            'judge_panels' => true,
            'weighted_scoring' => true,
            'single_round' => true,
            'criteria_weights' => [
                'technique' => 0.3,
                'creativity' => 0.25,
                'presentation' => 0.25,
                'overall_impact' => 0.2
            ]
        ];
    }

    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);

        $bracket = [
            'Artistic Judging' => [
                'round_number' => 1,
                'participants' => $participants,
                'evaluations' => $this->generateArtisticEvaluations($participants),
                'criteria' => $this->config['criteria'],
                'weights' => $this->config['criteria_weights']
            ]
        ];

        return [
            'success' => true,
            'bracket' => $bracket,
            'format' => 'Artistic Judging',
            'total_rounds' => 1,
            'scoring_type' => 'artistic'
        ];
    }

    private function generateArtisticEvaluations($participants) {
        $evaluations = [];
        foreach ($participants as $index => $participant) {
            $evaluations[] = [
                'evaluation_number' => $index + 1,
                'participant_id' => $participant['id'],
                'participant_name' => $participant['name'],
                'criteria_scores' => array_fill_keys($this->config['criteria'], 0),
                'weighted_score' => 0,
                'final_score' => 0,
                'rank' => null,
                'status' => 'pending',
                'judge_comments' => []
            ];
        }
        return $evaluations;
    }

    public function calculateRounds($participantCount) {
        return 1; // Single round evaluation
    }

    public function calculateMatches($participantCount) {
        return $participantCount; // One evaluation per participant
    }

    public function advanceParticipants($roundResults) {
        // No advancement in single-round artistic judging
        return $roundResults;
    }

    public function generateNextRound($currentRound, $participants) {
        return null; // Single round only
    }

    public function isComplete($tournament) {
        return $this->isEvaluationComplete($tournament);
    }

    private function isEvaluationComplete($tournament) {
        // Implementation to check if all evaluations are complete
        return false; // Simplified for now
    }

    public function getRankings($tournament) {
        $sql = "SELECT tp.*, tp.team_name, tp.department_id, d.name as department_name,
                       COALESCE(aj.weighted_score, 0) as final_score,
                       COALESCE(aj.technique_score, 0) as technique,
                       COALESCE(aj.creativity_score, 0) as creativity,
                       COALESCE(aj.presentation_score, 0) as presentation,
                       COALESCE(aj.overall_impact_score, 0) as overall_impact
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                LEFT JOIN artistic_judging_scores aj ON tp.id = aj.participant_id
                WHERE tp.tournament_structure_id = ?
                ORDER BY final_score DESC, tp.team_name ASC";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        return $stmt->fetchAll();
    }
}

/**
 * Individual Performance Algorithm
 * For individual performance evaluation systems like solo singing contests
 */
class IndividualPerformanceAlgorithm extends BaseTournamentAlgorithm {

    protected function getDefaultConfig() {
        return [
            'individual_scoring' => true,
            'performance_order' => 'random',
            'score_aggregation' => 'average',
            'ranking_system' => true,
            'performance_categories' => ['vocal_quality', 'stage_presence', 'song_choice', 'overall_performance'],
            'time_limit' => 5 // minutes
        ];
    }

    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);

        // Randomize performance order if specified
        if ($this->config['performance_order'] === 'random') {
            shuffle($participants);
        }

        $bracket = [
            'Individual Performances' => [
                'round_number' => 1,
                'participants' => $participants,
                'performances' => $this->generateIndividualPerformances($participants),
                'categories' => $this->config['performance_categories'],
                'time_limit' => $this->config['time_limit']
            ]
        ];

        return [
            'success' => true,
            'bracket' => $bracket,
            'format' => 'Individual Performance',
            'total_rounds' => 1,
            'scoring_type' => 'individual'
        ];
    }

    private function generateIndividualPerformances($participants) {
        $performances = [];
        foreach ($participants as $index => $participant) {
            $performances[] = [
                'performance_order' => $index + 1,
                'participant_id' => $participant['id'],
                'participant_name' => $participant['name'],
                'category_scores' => array_fill_keys($this->config['performance_categories'], 0),
                'total_score' => 0,
                'average_score' => 0,
                'rank' => null,
                'status' => 'pending',
                'performance_notes' => ''
            ];
        }
        return $performances;
    }

    public function calculateRounds($participantCount) {
        return 1; // Single round for individual performances
    }

    public function calculateMatches($participantCount) {
        return $participantCount; // One performance per participant
    }

    public function advanceParticipants($roundResults) {
        // No advancement in individual performance evaluation
        return $roundResults;
    }

    public function generateNextRound($currentRound, $participants) {
        return null; // Single round only
    }

    public function isComplete($tournament) {
        return $this->areAllPerformancesComplete($tournament);
    }

    private function areAllPerformancesComplete($tournament) {
        // Implementation to check if all individual performances are complete
        return false; // Simplified for now
    }

    public function getRankings($tournament) {
        $sql = "SELECT tp.*, tp.team_name, tp.department_id, d.name as department_name,
                       COALESCE(ip.total_score, 0) as final_score,
                       COALESCE(ip.average_score, 0) as average_score,
                       ip.performance_order
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                LEFT JOIN individual_performance_scores ip ON tp.id = ip.participant_id
                WHERE tp.tournament_structure_id = ?
                ORDER BY final_score DESC, average_score DESC, performance_order ASC";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        return $stmt->fetchAll();
    }
}
