<?php
/**
 * Tournament Algorithm Classes
 * SC_IMS Sports Competition and Event Management System
 * 
 * Comprehensive tournament management algorithms for all tournament formats
 */

// Base Tournament Algorithm Interface
interface TournamentAlgorithmInterface {
    public function generateBracket($participants, $config = []);
    public function calculateRounds($participantCount);
    public function calculateMatches($participantCount);
    public function advanceParticipants($roundResults);
    public function generateNextRound($currentRound, $participants);
    public function isComplete($tournament);
    public function getRankings($tournament);
}

// Abstract Base Tournament Algorithm
abstract class BaseTournamentAlgorithm implements TournamentAlgorithmInterface {
    protected $conn;
    protected $tournamentId;
    protected $config;
    protected $format;
    protected $calculator;

    public function __construct($conn, $tournamentId, $config = []) {
        $this->conn = $conn;
        $this->tournamentId = $tournamentId;
        $this->config = array_merge($this->getDefaultConfig(), $config);
    }

    /**
     * Set formula calculator for database-driven calculations
     */
    public function setFormulaCalculator($calculator) {
        $this->calculator = $calculator;
    }

    /**
     * Set tournament format data
     */
    public function setFormat($format) {
        $this->format = $format;
    }

    /**
     * Configure algorithm with settings
     */
    public function configure($config) {
        $this->config = array_merge($this->config, $config);
    }
    
    abstract protected function getDefaultConfig();
    
    protected function seedParticipants($participants, $method = 'random') {
        switch ($method) {
            case 'ranking':
                return $this->seedByRanking($participants);
            case 'manual':
                return $this->seedManually($participants);
            case 'hybrid':
                return $this->seedHybrid($participants);
            default:
                return $this->seedRandomly($participants);
        }
    }
    
    protected function seedRandomly($participants) {
        // Fisher-Yates shuffle for true randomization
        $count = count($participants);
        for ($i = $count - 1; $i > 0; $i--) {
            $j = mt_rand(0, $i);
            $temp = $participants[$i];
            $participants[$i] = $participants[$j];
            $participants[$j] = $temp;
        }

        // Assign seed numbers
        foreach ($participants as $index => $participant) {
            $participants[$index]['seed'] = $index + 1;
        }
        return $participants;
    }
    
    protected function seedByRanking($participants) {
        // Sort by multiple criteria: points, wins, goal difference, etc.
        usort($participants, function($a, $b) {
            // Primary: Points
            $pointsA = $a['points'] ?? $a['total_points'] ?? 0;
            $pointsB = $b['points'] ?? $b['total_points'] ?? 0;
            if ($pointsA !== $pointsB) {
                return $pointsB <=> $pointsA;
            }

            // Secondary: Wins
            $winsA = $a['wins'] ?? $a['total_wins'] ?? 0;
            $winsB = $b['wins'] ?? $b['total_wins'] ?? 0;
            if ($winsA !== $winsB) {
                return $winsB <=> $winsA;
            }

            // Tertiary: Goal difference or performance rating
            $gdA = $a['goal_difference'] ?? $a['performance_rating'] ?? 0;
            $gdB = $b['goal_difference'] ?? $b['performance_rating'] ?? 0;
            return $gdB <=> $gdA;
        });

        // Assign seed numbers based on ranking
        foreach ($participants as $index => $participant) {
            $participants[$index]['seed'] = $index + 1;
            $participants[$index]['ranking_position'] = $index + 1;
        }

        return $participants;
    }
    
    protected function seedManually($participants) {
        // Manual seeding should be handled by admin interface
        return $participants;
    }
    
    protected function seedHybrid($participants) {
        $totalCount = count($participants);

        // Determine how many top seeds to rank (25% or minimum 4, maximum 8)
        $topCount = max(4, min(8, ceil($totalCount * 0.25)));
        $topCount = min($topCount, $totalCount);

        // First, sort all participants by ranking to identify top performers
        $rankedParticipants = $this->seedByRanking($participants);

        // Split into top seeds and rest
        $topSeeds = array_slice($rankedParticipants, 0, $topCount);
        $restParticipants = array_slice($rankedParticipants, $topCount);

        // Keep top seeds in ranking order
        foreach ($topSeeds as $index => $participant) {
            $topSeeds[$index]['seed'] = $index + 1;
            $topSeeds[$index]['seeding_method'] = 'ranking';
        }

        // Randomize the rest
        $restParticipants = $this->seedRandomly($restParticipants);
        foreach ($restParticipants as $index => $participant) {
            $restParticipants[$index]['seed'] = $topCount + $index + 1;
            $restParticipants[$index]['seeding_method'] = 'random';
        }

        return array_merge($topSeeds, $restParticipants);
    }
    
    protected function handleByes($participants) {
        $count = count($participants);
        $nextPowerOf2 = pow(2, ceil(log($count, 2)));
        $byesNeeded = $nextPowerOf2 - $count;
        
        if ($byesNeeded > 0) {
            // Add bye participants (highest seeds get byes)
            for ($i = 0; $i < $byesNeeded; $i++) {
                $participants[] = [
                    'id' => 'bye_' . ($i + 1),
                    'name' => 'BYE',
                    'is_bye' => true,
                    'seed' => $count + $i + 1
                ];
            }
        }
        
        return $participants;
    }
    
    protected function createMatch($participant1, $participant2, $round, $position) {
        return [
            'tournament_structure_id' => $this->tournamentId,
            'tournament_round_id' => $round['id'],
            'team1_id' => $participant1['is_bye'] ?? false ? null : $participant1['id'],
            'team2_id' => $participant2['is_bye'] ?? false ? null : $participant2['id'],
            'round_number' => $round['round_number'],
            'bracket_position' => $position,
            'is_bye_match' => ($participant1['is_bye'] ?? false) || ($participant2['is_bye'] ?? false),
            'status' => 'scheduled'
        ];
    }
}

// Single Elimination Algorithm
class SingleEliminationAlgorithm extends BaseTournamentAlgorithm {
    protected function getDefaultConfig() {
        return [
            'bracket_seeding' => true,
            'third_place_match' => false,
            'consolation_rounds' => false
        ];
    }
    
    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);
        
        // Seed participants
        $seededParticipants = $this->seedParticipants($participants, $config['seeding_method'] ?? 'random');
        
        // Handle byes if needed
        if ($this->config['bracket_seeding']) {
            $seededParticipants = $this->handleByes($seededParticipants);
        }
        
        $totalRounds = $this->calculateRounds(count($participants));
        $bracket = [
            'format' => 'single_elimination',
            'participants' => $seededParticipants,
            'rounds' => $totalRounds,
            'structure' => $this->generateBracketStructure($seededParticipants, $totalRounds)
        ];
        
        return $bracket;
    }
    
    public function calculateRounds($participantCount) {
        // Use database-driven calculation if available
        if ($this->calculator && $this->format) {
            return $this->calculator->calculateRounds($this->format, $participantCount);
        }
        // Fallback to hardcoded formula
        return ceil(log($participantCount, 2));
    }

    public function calculateMatches($participantCount) {
        // Use database-driven calculation if available
        if ($this->calculator && $this->format) {
            return $this->calculator->calculateMatches($this->format, $participantCount);
        }
        // Fallback to hardcoded formula
        return $participantCount - 1;
    }
    
    private function generateBracketStructure($participants, $totalRounds) {
        $structure = [];
        $currentParticipants = $participants;
        
        for ($round = 1; $round <= $totalRounds; $round++) {
            $roundMatches = [];
            $nextRoundParticipants = [];
            
            // Pair participants for matches
            for ($i = 0; $i < count($currentParticipants); $i += 2) {
                $participant1 = $currentParticipants[$i];
                $participant2 = $currentParticipants[$i + 1] ?? null;
                
                if ($participant2 === null) {
                    // Odd number, automatic advancement
                    $nextRoundParticipants[] = $participant1;
                } else {
                    $matchPosition = 'R' . $round . 'M' . (count($roundMatches) + 1);
                    $roundMatches[] = [
                        'position' => $matchPosition,
                        'participant1' => $participant1,
                        'participant2' => $participant2,
                        'winner_advances_to' => $round < $totalRounds ? 'R' . ($round + 1) . 'M' . ceil((count($roundMatches) + 1) / 2) : 'champion'
                    ];
                    
                    // Placeholder for winner
                    $nextRoundParticipants[] = [
                        'id' => 'winner_' . $matchPosition,
                        'name' => 'Winner of ' . $matchPosition,
                        'is_placeholder' => true
                    ];
                }
            }
            
            $structure['round_' . $round] = [
                'round_number' => $round,
                'round_name' => $this->getRoundName($round, $totalRounds),
                'matches' => $roundMatches
            ];
            
            $currentParticipants = $nextRoundParticipants;
        }
        
        return $structure;
    }
    
    private function getRoundName($round, $totalRounds) {
        $remaining = $totalRounds - $round + 1;
        
        switch ($remaining) {
            case 1: return 'Final';
            case 2: return 'Semifinal';
            case 3: return 'Quarterfinal';
            case 4: return 'Round of 16';
            case 5: return 'Round of 32';
            default: return 'Round ' . $round;
        }
    }
    
    public function advanceParticipants($roundResults) {
        // Implementation for advancing winners to next round
        $winners = [];
        foreach ($roundResults as $matchResult) {
            if (isset($matchResult['winner_id'])) {
                $winners[] = $matchResult['winner_id'];
            }
        }
        return $winners;
    }
    
    public function generateNextRound($currentRound, $participants) {
        // Generate matches for the next round based on current round results
        $nextRoundNumber = $currentRound['round_number'] + 1;
        $matches = [];
        
        for ($i = 0; $i < count($participants); $i += 2) {
            if (isset($participants[$i + 1])) {
                $matches[] = $this->createMatch(
                    $participants[$i],
                    $participants[$i + 1],
                    ['id' => null, 'round_number' => $nextRoundNumber],
                    'R' . $nextRoundNumber . 'M' . (count($matches) + 1)
                );
            }
        }
        
        return $matches;
    }
    
    public function isComplete($tournament) {
        // Check if tournament is complete (final match played)
        $sql = "SELECT COUNT(*) as remaining FROM matches 
                WHERE tournament_structure_id = ? AND status != 'completed'";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        $result = $stmt->fetch();
        
        return $result['remaining'] == 0;
    }
    
    public function getRankings($tournament) {
        // Get final rankings for single elimination - simplified schema
        $sql = "SELECT tp.*, tp.team_name, tp.department_id, d.name as department_name
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                WHERE tp.tournament_structure_id = ?
                ORDER BY tp.current_status = 'active' DESC, tp.points DESC, tp.wins DESC";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        return $stmt->fetchAll();
    }
}

// Double Elimination Algorithm
class DoubleEliminationAlgorithm extends BaseTournamentAlgorithm {
    protected function getDefaultConfig() {
        return [
            'winners_bracket' => true,
            'losers_bracket' => true,
            'grand_final_advantage' => true,
            'bracket_seeding' => true
        ];
    }
    
    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);
        
        $seededParticipants = $this->seedParticipants($participants, $config['seeding_method'] ?? 'random');
        $seededParticipants = $this->handleByes($seededParticipants);
        
        $totalRounds = $this->calculateRounds(count($participants));
        
        $bracket = [
            'format' => 'double_elimination',
            'participants' => $seededParticipants,
            'rounds' => $totalRounds,
            'structure' => $this->generateDoubleBracketStructure($seededParticipants, $totalRounds)
        ];
        
        return $bracket;
    }
    
    public function calculateRounds($participantCount) {
        return ceil(log($participantCount, 2)) * 2 - 1;
    }
    
    public function calculateMatches($participantCount) {
        return 2 * $participantCount - 2;
    }
    
    private function generateDoubleBracketStructure($participants, $totalRounds) {
        // Implementation for double elimination bracket structure
        // This is more complex as it involves winners and losers brackets
        return [
            'winners_bracket' => $this->generateWinnersBracket($participants),
            'losers_bracket' => $this->generateLosersBracket($participants),
            'grand_final' => $this->generateGrandFinal()
        ];
    }
    
    private function generateWinnersBracket($participants) {
        // Similar to single elimination but losers go to losers bracket
        return [];
    }
    
    private function generateLosersBracket($participants) {
        // Complex bracket for eliminated participants
        return [];
    }
    
    private function generateGrandFinal() {
        // Final match between winners bracket champion and losers bracket champion
        return [];
    }
    
    public function advanceParticipants($roundResults) {
        // Handle advancement in both brackets
        return [];
    }
    
    public function generateNextRound($currentRound, $participants) {
        // Generate next round considering both brackets
        return [];
    }
    
    public function isComplete($tournament) {
        // Check if grand final is complete
        return false;
    }
    
    public function getRankings($tournament) {
        // Get rankings considering both brackets
        return [];
    }
}

// Round Robin Algorithm
class RoundRobinAlgorithm extends BaseTournamentAlgorithm {
    protected function getDefaultConfig() {
        return [
            'points_win' => 3,
            'points_draw' => 1,
            'points_loss' => 0,
            'head_to_head_tiebreaker' => true,
            'goal_difference_tiebreaker' => true
        ];
    }

    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);

        $seededParticipants = $this->seedParticipants($participants, $config['seeding_method'] ?? 'random');

        $bracket = [
            'format' => 'round_robin',
            'participants' => $seededParticipants,
            'rounds' => 1, // Single round robin
            'structure' => $this->generateRoundRobinStructure($seededParticipants)
        ];

        return $bracket;
    }

    public function calculateRounds($participantCount) {
        return 1; // Single round robin
    }

    public function calculateMatches($participantCount) {
        return $participantCount * ($participantCount - 1) / 2;
    }

    private function generateRoundRobinStructure($participants) {
        $matches = [];
        $participantCount = count($participants);

        // Generate all possible pairings
        for ($i = 0; $i < $participantCount; $i++) {
            for ($j = $i + 1; $j < $participantCount; $j++) {
                $matches[] = [
                    'position' => 'RR_' . ($i + 1) . '_' . ($j + 1),
                    'participant1' => $participants[$i],
                    'participant2' => $participants[$j],
                    'round_number' => 1
                ];
            }
        }

        return [
            'round_1' => [
                'round_number' => 1,
                'round_name' => 'Round Robin',
                'matches' => $matches
            ]
        ];
    }

    public function advanceParticipants($roundResults) {
        // In round robin, all participants continue until all matches are played
        return [];
    }

    public function generateNextRound($currentRound, $participants) {
        // Round robin typically has only one round
        return [];
    }

    public function isComplete($tournament) {
        // Check if all matches are completed
        $sql = "SELECT COUNT(*) as total,
                       SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
                FROM matches WHERE tournament_structure_id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        $result = $stmt->fetch();

        return $result['total'] > 0 && $result['total'] == $result['completed'];
    }

    public function getRankings($tournament) {
        // Calculate round robin standings - simplified schema
        $sql = "SELECT tp.*, tp.team_name, tp.department_id, d.name as department_name,
                       tp.points, tp.wins, tp.losses, tp.draws,
                       (tp.wins * {$this->config['points_win']} +
                        tp.draws * {$this->config['points_draw']} +
                        tp.losses * {$this->config['points_loss']}) as total_points
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                WHERE tp.tournament_structure_id = ?
                ORDER BY total_points DESC, tp.wins DESC, tp.points DESC";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        return $stmt->fetchAll();
    }
}

// Swiss System Algorithm
class SwissSystemAlgorithm extends BaseTournamentAlgorithm {
    protected function getDefaultConfig() {
        return [
            'pairing_algorithm' => 'swiss',
            'avoid_rematches' => true,
            'color_balancing' => false, // For chess-like games
            'points_win' => 1,
            'points_draw' => 0.5,
            'points_loss' => 0
        ];
    }

    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);

        $seededParticipants = $this->seedParticipants($participants, $config['seeding_method'] ?? 'random');
        $totalRounds = $this->calculateRounds(count($participants));

        $bracket = [
            'format' => 'swiss_system',
            'participants' => $seededParticipants,
            'rounds' => $totalRounds,
            'structure' => $this->generateSwissStructure($seededParticipants, $totalRounds)
        ];

        return $bracket;
    }

    public function calculateRounds($participantCount) {
        return ceil(log($participantCount, 2));
    }

    public function calculateMatches($participantCount) {
        $rounds = $this->calculateRounds($participantCount);
        return $rounds * floor($participantCount / 2);
    }

    private function generateSwissStructure($participants, $totalRounds) {
        $structure = [];

        // First round: pair by initial seeding
        $firstRoundMatches = $this->generateFirstRoundPairings($participants);

        $structure['round_1'] = [
            'round_number' => 1,
            'round_name' => 'Round 1',
            'matches' => $firstRoundMatches
        ];

        // Subsequent rounds will be generated dynamically based on standings
        for ($round = 2; $round <= $totalRounds; $round++) {
            $structure['round_' . $round] = [
                'round_number' => $round,
                'round_name' => 'Round ' . $round,
                'matches' => [] // To be generated after previous round
            ];
        }

        return $structure;
    }

    private function generateFirstRoundPairings($participants) {
        $matches = [];
        $count = count($participants);
        $half = floor($count / 2);

        // Swiss system first round: top half vs bottom half
        for ($i = 0; $i < $half; $i++) {
            $matches[] = [
                'position' => 'S1M' . ($i + 1),
                'participant1' => $participants[$i],
                'participant2' => $participants[$i + $half],
                'round_number' => 1
            ];
        }

        // Handle odd number of participants
        if ($count % 2 == 1) {
            $matches[] = [
                'position' => 'S1M' . ($half + 1),
                'participant1' => $participants[$count - 1],
                'participant2' => ['id' => 'bye', 'name' => 'BYE', 'is_bye' => true],
                'round_number' => 1
            ];
        }

        return $matches;
    }

    public function generateSwissPairings($round, $standings) {
        // Swiss pairing algorithm based on current standings
        $matches = [];
        $availableParticipants = array_filter($standings, function($p) {
            return $p['current_status'] == 'active';
        });

        // Sort by points, then by tiebreakers
        usort($availableParticipants, function($a, $b) {
            if ($a['points'] == $b['points']) {
                return $b['wins'] <=> $a['wins'];
            }
            return $b['points'] <=> $a['points'];
        });

        $paired = [];

        foreach ($availableParticipants as $participant) {
            if (in_array($participant['id'], $paired)) continue;

            // Find best opponent
            $opponent = $this->findBestOpponent($participant, $availableParticipants, $paired);

            if ($opponent) {
                $matches[] = [
                    'position' => 'S' . $round . 'M' . (count($matches) + 1),
                    'participant1' => $participant,
                    'participant2' => $opponent,
                    'round_number' => $round
                ];

                $paired[] = $participant['id'];
                $paired[] = $opponent['id'];
            }
        }

        return $matches;
    }

    private function findBestOpponent($participant, $availableParticipants, $paired) {
        foreach ($availableParticipants as $potential) {
            if ($potential['id'] == $participant['id']) continue;
            if (in_array($potential['id'], $paired)) continue;

            // Check if they haven't played before (if avoid_rematches is enabled)
            if ($this->config['avoid_rematches'] && $this->havePlayedBefore($participant, $potential)) {
                continue;
            }

            // Prefer opponents with similar points
            return $potential;
        }

        return null;
    }

    private function havePlayedBefore($participant1, $participant2) {
        $sql = "SELECT COUNT(*) as count FROM matches
                WHERE tournament_structure_id = ?
                AND ((team1_id = ? AND team2_id = ?) OR (team1_id = ? AND team2_id = ?))";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([
            $this->tournamentId,
            $participant1['id'], $participant2['id'],
            $participant2['id'], $participant1['id']
        ]);
        $result = $stmt->fetch();

        return $result['count'] > 0;
    }

    public function advanceParticipants($roundResults) {
        // Update participant standings after each round
        foreach ($roundResults as $matchResult) {
            $this->updateParticipantStandings($matchResult);
        }
        return [];
    }

    private function updateParticipantStandings($matchResult) {
        // Update wins, losses, draws, and points for participants - simplified schema
        if (isset($matchResult['winner_id'])) {
            // Update winner
            $sql = "UPDATE tournament_participants
                    SET wins = wins + 1, points = points + ?
                    WHERE tournament_structure_id = ? AND id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$this->config['points_win'], $this->tournamentId, $matchResult['winner_id']]);

            // Update loser
            $loserId = $matchResult['team1_id'] == $matchResult['winner_id'] ? $matchResult['team2_id'] : $matchResult['team1_id'];
            $sql = "UPDATE tournament_participants
                    SET losses = losses + 1, points = points + ?
                    WHERE tournament_structure_id = ? AND id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$this->config['points_loss'], $this->tournamentId, $loserId]);
        } else {
            // Draw
            $sql = "UPDATE tournament_participants
                    SET draws = draws + 1, points = points + ?
                    WHERE tournament_structure_id = ? AND id IN (?, ?)";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([
                $this->config['points_draw'],
                $this->tournamentId,
                $matchResult['team1_id'],
                $matchResult['team2_id']
            ]);
        }
    }

    public function generateNextRound($currentRound, $participants) {
        $nextRound = $currentRound['round_number'] + 1;
        return $this->generateSwissPairings($nextRound, $participants);
    }

    public function isComplete($tournament) {
        // Swiss system is complete when all planned rounds are finished
        $sql = "SELECT current_round, total_rounds FROM tournament_structures WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        $result = $stmt->fetch();

        return $result && $result['current_round'] >= $result['total_rounds'];
    }

    public function getRankings($tournament) {
        // Get Swiss system standings - simplified schema
        $sql = "SELECT tp.*, tp.team_name, tp.department_id, d.name as department_name
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                WHERE tp.tournament_structure_id = ?
                ORDER BY tp.points DESC, tp.wins DESC, tp.draws DESC";

        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        return $stmt->fetchAll();
    }
}

// Multi-Stage Tournament Algorithm (Group Stage + Knockout)
class MultiStageAlgorithm extends BaseTournamentAlgorithm {
    protected function getDefaultConfig() {
        return [
            'groups_count' => 4,
            'teams_per_group' => 4,
            'teams_advance_per_group' => 2,
            'group_stage_format' => 'round_robin',
            'knockout_stage_format' => 'single_elimination',
            'group_points_win' => 3,
            'group_points_draw' => 1,
            'group_points_loss' => 0,
            'third_place_match' => true
        ];
    }

    public function generateBracket($participants, $config = []) {
        $this->config = array_merge($this->config, $config);

        $participantCount = count($participants);
        $groupsCount = $this->config['groups_count'];
        $teamsPerGroup = ceil($participantCount / $groupsCount);

        // Seed participants
        $seededParticipants = $this->seedParticipants($participants, $config['seeding_method'] ?? 'random');

        // Create groups
        $groups = $this->createGroups($seededParticipants, $groupsCount, $teamsPerGroup);

        // Calculate total rounds (group stage + knockout stage)
        $groupRounds = $teamsPerGroup - 1; // Round robin within groups
        $knockoutRounds = ceil(log($this->config['teams_advance_per_group'] * $groupsCount, 2));
        $totalRounds = $groupRounds + $knockoutRounds;

        $bracket = [
            'format' => 'multi_stage',
            'participants' => $seededParticipants,
            'rounds' => $totalRounds,
            'structure' => [
                'group_stage' => [
                    'groups' => $groups,
                    'rounds' => $groupRounds,
                    'format' => $this->config['group_stage_format']
                ],
                'knockout_stage' => [
                    'rounds' => $knockoutRounds,
                    'format' => $this->config['knockout_stage_format'],
                    'teams_advance' => $this->config['teams_advance_per_group'] * $groupsCount
                ]
            ]
        ];

        return $bracket;
    }

    public function calculateRounds($participantCount) {
        $groupsCount = $this->config['groups_count'];
        $teamsPerGroup = ceil($participantCount / $groupsCount);
        $groupRounds = $teamsPerGroup - 1;
        $knockoutRounds = ceil(log($this->config['teams_advance_per_group'] * $groupsCount, 2));
        return $groupRounds + $knockoutRounds;
    }

    public function calculateMatches($participantCount) {
        $groupsCount = $this->config['groups_count'];
        $teamsPerGroup = ceil($participantCount / $groupsCount);

        // Group stage matches (round robin within each group)
        $groupMatches = $groupsCount * ($teamsPerGroup * ($teamsPerGroup - 1) / 2);

        // Knockout stage matches
        $advancingTeams = $this->config['teams_advance_per_group'] * $groupsCount;
        $knockoutMatches = $advancingTeams - 1;
        if ($this->config['third_place_match']) {
            $knockoutMatches += 1;
        }

        return $groupMatches + $knockoutMatches;
    }

    private function createGroups($participants, $groupsCount, $teamsPerGroup) {
        $groups = [];
        $participantIndex = 0;

        for ($g = 0; $g < $groupsCount; $g++) {
            $groups["Group " . chr(65 + $g)] = [];

            for ($t = 0; $t < $teamsPerGroup && $participantIndex < count($participants); $t++) {
                $groups["Group " . chr(65 + $g)][] = $participants[$participantIndex];
                $participantIndex++;
            }
        }

        return $groups;
    }

    public function advanceParticipants($roundResults) {
        // Implementation for advancing participants from group stage to knockout
        return [];
    }

    public function generateNextRound($currentRound, $participants) {
        // Implementation for generating next round matches
        return [];
    }

    public function isComplete($tournament) {
        // Check if both group stage and knockout stage are complete
        return false;
    }

    public function getRankings($tournament) {
        // Get rankings considering both group stage and knockout stage performance - simplified schema
        $sql = "SELECT tp.*, tp.team_name, tp.department_id, d.name as department_name
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                WHERE tp.tournament_structure_id = ?
                ORDER BY tp.points DESC, tp.wins DESC";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$this->tournamentId]);
        return $stmt->fetchAll();
    }
}
