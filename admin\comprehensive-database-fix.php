<?php
/**
 * Comprehensive Database Fix for Category Navigation
 * Ensures all necessary data exists for testing navigation flow
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Comprehensive Database Fix</h1>";

try {
    $conn->beginTransaction();
    
    echo "<h2>1. Creating Events</h2>";
    
    // Check if events exist
    $event_count = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
    if ($event_count == 0) {
        $conn->exec("
            INSERT INTO events (name, description, start_date, end_date, venue, status) 
            VALUES 
            ('Test Championship 2024', 'Test event for navigation testing', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 14 DAY), 'Main Sports Complex', 'active'),
            ('Spring Tournament', 'Spring sports tournament', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 10 DAY), 'Athletic Center', 'active'),
            ('Inter-Department Games', 'Annual inter-department competition', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY), 'University Gym', 'active')
        ");
        echo "<p style='color: green;'>✅ Created 3 test events</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Events already exist ($event_count events)</p>";
    }
    
    echo "<h2>2. Creating Sports</h2>";
    
    // Check if sports exist
    $sport_count = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
    if ($sport_count == 0) {
        $conn->exec("
            INSERT INTO sports (name, type, scoring_method, bracket_format, description) 
            VALUES 
            ('Basketball', 'traditional', 'point_based', 'single_elimination', '5v5 basketball competition'),
            ('Volleyball', 'traditional', 'set_based', 'round_robin', '6v6 volleyball tournament'),
            ('Badminton', 'traditional', 'point_based', 'single_elimination', 'Singles and doubles badminton'),
            ('Football', 'traditional', 'point_based', 'round_robin', '11v11 football matches'),
            ('Table Tennis', 'traditional', 'point_based', 'single_elimination', 'Singles table tennis'),
            ('Chess', 'academic', 'point_based', 'swiss_system', 'Individual chess competition'),
            ('Quiz Bowl', 'academic', 'point_based', 'knockout', 'Team-based quiz competition'),
            ('Dance Competition', 'judged', 'judged_scoring', 'judged_rounds', 'Performance-based dance contest')
        ");
        echo "<p style='color: green;'>✅ Created 8 test sports</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Sports already exist ($sport_count sports)</p>";
    }
    
    echo "<h2>3. Creating Event-Sports Relationships</h2>";
    
    // Check if event_sports relationships exist
    $es_count = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
    if ($es_count == 0) {
        // Get all events and sports
        $events = $conn->query("SELECT id FROM events")->fetchAll();
        $sports = $conn->query("SELECT id FROM sports")->fetchAll();
        
        $stmt = $conn->prepare("INSERT INTO event_sports (event_id, sport_id, max_teams, status) VALUES (?, ?, ?, ?)");
        
        $created_count = 0;
        foreach ($events as $event) {
            foreach ($sports as $sport) {
                $stmt->execute([$event['id'], $sport['id'], 8, 'registration']);
                $created_count++;
            }
        }
        echo "<p style='color: green;'>✅ Created $created_count event-sports relationships</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Event-sports relationships already exist ($es_count relationships)</p>";
    }
    
    echo "<h2>4. Creating Sport Categories</h2>";
    
    // Check if sport categories exist
    $cat_count = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
    if ($cat_count == 0) {
        // Get all event_sports relationships
        $event_sports = $conn->query("SELECT id FROM event_sports")->fetchAll();
        
        $categories = [
            ['Men\'s Division', 'men', 'John Referee', '<EMAIL>', 'Court A'],
            ['Women\'s Division', 'women', 'Jane Referee', '<EMAIL>', 'Court B'],
            ['Mixed Division', 'mixed', 'Mike Referee', '<EMAIL>', 'Court C'],
            ['Open Category', 'open', 'Sarah Referee', '<EMAIL>', 'Main Hall'],
            ['Youth Division', 'youth', 'Tom Referee', '<EMAIL>', 'Youth Center']
        ];
        
        $stmt = $conn->prepare("
            INSERT INTO sport_categories (event_sport_id, category_name, category_type, referee_name, referee_email, venue)
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $created_count = 0;
        foreach ($event_sports as $es) {
            foreach ($categories as $cat) {
                $stmt->execute([
                    $es['id'],
                    $cat[0],
                    $cat[1],
                    $cat[2],
                    $cat[3],
                    $cat[4]
                ]);
                $created_count++;
            }
        }
        echo "<p style='color: green;'>✅ Created $created_count sport categories</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Sport categories already exist ($cat_count categories)</p>";
    }
    
    echo "<h2>5. Creating Departments</h2>";
    
    // Check if departments exist
    $dept_count = $conn->query("SELECT COUNT(*) FROM departments")->fetchColumn();
    if ($dept_count == 0) {
        $conn->exec("
            INSERT INTO departments (name, contact_person, contact_email, contact_phone) 
            VALUES 
            ('Computer Science', 'Dr. Smith', '<EMAIL>', '************'),
            ('Engineering', 'Dr. Johnson', '<EMAIL>', '************'),
            ('Business Administration', 'Dr. Brown', '<EMAIL>', '************'),
            ('Liberal Arts', 'Dr. Davis', '<EMAIL>', '************'),
            ('Sciences', 'Dr. Wilson', '<EMAIL>', '************'),
            ('Medicine', 'Dr. Taylor', '<EMAIL>', '************'),
            ('Law', 'Dr. Anderson', '<EMAIL>', '************'),
            ('Education', 'Dr. Thomas', '<EMAIL>', '************')
        ");
        echo "<p style='color: green;'>✅ Created 8 test departments</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Departments already exist ($dept_count departments)</p>";
    }
    
    echo "<h2>6. Creating Department Registrations</h2>";
    
    // Check if department registrations exist
    $reg_count = $conn->query("SELECT COUNT(*) FROM department_registrations")->fetchColumn();
    if ($reg_count == 0) {
        // Get all events and departments
        $events = $conn->query("SELECT id FROM events")->fetchAll();
        $departments = $conn->query("SELECT id FROM departments")->fetchAll();
        
        $stmt = $conn->prepare("
            INSERT INTO department_registrations (event_id, department_id, registration_date, status)
            VALUES (?, ?, CURDATE(), 'confirmed')
        ");
        
        $created_count = 0;
        foreach ($events as $event) {
            foreach ($departments as $dept) {
                $stmt->execute([$event['id'], $dept['id']]);
                $created_count++;
            }
        }
        echo "<p style='color: green;'>✅ Created $created_count department registrations</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ Department registrations already exist ($reg_count registrations)</p>";
    }
    
    $conn->commit();
    
    echo "<h2>✅ Database Fix Complete</h2>";
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
    echo "<h3>Success!</h3>";
    echo "<p>All necessary test data has been created or verified. The navigation flow should now work properly.</p>";
    echo "</div>";
    
    // Show navigation test links
    echo "<h2>🧪 Test Navigation Now</h2>";
    
    $stmt = $conn->prepare("
        SELECT 
            sc.id as category_id,
            sc.category_name,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        ORDER BY e.name, s.name, sc.category_name
        LIMIT 5
    ");
    $stmt->execute();
    $test_paths = $stmt->fetchAll();
    
    if (!empty($test_paths)) {
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 15px;'>";
        foreach ($test_paths as $path) {
            $categories_url = "sport-categories.php?event_id={$path['event_id']}&sport_id={$path['sport_id']}";
            $manage_url = "manage-category.php?event_id={$path['event_id']}&sport_id={$path['sport_id']}&category_id={$path['category_id']}";
            
            echo "<div style='border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; background: white;'>";
            echo "<h4 style='margin-top: 0; color: #007bff;'>{$path['event_name']} → {$path['sport_name']}</h4>";
            echo "<p><strong>Category:</strong> {$path['category_name']}</p>";
            
            echo "<div style='display: flex; flex-direction: column; gap: 8px;'>";
            echo "<a href='$categories_url' target='_blank' style='background: #007bff; color: white; padding: 8px 12px; text-decoration: none; border-radius: 4px; text-align: center;'>";
            echo "📋 Categories List";
            echo "</a>";
            echo "<a href='$manage_url' target='_blank' style='background: #28a745; color: white; padding: 8px 12px; text-decoration: none; border-radius: 4px; text-align: center;'>";
            echo "⚙️ Manage Category";
            echo "</a>";
            echo "</div>";
            echo "</div>";
        }
        echo "</div>";
    }
    
} catch (Exception $e) {
    $conn->rollback();
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
