<?php
/**
 * Simplify Sport Categories Schema
 * Remove unnecessary columns from sport_categories table
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Simplifying Sport Categories Schema</h1>";

try {
    // Step 1: Check current table structure
    echo "<h2>Step 1: Current Table Structure</h2>";
    
    $stmt = $conn->prepare("DESCRIBE sport_categories");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($columns as $col) {
        $highlight = '';
        if (in_array($col['Field'], ['max_participants', 'registration_deadline', 'status'])) {
            $highlight = 'style="background-color: #ffe6e6;"'; // Red highlight for columns to be removed
        }
        echo "<tr {$highlight}>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "<td>{$col['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 2: Check if columns exist before removing
    echo "<h2>Step 2: Checking Columns to Remove</h2>";
    
    $columns_to_remove = ['max_participants', 'registration_deadline', 'status'];
    $existing_columns = array_column($columns, 'Field');
    
    foreach ($columns_to_remove as $column) {
        if (in_array($column, $existing_columns)) {
            echo "<p style='color: orange;'>⚠ Column '{$column}' exists and will be removed</p>";
        } else {
            echo "<p style='color: green;'>✓ Column '{$column}' does not exist (already removed)</p>";
        }
    }
    
    // Step 3: Remove unnecessary columns
    echo "<h2>Step 3: Removing Unnecessary Columns</h2>";
    
    foreach ($columns_to_remove as $column) {
        if (in_array($column, $existing_columns)) {
            try {
                echo "<p>Removing column '{$column}'...</p>";
                $conn->exec("ALTER TABLE sport_categories DROP COLUMN {$column}");
                echo "<p style='color: green;'>✓ Successfully removed column '{$column}'</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Error removing column '{$column}': " . $e->getMessage() . "</p>";
            }
        }
    }
    
    // Step 4: Verify the updated structure
    echo "<h2>Step 4: Updated Table Structure</h2>";
    
    $stmt = $conn->prepare("DESCRIBE sport_categories");
    $stmt->execute();
    $updated_columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    foreach ($updated_columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "<td>{$col['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 5: Summary
    echo "<h2>Step 5: Summary</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border: 1px solid #c3e6cb; border-radius: 5px;'>";
    echo "<h3>✅ Schema Simplification Complete</h3>";
    echo "<p><strong>Removed Columns:</strong></p>";
    echo "<ul>";
    
    $removed_count = 0;
    foreach ($columns_to_remove as $column) {
        if (in_array($column, $existing_columns)) {
            echo "<li><strong>{$column}</strong> - No longer needed for category management</li>";
            $removed_count++;
        }
    }
    
    echo "</ul>";
    echo "<p><strong>Remaining Essential Fields:</strong></p>";
    echo "<ul>";
    echo "<li><strong>category_name</strong> - Name of the competition category</li>";
    echo "<li><strong>category_type</strong> - Type (Men's, Women's, Mixed, etc.)</li>";
    echo "<li><strong>category_type_custom</strong> - Custom type description</li>";
    echo "<li><strong>referee_name</strong> - Assigned referee/umpire</li>";
    echo "<li><strong>referee_email</strong> - Referee contact email</li>";
    echo "<li><strong>venue</strong> - Competition venue/location</li>";
    echo "</ul>";
    
    echo "<p><strong>Purpose:</strong> This page now focuses exclusively on defining competition categories where departments compete to earn points, without unnecessary administrative fields.</p>";
    echo "</div>";
    
    echo "<p><a href='modify-sport-categories-form.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>➡️ Next: Update Form Interface</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Critical Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
