<?php
require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Check if user is admin
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['name']) || !isset($input['sport_type_id'])) {
        throw new Exception('Missing required parameters');
    }
    
    $name = trim($input['name']);
    $sport_type_id = (int)$input['sport_type_id'];
    $category = $input['category'] ?? '';
    
    if (empty($name)) {
        throw new Exception('Sport name cannot be empty');
    }
    
    // Check if sport already exists
    $stmt = $conn->prepare("SELECT id FROM sports WHERE name = ?");
    $stmt->execute([$name]);
    if ($stmt->fetch()) {
        throw new Exception('Sport with this name already exists');
    }
    
    // Create the sport
    $stmt = $conn->prepare("
        INSERT INTO sports (name, sport_type_id, description, status, created_at)
        VALUES (?, ?, ?, 'active', NOW())
    ");
    
    $description = "Test sport for $category category tournaments";
    $stmt->execute([$name, $sport_type_id, $description]);
    
    $sport_id = $conn->lastInsertId();
    
    // Log admin activity (if function exists)
    if (function_exists('logAdminActivity')) {
        logAdminActivity($_SESSION['admin_id'], 'create', 'sports', $sport_id, [
            'name' => $name,
            'sport_type_id' => $sport_type_id,
            'category' => $category
        ]);
    }
    
    echo json_encode([
        'success' => true,
        'sport_id' => $sport_id,
        'message' => "Test sport '$name' created successfully"
    ]);
    
} catch (Exception $e) {
    error_log("Error in create-test-sport.php: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
