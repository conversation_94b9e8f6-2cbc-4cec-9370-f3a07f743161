<?php
/**
 * Test Simplified Sport Categories Functionality
 * Verify that the streamlined category management works correctly
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Simplified Sport Categories</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .test-section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #17a2b8; }
        .removed-field { background: #ffe6e6; padding: 5px; margin: 2px 0; border-radius: 3px; }
        .kept-field { background: #e6ffe6; padding: 5px; margin: 2px 0; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 Test Simplified Sport Categories</h1>
        <p>Testing the streamlined category management functionality</p>
        
        <div class="test-section">
            <h2>1. Database Schema Verification</h2>
            <div id="schema-results">
                <p>Checking sport_categories table structure...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>2. Form Field Analysis</h2>
            <div class="row">
                <div class="col-md-6">
                    <h4>✅ Fields Kept (Essential for Category Definition)</h4>
                    <div class="kept-field"><strong>category_name</strong> - Competition category name</div>
                    <div class="kept-field"><strong>category_type</strong> - Men's, Women's, Mixed, etc.</div>
                    <div class="kept-field"><strong>category_type_custom</strong> - Custom type description</div>
                    <div class="kept-field"><strong>referee_name</strong> - Assigned referee/umpire</div>
                    <div class="kept-field"><strong>referee_email</strong> - Referee contact</div>
                    <div class="kept-field"><strong>venue</strong> - Competition location</div>
                </div>
                <div class="col-md-6">
                    <h4>❌ Fields Removed (Administrative Overhead)</h4>
                    <div class="removed-field"><strong>max_participants</strong> - Participant limits</div>
                    <div class="removed-field"><strong>registration_deadline</strong> - Registration deadlines</div>
                    <div class="removed-field"><strong>status</strong> - Administrative status</div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>3. Page Purpose Clarification</h2>
            <div class="alert alert-info">
                <h5>📋 Simplified Purpose</h5>
                <p><strong>This page now serves exclusively as a category management interface for sports.</strong></p>
                <ul>
                    <li>Categories represent different competition divisions (Men's, Women's, Mixed, etc.)</li>
                    <li>Departments compete in these categories to earn points for their department</li>
                    <li>Focus is on defining competition structure, not administrative management</li>
                    <li>Removed unnecessary fields that created administrative overhead</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h2>4. Functionality Test</h2>
            <p>Test the simplified category creation and management:</p>
            
            <div class="row">
                <div class="col-md-6">
                    <h5>Test Category Creation</h5>
                    <button onclick="testCategoryCreation()" class="btn btn-primary">Test Create Category</button>
                    <div id="create-test-results" class="mt-3"></div>
                </div>
                <div class="col-md-6">
                    <h5>Test Category Update</h5>
                    <button onclick="testCategoryUpdate()" class="btn btn-warning">Test Update Category</button>
                    <div id="update-test-results" class="mt-3"></div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>5. Live Page Test</h2>
            <p>Test the actual sport-categories.php page:</p>
            <div id="live-test-links"></div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Check database schema on page load
        document.addEventListener('DOMContentLoaded', function() {
            checkDatabaseSchema();
            generateLiveTestLinks();
        });
        
        function checkDatabaseSchema() {
            const resultsDiv = document.getElementById('schema-results');
            
            fetch('ajax/test-schema.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        let html = '<h5>Current sport_categories Table Structure:</h5>';
                        html += '<table class="table table-sm">';
                        html += '<tr><th>Field</th><th>Type</th><th>Status</th></tr>';
                        
                        const removedFields = ['max_participants', 'registration_deadline', 'status'];
                        const keptFields = ['category_name', 'category_type', 'category_type_custom', 'referee_name', 'referee_email', 'venue'];
                        
                        data.columns.forEach(column => {
                            let status = '';
                            let rowClass = '';
                            
                            if (removedFields.includes(column.Field)) {
                                status = '❌ Should be removed';
                                rowClass = 'table-danger';
                            } else if (keptFields.includes(column.Field)) {
                                status = '✅ Essential field';
                                rowClass = 'table-success';
                            } else {
                                status = '📋 System field';
                                rowClass = 'table-info';
                            }
                            
                            html += `<tr class="${rowClass}">`;
                            html += `<td><strong>${column.Field}</strong></td>`;
                            html += `<td>${column.Type}</td>`;
                            html += `<td>${status}</td>`;
                            html += '</tr>';
                        });
                        
                        html += '</table>';
                        resultsDiv.innerHTML = html;
                    } else {
                        resultsDiv.innerHTML = `<p class="error">❌ Error: ${data.message}</p>`;
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML = `<p class="error">❌ Network Error: ${error.message}</p>`;
                });
        }
        
        function testCategoryCreation() {
            const resultsDiv = document.getElementById('create-test-results');
            resultsDiv.innerHTML = '<p>Testing category creation...</p>';
            
            // Test data with only essential fields
            const testData = new FormData();
            testData.append('entity', 'sport_category');
            testData.append('action', 'create');
            testData.append('event_sport_id', '1'); // Assuming event sport ID 1 exists
            testData.append('category_name', 'Test Men\'s Division');
            testData.append('category_type', 'men');
            testData.append('referee_name', 'Test Referee');
            testData.append('referee_email', '<EMAIL>');
            testData.append('venue', 'Test Venue');
            testData.append('csrf_token', 'test_token');
            
            fetch('ajax/modal-handler.php', {
                method: 'POST',
                body: testData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-success">
                            <h6>✅ Category Creation Test Passed</h6>
                            <p>${data.message}</p>
                            <small>Created with only essential fields - no administrative overhead</small>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h6>❌ Category Creation Test Failed</h6>
                            <p>${data.message}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                resultsDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <h6>❌ Network Error</h6>
                        <p>${error.message}</p>
                    </div>
                `;
            });
        }
        
        function testCategoryUpdate() {
            const resultsDiv = document.getElementById('update-test-results');
            resultsDiv.innerHTML = '<p>Testing category update...</p>';
            
            // First, try to get an existing category
            fetch('ajax/get-sport-category.php?id=1')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        resultsDiv.innerHTML = `
                            <div class="alert alert-success">
                                <h6>✅ Category Retrieval Test Passed</h6>
                                <p>Successfully retrieved category data</p>
                                <small>Fields returned: ${Object.keys(data.category).join(', ')}</small>
                            </div>
                        `;
                    } else {
                        resultsDiv.innerHTML = `
                            <div class="alert alert-warning">
                                <h6>⚠ No Test Category Found</h6>
                                <p>Create a category first to test updates</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-danger">
                            <h6>❌ Update Test Error</h6>
                            <p>${error.message}</p>
                        </div>
                    `;
                });
        }
        
        function generateLiveTestLinks() {
            const linksDiv = document.getElementById('live-test-links');
            
            // Get available events and sports for testing
            fetch('ajax/get-test-data.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.event_sports.length > 0) {
                        let html = '<h5>Available Test Links:</h5>';
                        html += '<div class="list-group">';
                        
                        data.event_sports.forEach(es => {
                            const url = `sport-categories.php?event_id=${es.event_id}&sport_id=${es.sport_id}`;
                            html += `
                                <a href="${url}" class="list-group-item list-group-item-action" target="_blank">
                                    <strong>${es.event_name}</strong> - ${es.sport_name}
                                    <small class="text-muted d-block">Test the simplified category management</small>
                                </a>
                            `;
                        });
                        
                        html += '</div>';
                        linksDiv.innerHTML = html;
                    } else {
                        linksDiv.innerHTML = '<p class="info">No event-sport combinations available for testing</p>';
                    }
                })
                .catch(error => {
                    linksDiv.innerHTML = `<p class="error">Error loading test links: ${error.message}</p>`;
                });
        }
    </script>
</body>
</html>
