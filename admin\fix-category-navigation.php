<?php
/**
 * Fix Category Navigation Issues
 * Create test categories and fix database relationships
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$action = $_POST['action'] ?? '';
$message = '';
$message_type = '';

if ($action === 'create_test_categories') {
    try {
        // Get available event-sport combinations
        $stmt = $conn->prepare("
            SELECT es.id as event_sport_id, es.event_id, es.sport_id, 
                   e.name as event_name, s.name as sport_name
            FROM event_sports es
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            ORDER BY es.id
            LIMIT 5
        ");
        $stmt->execute();
        $event_sports = $stmt->fetchAll();
        
        if ($event_sports) {
            $categories_created = 0;
            
            foreach ($event_sports as $es) {
                // Create Men's and Women's categories for each sport
                $categories = [
                    ['name' => "Men's " . $es['sport_name'], 'type' => 'men'],
                    ['name' => "Women's " . $es['sport_name'], 'type' => 'women']
                ];
                
                foreach ($categories as $cat) {
                    // Check if category already exists
                    $check_stmt = $conn->prepare("
                        SELECT id FROM sport_categories 
                        WHERE event_sport_id = ? AND category_name = ?
                    ");
                    $check_stmt->execute([$es['event_sport_id'], $cat['name']]);
                    
                    if (!$check_stmt->fetch()) {
                        // Create the category
                        $insert_stmt = $conn->prepare("
                            INSERT INTO sport_categories 
                            (event_sport_id, category_name, category_type, referee_name, venue)
                            VALUES (?, ?, ?, ?, ?)
                        ");
                        
                        $insert_stmt->execute([
                            $es['event_sport_id'],
                            $cat['name'],
                            $cat['type'],
                            'Test Referee',
                            'Main Venue'
                        ]);
                        
                        $categories_created++;
                    }
                }
            }
            
            $message = "Successfully created $categories_created test categories!";
            $message_type = 'success';
        } else {
            $message = "No event-sport combinations found. Please create events and sports first.";
            $message_type = 'warning';
        }
    } catch (Exception $e) {
        $message = "Error creating categories: " . $e->getMessage();
        $message_type = 'danger';
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fix Category Navigation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🔧 Fix Category Navigation</h1>
        <p>Diagnose and fix category navigation issues</p>
        
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $message_type; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>1. Current Database Status</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    // Check current status
                    $events_count = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
                    $sports_count = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
                    $event_sports_count = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
                    $categories_count = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
                    
                    echo "<div class='row'>";
                    echo "<div class='col-md-3'><div class='card text-center'><div class='card-body'><h5>$events_count</h5><p>Events</p></div></div></div>";
                    echo "<div class='col-md-3'><div class='card text-center'><div class='card-body'><h5>$sports_count</h5><p>Sports</p></div></div></div>";
                    echo "<div class='col-md-3'><div class='card text-center'><div class='card-body'><h5>$event_sports_count</h5><p>Event-Sports</p></div></div></div>";
                    echo "<div class='col-md-3'><div class='card text-center'><div class='card-body'><h5>$categories_count</h5><p>Categories</p></div></div></div>";
                    echo "</div>";
                    
                    if ($categories_count == 0) {
                        echo "<div class='alert alert-warning mt-3'>";
                        echo "<strong>⚠️ Problem Identified:</strong> No sport categories exist! This is why navigation fails.";
                        echo "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database Error: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>2. Fix Actions</h3>
            </div>
            <div class="card-body">
                <?php if ($categories_count == 0): ?>
                    <div class="alert alert-info">
                        <h5>🔧 Recommended Fix:</h5>
                        <p>Create test categories to enable navigation testing. This will create Men's and Women's categories for available sports.</p>
                    </div>
                    
                    <form method="POST">
                        <input type="hidden" name="action" value="create_test_categories">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Create Test Categories
                        </button>
                    </form>
                <?php else: ?>
                    <div class="alert alert-success">
                        <h5>✅ Categories Exist</h5>
                        <p>Sport categories are available. Navigation should work now.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>3. Test Navigation</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    $stmt = $conn->prepare("
                        SELECT sc.id as category_id, sc.category_name,
                               es.event_id, es.sport_id,
                               e.name as event_name, s.name as sport_name
                        FROM sport_categories sc
                        JOIN event_sports es ON sc.event_sport_id = es.id
                        JOIN events e ON es.event_id = e.id
                        JOIN sports s ON es.sport_id = s.id
                        ORDER BY e.name, s.name, sc.category_name
                        LIMIT 10
                    ");
                    $stmt->execute();
                    $test_links = $stmt->fetchAll();
                    
                    if ($test_links) {
                        echo "<h5>Available Test Links:</h5>";
                        echo "<div class='row'>";
                        
                        foreach ($test_links as $link) {
                            $categories_url = "sport-categories.php?event_id={$link['event_id']}&sport_id={$link['sport_id']}";
                            $manage_url = "manage-category.php?event_id={$link['event_id']}&sport_id={$link['sport_id']}&category_id={$link['category_id']}";
                            
                            echo "<div class='col-md-6 mb-3'>";
                            echo "<div class='card'>";
                            echo "<div class='card-body'>";
                            echo "<h6 class='card-title'>" . htmlspecialchars($link['category_name']) . "</h6>";
                            echo "<p class='card-text'>";
                            echo "<small class='text-muted'>" . htmlspecialchars($link['event_name']) . " - " . htmlspecialchars($link['sport_name']) . "</small>";
                            echo "</p>";
                            echo "<div class='d-flex gap-2'>";
                            echo "<a href='$categories_url' class='btn btn-sm btn-outline-primary' target='_blank'>Categories Page</a>";
                            echo "<a href='$manage_url' class='btn btn-sm btn-success' target='_blank'>Direct Link</a>";
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                        }
                        
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-warning'>No test links available yet. Create categories first.</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Error loading test links: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3>4. Parameter Validation Fix</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5>🔧 Additional Fix Needed:</h5>
                    <p>The parameter validation in manage-category.php might be too strict. It should handle string parameters properly.</p>
                    
                    <h6>Current validation:</h6>
                    <code>if (!$event_id || !$sport_id || !$category_id)</code>
                    
                    <h6>Should be:</h6>
                    <code>if (empty($event_id) || empty($sport_id) || empty($category_id))</code>
                    
                    <p class="mt-2">This ensures that string "0" values are treated as valid parameters.</p>
                </div>
                
                <button class="btn btn-warning" onclick="fixParameterValidation()">
                    Fix Parameter Validation
                </button>
            </div>
        </div>
    </div>
    
    <script>
        function fixParameterValidation() {
            if (confirm('This will update the parameter validation in manage-category.php. Continue?')) {
                fetch('ajax/fix-parameter-validation.php', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({action: 'fix_validation'})
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Parameter validation fixed successfully!');
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    alert('Network error: ' + error.message);
                });
            }
        }
    </script>
</body>
</html>
