<?php
/**
 * Tournament Configuration AJAX Handler
 * SC_IMS Sports Competition and Event Management System
 * 
 * Handles tournament configuration requests including format selection,
 * seeding options, and advanced bracket settings.
 */

require_once '../../config/database.php';
require_once '../../includes/unified_bracket_engine.php';

header('Content-Type: application/json');

try {
    $action = $_POST['action'] ?? $_GET['action'] ?? '';
    
    switch ($action) {
        case 'get_format_options':
            handleGetFormatOptions();
            break;
        case 'get_format_config':
            handleGetFormatConfig();
            break;
        case 'generate_tournament':
            handleGenerateTournament();
            break;
        case 'preview_bracket':
            handlePreviewBracket();
            break;
        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get available tournament formats for a sport
 */
function handleGetFormatOptions() {
    global $conn;

    $eventSportId = $_POST['event_sport_id'] ?? '';
    $participantCount = intval($_POST['participant_count'] ?? 0);

    if (empty($eventSportId)) {
        throw new Exception('Event sport ID is required');
    }

    // Get sport information from event_sport
    $sql = "SELECT s.id, s.type, st.category
            FROM event_sports es
            JOIN sports s ON es.sport_id = s.id
            LEFT JOIN sport_types st ON s.sport_type_id = st.id
            WHERE es.id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$eventSportId]);
    $sport = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$sport) {
        throw new Exception('Sport not found');
    }

    $sportCategory = $sport['category'] ?? $sport['type'] ?? 'traditional';
    
    // Get compatible tournament formats
    $sql = "SELECT * FROM tournament_formats 
            WHERE (sport_type_category = ? OR sport_type_category = 'all')
            AND min_participants <= ?
            AND (max_participants IS NULL OR max_participants >= ?)
            ORDER BY name";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$sportCategory, $participantCount, $participantCount]);
    $formats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode([
        'success' => true,
        'formats' => $formats,
        'sport_category' => $sportCategory,
        'participant_count' => $participantCount
    ]);
}

/**
 * Get configuration options for a specific tournament format
 */
function handleGetFormatConfig() {
    global $conn;
    
    $formatId = $_POST['format_id'] ?? '';
    
    if (empty($formatId)) {
        throw new Exception('Format ID is required');
    }
    
    $sql = "SELECT * FROM tournament_formats WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$formatId]);
    $format = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$format) {
        throw new Exception('Tournament format not found');
    }
    
    // Parse configuration JSON
    $config = json_decode($format['configuration'] ?? '{}', true);
    
    // Generate format-specific configuration options
    $configOptions = generateFormatConfigOptions($format, $config);
    
    echo json_encode([
        'success' => true,
        'format' => $format,
        'config_options' => $configOptions
    ]);
}

/**
 * Generate tournament with specified configuration
 */
function handleGenerateTournament() {
    global $conn;
    
    $eventSportId = $_POST['event_sport_id'] ?? '';
    $formatId = $_POST['format_id'] ?? '';
    $config = $_POST['config'] ?? [];
    
    if (empty($eventSportId)) {
        throw new Exception('Event sport ID is required');
    }
    
    // Parse configuration if it's a JSON string
    if (is_string($config)) {
        $config = json_decode($config, true) ?? [];
    }
    
    // Add format ID to config
    if (!empty($formatId)) {
        $config['format_id'] = $formatId;
    }
    
    // Create unified bracket engine
    $bracketEngine = new UnifiedBracketEngine($conn);
    
    // Generate tournament
    $result = $bracketEngine->generateBracketForCategory($eventSportId, $config);
    
    echo json_encode($result);
}

/**
 * Preview bracket structure without creating tournament
 */
function handlePreviewBracket() {
    global $conn;
    
    $eventSportId = $_POST['event_sport_id'] ?? '';
    $formatId = $_POST['format_id'] ?? '';
    $config = $_POST['config'] ?? [];
    
    if (empty($eventSportId) || empty($formatId)) {
        throw new Exception('Event sport ID and format ID are required');
    }
    
    // Parse configuration if it's a JSON string
    if (is_string($config)) {
        $config = json_decode($config, true) ?? [];
    }
    
    // Get format information
    $sql = "SELECT * FROM tournament_formats WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$formatId]);
    $format = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$format) {
        throw new Exception('Tournament format not found');
    }
    
    // Get participants
    $bracketEngine = new UnifiedBracketEngine($conn);
    $participants = $bracketEngine->getEventParticipants($eventSportId);
    
    if (count($participants) < 2) {
        throw new Exception('At least 2 participants required for preview');
    }
    
    // Generate preview bracket
    require_once '../../includes/tournament_algorithm_factory.php';
    $algorithmFactory = new TournamentAlgorithmFactory($conn);
    
    $algorithm = $algorithmFactory->createAlgorithm($format, null, $config);
    $bracket = $algorithm->generateBracket($participants, $config);
    
    // Calculate statistics
    $stats = [
        'participant_count' => count($participants),
        'total_rounds' => $bracket['rounds'],
        'total_matches' => calculatePreviewMatches($bracket, $format),
        'format_name' => $format['name'],
        'seeding_method' => $config['seeding_method'] ?? 'random'
    ];
    
    echo json_encode([
        'success' => true,
        'preview' => $bracket,
        'statistics' => $stats,
        'participants' => $participants
    ]);
}

/**
 * Generate format-specific configuration options
 */
function generateFormatConfigOptions($format, $baseConfig) {
    $options = [];
    
    // Common options for all formats
    $options['seeding'] = [
        'type' => 'select',
        'label' => 'Seeding Method',
        'options' => [
            'random' => 'Random Seeding',
            'ranking' => 'Ranking-Based Seeding',
            'manual' => 'Manual Seeding',
            'hybrid' => 'Hybrid Seeding'
        ],
        'default' => 'random'
    ];
    
    // Format-specific options
    switch ($format['code']) {
        case 'single_elimination':
        case 'double_elimination':
            $options['third_place_match'] = [
                'type' => 'checkbox',
                'label' => 'Third Place Match',
                'default' => $baseConfig['third_place_match'] ?? false
            ];
            $options['bracket_seeding'] = [
                'type' => 'checkbox',
                'label' => 'Enable Bracket Seeding',
                'default' => $baseConfig['bracket_seeding'] ?? true
            ];
            break;
            
        case 'round_robin':
            $options['points_win'] = [
                'type' => 'number',
                'label' => 'Points for Win',
                'default' => $baseConfig['points_win'] ?? 3,
                'min' => 0,
                'max' => 10
            ];
            $options['points_draw'] = [
                'type' => 'number',
                'label' => 'Points for Draw',
                'default' => $baseConfig['points_draw'] ?? 1,
                'min' => 0,
                'max' => 10
            ];
            $options['points_loss'] = [
                'type' => 'number',
                'label' => 'Points for Loss',
                'default' => $baseConfig['points_loss'] ?? 0,
                'min' => 0,
                'max' => 10
            ];
            break;
            
        case 'multi_stage':
            $options['groups_count'] = [
                'type' => 'number',
                'label' => 'Number of Groups',
                'default' => $baseConfig['groups_count'] ?? 4,
                'min' => 2,
                'max' => 8
            ];
            $options['teams_advance_per_group'] = [
                'type' => 'number',
                'label' => 'Teams Advancing per Group',
                'default' => $baseConfig['teams_advance_per_group'] ?? 2,
                'min' => 1,
                'max' => 4
            ];
            $options['group_points_win'] = [
                'type' => 'number',
                'label' => 'Group Stage Points for Win',
                'default' => $baseConfig['group_points_win'] ?? 3,
                'min' => 0,
                'max' => 10
            ];
            break;
            
        case 'swiss_system':
            $options['avoid_rematches'] = [
                'type' => 'checkbox',
                'label' => 'Avoid Rematches',
                'default' => $baseConfig['avoid_rematches'] ?? true
            ];
            $options['points_win'] = [
                'type' => 'number',
                'label' => 'Points for Win',
                'default' => $baseConfig['points_win'] ?? 1,
                'min' => 0,
                'max' => 10,
                'step' => 0.5
            ];
            break;
    }
    
    return $options;
}

/**
 * Calculate matches for preview
 */
function calculatePreviewMatches($bracket, $format) {
    $participantCount = count($bracket['participants'] ?? []);
    
    switch ($format['code']) {
        case 'single_elimination':
            return $participantCount - 1;
        case 'double_elimination':
            return 2 * $participantCount - 2;
        case 'round_robin':
            return $participantCount * ($participantCount - 1) / 2;
        case 'multi_stage':
            // Simplified calculation for multi-stage
            $groupMatches = 4 * 3; // Assuming 4 groups of 4 teams each
            $knockoutMatches = 8 - 1; // 8 teams advance to knockout
            return $groupMatches + $knockoutMatches;
        case 'swiss_system':
            $rounds = ceil(log($participantCount, 2));
            return $rounds * floor($participantCount / 2);
        default:
            return $participantCount - 1;
    }
}
?>
