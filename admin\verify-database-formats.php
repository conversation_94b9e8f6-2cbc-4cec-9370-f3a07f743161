<?php
/**
 * Verify Database Tournament Formats
 */

require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Database Tournament Formats Verification</h2>";

// 1. Check tournament_formats table
echo "<h3>1. Tournament Formats Table</h3>";
try {
    $stmt = $conn->prepare("SELECT id, name, code, sport_type_category, sport_types FROM tournament_formats ORDER BY id");
    $stmt->execute();
    $formats = $stmt->fetchAll();
    
    if (empty($formats)) {
        echo "<p style='color: red;'>❌ No tournament formats found!</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Sport Type Category</th><th>Sport Types</th></tr>";
        foreach ($formats as $format) {
            echo "<tr>";
            echo "<td>{$format['id']}</td>";
            echo "<td>{$format['name']}</td>";
            echo "<td>{$format['code']}</td>";
            echo "<td>" . ($format['sport_type_category'] ?? 'NULL') . "</td>";
            echo "<td>" . ($format['sport_types'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// 2. Check sports table
echo "<h3>2. Sports with Categories</h3>";
try {
    $stmt = $conn->prepare("
        SELECT s.id, s.name, s.type, st.category as sport_type_category, st.name as sport_type_name
        FROM sports s
        LEFT JOIN sport_types st ON s.sport_type_id = st.id
        ORDER BY st.category, s.name
    ");
    $stmt->execute();
    $sports = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Type</th><th>Sport Type Category</th><th>Sport Type Name</th></tr>";
    foreach ($sports as $sport) {
        $category = $sport['sport_type_category'] ?? $sport['type'] ?? 'NULL';
        $row_color = '';
        if ($category === 'judged') $row_color = 'background-color: #ffe6e6;';
        if ($category === 'academic') $row_color = 'background-color: #e6f3ff;';
        
        echo "<tr style='{$row_color}'>";
        echo "<td>{$sport['id']}</td>";
        echo "<td>{$sport['name']}</td>";
        echo "<td>" . ($sport['type'] ?? 'NULL') . "</td>";
        echo "<td>" . ($sport['sport_type_category'] ?? 'NULL') . "</td>";
        echo "<td>" . ($sport['sport_type_name'] ?? 'NULL') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// 3. Test specific queries
echo "<h3>3. Query Tests</h3>";

// Test judged formats
echo "<h4>Judged Formats Query:</h4>";
try {
    $stmt = $conn->prepare("
        SELECT id, name, code 
        FROM tournament_formats 
        WHERE sport_type_category = 'judged' OR sport_types LIKE '%judged%'
    ");
    $stmt->execute();
    $judged_formats = $stmt->fetchAll();
    
    if (empty($judged_formats)) {
        echo "<p style='color: red;'>❌ No judged formats found!</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($judged_formats) . " judged formats:</p>";
        foreach ($judged_formats as $format) {
            echo "<li>{$format['name']} ({$format['code']})</li>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// Test academic formats
echo "<h4>Academic Formats Query:</h4>";
try {
    $stmt = $conn->prepare("
        SELECT id, name, code 
        FROM tournament_formats 
        WHERE sport_type_category = 'academic' OR sport_types LIKE '%academic%'
    ");
    $stmt->execute();
    $academic_formats = $stmt->fetchAll();
    
    if (empty($academic_formats)) {
        echo "<p style='color: red;'>❌ No academic formats found!</p>";
    } else {
        echo "<p style='color: green;'>✅ Found " . count($academic_formats) . " academic formats:</p>";
        foreach ($academic_formats as $format) {
            echo "<li>{$format['name']} ({$format['code']})</li>";
        }
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

// 4. Check table structure
echo "<h3>4. Table Structure</h3>";
try {
    $stmt = $conn->prepare("DESCRIBE tournament_formats");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    foreach ($columns as $col) {
        echo "<tr>";
        echo "<td>{$col['Field']}</td>";
        echo "<td>{$col['Type']}</td>";
        echo "<td>{$col['Null']}</td>";
        echo "<td>{$col['Key']}</td>";
        echo "<td>{$col['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='fix-missing-tournament-formats.php'>Run Tournament Format Fix</a></p>";
?>
