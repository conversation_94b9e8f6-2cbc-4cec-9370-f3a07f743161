<?php
/**
 * Debug Constraint Error in Detail
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔍 Debug Constraint Error in Detail</h1>";

echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border-left: 4px solid #dc3545; margin-bottom: 20px;'>";
echo "<h2 style='margin-top: 0;'>❌ Current Error</h2>";
echo "<p><strong>SQLSTATE[23000]: Integrity constraint violation: 1452</strong></p>";
echo "<p>Cannot add or update a child row: a foreign key constraint fails</p>";
echo "<p><code>('sc_ims_db'.'sport_categories', CONSTRAINT 'sport_categories_ibfk_1' FOREIGN KEY ('event_sport_id') REFERENCES 'event_sports' ('id'))</code></p>";
echo "</div>";

try {
    echo "<h2>1. Check Database Tables Structure</h2>";
    
    // Check if tables exist
    $tables = ['events', 'sports', 'event_sports', 'sport_categories'];
    foreach ($tables as $table) {
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        $exists = $stmt->rowCount() > 0;
        $status = $exists ? "✅" : "❌";
        echo "<p>$status <strong>$table table:</strong> " . ($exists ? "EXISTS" : "MISSING") . "</p>";
    }
    
    echo "<h2>2. Check Foreign Key Constraints</h2>";
    
    $stmt = $conn->query("
        SELECT 
            CONSTRAINT_NAME,
            TABLE_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'sport_categories'
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    $constraints = $stmt->fetchAll();
    
    echo "<h3>Sport Categories Foreign Keys:</h3>";
    if (empty($constraints)) {
        echo "<p>❌ No foreign key constraints found for sport_categories table</p>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #007bff; color: white;'>";
        echo "<th>Constraint</th><th>Column</th><th>References</th>";
        echo "</tr>";
        foreach ($constraints as $fk) {
            echo "<tr>";
            echo "<td>{$fk['CONSTRAINT_NAME']}</td>";
            echo "<td>{$fk['COLUMN_NAME']}</td>";
            echo "<td>{$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>3. Check Specific Data</h2>";
    
    $event_id = 4;
    $sport_id = 40;
    
    echo "<h3>Event ID $event_id:</h3>";
    $stmt = $conn->prepare("SELECT id, name FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch();
    
    if ($event) {
        echo "<p>✅ Event exists: {$event['name']}</p>";
    } else {
        echo "<p>❌ Event does not exist</p>";
    }
    
    echo "<h3>Sport ID $sport_id:</h3>";
    $stmt = $conn->prepare("SELECT id, name FROM sports WHERE id = ?");
    $stmt->execute([$sport_id]);
    $sport = $stmt->fetch();
    
    if ($sport) {
        echo "<p>✅ Sport exists: {$sport['name']}</p>";
    } else {
        echo "<p>❌ Sport does not exist</p>";
    }
    
    echo "<h3>Event-Sport Relationship:</h3>";
    $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    
    if ($event_sport) {
        echo "<p>✅ Event-Sport relationship exists: ID {$event_sport['id']}</p>";
        $event_sport_id = $event_sport['id'];
    } else {
        echo "<p>❌ Event-Sport relationship does not exist</p>";
        $event_sport_id = null;
    }
    
    echo "<h2>4. Check sport-categories.php Form Data</h2>";
    
    // Simulate what the form is sending
    echo "<h3>Form Data Being Sent:</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
    echo "entity: sport_category<br>";
    echo "action: create<br>";
    echo "event_sport_id: " . ($event_sport_id ?? "MISSING") . "<br>";
    echo "category_name: [user input]<br>";
    echo "category_type: [user input]<br>";
    echo "referee_name: [user input]<br>";
    echo "referee_email: [user input]<br>";
    echo "venue: [user input]<br>";
    echo "</div>";
    
    if (!$event_sport_id) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>PROBLEM IDENTIFIED:</strong> The event_sport_id is missing or NULL</p>";
        echo "<p>This is why the foreign key constraint is failing.</p>";
        echo "</div>";
    }
    
    echo "<h2>5. Fix the Issue</h2>";
    
    if (!$event_sport_id && $event && $sport) {
        echo "<h3>Create Missing Event-Sport Relationship</h3>";
        
        echo "<form method='post' style='margin: 20px 0;'>";
        echo "<input type='hidden' name='fix_event_sport' value='1'>";
        echo "<input type='hidden' name='event_id' value='$event_id'>";
        echo "<input type='hidden' name='sport_id' value='$sport_id'>";
        echo "<button type='submit' style='background: #28a745; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer;'>";
        echo "🔧 Create Event-Sport Relationship";
        echo "</button>";
        echo "</form>";
    }
    
    // Handle the fix
    if (isset($_POST['fix_event_sport'])) {
        $event_id = intval($_POST['event_id']);
        $sport_id = intval($_POST['sport_id']);
        
        try {
            $stmt = $conn->prepare("
                INSERT INTO event_sports (event_id, sport_id, max_teams, venue, status)
                VALUES (?, ?, 8, 'Main Sports Hall', 'registration')
            ");
            $stmt->execute([$event_id, $sport_id]);
            $new_event_sport_id = $conn->lastInsertId();
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>✅ <strong>SUCCESS!</strong> Created event-sport relationship with ID: $new_event_sport_id</p>";
            echo "</div>";
            
            // Refresh to show updated data
            echo "<script>setTimeout(() => window.location.reload(), 2000);</script>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>❌ <strong>Error:</strong> " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    echo "<h2>6. Test Category Creation</h2>";
    
    if ($event_sport_id) {
        echo "<form method='post' style='margin: 20px 0;'>";
        echo "<input type='hidden' name='test_category' value='1'>";
        echo "<input type='hidden' name='event_sport_id' value='$event_sport_id'>";
        echo "<button type='submit' style='background: #007bff; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer;'>";
        echo "🧪 Test Category Creation";
        echo "</button>";
        echo "</form>";
    }
    
    // Handle test category creation
    if (isset($_POST['test_category'])) {
        $test_event_sport_id = intval($_POST['event_sport_id']);
        
        try {
            $stmt = $conn->prepare("
                INSERT INTO sport_categories 
                (event_sport_id, category_name, category_type, referee_name, referee_email, venue)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $test_name = "Test Category " . date('His');
            $stmt->execute([
                $test_event_sport_id,
                $test_name,
                'mixed',
                'Test Referee',
                '<EMAIL>',
                'Test Venue'
            ]);
            
            $category_id = $conn->lastInsertId();
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>✅ <strong>SUCCESS!</strong> Created test category: $test_name (ID: $category_id)</p>";
            echo "</div>";
            
            // Clean up
            $stmt = $conn->prepare("DELETE FROM sport_categories WHERE id = ?");
            $stmt->execute([$category_id]);
            echo "<p><small>🧹 Cleaned up test category</small></p>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>❌ <strong>Test failed:</strong> " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    echo "<h2>7. Check sport-categories.php File</h2>";
    
    // Check if the sport-categories.php file has the correct event_sport_id
    echo "<h3>Verify sport-categories.php Logic:</h3>";
    
    $stmt = $conn->prepare("
        SELECT e.name as event_name, s.name as sport_name, es.id as event_sport_id
        FROM events e
        JOIN event_sports es ON e.id = es.event_id
        JOIN sports s ON es.sport_id = s.id
        WHERE e.id = ? AND s.id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $page_data = $stmt->fetch();
    
    if ($page_data) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<p>✅ <strong>sport-categories.php should work correctly</strong></p>";
        echo "<p>Event: {$page_data['event_name']}</p>";
        echo "<p>Sport: {$page_data['sport_name']}</p>";
        echo "<p>Event Sport ID: {$page_data['event_sport_id']}</p>";
        echo "</div>";
        
        $test_url = "sport-categories.php?event_id=$event_id&sport_id=$sport_id";
        echo "<p><a href='$test_url' target='_blank' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;'>";
        echo "🔗 Test sport-categories.php";
        echo "</a></p>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<p>❌ <strong>sport-categories.php will fail</strong></p>";
        echo "<p>The query in sport-categories.php won't find the event-sport relationship</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>8. Summary & Next Steps</h2>";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff;'>";
echo "<h3>🎯 Root Cause Analysis</h3>";
echo "<p>The constraint error occurs because:</p>";
echo "<ol>";
echo "<li>The sport_categories table has a foreign key constraint on event_sport_id</li>";
echo "<li>The form is trying to insert a category with an event_sport_id that doesn't exist</li>";
echo "<li>This happens when the event-sport relationship is missing from the event_sports table</li>";
echo "</ol>";
echo "<p><strong>Solution:</strong> Ensure the event-sport relationship exists before trying to create categories.</p>";
echo "</div>";
?>
