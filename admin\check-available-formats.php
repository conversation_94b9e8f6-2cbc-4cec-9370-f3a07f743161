<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>🔍 Available Tournament Formats</h2>";

echo "<h3>1. All Tournament Formats in Database</h3>";
$stmt = $conn->prepare("
    SELECT * FROM tournament_formats 
    ORDER BY sport_type_category, name
");
$stmt->execute();
$formats = $stmt->fetchAll(PDO::FETCH_ASSOC);

$categories = [];
foreach ($formats as $format) {
    $category = $format['sport_type_category'] ?? 'unknown';
    if (!isset($categories[$category])) {
        $categories[$category] = [];
    }
    $categories[$category][] = $format;
}

foreach ($categories as $category => $categoryFormats) {
    echo "<h4>📋 " . ucfirst($category) . " Formats</h4>";
    foreach ($categoryFormats as $format) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0; background: #f9f9f9;'>";
        echo "<p><strong>" . $format['name'] . "</strong> (Code: " . $format['code'] . ")</p>";
        echo "<p>" . $format['description'] . "</p>";
        echo "<p><strong>Algorithm:</strong> " . ($format['algorithm_class'] ?? 'Not specified') . "</p>";
        echo "<p><strong>Participants:</strong> " . $format['min_participants'] . " - " . ($format['max_participants'] ?? 'unlimited') . "</p>";
        echo "</div>";
    }
}

echo "<h3>2. Sport Types and Their Categories</h3>";
$stmt = $conn->prepare("
    SELECT id, name, category, tournament_formats 
    FROM sport_types 
    ORDER BY category, name
");
$stmt->execute();
$sportTypes = $stmt->fetchAll(PDO::FETCH_ASSOC);

$sportCategories = [];
foreach ($sportTypes as $sportType) {
    $category = $sportType['category'];
    if (!isset($sportCategories[$category])) {
        $sportCategories[$category] = [];
    }
    $sportCategories[$category][] = $sportType;
}

foreach ($sportCategories as $category => $sports) {
    echo "<h4>🏆 " . ucfirst($category) . " Sports</h4>";
    foreach ($sports as $sport) {
        echo "<div style='border: 1px solid #ddd; padding: 8px; margin: 3px 0;'>";
        echo "<p><strong>" . $sport['name'] . "</strong></p>";
        echo "<p>Available formats: " . ($sport['tournament_formats'] ?? 'None specified') . "</p>";
        echo "</div>";
    }
}

echo "<h3>3. Create Test Sports for Different Categories</h3>";

// Check if we have sports for each category
$testSports = [
    'judged' => 'Dance Competition',
    'academic' => 'Quiz Bowl',
    'performance' => 'Talent Show'
];

foreach ($testSports as $category => $sportName) {
    // Check if sport type exists
    $stmt = $conn->prepare("SELECT id FROM sport_types WHERE category = ? LIMIT 1");
    $stmt->execute([$category]);
    $sportType = $stmt->fetch();
    
    if ($sportType) {
        echo "<p>✅ <strong>$category</strong> sport type exists (ID: " . $sportType['id'] . ")</p>";
        
        // Check if we have a sport of this type
        $stmt = $conn->prepare("SELECT id, name FROM sports WHERE sport_type_id = ? LIMIT 1");
        $stmt->execute([$sportType['id']]);
        $sport = $stmt->fetch();
        
        if ($sport) {
            echo "<p>✅ Sample sport exists: " . $sport['name'] . " (ID: " . $sport['id'] . ")</p>";
        } else {
            echo "<p>⚠️ No sports found for $category category</p>";
            echo "<p><a href='#' onclick='createTestSport(\"$category\", \"$sportName\", " . $sportType['id'] . ")'>Create Test Sport: $sportName</a></p>";
        }
    } else {
        echo "<p>❌ No sport type found for $category category</p>";
    }
}

?>

<script>
function createTestSport(category, sportName, sportTypeId) {
    if (confirm('Create test sport "' + sportName + '" for ' + category + ' category?')) {
        fetch('ajax/create-test-sport.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                name: sportName,
                sport_type_id: sportTypeId,
                category: category
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Test sport created successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.message);
            }
        })
        .catch(error => {
            alert('Error creating test sport: ' + error);
        });
    }
}
</script>

<p><a href="manage-category.php?event_id=4&sport_id=37&category_id=16">← Back to Category Management</a></p>
