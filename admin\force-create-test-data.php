<?php
/**
 * Force Create Test Data
 * Ensures we have the necessary data for category navigation testing
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$action = $_POST['action'] ?? $_GET['action'] ?? '';

if ($action === 'create_all') {
    try {
        $conn->beginTransaction();
        
        $messages = [];
        
        // 1. Ensure we have at least one event
        $event_count = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
        if ($event_count == 0) {
            $conn->exec("
                INSERT INTO events (name, description, start_date, end_date, venue, status) 
                VALUES ('Test Event 2024', 'Test event for category navigation', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY), 'Main Campus', 'active')
            ");
            $messages[] = "Created test event";
        }
        
        // 2. Ensure we have at least one sport
        $sport_count = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
        if ($sport_count == 0) {
            $conn->exec("
                INSERT INTO sports (name, type, description) 
                VALUES ('Basketball', 'traditional', 'Basketball sport for testing')
            ");
            $messages[] = "Created test sport";
        }
        
        // 3. Ensure we have event_sports relationship
        $es_count = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
        if ($es_count == 0) {
            $conn->exec("
                INSERT INTO event_sports (event_id, sport_id) 
                SELECT e.id, s.id 
                FROM events e 
                CROSS JOIN sports s 
                LIMIT 3
            ");
            $messages[] = "Created event-sport relationships";
        }
        
        // 4. Ensure we have sport categories
        $cat_count = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
        if ($cat_count == 0) {
            $stmt = $conn->prepare("
                INSERT INTO sport_categories (event_sport_id, category_name, category_type, referee_name, referee_email, venue)
                SELECT 
                    es.id, 
                    CONCAT(CASE 
                        WHEN ROW_NUMBER() OVER (PARTITION BY es.id ORDER BY es.id) = 1 THEN 'Men''s '
                        WHEN ROW_NUMBER() OVER (PARTITION BY es.id ORDER BY es.id) = 2 THEN 'Women''s '
                        ELSE 'Mixed '
                    END, s.name),
                    CASE 
                        WHEN ROW_NUMBER() OVER (PARTITION BY es.id ORDER BY es.id) = 1 THEN 'men'
                        WHEN ROW_NUMBER() OVER (PARTITION BY es.id ORDER BY es.id) = 2 THEN 'women'
                        ELSE 'mixed'
                    END,
                    'Test Referee',
                    '<EMAIL>',
                    'Sports Complex'
                FROM event_sports es
                JOIN sports s ON es.sport_id = s.id
                CROSS JOIN (SELECT 1 UNION SELECT 2 UNION SELECT 3) AS numbers(n)
                LIMIT 9
            ");
            $stmt->execute();
            $messages[] = "Created sport categories";
        }
        
        // 5. Ensure we have some departments
        $dept_count = $conn->query("SELECT COUNT(*) FROM departments")->fetchColumn();
        if ($dept_count == 0) {
            $departments = [
                ['Computer Science', 'CS', '#007bff'],
                ['Engineering', 'ENG', '#28a745'],
                ['Business', 'BUS', '#ffc107'],
                ['Arts & Sciences', 'AS', '#dc3545'],
                ['Medicine', 'MED', '#6f42c1']
            ];
            
            foreach ($departments as $dept) {
                $stmt = $conn->prepare("
                    INSERT INTO departments (name, abbreviation, color_code, contact_person, contact_email)
                    VALUES (?, ?, ?, 'Test Contact', '<EMAIL>')
                ");
                $stmt->execute($dept);
            }
            $messages[] = "Created test departments";
        }
        
        // 6. Ensure we have event registrations
        $reg_count = $conn->query("SELECT COUNT(*) FROM event_department_registrations")->fetchColumn();
        if ($reg_count == 0) {
            $stmt = $conn->prepare("
                INSERT INTO event_department_registrations (event_id, department_id, registration_date)
                SELECT e.id, d.id, CURDATE()
                FROM events e
                CROSS JOIN departments d
                LIMIT 15
            ");
            $stmt->execute();
            $messages[] = "Created department registrations";
        }
        
        $conn->commit();
        
        echo json_encode([
            'success' => true,
            'message' => 'Test data created successfully: ' . implode(', ', $messages)
        ]);
        
    } catch (Exception $e) {
        $conn->rollback();
        echo json_encode([
            'success' => false,
            'message' => 'Error creating test data: ' . $e->getMessage()
        ]);
    }
    exit;
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Force Create Test Data</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🔧 Force Create Test Data</h1>
        <p>This will create all necessary test data for category navigation</p>
        
        <div class="card">
            <div class="card-header">
                <h3>Current Database Status</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    $events = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
                    $sports = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
                    $event_sports = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
                    $categories = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
                    $departments = $conn->query("SELECT COUNT(*) FROM departments")->fetchColumn();
                    $registrations = $conn->query("SELECT COUNT(*) FROM event_department_registrations")->fetchColumn();
                    
                    echo "<div class='row'>";
                    echo "<div class='col-md-2'><div class='alert alert-info text-center'><h5>$events</h5>Events</div></div>";
                    echo "<div class='col-md-2'><div class='alert alert-info text-center'><h5>$sports</h5>Sports</div></div>";
                    echo "<div class='col-md-2'><div class='alert alert-info text-center'><h5>$event_sports</h5>Event-Sports</div></div>";
                    echo "<div class='col-md-2'><div class='alert alert-info text-center'><h5>$categories</h5>Categories</div></div>";
                    echo "<div class='col-md-2'><div class='alert alert-info text-center'><h5>$departments</h5>Departments</div></div>";
                    echo "<div class='col-md-2'><div class='alert alert-info text-center'><h5>$registrations</h5>Registrations</div></div>";
                    echo "</div>";
                    
                    $needs_data = ($events == 0 || $sports == 0 || $event_sports == 0 || $categories == 0 || $departments == 0 || $registrations == 0);
                    
                    if ($needs_data) {
                        echo "<div class='alert alert-warning'>";
                        echo "<h5>⚠️ Missing Data Detected</h5>";
                        echo "<p>Some required data is missing. Click the button below to create all necessary test data.</p>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-success'>";
                        echo "<h5>✅ All Data Present</h5>";
                        echo "<p>All necessary data exists. Navigation should work now.</p>";
                        echo "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database Error: " . $e->getMessage() . "</div>";
                }
                ?>
                
                <button class="btn btn-primary btn-lg" onclick="createAllData()">
                    <i class="fas fa-database"></i> Create All Test Data
                </button>
                
                <div id="result" class="mt-3"></div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h3>Test Navigation After Data Creation</h3>
            </div>
            <div class="card-body">
                <div id="test-links">
                    <p>Create test data first, then test links will appear here.</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        function createAllData() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<div class="alert alert-info"><i class="fas fa-spinner fa-spin"></i> Creating all test data...</div>';
            
            fetch('force-create-test-data.php', {
                method: 'POST',
                headers: {'Content-Type': 'application/x-www-form-urlencoded'},
                body: 'action=create_all'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">Error: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">Network error: ${error.message}</div>`;
            });
        }
        
        // Load test links after page loads
        document.addEventListener('DOMContentLoaded', function() {
            loadTestLinks();
        });
        
        function loadTestLinks() {
            fetch('debug-navigation-issue.php')
                .then(response => response.text())
                .then(html => {
                    // Extract test links from the debug page
                    const testLinksDiv = document.getElementById('test-links');
                    testLinksDiv.innerHTML = `
                        <h5>Quick Test Links:</h5>
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="sport-categories.php?event_id=1&sport_id=1" class="btn btn-primary" target="_blank">
                                <i class="fas fa-list"></i> Categories List
                            </a>
                            <a href="manage-category.php?event_id=1&sport_id=1&category_id=1" class="btn btn-success" target="_blank">
                                <i class="fas fa-cog"></i> Direct Category Management
                            </a>
                            <a href="manage-category.php?event_id=1&sport_id=1&category_id=1&debug=1" class="btn btn-warning" target="_blank">
                                <i class="fas fa-bug"></i> Debug Mode
                            </a>
                        </div>
                        <p class="mt-2"><small class="text-muted">After creating test data, use these links to test navigation.</small></p>
                    `;
                });
        }
    </script>
</body>
</html>
