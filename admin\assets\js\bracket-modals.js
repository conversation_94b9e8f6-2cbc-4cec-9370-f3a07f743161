/**
 * SC_IMS Tournament Bracket Modal System
 * Enhanced modal-based match editing with unique functionality
 */

class SCBracketModals {
    constructor() {
        this.currentMatchId = null;
        this.participants = [];
        this.initializeModals();
        this.loadParticipants();
    }

    initializeModals() {
        // Create modal container if it doesn't exist
        if (!document.getElementById('sc-modal-container')) {
            const modalContainer = document.createElement('div');
            modalContainer.id = 'sc-modal-container';
            document.body.appendChild(modalContainer);
        }
    }

    async loadParticipants() {
        try {
            const response = await fetch('ajax/get_tournament_participants.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    event_id: window.currentEventId,
                    sport_id: window.currentSportId
                })
            });
            
            if (response.ok) {
                const data = await response.json();
                this.participants = data.participants || [];
            }
        } catch (error) {
            console.error('Error loading participants:', error);
        }
    }

    openMatchEditModal(matchId) {
        this.currentMatchId = matchId;
        this.showMatchEditModal();
    }

    showMatchEditModal() {
        const modalHtml = `
            <div class="sc-modal-overlay" id="match-edit-modal">
                <div class="sc-modal-container">
                    <div class="sc-modal-header">
                        <h3 class="sc-modal-title">
                            <i class="fas fa-edit"></i>
                            Edit Match Details
                        </h3>
                        <button class="sc-modal-close" onclick="scBracketModals.closeModal('match-edit-modal')">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <div class="sc-modal-body">
                        <form id="match-edit-form">
                            <div class="sc-form-row">
                                <div class="sc-form-group">
                                    <label class="sc-form-label">
                                        <i class="fas fa-users"></i>
                                        Team 1
                                    </label>
                                    <select class="sc-form-select" id="team1-select" name="team1_id">
                                        <option value="">Select Team 1</option>
                                        ${this.generateParticipantOptions()}
                                    </select>
                                </div>
                                
                                <div class="sc-form-group">
                                    <label class="sc-form-label">
                                        <i class="fas fa-trophy"></i>
                                        Team 1 Score
                                    </label>
                                    <input type="number" class="sc-form-input" id="team1-score" name="team1_score" min="0" placeholder="0">
                                </div>
                            </div>
                            
                            <div class="sc-vs-divider">
                                <div class="sc-vs-circle-modal">VS</div>
                            </div>
                            
                            <div class="sc-form-row">
                                <div class="sc-form-group">
                                    <label class="sc-form-label">
                                        <i class="fas fa-users"></i>
                                        Team 2
                                    </label>
                                    <select class="sc-form-select" id="team2-select" name="team2_id">
                                        <option value="">Select Team 2</option>
                                        ${this.generateParticipantOptions()}
                                    </select>
                                </div>
                                
                                <div class="sc-form-group">
                                    <label class="sc-form-label">
                                        <i class="fas fa-trophy"></i>
                                        Team 2 Score
                                    </label>
                                    <input type="number" class="sc-form-input" id="team2-score" name="team2_score" min="0" placeholder="0">
                                </div>
                            </div>
                            
                            <div class="sc-form-row">
                                <div class="sc-form-group">
                                    <label class="sc-form-label">
                                        <i class="fas fa-flag"></i>
                                        Match Status
                                    </label>
                                    <select class="sc-form-select" id="match-status" name="status">
                                        <option value="pending">Pending</option>
                                        <option value="in_progress">In Progress</option>
                                        <option value="completed">Completed</option>
                                    </select>
                                </div>
                                
                                <div class="sc-form-group">
                                    <label class="sc-form-label">
                                        <i class="fas fa-clock"></i>
                                        Scheduled Time
                                    </label>
                                    <input type="time" class="sc-form-input" id="scheduled-time" name="scheduled_time">
                                </div>
                            </div>
                            
                            <div class="sc-form-group">
                                <label class="sc-form-label">
                                    <i class="fas fa-crown"></i>
                                    Winner Determination
                                </label>
                                <div class="sc-winner-controls">
                                    <button type="button" class="sc-winner-btn" id="team1-winner" onclick="scBracketModals.selectWinner(1)">
                                        <i class="fas fa-trophy"></i>
                                        Team 1 Wins
                                    </button>
                                    <button type="button" class="sc-winner-btn" id="no-winner" onclick="scBracketModals.selectWinner(0)">
                                        <i class="fas fa-minus"></i>
                                        No Winner
                                    </button>
                                    <button type="button" class="sc-winner-btn" id="team2-winner" onclick="scBracketModals.selectWinner(2)">
                                        <i class="fas fa-trophy"></i>
                                        Team 2 Wins
                                    </button>
                                </div>
                            </div>
                            
                            <div class="sc-form-group">
                                <label class="sc-form-label">
                                    <i class="fas fa-sticky-note"></i>
                                    Match Notes
                                </label>
                                <textarea class="sc-form-textarea" id="match-notes" name="notes" rows="3" placeholder="Add any notes about this match..."></textarea>
                            </div>
                        </form>
                    </div>
                    
                    <div class="sc-modal-footer">
                        <div class="sc-modal-actions">
                            <button class="sc-btn-secondary" onclick="scBracketModals.closeModal('match-edit-modal')">
                                <i class="fas fa-times"></i>
                                Cancel
                            </button>
                            <button class="sc-btn-referee" onclick="scBracketModals.sendToReferee()">
                                <i class="fas fa-whistle"></i>
                                Send to Referee
                            </button>
                            <button class="sc-btn-primary" onclick="scBracketModals.saveMatch()">
                                <i class="fas fa-save"></i>
                                Save Match
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('sc-modal-container').innerHTML = modalHtml;
        this.loadMatchData();
        this.setupFormValidation();
    }

    generateParticipantOptions() {
        return this.participants.map(participant => 
            `<option value="${participant.id}">${participant.name} (${participant.department})</option>`
        ).join('');
    }

    async loadMatchData() {
        if (!this.currentMatchId) return;

        try {
            const response = await fetch('ajax/get_match_details.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    match_id: this.currentMatchId
                })
            });

            if (response.ok) {
                const data = await response.json();
                this.populateForm(data.match);
            }
        } catch (error) {
            console.error('Error loading match data:', error);
        }
    }

    populateForm(matchData) {
        if (!matchData) return;

        // Populate form fields
        document.getElementById('team1-select').value = matchData.team1_id || '';
        document.getElementById('team2-select').value = matchData.team2_id || '';
        document.getElementById('team1-score').value = matchData.team1_score || '';
        document.getElementById('team2-score').value = matchData.team2_score || '';
        document.getElementById('match-status').value = matchData.status || 'pending';
        document.getElementById('scheduled-time').value = matchData.scheduled_time || '';
        document.getElementById('match-notes').value = matchData.notes || '';

        // Set winner selection
        if (matchData.winner_id) {
            if (matchData.winner_id == matchData.team1_id) {
                this.selectWinner(1);
            } else if (matchData.winner_id == matchData.team2_id) {
                this.selectWinner(2);
            }
        }
    }

    selectWinner(team) {
        // Reset all winner buttons
        document.querySelectorAll('.sc-winner-btn').forEach(btn => {
            btn.classList.remove('active');
        });

        // Set active winner
        if (team === 1) {
            document.getElementById('team1-winner').classList.add('active');
        } else if (team === 2) {
            document.getElementById('team2-winner').classList.add('active');
        } else {
            document.getElementById('no-winner').classList.add('active');
        }
    }

    setupFormValidation() {
        // Auto-determine winner based on scores
        const team1Score = document.getElementById('team1-score');
        const team2Score = document.getElementById('team2-score');

        [team1Score, team2Score].forEach(input => {
            input.addEventListener('input', () => {
                const score1 = parseInt(team1Score.value) || 0;
                const score2 = parseInt(team2Score.value) || 0;

                if (score1 > score2) {
                    this.selectWinner(1);
                } else if (score2 > score1) {
                    this.selectWinner(2);
                } else if (score1 === score2 && (score1 > 0 || score2 > 0)) {
                    this.selectWinner(0); // Tie
                }
            });
        });
    }

    async saveMatch() {
        const formData = new FormData(document.getElementById('match-edit-form'));
        
        // Add winner information
        const activeWinner = document.querySelector('.sc-winner-btn.active');
        if (activeWinner) {
            if (activeWinner.id === 'team1-winner') {
                formData.append('winner_id', formData.get('team1_id'));
            } else if (activeWinner.id === 'team2-winner') {
                formData.append('winner_id', formData.get('team2_id'));
            }
        }

        formData.append('match_id', this.currentMatchId);

        try {
            const response = await fetch('ajax/save_match.php', {
                method: 'POST',
                body: formData
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.showSuccessMessage('Match updated successfully!');
                    this.closeModal('match-edit-modal');
                    this.refreshBracket();
                } else {
                    this.showErrorMessage(result.message || 'Error saving match');
                }
            }
        } catch (error) {
            console.error('Error saving match:', error);
            this.showErrorMessage('Error saving match');
        }
    }

    async sendToReferee() {
        try {
            const response = await fetch('ajax/send_to_referee.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    match_id: this.currentMatchId
                })
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    this.showSuccessMessage('Match sent to referee successfully!');
                    // Optionally open referee interface
                    if (result.referee_url) {
                        window.open(result.referee_url, '_blank');
                    }
                } else {
                    this.showErrorMessage(result.message || 'Error sending to referee');
                }
            }
        } catch (error) {
            console.error('Error sending to referee:', error);
            this.showErrorMessage('Error sending to referee');
        }
    }

    closeModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.remove();
        }
    }

    showSuccessMessage(message) {
        this.showNotification(message, 'success');
    }

    showErrorMessage(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `sc-notification sc-notification-${type}`;
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'}"></i>
            ${message}
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    refreshBracket() {
        // Reload the bracket display
        if (typeof loadBracketData === 'function') {
            loadBracketData();
        } else {
            location.reload();
        }
    }
}

// Global functions for onclick handlers
function openMatchEditModal(matchId) {
    scBracketModals.openMatchEditModal(matchId);
}

function sendToReferee(matchId) {
    scBracketModals.currentMatchId = matchId;
    scBracketModals.sendToReferee();
}

// Initialize the modal system
const scBracketModals = new SCBracketModals();
