<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is admin
requireAdmin();

echo "<h2>Testing Enhanced Bracket System</h2>";

try {
    // Test database connection
    echo "<h3>1. Database Connection</h3>";
    echo "✅ Database connected successfully<br>";
    
    // Check if required tables exist
    echo "<h3>2. Required Tables</h3>";
    
    $tables_to_check = [
        'referee_sessions',
        'tournament_standings',
        'tournament_matches',
        'event_sports',
        'tournament_formats'
    ];
    
    foreach ($tables_to_check as $table) {
        $stmt = $pdo->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '$table' exists<br>";
        } else {
            echo "❌ Table '$table' missing<br>";
            
            // Try to create missing tables
            if ($table === 'referee_sessions') {
                $pdo->exec("
                    CREATE TABLE referee_sessions (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        match_id VARCHAR(255) NOT NULL,
                        token VARCHAR(64) NOT NULL UNIQUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        expires_at TIMESTAMP NOT NULL,
                        status ENUM('active', 'used', 'expired') DEFAULT 'active',
                        last_accessed TIMESTAMP NULL,
                        ip_address VARCHAR(45) NULL,
                        user_agent TEXT NULL
                    )
                ");
                echo "✅ Created 'referee_sessions' table<br>";
            }
            
            if ($table === 'tournament_standings') {
                $pdo->exec("
                    CREATE TABLE tournament_standings (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        event_sport_id INT NOT NULL,
                        team_id INT NOT NULL,
                        matches_played INT DEFAULT 0,
                        wins INT DEFAULT 0,
                        losses INT DEFAULT 0,
                        draws INT DEFAULT 0,
                        points_for INT DEFAULT 0,
                        points_against INT DEFAULT 0,
                        points INT DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        UNIQUE KEY unique_team_event_sport (event_sport_id, team_id)
                    )
                ");
                echo "✅ Created 'tournament_standings' table<br>";
            }
        }
    }
    
    // Test CSS and JS files
    echo "<h3>3. Asset Files</h3>";
    
    $assets_to_check = [
        'assets/css/bracket-styles.css',
        'assets/css/bracket-modals.css',
        'assets/js/bracket-modals.js'
    ];
    
    foreach ($assets_to_check as $asset) {
        if (file_exists($asset)) {
            echo "✅ Asset '$asset' exists<br>";
        } else {
            echo "❌ Asset '$asset' missing<br>";
        }
    }
    
    // Test AJAX endpoints
    echo "<h3>4. AJAX Endpoints</h3>";
    
    $endpoints_to_check = [
        'ajax/get_tournament_participants.php',
        'ajax/get_match_details.php',
        'ajax/save_match.php',
        'ajax/send_to_referee.php'
    ];
    
    foreach ($endpoints_to_check as $endpoint) {
        if (file_exists($endpoint)) {
            echo "✅ Endpoint '$endpoint' exists<br>";
        } else {
            echo "❌ Endpoint '$endpoint' missing<br>";
        }
    }
    
    // Test referee interface
    echo "<h3>5. Referee Interface</h3>";
    
    $referee_files = [
        '../referee/match.php',
        '../referee/save_referee_scores.php',
        '../referee/update_match_status.php'
    ];
    
    foreach ($referee_files as $file) {
        if (file_exists($file)) {
            echo "✅ Referee file '$file' exists<br>";
        } else {
            echo "❌ Referee file '$file' missing<br>";
        }
    }
    
    // Test demo page
    echo "<h3>6. Demo Page</h3>";
    if (file_exists('demo-bracket.php')) {
        echo "✅ Demo page exists<br>";
        echo "<a href='demo-bracket.php' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>View Demo</a><br>";
    } else {
        echo "❌ Demo page missing<br>";
    }
    
    echo "<h3>7. System Status</h3>";
    echo "✅ Enhanced bracket system is ready!<br>";
    echo "<p><strong>Features Available:</strong></p>";
    echo "<ul>";
    echo "<li>Modal-based match editing with comprehensive form controls</li>";
    echo "<li>Unique SC_IMS visual design with custom styling</li>";
    echo "<li>Referee integration with secure token-based access</li>";
    echo "<li>Real-time score updates and winner determination</li>";
    echo "<li>Responsive design for all device types</li>";
    echo "<li>Integration with unified registration system</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<h3>❌ Error</h3>";
    echo "Error: " . $e->getMessage() . "<br>";
}
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
}

h2 {
    color: #007bff;
    border-bottom: 2px solid #007bff;
    padding-bottom: 10px;
}

h3 {
    color: #343a40;
    margin-top: 25px;
}

ul {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

li {
    margin: 8px 0;
}
</style>
