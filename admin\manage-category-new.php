<?php
/**
 * Category Management Hub for SC_IMS Admin Panel
 * Comprehensive category management with Overview, Fixtures, and Standings
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get current admin user
$current_admin = getCurrentAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get URL parameters
$event_id = $_GET['event_id'] ?? null;
$sport_id = $_GET['sport_id'] ?? null;
$category_id = $_GET['category_id'] ?? null;

// Debug: Log all access attempts
error_log("manage-category.php accessed - event_id: $event_id, sport_id: $sport_id, category_id: $category_id");

// Validate required parameters
if (empty($event_id) || empty($sport_id) || empty($category_id) || 
    !is_numeric($event_id) || !is_numeric($sport_id) || !is_numeric($category_id)) {
    
    error_log("manage-category.php REDIRECT - Invalid parameters");
    
    // If debug mode, show error instead of redirecting
    if (isset($_GET['debug'])) {
        die("DEBUG: Invalid parameters - event_id: $event_id, sport_id: $sport_id, category_id: $category_id");
    }
    
    header('Location: events.php');
    exit;
}

// Convert to integers
$event_id = intval($event_id);
$sport_id = intval($sport_id);
$category_id = intval($category_id);

try {
    // Get comprehensive category information
    $stmt = $conn->prepare("
        SELECT 
            sc.*,
            e.name as event_name,
            e.description as event_description,
            e.start_date,
            e.end_date,
            e.venue as event_venue,
            s.name as sport_name,
            s.type as sport_type,
            s.description as sport_description,
            es.id as event_sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ? AND es.event_id = ? AND s.id = ?
    ");
    
    $stmt->execute([$category_id, $event_id, $sport_id]);
    $category = $stmt->fetch();
    
    if (!$category) {
        error_log("manage-category.php REDIRECT - Category not found");
        
        if (isset($_GET['debug'])) {
            die("DEBUG: Category not found - category_id: $category_id, event_id: $event_id, sport_id: $sport_id");
        }
        
        header('Location: events.php');
        exit;
    }
    
    error_log("manage-category.php SUCCESS - Category found: " . $category['category_name']);
    
} catch (Exception $e) {
    error_log("manage-category.php ERROR: " . $e->getMessage());
    
    if (isset($_GET['debug'])) {
        die("DEBUG: Database error - " . $e->getMessage());
    }
    
    header('Location: events.php');
    exit;
}

// Get registered departments for this category
try {
    $stmt = $conn->prepare("
        SELECT 
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            edr.id as registration_id,
            edr.registration_date,
            COUNT(DISTINCT m.id) as matches_played,
            COUNT(DISTINCT CASE WHEN m.winner_id = d.id THEN m.id END) as wins,
            COUNT(DISTINCT CASE WHEN m.loser_id = d.id THEN m.id END) as losses,
            COALESCE(SUM(CASE WHEN m.winner_id = d.id THEN 3 WHEN m.loser_id = d.id THEN 1 ELSE 0 END), 0) as points
        FROM departments d
        JOIN event_department_registrations edr ON d.id = edr.department_id
        LEFT JOIN matches m ON (m.team_a_id = d.id OR m.team_b_id = d.id) 
                            AND m.sport_category_id = ?
        WHERE edr.event_id = ?
        GROUP BY d.id, d.name, d.abbreviation, d.color_code, edr.id, edr.registration_date
        ORDER BY points DESC, wins DESC, d.name
    ");
    $stmt->execute([$category_id, $event_id]);
    $participants = $stmt->fetchAll();
} catch (Exception $e) {
    $participants = [];
    error_log("Error fetching participants: " . $e->getMessage());
}

// Get matches for this category
try {
    $stmt = $conn->prepare("
        SELECT 
            m.*,
            ta.name as team_a_name,
            ta.abbreviation as team_a_abbr,
            ta.color_code as team_a_color,
            tb.name as team_b_name,
            tb.abbreviation as team_b_abbr,
            tb.color_code as team_b_color,
            w.name as winner_name,
            w.abbreviation as winner_abbr
        FROM matches m
        LEFT JOIN departments ta ON m.team_a_id = ta.id
        LEFT JOIN departments tb ON m.team_b_id = tb.id
        LEFT JOIN departments w ON m.winner_id = w.id
        WHERE m.sport_category_id = ?
        ORDER BY m.round_number, m.match_number
    ");
    $stmt->execute([$category_id]);
    $matches = $stmt->fetchAll();
} catch (Exception $e) {
    $matches = [];
    error_log("Error fetching matches: " . $e->getMessage());
}

// Calculate statistics
$total_participants = count($participants);
$total_matches = count($matches);
$completed_matches = count(array_filter($matches, function($m) { return $m['status'] === 'completed'; }));
$pending_matches = $total_matches - $completed_matches;

?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($category['category_name']); ?> - <?php echo htmlspecialchars($category['sport_name']); ?> | SC_IMS Admin</title>
    <?php include 'includes/admin-styles.php'; ?>
    <style>
        /* Tab Navigation Styles */
        .tab-navigation {
            display: flex;
            background: #f8f9fa;
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 20px;
            border-radius: 8px 8px 0 0;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-weight: 600;
            color: #6c757d;
            transition: all 0.3s ease;
            position: relative;
        }

        .tab-button:hover {
            background: #e9ecef;
            color: #495057;
        }

        .tab-button.active {
            background: #007bff;
            color: white;
        }

        /* Tab Content Styles */
        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease-in-out;
        }

        .tab-content.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Category Header Styles */
        .category-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .category-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .category-subtitle {
            opacity: 0.9;
            font-size: 1rem;
        }

        /* Statistics Cards */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stats-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.2s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #007bff;
        }

        .stats-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        /* Card Styles */
        .card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .card-header {
            background: #f8f9fa;
            padding: 15px 20px;
            border-bottom: 1px solid #dee2e6;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #495057;
            margin: 0;
        }

        .card-body {
            padding: 20px;
        }

        /* Button Styles */
        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-primary:hover {
            background: #0056b3;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-sm {
            padding: 4px 8px;
            font-size: 0.875rem;
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="main-content">
        <!-- Breadcrumb Navigation -->
        <nav aria-label="breadcrumb" style="margin-bottom: 20px;">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="events.php">Events</a></li>
                <li class="breadcrumb-item"><a href="manage-event.php?id=<?php echo $event_id; ?>"><?php echo htmlspecialchars($category['event_name']); ?></a></li>
                <li class="breadcrumb-item"><a href="sport-categories.php?event_id=<?php echo $event_id; ?>&sport_id=<?php echo $sport_id; ?>"><?php echo htmlspecialchars($category['sport_name']); ?> Categories</a></li>
                <li class="breadcrumb-item active" aria-current="page"><?php echo htmlspecialchars($category['category_name']); ?></li>
            </ol>
        </nav>

        <!-- Category Header -->
        <div class="category-header">
            <div class="category-title"><?php echo htmlspecialchars($category['category_name']); ?></div>
            <div class="category-subtitle">
                <?php echo htmlspecialchars($category['sport_name']); ?> - <?php echo htmlspecialchars($category['event_name']); ?>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="stats-grid">
            <div class="stats-card">
                <div class="stats-number"><?php echo $total_participants; ?></div>
                <div class="stats-label">Participants</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $total_matches; ?></div>
                <div class="stats-label">Total Matches</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $completed_matches; ?></div>
                <div class="stats-label">Completed</div>
            </div>
            <div class="stats-card">
                <div class="stats-number"><?php echo $pending_matches; ?></div>
                <div class="stats-label">Pending</div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="tab-navigation">
            <button class="tab-button active" onclick="showTab('overview')">
                <i class="fas fa-info-circle"></i> Overview
            </button>
            <button class="tab-button" onclick="showTab('fixtures')">
                <i class="fas fa-sitemap"></i> Fixtures
            </button>
            <button class="tab-button" onclick="showTab('standings')">
                <i class="fas fa-trophy"></i> Standings
            </button>
        </div>

        <!-- Tab Content -->
        <div id="overview-tab" class="tab-content active">
            <!-- Overview Tab Content -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Category Information</h3>
                </div>
                <div class="card-body">
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px;">
                        <div>
                            <h5>Basic Details</h5>
                            <p><strong>Category Name:</strong> <?php echo htmlspecialchars($category['category_name']); ?></p>
                            <p><strong>Category Type:</strong> <?php echo htmlspecialchars($category['category_type']); ?></p>
                            <p><strong>Sport:</strong> <?php echo htmlspecialchars($category['sport_name']); ?></p>
                            <p><strong>Event:</strong> <?php echo htmlspecialchars($category['event_name']); ?></p>
                        </div>
                        <div>
                            <h5>Officials & Venue</h5>
                            <p><strong>Referee:</strong> <?php echo htmlspecialchars($category['referee_name'] ?? 'Not assigned'); ?></p>
                            <p><strong>Referee Email:</strong> <?php echo htmlspecialchars($category['referee_email'] ?? 'Not provided'); ?></p>
                            <p><strong>Venue:</strong> <?php echo htmlspecialchars($category['venue'] ?? 'Not specified'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Participants List -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Registered Participants</h3>
                    <span class="btn btn-secondary btn-sm"><?php echo count($participants); ?> Departments</span>
                </div>
                <div class="card-body">
                    <?php if (empty($participants)): ?>
                        <div style="text-align: center; padding: 40px; color: #6c757d;">
                            <i class="fas fa-users" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.3;"></i>
                            <p>No participants registered yet</p>
                        </div>
                    <?php else: ?>
                        <div style="display: grid; grid-template-columns: repeat(auto-fill, minmax(300px, 1fr)); gap: 15px;">
                            <?php foreach ($participants as $participant): ?>
                                <div class="participant-card">
                                    <div class="participant-header">
                                        <div style="width: 20px; height: 20px; background: <?php echo $participant['color_code'] ?? '#6c757d'; ?>; border-radius: 50%;"></div>
                                        <div>
                                            <strong><?php echo htmlspecialchars($participant['name']); ?></strong>
                                            <div style="font-size: 0.9rem; color: #6c757d;"><?php echo htmlspecialchars($participant['abbreviation']); ?></div>
                                        </div>
                                    </div>
                                    <div class="participant-stats">
                                        <span><strong>Matches:</strong> <?php echo $participant['matches_played']; ?></span>
                                        <span><strong>Wins:</strong> <?php echo $participant['wins']; ?></span>
                                        <span><strong>Points:</strong> <?php echo $participant['points']; ?></span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div id="fixtures-tab" class="tab-content">
            <!-- Fixtures Tab Content -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Tournament Fixtures</h3>
                    <div>
                        <button class="btn btn-primary btn-sm" onclick="generateMatches()">
                            <i class="fas fa-plus"></i> Generate Matches
                        </button>
                        <button class="btn btn-secondary btn-sm" onclick="refreshFixtures()">
                            <i class="fas fa-sync"></i> Refresh
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <?php if (empty($matches)): ?>
                        <div style="text-align: center; padding: 40px; color: #6c757d;">
                            <i class="fas fa-sitemap" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.3;"></i>
                            <p>No matches scheduled yet</p>
                            <p style="font-size: 0.9rem;">Click "Generate Matches" to create tournament brackets</p>
                        </div>
                    <?php else: ?>
                        <div id="matches-container">
                            <?php foreach ($matches as $match): ?>
                                <div class="match-box" data-match-id="<?php echo $match['id']; ?>">
                                    <div class="match-teams">
                                        <div class="team-info">
                                            <div class="team-color" style="background: <?php echo $match['team_a_color'] ?? '#6c757d'; ?>;"></div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($match['team_a_name'] ?? 'TBD'); ?></strong>
                                                <div style="font-size: 0.9rem; color: #6c757d;"><?php echo htmlspecialchars($match['team_a_abbr'] ?? ''); ?></div>
                                            </div>
                                        </div>
                                        <div class="vs-divider">VS</div>
                                        <div class="team-info">
                                            <div class="team-color" style="background: <?php echo $match['team_b_color'] ?? '#6c757d'; ?>;"></div>
                                            <div>
                                                <strong><?php echo htmlspecialchars($match['team_b_name'] ?? 'TBD'); ?></strong>
                                                <div style="font-size: 0.9rem; color: #6c757d;"><?php echo htmlspecialchars($match['team_b_abbr'] ?? ''); ?></div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="score-section">
                                        <div>
                                            <label>Score A:</label>
                                            <input type="number" class="score-input" id="score-a-<?php echo $match['id']; ?>"
                                                   value="<?php echo $match['team_a_score'] ?? ''; ?>" min="0" max="999">
                                        </div>
                                        <div>
                                            <label>Score B:</label>
                                            <input type="number" class="score-input" id="score-b-<?php echo $match['id']; ?>"
                                                   value="<?php echo $match['team_b_score'] ?? ''; ?>" min="0" max="999">
                                        </div>
                                        <div>
                                            <?php if ($match['status'] === 'completed'): ?>
                                                <button class="btn btn-secondary btn-sm" onclick="editMatch(<?php echo $match['id']; ?>)">
                                                    <i class="fas fa-edit"></i> Edit
                                                </button>
                                            <?php else: ?>
                                                <button class="btn btn-success btn-sm" onclick="saveMatch(<?php echo $match['id']; ?>)">
                                                    <i class="fas fa-save"></i> Save
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </div>

                                    <?php if ($match['status'] === 'completed' && $match['winner_name']): ?>
                                        <div style="text-align: center; margin-top: 10px; padding: 8px; background: #d4edda; border-radius: 4px; color: #155724;">
                                            <i class="fas fa-crown"></i> Winner: <strong><?php echo htmlspecialchars($match['winner_name']); ?></strong>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div id="standings-tab" class="tab-content">
            <!-- Standings Tab Content -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Current Standings</h3>
                    <button class="btn btn-secondary btn-sm" onclick="refreshStandings()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
                <div class="card-body">
                    <?php if (empty($participants)): ?>
                        <div style="text-align: center; padding: 40px; color: #6c757d;">
                            <i class="fas fa-trophy" style="font-size: 3rem; margin-bottom: 15px; opacity: 0.3;"></i>
                            <p>No standings available yet</p>
                            <p style="font-size: 0.9rem;">Standings will appear once matches are completed</p>
                        </div>
                    <?php else: ?>
                        <div class="standings-table">
                            <table style="width: 100%; border-collapse: collapse;">
                                <thead>
                                    <tr style="background: #007bff; color: white;">
                                        <th style="padding: 15px; text-align: center;">Rank</th>
                                        <th style="padding: 15px; text-align: left;">Department</th>
                                        <th style="padding: 15px; text-align: center;">Matches</th>
                                        <th style="padding: 15px; text-align: center;">Wins</th>
                                        <th style="padding: 15px; text-align: center;">Losses</th>
                                        <th style="padding: 15px; text-align: center;">Points</th>
                                        <th style="padding: 15px; text-align: center;">Win Rate</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    $rank = 1;
                                    foreach ($participants as $participant):
                                        $win_rate = $participant['matches_played'] > 0 ?
                                            round(($participant['wins'] / $participant['matches_played']) * 100, 1) : 0;
                                        $rank_class = '';
                                        if ($rank == 1) $rank_class = 'rank-1';
                                        elseif ($rank == 2) $rank_class = 'rank-2';
                                        elseif ($rank == 3) $rank_class = 'rank-3';
                                        else $rank_class = 'rank-other';
                                    ?>
                                        <tr style="border-bottom: 1px solid #dee2e6;">
                                            <td style="padding: 12px; text-align: center;">
                                                <div class="rank-badge <?php echo $rank_class; ?>">
                                                    <?php echo $rank; ?>
                                                </div>
                                            </td>
                                            <td style="padding: 12px;">
                                                <div style="display: flex; align-items: center; gap: 10px;">
                                                    <div style="width: 20px; height: 20px; background: <?php echo $participant['color_code'] ?? '#6c757d'; ?>; border-radius: 50%;"></div>
                                                    <div>
                                                        <strong><?php echo htmlspecialchars($participant['name']); ?></strong>
                                                        <div style="font-size: 0.8rem; color: #6c757d;"><?php echo htmlspecialchars($participant['abbreviation']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td style="padding: 12px; text-align: center;"><?php echo $participant['matches_played']; ?></td>
                                            <td style="padding: 12px; text-align: center; color: #28a745; font-weight: bold;"><?php echo $participant['wins']; ?></td>
                                            <td style="padding: 12px; text-align: center; color: #dc3545; font-weight: bold;"><?php echo $participant['losses']; ?></td>
                                            <td style="padding: 12px; text-align: center;">
                                                <span style="background: #007bff; color: white; padding: 4px 8px; border-radius: 4px; font-weight: bold;">
                                                    <?php echo $participant['points']; ?>
                                                </span>
                                            </td>
                                            <td style="padding: 12px; text-align: center;">
                                                <span style="color: <?php echo $win_rate >= 70 ? '#28a745' : ($win_rate >= 50 ? '#ffc107' : '#dc3545'); ?>; font-weight: bold;">
                                                    <?php echo $win_rate; ?>%
                                                </span>
                                            </td>
                                        </tr>
                                    <?php
                                        $rank++;
                                    endforeach;
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript for Tab Functionality and AJAX -->
    <script>
        // Tab switching functionality
        function showTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            document.getElementById(tabName + '-tab').classList.add('active');

            // Add active class to clicked button
            event.target.classList.add('active');
        }

        // Save match score
        function saveMatch(matchId) {
            const scoreA = document.getElementById('score-a-' + matchId).value;
            const scoreB = document.getElementById('score-b-' + matchId).value;

            if (scoreA === '' || scoreB === '') {
                alert('Please enter scores for both teams');
                return;
            }

            const formData = new FormData();
            formData.append('action', 'save_match_score');
            formData.append('match_id', matchId);
            formData.append('team1_score', scoreA);
            formData.append('team2_score', scoreB);
            formData.append('csrf_token', '<?php echo generateCSRFToken(); ?>');

            fetch('ajax/match-scoring.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Match score saved successfully!');
                    location.reload(); // Refresh to show updated data
                } else {
                    alert('Error saving match score: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Network error occurred');
            });
        }

        // Edit match score
        function editMatch(matchId) {
            const scoreAInput = document.getElementById('score-a-' + matchId);
            const scoreBInput = document.getElementById('score-b-' + matchId);

            scoreAInput.disabled = false;
            scoreBInput.disabled = false;

            // Change button to save
            const button = event.target;
            button.innerHTML = '<i class="fas fa-save"></i> Save';
            button.onclick = function() { saveMatch(matchId); };
            button.className = 'btn btn-success btn-sm';
        }

        // Generate matches
        function generateMatches() {
            if (confirm('This will generate tournament matches. Continue?')) {
                const formData = new FormData();
                formData.append('action', 'generate_matches');
                formData.append('category_id', '<?php echo $category_id; ?>');
                formData.append('event_id', '<?php echo $event_id; ?>');

                fetch('ajax/generate-matches.php', {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert('Matches generated successfully!');
                        location.reload();
                    } else {
                        alert('Error generating matches: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Network error occurred');
                });
            }
        }

        // Refresh fixtures
        function refreshFixtures() {
            location.reload();
        }

        // Refresh standings
        function refreshStandings() {
            location.reload();
        }

        // Auto-save on Enter key
        document.addEventListener('DOMContentLoaded', function() {
            const scoreInputs = document.querySelectorAll('.score-input');
            scoreInputs.forEach(input => {
                input.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        const matchId = this.id.split('-')[2];
                        saveMatch(matchId);
                    }
                });
            });
        });
    </script>
</body>
</html>
