<?php
/**
 * Public Tournament Bracket Display
 * Real-time tournament viewing for spectators
 */

require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

// Get parameters
$event_id = $_GET['event_id'] ?? 0;
$sport_id = $_GET['sport_id'] ?? 0;
$category_id = $_GET['category_id'] ?? 0;

if (!$event_id || !$sport_id) {
    die('Invalid parameters');
}

// Get event and sport information
try {
    $stmt = $conn->prepare("
        SELECT 
            e.name as event_name,
            s.name as sport_name,
            es.id as event_sport_id,
            tf.name as tournament_format_name
        FROM events e
        JOIN event_sports es ON e.id = es.event_id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE e.id = ? AND s.id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $event_info = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$event_info) {
        die('Event or sport not found');
    }
    
} catch (Exception $e) {
    die('Database error: ' . $e->getMessage());
}

// Get tournament data
$tournament_data = null;
try {
    $stmt = $conn->prepare("
        SELECT ts.*, tf.name as format_name
        FROM tournament_structures ts
        LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
        WHERE ts.event_sport_id = ? AND ts.status NOT IN ('cancelled')
        ORDER BY ts.created_at DESC LIMIT 1
    ");
    $stmt->execute([$event_info['event_sport_id']]);
    $tournament = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($tournament && $tournament['bracket_data']) {
        $tournament_data = json_decode($tournament['bracket_data'], true);
    }
    
} catch (Exception $e) {
    error_log("Error fetching tournament data: " . $e->getMessage());
}

// Get live matches
$live_matches = [];
try {
    $stmt = $conn->prepare("
        SELECT 
            m.*,
            t1.name as team1_name,
            t2.name as team2_name,
            tw.name as winner_name
        FROM matches m
        LEFT JOIN tournament_participants tp1 ON m.team1_id = tp1.id
        LEFT JOIN departments t1 ON tp1.department_id = t1.id
        LEFT JOIN tournament_participants tp2 ON m.team2_id = tp2.id
        LEFT JOIN departments t2 ON tp2.department_id = t2.id
        LEFT JOIN tournament_participants tpw ON m.winner_id = tpw.id
        LEFT JOIN departments tw ON tpw.department_id = tw.id
        WHERE m.tournament_structure_id = ? AND m.status = 'in_progress'
        ORDER BY m.round_number, m.match_number
    ");
    $stmt->execute([$tournament['id'] ?? 0]);
    $live_matches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (Exception $e) {
    error_log("Error fetching live matches: " . $e->getMessage());
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($event_info['event_name']); ?> - <?php echo htmlspecialchars($event_info['sport_name']); ?> Tournament</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .tournament-header {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px auto;
            max-width: 1200px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .tournament-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .tournament-subtitle {
            font-size: 1.2rem;
            color: #7f8c8d;
            margin-bottom: 20px;
        }
        
        .live-indicator {
            background: #e74c3c;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            display: inline-block;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
        
        .bracket-container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px auto;
            max-width: 1200px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow-x: auto;
        }
        
        .bracket-rounds {
            display: flex;
            gap: 30px;
            min-width: 800px;
        }
        
        .bracket-round {
            flex: 1;
            min-width: 200px;
        }
        
        .round-header {
            text-align: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
        }
        
        .round-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: #2c3e50;
            margin: 0;
        }
        
        .match-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .match-card.live {
            border-color: #e74c3c;
            background: #fff5f5;
            animation: glow 2s infinite;
        }
        
        .match-card.completed {
            border-color: #28a745;
            background: #f8fff9;
        }
        
        @keyframes glow {
            0% { box-shadow: 0 0 5px rgba(231, 76, 60, 0.5); }
            50% { box-shadow: 0 0 20px rgba(231, 76, 60, 0.8); }
            100% { box-shadow: 0 0 5px rgba(231, 76, 60, 0.5); }
        }
        
        .match-team {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 6px;
            background: white;
        }
        
        .match-team.winner {
            background: #d4edda;
            font-weight: 600;
            border: 1px solid #c3e6cb;
        }
        
        .team-name {
            font-weight: 500;
            color: #2c3e50;
        }
        
        .team-score {
            font-weight: 700;
            color: #3498db;
            font-size: 1.1rem;
        }
        
        .match-vs {
            text-align: center;
            font-size: 0.8rem;
            color: #7f8c8d;
            margin: 5px 0;
        }
        
        .live-matches {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px auto;
            max-width: 1200px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .live-match-card {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
        }
        
        .live-match-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .live-match-title {
            font-size: 1.1rem;
            font-weight: 600;
        }
        
        .live-teams {
            display: grid;
            grid-template-columns: 1fr auto 1fr;
            gap: 15px;
            align-items: center;
        }
        
        .live-team {
            text-align: center;
        }
        
        .live-team-name {
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .live-team-score {
            font-size: 2rem;
            font-weight: 900;
        }
        
        .live-vs {
            font-size: 1.5rem;
            font-weight: 700;
        }
        
        .no-tournament {
            text-align: center;
            padding: 60px 20px;
            color: #7f8c8d;
        }
        
        .no-tournament i {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.3;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
        }
        
        .refresh-btn:hover {
            background: #2980b9;
            transform: scale(1.1);
        }
        
        @media (max-width: 768px) {
            .tournament-title {
                font-size: 2rem;
            }
            
            .bracket-rounds {
                flex-direction: column;
                min-width: auto;
            }
            
            .live-teams {
                grid-template-columns: 1fr;
                gap: 10px;
            }
            
            .live-vs {
                order: -1;
            }
        }
    </style>
</head>
<body>
    <!-- Tournament Header -->
    <div class="tournament-header">
        <div class="tournament-title">
            <?php echo htmlspecialchars($event_info['event_name']); ?>
        </div>
        <div class="tournament-subtitle">
            <?php echo htmlspecialchars($event_info['sport_name']); ?> Tournament
            <?php if ($event_info['tournament_format_name']): ?>
            - <?php echo htmlspecialchars($event_info['tournament_format_name']); ?>
            <?php endif; ?>
        </div>
        <?php if (!empty($live_matches)): ?>
        <div class="live-indicator">
            <i class="fas fa-circle"></i> LIVE MATCHES
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Live Matches -->
    <?php if (!empty($live_matches)): ?>
    <div class="live-matches">
        <h3><i class="fas fa-broadcast-tower"></i> Live Matches</h3>
        <?php foreach ($live_matches as $match): ?>
        <div class="live-match-card">
            <div class="live-match-header">
                <div class="live-match-title">
                    Round <?php echo $match['round_number']; ?> - Match <?php echo $match['match_number']; ?>
                </div>
                <div class="live-indicator">
                    <i class="fas fa-circle"></i> LIVE
                </div>
            </div>
            <div class="live-teams">
                <div class="live-team">
                    <div class="live-team-name"><?php echo htmlspecialchars($match['team1_name'] ?? 'Team 1'); ?></div>
                    <div class="live-team-score"><?php echo $match['team1_score'] ?? 0; ?></div>
                </div>
                <div class="live-vs">VS</div>
                <div class="live-team">
                    <div class="live-team-name"><?php echo htmlspecialchars($match['team2_name'] ?? 'Team 2'); ?></div>
                    <div class="live-team-score"><?php echo $match['team2_score'] ?? 0; ?></div>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
    <?php endif; ?>
    
    <!-- Tournament Bracket -->
    <div class="bracket-container">
        <?php if ($tournament_data && isset($tournament_data['rounds'])): ?>
        <h3><i class="fas fa-sitemap"></i> Tournament Bracket</h3>
        <div class="bracket-rounds">
            <?php foreach ($tournament_data['rounds'] as $round): ?>
            <div class="bracket-round">
                <div class="round-header">
                    <div class="round-title"><?php echo htmlspecialchars($round['round_name']); ?></div>
                </div>
                <?php foreach ($round['matches'] as $match): ?>
                <div class="match-card <?php echo ($match['status'] ?? 'pending') === 'in_progress' ? 'live' : (($match['status'] ?? 'pending') === 'completed' ? 'completed' : ''); ?>">
                    <div class="match-team <?php echo isset($match['winner_id']) && $match['winner_id'] == $match['team1_id'] ? 'winner' : ''; ?>">
                        <span class="team-name"><?php echo htmlspecialchars($match['team1_name'] ?? 'TBD'); ?></span>
                        <?php if (isset($match['team1_score'])): ?>
                        <span class="team-score"><?php echo $match['team1_score']; ?></span>
                        <?php endif; ?>
                    </div>
                    <div class="match-vs">VS</div>
                    <div class="match-team <?php echo isset($match['winner_id']) && $match['winner_id'] == $match['team2_id'] ? 'winner' : ''; ?>">
                        <span class="team-name"><?php echo htmlspecialchars($match['team2_name'] ?? 'TBD'); ?></span>
                        <?php if (isset($match['team2_score'])): ?>
                        <span class="team-score"><?php echo $match['team2_score']; ?></span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php endforeach; ?>
        </div>
        <?php else: ?>
        <div class="no-tournament">
            <i class="fas fa-sitemap"></i>
            <h4>No Tournament Available</h4>
            <p>Tournament bracket will appear here once the tournament is generated</p>
        </div>
        <?php endif; ?>
    </div>
    
    <!-- Refresh Button -->
    <button class="refresh-btn" onclick="location.reload()" title="Refresh">
        <i class="fas fa-sync"></i>
    </button>
    
    <!-- Auto-refresh script -->
    <script>
        // Auto-refresh every 30 seconds for live updates
        setInterval(function() {
            location.reload();
        }, 30000);
        
        // Show last updated time
        console.log('Last updated:', new Date().toLocaleTimeString());
    </script>
</body>
</html>
