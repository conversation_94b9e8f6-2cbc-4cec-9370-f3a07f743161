<?php
/**
 * Check Tournament Status and Auto-Generate if Conditions are Met
 * SC_IMS Auto-Tournament Generation System
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/advanced_tournament_engine.php';
require_once '../../includes/tournament_algorithms_advanced.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $event_sport_id = $_POST['event_sport_id'] ?? 0;
    $category_id = $_POST['category_id'] ?? 0;
    
    if (!$event_sport_id || !$category_id) {
        echo json_encode(['error' => 'Missing required parameters']);
        exit;
    }
    
    // Check if tournament already exists
    $stmt = $conn->prepare("
        SELECT id, status, tournament_format_id, updated_at 
        FROM tournament_structures
        WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')
        ORDER BY created_at DESC LIMIT 1
    ");
    $stmt->execute([$event_sport_id]);
    $existing_tournament = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Get category and format information
    $stmt = $conn->prepare("
        SELECT
            sc.*,
            es.tournament_format_id,
            tf.min_participants as format_min_participants
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE sc.id = ?
    ");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$category) {
        echo json_encode(['error' => 'Category not found']);
        exit;
    }
    
    // Get current participants count from event registrations
    $stmt = $conn->prepare("
        SELECT COUNT(DISTINCT edr.department_id) as participant_count
        FROM event_department_registrations edr
        JOIN event_sports es ON edr.event_id = es.event_id
        WHERE es.id = ? AND edr.status IN ('pending', 'approved')
    ");
    $stmt->execute([$event_sport_id]);
    $participant_data = $stmt->fetch(PDO::FETCH_ASSOC);
    $participant_count = $participant_data['participant_count'] ?? 0;
    
    $min_participants = $category['format_min_participants'] ?? 2;
    $tournament_generated = false;
    $status_changed = false;
    $message = '';
    
    // Check if all conditions are met for auto-generation
    $has_format = !empty($category['tournament_format_id']);
    $has_referee = !empty($category['referee_name']);
    $has_participants = $participant_count >= $min_participants;

    // Auto-generate tournament if all conditions are met
    if (!$existing_tournament && $has_participants && $has_format && $has_referee) {
        try {
            // Use advanced tournament engine for auto-generation
            $engine = new AdvancedTournamentEngine($conn);
            $result = $engine->generateTournament($event_sport_id, $category_id, [
                'seeding_method' => 'random',
                'third_place_playoff' => false,
                'scoring_config' => [
                    'points_win' => 3,
                    'points_draw' => 1,
                    'points_loss' => 0
                ]
            ]);
            
            if ($result['success']) {
                $tournament_generated = true;
                $message = "Tournament automatically generated using {$result['format']} format with {$result['participants_count']} participants.";
                
                // Log the auto-generation
                error_log("Auto-generated tournament for event_sport_id: $event_sport_id, participants: {$result['participants_count']}");
            } else {
                $message = "Auto-generation failed: " . $result['message'];
                error_log("Auto-generation failed for event_sport_id: $event_sport_id - " . $result['message']);
            }
            
        } catch (Exception $e) {
            $message = "Auto-generation error: " . $e->getMessage();
            error_log("Auto-generation exception for event_sport_id: $event_sport_id - " . $e->getMessage());
        }
    } elseif (!$existing_tournament) {
        // Conditions not met - provide status
        $missing = [];
        if (!$has_participants) $missing[] = "participants ({$participant_count}/{$min_participants})";
        if (!$has_format) $missing[] = "tournament format";
        if (!$has_referee) $missing[] = "referee assignment";

        $message = "Waiting for: " . implode(', ', $missing);
    }
    
    // Check if existing tournament status has changed
    if ($existing_tournament) {
        $last_check = $_SESSION['last_tournament_check_' . $event_sport_id] ?? '';
        $current_updated = $existing_tournament['updated_at'];
        
        if ($last_check !== $current_updated) {
            $status_changed = true;
            $_SESSION['last_tournament_check_' . $event_sport_id] = $current_updated;
        }
    }
    
    // Response data
    $response = [
        'success' => true,
        'tournament_generated' => $tournament_generated,
        'status_changed' => $status_changed,
        'has_tournament' => !empty($existing_tournament),
        'participant_count' => $participant_count,
        'min_participants' => $min_participants,
        'conditions_met' => $participant_count >= $min_participants,
        'message' => $message
    ];
    
    echo json_encode($response);
    
} catch (Exception $e) {
    error_log("Tournament status check error: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'error' => 'Failed to check tournament status',
        'message' => $e->getMessage()
    ]);
}
?>
