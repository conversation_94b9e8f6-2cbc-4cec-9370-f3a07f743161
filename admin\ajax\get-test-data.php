<?php
/**
 * Get Test Data AJAX Endpoint
 * Returns available event-sport combinations for testing
 */

require_once '../auth.php';
require_once '../../config/database.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get available event-sport combinations
    $stmt = $conn->prepare("
        SELECT es.event_id, es.sport_id, e.name as event_name, s.name as sport_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        ORDER BY e.name, s.name
        LIMIT 10
    ");
    $stmt->execute();
    $event_sports = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'event_sports' => $event_sports
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error getting test data: ' . $e->getMessage()
    ]);
}
?>
