<?php
/**
 * Force Transaction Cleanup
 * This script will forcefully close any active transactions and reset the database connection
 */

require_once '../config/database.php';

echo "<h2>🔧 Force Transaction Cleanup</h2>";

try {
    // Create a fresh database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h3>Step 1: Check Current Transaction Status</h3>";
    $inTransaction = $conn->inTransaction();
    echo "<p><strong>Initial Status:</strong> " . ($inTransaction ? "ACTIVE TRANSACTION" : "NO TRANSACTION") . "</p>";
    
    if ($inTransaction) {
        echo "<p style='color: orange;'>⚠️ Found active transaction. Attempting cleanup...</p>";
        
        // Try multiple cleanup methods
        $methods = [
            'rollback' => function($conn) { return $conn->rollBack(); },
            'commit' => function($conn) { return $conn->commit(); }
        ];
        
        foreach ($methods as $method => $func) {
            try {
                if ($conn->inTransaction()) {
                    $func($conn);
                    echo "<p style='color: green;'>✅ Successfully used {$method} to close transaction</p>";
                    break;
                }
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ {$method} failed: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h3>Step 2: Create New Connection</h3>";
    // Force a new connection
    $database = new Database();
    $conn = $database->getConnection();
    
    $inTransaction = $conn->inTransaction();
    echo "<p><strong>New Connection Status:</strong> " . ($inTransaction ? "STILL ACTIVE" : "CLEAN") . "</p>";
    
    echo "<h3>Step 3: Test Transaction Functionality</h3>";
    
    // Test basic transaction functionality
    $conn->beginTransaction();
    echo "<p>✅ Started test transaction</p>";
    
    $conn->rollBack();
    echo "<p>✅ Rolled back test transaction</p>";
    
    // Test nested transaction handling
    $conn->beginTransaction();
    echo "<p>✅ Started outer transaction</p>";
    
    $inTransaction = $conn->inTransaction();
    if (!$inTransaction) {
        $conn->beginTransaction(); // This should not start a new transaction
    }
    echo "<p>✅ Tested nested transaction handling</p>";
    
    $conn->rollBack();
    echo "<p>✅ Cleaned up test transactions</p>";
    
    echo "<h3>Step 4: Clear Any Locks</h3>";
    
    // Clear any table locks
    try {
        $conn->exec("UNLOCK TABLES");
        echo "<p>✅ Cleared table locks</p>";
    } catch (Exception $e) {
        echo "<p>⚠️ No table locks to clear</p>";
    }
    
    // Reset connection
    try {
        $conn->exec("RESET CONNECTION");
        echo "<p>✅ Reset connection</p>";
    } catch (Exception $e) {
        echo "<p>⚠️ Connection reset not supported</p>";
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3 style='color: green;'>✅ Transaction Cleanup Complete!</h3>";
    echo "<p>The database connection has been cleaned and is ready for use.</p>";
    echo "</div>";
    
    echo "<h3>Next Steps</h3>";
    echo "<ol>";
    echo "<li>Go back to your category management page</li>";
    echo "<li>Try generating the tournament again</li>";
    echo "<li>The transaction errors should be resolved</li>";
    echo "</ol>";
    
    echo "<div style='margin-top: 20px;'>";
    echo "<a href='manage-category.php?event_id=4&sport_id=37&category_id=16' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Return to Category Management</a>";
    echo "<a href='test-tournament-generation.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Tournament Generation</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3 style='color: red;'>❌ Cleanup Failed</h3>";
    echo "<p><strong>Error:</strong> " . htmlspecialchars($e->getMessage()) . "</p>";
    echo "<p><strong>File:</strong> " . htmlspecialchars($e->getFile()) . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
    
    echo "<h3>Manual Recovery Steps</h3>";
    echo "<ol>";
    echo "<li>Restart your MySQL server</li>";
    echo "<li>Restart your web server (Apache/Nginx)</li>";
    echo "<li>Clear any PHP session files</li>";
    echo "<li>Try accessing the system again</li>";
    echo "</ol>";
}
?>
