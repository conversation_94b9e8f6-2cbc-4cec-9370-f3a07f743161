<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🔧 Fix Foreign Key Constraints</h1>";
echo "<p>Fixing foreign key constraint mismatches in tournament tables...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $fixes_applied = [];
    $errors = [];
    
    echo "<h2>1. Checking Current Foreign Key Constraints</h2>";
    
    // Get all foreign key constraints for tournament_participants
    $stmt = $conn->query("
        SELECT 
            CONSTRAINT_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'tournament_participants' 
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    $existing_fks = $stmt->fetchAll();
    
    echo "<h3>Current Foreign Keys in tournament_participants:</h3>";
    if (!empty($existing_fks)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>Constraint Name</th><th>Column</th><th>References Table</th><th>References Column</th><th>Action</th>";
        echo "</tr>";
        
        foreach ($existing_fks as $fk) {
            echo "<tr>";
            echo "<td>{$fk['CONSTRAINT_NAME']}</td>";
            echo "<td>{$fk['COLUMN_NAME']}</td>";
            echo "<td>{$fk['REFERENCED_TABLE_NAME']}</td>";
            echo "<td>{$fk['REFERENCED_COLUMN_NAME']}</td>";
            
            // Check if this is an old constraint that needs to be dropped
            if ($fk['REFERENCED_TABLE_NAME'] === 'tournaments' || $fk['COLUMN_NAME'] === 'tournament_id') {
                echo "<td style='color: red;'>❌ NEEDS REMOVAL</td>";
            } else {
                echo "<td style='color: green;'>✅ OK</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No foreign key constraints found.</p>";
    }
    
    echo "<h2>2. Checking Table Structure</h2>";
    
    // Check tournament_participants table structure
    try {
        $stmt = $conn->query("DESCRIBE tournament_participants");
        $columns = $stmt->fetchAll();
        $existing_columns = array_column($columns, 'Field');
        
        echo "<h3>tournament_participants columns:</h3>";
        echo "<ul>";
        foreach ($existing_columns as $col) {
            echo "<li>{$col}</li>";
        }
        echo "</ul>";
        
        $has_tournament_structure_id = in_array('tournament_structure_id', $existing_columns);
        $has_tournament_id = in_array('tournament_id', $existing_columns);
        
        echo "<p><strong>Has tournament_structure_id:</strong> " . ($has_tournament_structure_id ? "✅ Yes" : "❌ No") . "</p>";
        echo "<p><strong>Has tournament_id:</strong> " . ($has_tournament_id ? "⚠️ Yes (legacy)" : "✅ No") . "</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error checking tournament_participants: " . $e->getMessage() . "</p>";
        $errors[] = "Cannot check tournament_participants structure: " . $e->getMessage();
    }
    
    echo "<h2>3. Fixing Foreign Key Constraints</h2>";
    
    // Step 1: Drop old foreign key constraints
    foreach ($existing_fks as $fk) {
        if ($fk['REFERENCED_TABLE_NAME'] === 'tournaments' || $fk['COLUMN_NAME'] === 'tournament_id') {
            echo "<h4>Dropping old constraint: {$fk['CONSTRAINT_NAME']}</h4>";
            try {
                $sql = "ALTER TABLE tournament_participants DROP FOREIGN KEY {$fk['CONSTRAINT_NAME']}";
                $conn->exec($sql);
                echo "<p style='color: green;'>✅ Dropped old constraint: {$fk['CONSTRAINT_NAME']}</p>";
                $fixes_applied[] = "Dropped old constraint: {$fk['CONSTRAINT_NAME']}";
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Failed to drop {$fk['CONSTRAINT_NAME']}: " . $e->getMessage() . "</p>";
                $errors[] = "Failed to drop {$fk['CONSTRAINT_NAME']}: " . $e->getMessage();
            }
        }
    }
    
    // Step 2: Drop old tournament_id column if it exists
    if (in_array('tournament_id', $existing_columns)) {
        echo "<h4>Dropping old tournament_id column</h4>";
        try {
            $sql = "ALTER TABLE tournament_participants DROP COLUMN tournament_id";
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Dropped old tournament_id column</p>";
            $fixes_applied[] = "Dropped old tournament_id column";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to drop tournament_id column: " . $e->getMessage() . "</p>";
            $errors[] = "Failed to drop tournament_id column: " . $e->getMessage();
        }
    }
    
    // Step 3: Ensure tournament_structure_id column exists
    if (!in_array('tournament_structure_id', $existing_columns)) {
        echo "<h4>Adding tournament_structure_id column</h4>";
        try {
            $sql = "ALTER TABLE tournament_participants ADD COLUMN tournament_structure_id INT NOT NULL";
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Added tournament_structure_id column</p>";
            $fixes_applied[] = "Added tournament_structure_id column";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to add tournament_structure_id column: " . $e->getMessage() . "</p>";
            $errors[] = "Failed to add tournament_structure_id column: " . $e->getMessage();
        }
    }
    
    // Step 4: Ensure registration_id column exists
    if (!in_array('registration_id', $existing_columns)) {
        echo "<h4>Adding registration_id column</h4>";
        try {
            $sql = "ALTER TABLE tournament_participants ADD COLUMN registration_id INT NOT NULL";
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Added registration_id column</p>";
            $fixes_applied[] = "Added registration_id column";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Failed to add registration_id column: " . $e->getMessage() . "</p>";
            $errors[] = "Failed to add registration_id column: " . $e->getMessage();
        }
    }
    
    echo "<h2>4. Adding Correct Foreign Key Constraints</h2>";
    
    // Check if tournament_structures table exists
    try {
        $conn->query("SELECT 1 FROM tournament_structures LIMIT 1");
        echo "<p style='color: green;'>✅ tournament_structures table exists</p>";
        
        // Add correct foreign key constraint for tournament_structure_id
        try {
            $sql = "ALTER TABLE tournament_participants 
                    ADD CONSTRAINT fk_tp_tournament_structure 
                    FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) ON DELETE CASCADE";
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Added correct foreign key constraint for tournament_structure_id</p>";
            $fixes_applied[] = "Added correct foreign key constraint for tournament_structure_id";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<p style='color: green;'>✅ Foreign key constraint for tournament_structure_id already exists</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Foreign key constraint issue: " . $e->getMessage() . "</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ tournament_structures table does not exist</p>";
        $errors[] = "tournament_structures table does not exist";
    }
    
    // Check if registrations table exists and add foreign key
    try {
        $conn->query("SELECT 1 FROM registrations LIMIT 1");
        echo "<p style='color: green;'>✅ registrations table exists</p>";
        
        // Add foreign key constraint for registration_id
        try {
            $sql = "ALTER TABLE tournament_participants 
                    ADD CONSTRAINT fk_tp_registration 
                    FOREIGN KEY (registration_id) REFERENCES registrations(id) ON DELETE CASCADE";
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Added foreign key constraint for registration_id</p>";
            $fixes_applied[] = "Added foreign key constraint for registration_id";
        } catch (Exception $e) {
            if (strpos($e->getMessage(), 'Duplicate key name') !== false) {
                echo "<p style='color: green;'>✅ Foreign key constraint for registration_id already exists</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Foreign key constraint issue: " . $e->getMessage() . "</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ registrations table does not exist</p>";
        $errors[] = "registrations table does not exist";
    }
    
    echo "<h2>5. Verifying Current Participants Data</h2>";
    
    // Get current event sport and participants
    $event_id = 4;
    $sport_id = 37;
    $category_id = 15;
    
    echo "<h3>Current Event Sport Participants:</h3>";
    
    try {
        // Get event sport ID
        $stmt = $conn->prepare("
            SELECT es.id as event_sport_id
            FROM sport_categories sc
            JOIN event_sports es ON sc.event_sport_id = es.id
            WHERE sc.id = ? AND es.event_id = ? AND es.sport_id = ?
        ");
        $stmt->execute([$category_id, $event_id, $sport_id]);
        $category = $stmt->fetch();
        
        if ($category) {
            echo "<p><strong>Event Sport ID:</strong> {$category['event_sport_id']}</p>";
            
            // Get participants from registrations
            $stmt = $conn->prepare("
                SELECT 
                    r.id,
                    r.department_id,
                    d.name as department_name,
                    r.status,
                    JSON_LENGTH(r.participants) as participant_count
                FROM registrations r
                JOIN departments d ON r.department_id = d.id
                WHERE r.event_sport_id = ? AND r.status IN ('confirmed', 'registered', 'approved')
                ORDER BY d.name
            ");
            $stmt->execute([$category['event_sport_id']]);
            $participants = $stmt->fetchAll();
            
            echo "<p><strong>Available Participants:</strong> " . count($participants) . "</p>";
            
            if (!empty($participants)) {
                echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
                echo "<tr style='background: #f5f5f5;'>";
                echo "<th>Registration ID</th><th>Department</th><th>Status</th><th>Participants</th>";
                echo "</tr>";
                foreach ($participants as $p) {
                    echo "<tr>";
                    echo "<td>{$p['id']}</td>";
                    echo "<td>{$p['department_name']}</td>";
                    echo "<td>{$p['status']}</td>";
                    echo "<td>{$p['participant_count']}</td>";
                    echo "</tr>";
                }
                echo "</table>";
            }
        } else {
            echo "<p style='color: red;'>❌ Category not found</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error getting participants: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>6. Summary</h2>";
    
    if (!empty($fixes_applied)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>✅ Fixes Applied Successfully</h3>";
        echo "<ul>";
        foreach ($fixes_applied as $fix) {
            echo "<li>{$fix}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (!empty($errors)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Errors Encountered</h3>";
        echo "<ul>";
        foreach ($errors as $error) {
            echo "<li>{$error}</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    if (empty($errors)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🎉 Foreign Key Constraints Fixed!</h3>";
        echo "<p>The database should now be ready for tournament auto-generation.</p>";
        echo "<p><a href='manage-category.php?category_id=15&event_id=4&sport_id=37' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🚀 Test Auto-Generation</a></p>";
        echo "</div>";
    }
    
    echo "<h3>Next Steps:</h3>";
    echo "<p><a href='tournament-status-dashboard.php?event_id=4&sport_id=37&category_id=15' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔍 Check Overall Status</a></p>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Critical Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
