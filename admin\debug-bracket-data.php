<?php
require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get parameters from URL
$event_id = $_GET['event_id'] ?? 0;
$sport_id = $_GET['sport_id'] ?? 0;
$category_id = $_GET['category_id'] ?? 0;

echo "<h1>Bracket Data Debug</h1>";
echo "<p>Event ID: $event_id, Sport ID: $sport_id, Category ID: $category_id</p>";

// Check if we have the category
try {
    $stmt = $conn->prepare("
        SELECT
            sc.*,
            e.name as event_name,
            s.name as sport_name,
            s.type as sport_type,
            es.id as event_sport_id,
            tf.name as format_name
        FROM sport_categories sc
        JOIN events e ON sc.event_id = e.id
        JOIN sports s ON sc.sport_id = s.id
        LEFT JOIN event_sports es ON (es.event_id = sc.event_id AND es.sport_id = sc.sport_id)
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE sc.id = ?
    ");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();
    
    if ($category) {
        echo "<h2>Category Found:</h2>";
        echo "<pre>" . print_r($category, true) . "</pre>";
        
        // Check for tournament structure
        $stmt = $conn->prepare("
            SELECT * FROM tournament_structures
            WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')
            ORDER BY created_at DESC LIMIT 1
        ");
        $stmt->execute([$category['event_sport_id']]);
        $tournament = $stmt->fetch();
        
        if ($tournament) {
            echo "<h2>Tournament Structure Found:</h2>";
            echo "<pre>" . print_r($tournament, true) . "</pre>";
            
            // Check for matches
            $stmt = $conn->prepare("
                SELECT 
                    m.*,
                    d1.name as team1_name,
                    d1.abbreviation as team1_department,
                    d2.name as team2_name,
                    d2.abbreviation as team2_department
                FROM tournament_matches m
                LEFT JOIN departments d1 ON m.team1_id = d1.id
                LEFT JOIN departments d2 ON m.team2_id = d2.id
                WHERE m.tournament_structure_id = ?
                ORDER BY m.round_number, m.match_number
            ");
            $stmt->execute([$tournament['id']]);
            $matches = $stmt->fetchAll();
            
            echo "<h2>Matches Found (" . count($matches) . "):</h2>";
            if (!empty($matches)) {
                echo "<pre>" . print_r($matches, true) . "</pre>";
                
                // Test the enhanced bracket display
                echo "<h2>Enhanced Bracket Display Test:</h2>";
                require_once 'includes/bracket_display.php';
                $bracketDisplay = new BracketDisplay($conn, $tournament['id']);
                echo $bracketDisplay->renderBracket();
                
            } else {
                echo "<p>No matches found for this tournament.</p>";
                
                // Create sample matches for testing
                echo "<h3>Creating Sample Matches for Testing:</h3>";
                
                // Get some departments
                $stmt = $conn->prepare("SELECT id, name, abbreviation FROM departments LIMIT 4");
                $stmt->execute();
                $departments = $stmt->fetchAll();
                
                if (count($departments) >= 2) {
                    // Create sample matches
                    $stmt = $conn->prepare("
                        INSERT INTO tournament_matches 
                        (tournament_structure_id, round_number, match_number, team1_id, team2_id, status, created_at)
                        VALUES (?, ?, ?, ?, ?, ?, NOW())
                    ");
                    
                    // Match 1
                    $stmt->execute([
                        $tournament['id'], 1, 1, 
                        $departments[0]['id'], 
                        $departments[1]['id'] ?? null, 
                        'pending'
                    ]);
                    
                    // Match 2 if we have more departments
                    if (count($departments) >= 4) {
                        $stmt->execute([
                            $tournament['id'], 1, 2, 
                            $departments[2]['id'], 
                            $departments[3]['id'], 
                            'pending'
                        ]);
                    }
                    
                    echo "<p>✅ Sample matches created! Refresh the page to see the bracket.</p>";
                } else {
                    echo "<p>❌ Not enough departments to create sample matches.</p>";
                }
            }
        } else {
            echo "<h2>No Tournament Structure Found</h2>";
            echo "<p>Creating tournament structure for testing...</p>";
            
            // Create a tournament structure
            $stmt = $conn->prepare("
                INSERT INTO tournament_structures 
                (event_sport_id, tournament_format_id, status, created_at)
                VALUES (?, 1, 'active', NOW())
            ");
            $stmt->execute([$category['event_sport_id']]);
            $new_tournament_id = $conn->lastInsertId();
            
            echo "<p>✅ Tournament structure created with ID: $new_tournament_id</p>";
            echo "<p><a href='?event_id=$event_id&sport_id=$sport_id&category_id=$category_id'>Refresh to continue</a></p>";
        }
        
    } else {
        echo "<h2>❌ Category Not Found</h2>";
        echo "<p>Please check the category ID and try again.</p>";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Database Error</h2>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
}

// Add CSS for better display
echo "<style>
body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; }
h1, h2, h3 { color: #007bff; }
p { margin: 10px 0; }
a { color: #007bff; text-decoration: none; }
a:hover { text-decoration: underline; }
</style>";

// Include the enhanced bracket CSS
echo "<link rel='stylesheet' href='assets/css/bracket-styles.css'>";
echo "<link rel='stylesheet' href='assets/css/bracket-modals.css'>";
echo "<link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css'>";
?>
