<!-- Tournament Configuration Modal -->
<div class="modal fade" id="tournamentConfigModal" tabindex="-1" role="dialog" aria-labelledby="tournamentConfigModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="tournamentConfigModalLabel">
                    <i class="fas fa-cogs"></i> Tournament Configuration
                </h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="tournamentConfigForm">
                    <input type="hidden" id="config_event_sport_id" name="event_sport_id">
                    
                    <!-- Tournament Format Selection -->
                    <div class="form-group">
                        <label for="tournament_format">
                            <i class="fas fa-trophy"></i> Tournament Format
                        </label>
                        <select class="form-control" id="tournament_format" name="format_id" required>
                            <option value="">Select Tournament Format...</option>
                        </select>
                        <small class="form-text text-muted">
                            Choose the tournament format based on sport type and participant count.
                        </small>
                    </div>
                    
                    <!-- Format Description -->
                    <div id="format_description" class="alert alert-info" style="display: none;">
                        <i class="fas fa-info-circle"></i>
                        <span id="format_description_text"></span>
                    </div>
                    
                    <!-- Seeding Configuration -->
                    <div class="form-group">
                        <label for="seeding_method">
                            <i class="fas fa-sort-numeric-down"></i> Seeding Method
                        </label>
                        <select class="form-control" id="seeding_method" name="seeding_method">
                            <option value="random">Random Seeding</option>
                            <option value="ranking">Ranking-Based Seeding</option>
                            <option value="manual">Manual Seeding</option>
                            <option value="hybrid">Hybrid Seeding (Top 4 ranked, rest random)</option>
                        </select>
                        <small class="form-text text-muted">
                            Determines how participants are arranged in the tournament bracket.
                        </small>
                    </div>
                    
                    <!-- Format-Specific Configuration -->
                    <div id="format_specific_config">
                        <!-- Dynamic content will be loaded here -->
                    </div>
                    
                    <!-- Advanced Options -->
                    <div class="card mt-3">
                        <div class="card-header">
                            <h6 class="mb-0">
                                <i class="fas fa-sliders-h"></i> Advanced Options
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="auto_schedule" name="auto_schedule" checked>
                                <label class="form-check-label" for="auto_schedule">
                                    Automatically schedule matches
                                </label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="enable_notifications" name="enable_notifications" checked>
                                <label class="form-check-label" for="enable_notifications">
                                    Enable match notifications
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Tournament Preview -->
                    <div id="tournament_preview" class="mt-3" style="display: none;">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-eye"></i> Tournament Preview
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-primary" id="preview_participants">0</h4>
                                            <small class="text-muted">Participants</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-success" id="preview_rounds">0</h4>
                                            <small class="text-muted">Rounds</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-warning" id="preview_matches">0</h4>
                                            <small class="text-muted">Total Matches</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="text-center">
                                            <h4 class="text-info" id="preview_duration">0</h4>
                                            <small class="text-muted">Est. Duration (hrs)</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        Preview is based on current configuration and participant count.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="button" class="btn btn-info" id="previewTournamentBtn">
                    <i class="fas fa-eye"></i> Preview
                </button>
                <button type="button" class="btn btn-primary" id="generateTournamentBtn">
                    <i class="fas fa-play"></i> Generate Tournament
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Tournament Configuration Modal Styles */
#tournamentConfigModal .modal-dialog {
    max-width: 800px;
}

#tournamentConfigModal .form-group label {
    font-weight: 600;
    color: #495057;
}

#tournamentConfigModal .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

#tournamentConfigModal .form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

#tournamentConfigModal .card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

#tournamentConfigModal .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
}

#tournamentConfigModal .card-header h6 {
    color: #495057;
    margin: 0;
}

#tournamentConfigModal .form-check {
    margin-bottom: 0.5rem;
}

#tournamentConfigModal .form-check-label {
    color: #495057;
    font-size: 0.9rem;
}

#tournamentConfigModal .alert {
    border-radius: 6px;
    border: none;
    padding: 0.75rem 1rem;
}

#tournamentConfigModal .alert-info {
    background-color: #e7f3ff;
    color: #0c5460;
}

#tournamentConfigModal .btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.5rem 1rem;
}

#tournamentConfigModal .btn i {
    margin-right: 0.5rem;
}

/* Format-specific configuration styles */
#format_specific_config .form-group {
    margin-bottom: 1rem;
}

#format_specific_config .form-row .col-md-6 {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

/* Tournament preview styles */
#tournament_preview .text-center h4 {
    margin-bottom: 0.25rem;
    font-weight: 600;
}

#tournament_preview .text-muted {
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Loading states */
.loading-spinner {
    display: inline-block;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #tournamentConfigModal .modal-dialog {
        max-width: 95%;
        margin: 1rem auto;
    }
    
    #tournament_preview .col-md-3 {
        margin-bottom: 1rem;
    }
}
</style>

<script>
// Tournament Configuration Modal JavaScript
$(document).ready(function() {
    let currentEventSportId = null;
    let currentParticipantCount = 0;
    
    // Initialize tournament configuration modal
    window.openTournamentConfig = function(eventSportId, participantCount) {
        currentEventSportId = eventSportId;
        currentParticipantCount = participantCount;
        
        $('#config_event_sport_id').val(eventSportId);
        $('#tournamentConfigModal').modal('show');
        
        // Load available tournament formats
        loadTournamentFormats(eventSportId, participantCount);
    };
    
    // Load tournament formats
    function loadTournamentFormats(eventSportId, participantCount) {
        // Get sport ID from event_sport
        $.ajax({
            url: 'ajax/tournament-configuration.php',
            method: 'POST',
            data: {
                action: 'get_format_options',
                event_sport_id: eventSportId,
                participant_count: participantCount
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    populateFormatOptions(response.formats);
                } else {
                    showAlert('Error loading tournament formats: ' + response.message, 'danger');
                }
            },
            error: function() {
                showAlert('Failed to load tournament formats', 'danger');
            }
        });
    }
    
    // Populate format options
    function populateFormatOptions(formats) {
        const $select = $('#tournament_format');
        $select.empty().append('<option value="">Select Tournament Format...</option>');
        
        formats.forEach(function(format) {
            $select.append(`<option value="${format.id}" data-description="${format.description}">${format.name}</option>`);
        });
    }
    
    // Handle format selection change
    $('#tournament_format').on('change', function() {
        const formatId = $(this).val();
        const description = $(this).find('option:selected').data('description');
        
        if (formatId) {
            // Show format description
            $('#format_description_text').text(description);
            $('#format_description').show();
            
            // Load format-specific configuration
            loadFormatConfiguration(formatId);
        } else {
            $('#format_description').hide();
            $('#format_specific_config').empty();
            $('#tournament_preview').hide();
        }
    });
    
    // Load format-specific configuration
    function loadFormatConfiguration(formatId) {
        $.ajax({
            url: 'ajax/tournament-configuration.php',
            method: 'POST',
            data: {
                action: 'get_format_config',
                format_id: formatId
            },
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    renderFormatConfiguration(response.config_options);
                } else {
                    showAlert('Error loading format configuration: ' + response.message, 'danger');
                }
            },
            error: function() {
                showAlert('Failed to load format configuration', 'danger');
            }
        });
    }
    
    // Render format-specific configuration
    function renderFormatConfiguration(configOptions) {
        const $container = $('#format_specific_config');
        $container.empty();
        
        if (Object.keys(configOptions).length === 0) {
            return;
        }
        
        $container.append('<h6 class="mt-3 mb-3"><i class="fas fa-cog"></i> Format-Specific Settings</h6>');
        
        Object.keys(configOptions).forEach(function(key) {
            const option = configOptions[key];
            const fieldHtml = renderConfigField(key, option);
            $container.append(fieldHtml);
        });
    }
    
    // Render individual configuration field
    function renderConfigField(key, option) {
        let fieldHtml = '<div class="form-group">';
        fieldHtml += `<label for="config_${key}">${option.label}</label>`;
        
        switch (option.type) {
            case 'select':
                fieldHtml += `<select class="form-control" id="config_${key}" name="config[${key}]">`;
                Object.keys(option.options).forEach(function(value) {
                    const selected = value === option.default ? 'selected' : '';
                    fieldHtml += `<option value="${value}" ${selected}>${option.options[value]}</option>`;
                });
                fieldHtml += '</select>';
                break;
                
            case 'number':
                const min = option.min !== undefined ? `min="${option.min}"` : '';
                const max = option.max !== undefined ? `max="${option.max}"` : '';
                const step = option.step !== undefined ? `step="${option.step}"` : '';
                fieldHtml += `<input type="number" class="form-control" id="config_${key}" name="config[${key}]" value="${option.default}" ${min} ${max} ${step}>`;
                break;
                
            case 'checkbox':
                const checked = option.default ? 'checked' : '';
                fieldHtml += `<div class="form-check">`;
                fieldHtml += `<input class="form-check-input" type="checkbox" id="config_${key}" name="config[${key}]" ${checked}>`;
                fieldHtml += `<label class="form-check-label" for="config_${key}">${option.label}</label>`;
                fieldHtml += `</div>`;
                break;
                
            default:
                fieldHtml += `<input type="text" class="form-control" id="config_${key}" name="config[${key}]" value="${option.default || ''}">`;
        }
        
        fieldHtml += '</div>';
        return fieldHtml;
    }
    
    // Preview tournament
    $('#previewTournamentBtn').on('click', function() {
        const formData = new FormData($('#tournamentConfigForm')[0]);
        formData.append('action', 'preview_bracket');
        
        const $btn = $(this);
        const originalText = $btn.html();
        $btn.html('<span class="loading-spinner"></span>Generating Preview...').prop('disabled', true);
        
        $.ajax({
            url: 'ajax/tournament-configuration.php',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    displayTournamentPreview(response.statistics);
                } else {
                    showAlert('Preview failed: ' + response.message, 'warning');
                }
            },
            error: function() {
                showAlert('Failed to generate preview', 'danger');
            },
            complete: function() {
                $btn.html(originalText).prop('disabled', false);
            }
        });
    });
    
    // Display tournament preview
    function displayTournamentPreview(stats) {
        $('#preview_participants').text(stats.participant_count);
        $('#preview_rounds').text(stats.total_rounds);
        $('#preview_matches').text(stats.total_matches);
        $('#preview_duration').text(Math.ceil(stats.total_matches * 0.5)); // Estimate 30 min per match
        $('#tournament_preview').show();
    }
    
    // Generate tournament
    $('#generateTournamentBtn').on('click', function() {
        const formData = new FormData($('#tournamentConfigForm')[0]);
        formData.append('action', 'generate_tournament');
        
        const $btn = $(this);
        const originalText = $btn.html();
        $btn.html('<span class="loading-spinner"></span>Generating Tournament...').prop('disabled', true);
        
        $.ajax({
            url: 'ajax/tournament-configuration.php',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    showAlert(`Tournament generated successfully! Format: ${response.format}, Participants: ${response.participant_count}`, 'success');
                    $('#tournamentConfigModal').modal('hide');
                    
                    // Refresh the page or update the fixtures tab
                    if (typeof refreshFixtures === 'function') {
                        refreshFixtures();
                    } else {
                        location.reload();
                    }
                } else {
                    showAlert('Tournament generation failed: ' + response.message, 'danger');
                }
            },
            error: function() {
                showAlert('Failed to generate tournament', 'danger');
            },
            complete: function() {
                $btn.html(originalText).prop('disabled', false);
            }
        });
    });
    
    // Show alert function
    function showAlert(message, type) {
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        `;
        
        // Insert alert at the top of modal body
        $('#tournamentConfigModal .modal-body').prepend(alertHtml);
        
        // Auto-dismiss after 5 seconds
        setTimeout(function() {
            $('#tournamentConfigModal .alert').fadeOut();
        }, 5000);
    }
    
    // Reset modal when closed
    $('#tournamentConfigModal').on('hidden.bs.modal', function() {
        $('#tournamentConfigForm')[0].reset();
        $('#format_description').hide();
        $('#format_specific_config').empty();
        $('#tournament_preview').hide();
        $('#tournamentConfigModal .alert').remove();
    });
});
</script>
