/* SC_IMS Tournament Bracket Styles - Unique Visual Design */

/* Main Bracket Container */
.sc-bracket-container {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

.sc-bracket-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #28a745, #ffc107, #dc3545);
    border-radius: 15px 15px 0 0;
}

/* Bracket Notice */
.sc-bracket-notice {
    background: rgba(0, 123, 255, 0.1);
    border: 2px solid #007bff;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 20px;
    text-align: center;
    color: #007bff;
    font-weight: 600;
}

.sc-bracket-notice i {
    margin-right: 8px;
    font-size: 1.2em;
}

/* Round Container */
.sc-round {
    margin-bottom: 30px;
    position: relative;
}

.sc-round-header {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    padding: 12px 20px;
    border-radius: 10px 10px 0 0;
    font-weight: bold;
    font-size: 1.1em;
    text-align: center;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.sc-round-matches {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    padding: 20px;
    background: white;
    border-radius: 0 0 10px 10px;
    border: 2px solid #e9ecef;
    border-top: none;
}

/* Enhanced Match Card Design */
.sc-match-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 6px 25px rgba(0, 0, 0, 0.08);
    border: 2px solid #e9ecef;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    overflow: hidden;
    position: relative;
}

.sc-match-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

.sc-match-card.sc-match-completed {
    border-color: #28a745;
    background: linear-gradient(135deg, #ffffff 0%, #f8fff9 100%);
}

.sc-match-card.sc-match-active {
    border-color: #ffc107;
    background: linear-gradient(135deg, #ffffff 0%, #fffef8 100%);
    animation: pulse-active 2s infinite;
}

.sc-match-card.sc-match-pending {
    border-color: #6c757d;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

@keyframes pulse-active {
    0%, 100% { box-shadow: 0 6px 25px rgba(255, 193, 7, 0.2); }
    50% { box-shadow: 0 8px 35px rgba(255, 193, 7, 0.4); }
}

/* Match Header */
.sc-match-header {
    background: linear-gradient(135deg, #495057, #343a40);
    color: white;
    padding: 12px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.sc-match-info {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.sc-match-number {
    font-weight: bold;
    font-size: 0.9em;
    color: #ffc107;
}

.sc-match-format {
    font-size: 0.75em;
    opacity: 0.8;
}

.sc-match-actions {
    display: flex;
    gap: 8px;
}

.sc-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    padding: 6px 10px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.85em;
}

.sc-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.05);
}

.sc-btn-edit:hover {
    background: #007bff;
    border-color: #007bff;
}

.sc-btn-referee:hover {
    background: #28a745;
    border-color: #28a745;
}

/* Match Content */
.sc-match-content {
    padding: 20px 15px;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

/* Team Container */
.sc-team-container {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 10px;
    background: #f8f9fa;
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.sc-team-container.winner {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border-color: #28a745;
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
}

.sc-team-container.winner .sc-team-name {
    font-weight: bold;
    color: #155724;
}

.sc-team-avatar {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2em;
    flex-shrink: 0;
}

.sc-team-container.winner .sc-team-avatar {
    background: linear-gradient(135deg, #28a745, #1e7e34);
}

.sc-team-details {
    flex: 1;
    min-width: 0;
}

.sc-team-name {
    font-weight: 600;
    font-size: 1em;
    color: #343a40;
    margin-bottom: 2px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sc-team-department {
    font-size: 0.8em;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sc-team-score {
    background: #e9ecef;
    border-radius: 8px;
    padding: 8px 12px;
    min-width: 50px;
    text-align: center;
    flex-shrink: 0;
}

.sc-team-container.winner .sc-team-score {
    background: #28a745;
    color: white;
}

.sc-score-value {
    font-weight: bold;
    font-size: 1.1em;
}

/* VS Separator */
.sc-match-vs {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 5px 0;
}

.sc-vs-circle {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #6c757d, #495057);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.9em;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Match Footer */
.sc-match-footer {
    background: #f8f9fa;
    padding: 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e9ecef;
}

.sc-status-badge {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    padding: 4px 10px;
    border-radius: 15px;
    font-size: 0.8em;
    font-weight: 600;
    text-transform: uppercase;
}

.sc-status-pending {
    background: #6c757d;
    color: white;
}

.sc-status-in_progress {
    background: #ffc107;
    color: #212529;
}

.sc-status-completed {
    background: #28a745;
    color: white;
}

.sc-match-time {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #6c757d;
    font-size: 0.85em;
}

/* Responsive Design */
@media (max-width: 768px) {
    .sc-bracket-container {
        padding: 15px;
        margin: 10px 0;
    }
    
    .sc-round-matches {
        grid-template-columns: 1fr;
        gap: 15px;
        padding: 15px;
    }
    
    .sc-match-card {
        border-radius: 10px;
    }
    
    .sc-team-container {
        padding: 10px;
    }
    
    .sc-team-avatar {
        width: 35px;
        height: 35px;
        font-size: 1em;
    }
    
    .sc-match-actions {
        gap: 5px;
    }
    
    .sc-btn {
        padding: 5px 8px;
        font-size: 0.8em;
    }
}

@media (max-width: 480px) {
    .sc-match-content {
        padding: 15px 10px;
    }
    
    .sc-team-name {
        font-size: 0.9em;
    }
    
    .sc-team-department {
        font-size: 0.75em;
    }
    
    .sc-vs-circle {
        width: 40px;
        height: 40px;
        font-size: 0.8em;
    }
}
