<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🧪 Direct Tournament Creation Test</h1>";
echo "<p>Testing tournament creation directly to identify the exact issue...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Test parameters
    $event_id = 4;
    $sport_id = 37;
    $category_id = 15;
    
    echo "<h2>1. Get Event Sport</h2>";
    
    $stmt = $conn->prepare("
        SELECT es.*, e.name as event_name, s.name as sport_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    
    if (!$event_sport) {
        echo "<p style='color: red;'>❌ Event sport not found!</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Event Sport Found: {$event_sport['event_name']} - {$event_sport['sport_name']}</p>";
    
    echo "<h2>2. Clear Any Existing Tournaments</h2>";
    
    // Cancel any existing active tournaments to allow new creation
    $stmt = $conn->prepare("
        UPDATE tournament_structures 
        SET status = 'cancelled' 
        WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')
    ");
    $stmt->execute([$event_sport['id']]);
    $cancelled_count = $stmt->rowCount();
    
    if ($cancelled_count > 0) {
        echo "<p style='color: orange;'>⚠️ Cancelled {$cancelled_count} existing active tournaments</p>";
    } else {
        echo "<p style='color: blue;'>ℹ️ No existing active tournaments to cancel</p>";
    }
    
    echo "<h2>3. Test Tournament Manager Creation</h2>";
    
    try {
        require_once '../includes/tournament_manager.php';
        $tournamentManager = new TournamentManager($conn);
        echo "<p style='color: green;'>✅ TournamentManager loaded</p>";
        
        // Get participants
        $participants = $tournamentManager->getEventSportParticipants($event_sport['id']);
        echo "<p>Found " . count($participants) . " participants</p>";
        
        if (count($participants) < 2) {
            echo "<p style='color: red;'>❌ Not enough participants</p>";
            exit;
        }
        
        // Get tournament format
        $format_id = $event_sport['tournament_format_id'];
        if (!$format_id) {
            // Get default format
            $stmt = $conn->prepare("SELECT id FROM tournament_formats ORDER BY id LIMIT 1");
            $stmt->execute();
            $format_id = $stmt->fetchColumn();
        }
        
        echo "<p>Using tournament format ID: {$format_id}</p>";
        
        // Test tournament creation
        $tournament_name = "Direct Test Tournament - " . date('Y-m-d H:i:s');
        $config = [
            'seeding_method' => 'random',
            'bracket_seeding' => true,
            'auto_generated' => true,
            'comprehensive_validation' => true,
            'fallback_enabled' => true
        ];
        
        echo "<h3>Creating Tournament...</h3>";
        echo "<p>Name: {$tournament_name}</p>";
        echo "<p>Event Sport ID: {$event_sport['id']}</p>";
        echo "<p>Format ID: {$format_id}</p>";
        echo "<p>Participants: " . count($participants) . "</p>";
        
        $tournament_id = $tournamentManager->createTournament(
            $event_sport['id'],
            $format_id,
            $tournament_name,
            $config
        );
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🎉 <strong>SUCCESS!</strong></h3>";
        echo "<p>Tournament created successfully with ID: <strong>{$tournament_id}</strong></p>";
        echo "</div>";
        
        // Verify tournament was created
        $stmt = $conn->prepare("SELECT * FROM tournament_structures WHERE id = ?");
        $stmt->execute([$tournament_id]);
        $tournament = $stmt->fetch();
        
        if ($tournament) {
            echo "<h3>Tournament Details:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><td><strong>ID</strong></td><td>{$tournament['id']}</td></tr>";
            echo "<tr><td><strong>Name</strong></td><td>{$tournament['name']}</td></tr>";
            echo "<tr><td><strong>Status</strong></td><td>{$tournament['status']}</td></tr>";
            echo "<tr><td><strong>Participants</strong></td><td>{$tournament['participant_count']}</td></tr>";
            echo "<tr><td><strong>Rounds</strong></td><td>{$tournament['total_rounds']}</td></tr>";
            echo "</table>";
            
            // Check if matches were created
            $stmt = $conn->prepare("SELECT COUNT(*) as match_count FROM matches WHERE tournament_structure_id = ?");
            $stmt->execute([$tournament_id]);
            $match_count = $stmt->fetch()['match_count'];
            
            echo "<p>Matches created: {$match_count}</p>";
            
            if ($match_count > 0) {
                echo "<p style='color: green;'>✅ Tournament fully created with matches!</p>";
            } else {
                echo "<p style='color: orange;'>⚠️ Tournament created but no matches found</p>";
            }
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ <strong>TOURNAMENT CREATION FAILED</strong></h3>";
        echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
        echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
        echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
        echo "<details>";
        echo "<summary>Full Stack Trace</summary>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
        echo "</details>";
        echo "</div>";
        
        // Try to identify the specific issue
        echo "<h3>🔍 Diagnostic Information:</h3>";
        
        // Check if algorithm class exists
        $stmt = $conn->prepare("SELECT algorithm_class FROM tournament_formats WHERE id = ?");
        $stmt->execute([$format_id]);
        $algorithm_class = $stmt->fetchColumn();
        
        if ($algorithm_class) {
            echo "<p>Algorithm class: {$algorithm_class}</p>";
            
            try {
                require_once '../includes/tournament_algorithms.php';
                if (class_exists($algorithm_class)) {
                    echo "<p style='color: green;'>✅ Algorithm class exists</p>";
                } else {
                    echo "<p style='color: red;'>❌ Algorithm class does not exist</p>";
                }
            } catch (Exception $alg_e) {
                echo "<p style='color: red;'>❌ Error loading algorithm: " . $alg_e->getMessage() . "</p>";
            }
        }
        
        // Check database tables
        $required_tables = ['tournament_structures', 'tournament_rounds', 'tournament_participants'];
        foreach ($required_tables as $table) {
            try {
                $stmt = $conn->prepare("SHOW TABLES LIKE ?");
                $stmt->execute([$table]);
                if ($stmt->fetch()) {
                    echo "<p style='color: green;'>✅ Table {$table} exists</p>";
                } else {
                    echo "<p style='color: red;'>❌ Table {$table} missing</p>";
                }
            } catch (Exception $table_e) {
                echo "<p style='color: red;'>❌ Error checking table {$table}: " . $table_e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h2>4. Test AJAX Endpoint</h2>";
    
    echo "<p>Testing the actual AJAX endpoint that the frontend calls...</p>";
    
    // Simulate the AJAX request
    $_POST['action'] = 'generate_tournament_bracket';
    $_POST['category_id'] = $category_id;
    $_POST['auto_generated'] = 'true';
    
    echo "<p>Simulating AJAX request with:</p>";
    echo "<ul>";
    echo "<li>action: generate_tournament_bracket</li>";
    echo "<li>category_id: {$category_id}</li>";
    echo "<li>auto_generated: true</li>";
    echo "</ul>";
    
    ob_start();
    try {
        include 'ajax/tournament-bracket-generator.php';
        $ajax_output = ob_get_contents();
    } catch (Exception $ajax_e) {
        $ajax_output = "AJAX Error: " . $ajax_e->getMessage();
    }
    ob_end_clean();
    
    echo "<h3>AJAX Response:</h3>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px;'>";
    echo htmlspecialchars($ajax_output);
    echo "</pre>";
    
    // Try to parse as JSON
    $ajax_data = json_decode($ajax_output, true);
    if ($ajax_data) {
        if ($ajax_data['success']) {
            echo "<p style='color: green;'>✅ AJAX endpoint returned success</p>";
        } else {
            echo "<p style='color: red;'>❌ AJAX endpoint returned error: " . ($ajax_data['message'] ?? 'Unknown error') . "</p>";
        }
    } else {
        echo "<p style='color: orange;'>⚠️ AJAX response is not valid JSON</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Fatal Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}
?>
