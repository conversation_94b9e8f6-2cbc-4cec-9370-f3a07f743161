<?php
/**
 * Debug Category Navigation Issues
 * Investigate why category links redirect to events.php
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get URL parameters
$event_id = $_GET['event_id'] ?? null;
$sport_id = $_GET['sport_id'] ?? null;
$category_id = $_GET['category_id'] ?? null;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Category Navigation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🔍 Debug Category Navigation</h1>
        <p>Investigating why category links redirect to events.php instead of manage-category.php</p>
        
        <div class="debug-section">
            <h2>1. URL Parameters Analysis</h2>
            <table class="table table-bordered">
                <tr>
                    <th>Parameter</th>
                    <th>Value</th>
                    <th>Type</th>
                    <th>Status</th>
                </tr>
                <tr>
                    <td>event_id</td>
                    <td><?php echo htmlspecialchars($event_id ?? 'NULL'); ?></td>
                    <td><?php echo gettype($event_id); ?></td>
                    <td class="<?php echo $event_id ? 'success' : 'error'; ?>">
                        <?php echo $event_id ? '✅ Valid' : '❌ Missing/Invalid'; ?>
                    </td>
                </tr>
                <tr>
                    <td>sport_id</td>
                    <td><?php echo htmlspecialchars($sport_id ?? 'NULL'); ?></td>
                    <td><?php echo gettype($sport_id); ?></td>
                    <td class="<?php echo $sport_id ? 'success' : 'error'; ?>">
                        <?php echo $sport_id ? '✅ Valid' : '❌ Missing/Invalid'; ?>
                    </td>
                </tr>
                <tr>
                    <td>category_id</td>
                    <td><?php echo htmlspecialchars($category_id ?? 'NULL'); ?></td>
                    <td><?php echo gettype($category_id); ?></td>
                    <td class="<?php echo $category_id ? 'success' : 'error'; ?>">
                        <?php echo $category_id ? '✅ Valid' : '❌ Missing/Invalid'; ?>
                    </td>
                </tr>
            </table>
            
            <?php
            $params_valid = $event_id && $sport_id && $category_id;
            echo "<p class='" . ($params_valid ? 'success' : 'error') . "'>";
            echo $params_valid ? "✅ All parameters are present" : "❌ One or more parameters are missing";
            echo "</p>";
            ?>
        </div>
        
        <div class="debug-section">
            <h2>2. Database Validation</h2>
            
            <?php if ($params_valid): ?>
                <h4>Testing Database Queries:</h4>
                
                <!-- Test 1: Check if event exists -->
                <?php
                try {
                    $stmt = $conn->prepare("SELECT id, name FROM events WHERE id = ?");
                    $stmt->execute([$event_id]);
                    $event = $stmt->fetch();
                    
                    echo "<div class='alert alert-" . ($event ? 'success' : 'danger') . "'>";
                    echo "<strong>Event Check:</strong> ";
                    if ($event) {
                        echo "✅ Event found: " . htmlspecialchars($event['name']);
                    } else {
                        echo "❌ Event with ID $event_id not found";
                    }
                    echo "</div>";
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'><strong>Event Query Error:</strong> " . $e->getMessage() . "</div>";
                }
                ?>
                
                <!-- Test 2: Check if sport exists -->
                <?php
                try {
                    $stmt = $conn->prepare("SELECT id, name FROM sports WHERE id = ?");
                    $stmt->execute([$sport_id]);
                    $sport = $stmt->fetch();
                    
                    echo "<div class='alert alert-" . ($sport ? 'success' : 'danger') . "'>";
                    echo "<strong>Sport Check:</strong> ";
                    if ($sport) {
                        echo "✅ Sport found: " . htmlspecialchars($sport['name']);
                    } else {
                        echo "❌ Sport with ID $sport_id not found";
                    }
                    echo "</div>";
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'><strong>Sport Query Error:</strong> " . $e->getMessage() . "</div>";
                }
                ?>
                
                <!-- Test 3: Check if event_sports relationship exists -->
                <?php
                try {
                    $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
                    $stmt->execute([$event_id, $sport_id]);
                    $event_sport = $stmt->fetch();
                    
                    echo "<div class='alert alert-" . ($event_sport ? 'success' : 'danger') . "'>";
                    echo "<strong>Event-Sport Relationship:</strong> ";
                    if ($event_sport) {
                        echo "✅ Event-Sport relationship found (ID: " . $event_sport['id'] . ")";
                    } else {
                        echo "❌ No relationship found between Event $event_id and Sport $sport_id";
                    }
                    echo "</div>";
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'><strong>Event-Sport Query Error:</strong> " . $e->getMessage() . "</div>";
                }
                ?>
                
                <!-- Test 4: Check if category exists -->
                <?php
                try {
                    $stmt = $conn->prepare("SELECT id, category_name FROM sport_categories WHERE id = ?");
                    $stmt->execute([$category_id]);
                    $category = $stmt->fetch();
                    
                    echo "<div class='alert alert-" . ($category ? 'success' : 'danger') . "'>";
                    echo "<strong>Category Check:</strong> ";
                    if ($category) {
                        echo "✅ Category found: " . htmlspecialchars($category['category_name']);
                    } else {
                        echo "❌ Category with ID $category_id not found";
                    }
                    echo "</div>";
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'><strong>Category Query Error:</strong> " . $e->getMessage() . "</div>";
                }
                ?>
                
                <!-- Test 5: Full join query (same as manage-category.php) -->
                <?php
                try {
                    $sql = "SELECT
                                sc.id as category_id,
                                sc.category_name,
                                sc.category_type,
                                sc.referee_name,
                                sc.referee_email,
                                sc.venue,
                                es.id as event_sport_id,
                                s.id as sport_id,
                                s.name as sport_name,
                                s.type as sport_type,
                                e.name as event_name
                            FROM sport_categories sc
                            JOIN event_sports es ON sc.event_sport_id = es.id
                            JOIN sports s ON es.sport_id = s.id
                            JOIN events e ON es.event_id = e.id
                            WHERE sc.id = ? AND es.event_id = ? AND s.id = ?";
                    
                    $stmt = $conn->prepare($sql);
                    $stmt->execute([$category_id, $event_id, $sport_id]);
                    $full_category = $stmt->fetch();
                    
                    echo "<div class='alert alert-" . ($full_category ? 'success' : 'danger') . "'>";
                    echo "<strong>Full Join Query:</strong> ";
                    if ($full_category) {
                        echo "✅ Full category data found:<br>";
                        echo "Event: " . htmlspecialchars($full_category['event_name']) . "<br>";
                        echo "Sport: " . htmlspecialchars($full_category['sport_name']) . "<br>";
                        echo "Category: " . htmlspecialchars($full_category['category_name']);
                    } else {
                        echo "❌ Full join query failed - this is why manage-category.php redirects!";
                    }
                    echo "</div>";
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'><strong>Full Join Query Error:</strong> " . $e->getMessage() . "</div>";
                }
                ?>
                
            <?php else: ?>
                <div class="alert alert-warning">
                    <strong>Cannot test database queries:</strong> Required parameters are missing.
                </div>
            <?php endif; ?>
        </div>
        
        <div class="debug-section">
            <h2>3. Available Test Data</h2>
            <h4>Available Events with Sports and Categories:</h4>
            
            <?php
            try {
                $sql = "SELECT 
                            e.id as event_id, e.name as event_name,
                            s.id as sport_id, s.name as sport_name,
                            sc.id as category_id, sc.category_name,
                            es.id as event_sport_id
                        FROM events e
                        JOIN event_sports es ON e.id = es.event_id
                        JOIN sports s ON es.sport_id = s.id
                        LEFT JOIN sport_categories sc ON es.id = sc.event_sport_id
                        ORDER BY e.name, s.name, sc.category_name";
                
                $stmt = $conn->prepare($sql);
                $stmt->execute();
                $test_data = $stmt->fetchAll();
                
                if ($test_data) {
                    echo "<table class='table table-striped'>";
                    echo "<thead><tr><th>Event</th><th>Sport</th><th>Category</th><th>Test Link</th></tr></thead>";
                    echo "<tbody>";
                    
                    foreach ($test_data as $row) {
                        $link_class = $row['category_id'] ? 'btn-success' : 'btn-secondary';
                        $link_text = $row['category_id'] ? 'Test Navigation' : 'No Categories';
                        $link_url = $row['category_id'] ? 
                            "manage-category.php?event_id={$row['event_id']}&sport_id={$row['sport_id']}&category_id={$row['category_id']}" : 
                            "#";
                        
                        echo "<tr>";
                        echo "<td>" . htmlspecialchars($row['event_name']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['sport_name']) . "</td>";
                        echo "<td>" . htmlspecialchars($row['category_name'] ?? 'No categories') . "</td>";
                        echo "<td>";
                        if ($row['category_id']) {
                            echo "<a href='$link_url' class='btn $link_class btn-sm' target='_blank'>$link_text</a>";
                        } else {
                            echo "<span class='text-muted'>$link_text</span>";
                        }
                        echo "</td>";
                        echo "</tr>";
                    }
                    
                    echo "</tbody></table>";
                } else {
                    echo "<div class='alert alert-warning'>No test data found in the database.</div>";
                }
            } catch (Exception $e) {
                echo "<div class='alert alert-danger'><strong>Test Data Query Error:</strong> " . $e->getMessage() . "</div>";
            }
            ?>
        </div>
        
        <div class="debug-section">
            <h2>4. Recommended Actions</h2>
            <div class="alert alert-info">
                <h5>🔧 Next Steps:</h5>
                <ol>
                    <li><strong>If parameters are missing:</strong> Check the URL generation in sport-categories.php</li>
                    <li><strong>If database queries fail:</strong> Check if categories exist and are properly linked</li>
                    <li><strong>If full join fails:</strong> Verify the event_sport_id relationship in sport_categories table</li>
                    <li><strong>Test with working data:</strong> Use the test links above to verify navigation</li>
                </ol>
            </div>
        </div>
    </div>
</body>
</html>
