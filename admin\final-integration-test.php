<?php
/**
 * Final Integration Test
 * End-to-end testing of the complete enhanced tournament management system
 */

require_once '../config/database.php';
require_once 'auth.php';
require_once '../includes/advanced_tournament_engine.php';
require_once '../includes/tournament_algorithms_advanced.php';

requireAdmin();

$database = new Database();
$conn = $database->getConnection();

echo "<h1>🏆 Final Integration Test - Enhanced Tournament Management System</h1>";

$test_results = [];
$workflow_steps = [];

// Step 1: Verify Unified Registration System
echo "<h2>👥 Step 1: Unified Registration System Verification</h2>";

try {
    // Check event department registrations
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM event_department_registrations edr
        JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
        WHERE edr.event_id = 4
    ");
    $stmt->execute();
    $registration_count = $stmt->fetch()['count'];
    
    if ($registration_count > 0) {
        echo "<p style='color: green;'>✅ Unified registration system active with $registration_count sport participations</p>";
        $test_results['registration'] = true;
        $workflow_steps[] = "✅ Departments registered for event with automatic sport participation";
    } else {
        echo "<p style='color: red;'>❌ No unified registrations found</p>";
        $test_results['registration'] = false;
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Registration verification failed: " . $e->getMessage() . "</p>";
    $test_results['registration'] = false;
}

// Step 2: Test Automatic Data Inheritance
echo "<h2>🔄 Step 2: Automatic Data Inheritance</h2>";

try {
    // Get event sport with participants
    $stmt = $conn->prepare("
        SELECT 
            es.id as event_sport_id,
            es.tournament_format_id,
            tf.name as format_name,
            COUNT(dsp.id) as participant_count
        FROM event_sports es
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        JOIN event_department_registrations edr ON es.event_id = edr.event_id
        JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id AND dsp.event_sport_id = es.id
        WHERE es.event_id = 4
        GROUP BY es.id, es.tournament_format_id, tf.name
        HAVING participant_count >= 2
        LIMIT 1
    ");
    $stmt->execute();
    $event_sport = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($event_sport) {
        echo "<p style='color: green;'>✅ Data inheritance working: Event Sport ID {$event_sport['event_sport_id']}</p>";
        echo "<p>Tournament Format: {$event_sport['format_name']}</p>";
        echo "<p>Participants: {$event_sport['participant_count']}</p>";
        $test_results['inheritance'] = true;
        $workflow_steps[] = "✅ Tournament format and participants automatically inherited from event configuration";
    } else {
        echo "<p style='color: red;'>❌ No suitable event sport found for inheritance test</p>";
        $test_results['inheritance'] = false;
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Data inheritance test failed: " . $e->getMessage() . "</p>";
    $test_results['inheritance'] = false;
}

// Step 3: Test Advanced Tournament Generation
if ($event_sport) {
    echo "<h2>⚙️ Step 3: Advanced Tournament Generation</h2>";
    
    try {
        $engine = new AdvancedTournamentEngine($conn);
        $result = $engine->generateTournament($event_sport['event_sport_id'], null, [
            'seeding_method' => 'random',
            'test_mode' => true
        ]);
        
        if ($result['success']) {
            echo "<p style='color: green;'>✅ Tournament generated successfully</p>";
            echo "<p>Tournament ID: {$result['tournament_id']}</p>";
            echo "<p>Format: {$result['format']}</p>";
            echo "<p>Participants: {$result['participants_count']}</p>";
            $test_results['generation'] = true;
            $workflow_steps[] = "✅ Advanced tournament engine generated bracket with {$result['participants_count']} participants";
            
            $tournament_id = $result['tournament_id'];
        } else {
            echo "<p style='color: red;'>❌ Tournament generation failed: {$result['message']}</p>";
            $test_results['generation'] = false;
            $tournament_id = null;
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Tournament generation test failed: " . $e->getMessage() . "</p>";
        $test_results['generation'] = false;
        $tournament_id = null;
    }
} else {
    $test_results['generation'] = false;
    $tournament_id = null;
}

// Step 4: Test Interactive Match Management
if ($tournament_id) {
    echo "<h2>🎮 Step 4: Interactive Match Management</h2>";
    
    try {
        // Get a match from the generated tournament
        $stmt = $conn->prepare("
            SELECT m.*, 
                   d1.name as team1_name, 
                   d2.name as team2_name
            FROM matches m
            LEFT JOIN tournament_participants tp1 ON m.team1_id = tp1.id
            LEFT JOIN departments d1 ON tp1.department_id = d1.id
            LEFT JOIN tournament_participants tp2 ON m.team2_id = tp2.id
            LEFT JOIN departments d2 ON tp2.department_id = d2.id
            WHERE m.tournament_structure_id = ?
            ORDER BY m.round_number, m.match_number
            LIMIT 1
        ");
        $stmt->execute([$tournament_id]);
        $test_match = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($test_match) {
            echo "<p style='color: green;'>✅ Match management interface ready</p>";
            echo "<p>Test Match: {$test_match['team1_name']} vs {$test_match['team2_name']}</p>";
            
            // Test match update
            $stmt = $conn->prepare("
                UPDATE matches 
                SET team1_score = 3, team2_score = 1, status = 'completed', winner_id = team1_id
                WHERE id = ?
            ");
            $stmt->execute([$test_match['id']]);
            
            echo "<p style='color: green;'>✅ Match result updated successfully</p>";
            $test_results['match_management'] = true;
            $workflow_steps[] = "✅ Interactive match management allows real-time score updates";
            
            $match_id = $test_match['id'];
        } else {
            echo "<p style='color: red;'>❌ No matches found in generated tournament</p>";
            $test_results['match_management'] = false;
            $match_id = null;
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Match management test failed: " . $e->getMessage() . "</p>";
        $test_results['match_management'] = false;
        $match_id = null;
    }
} else {
    $test_results['match_management'] = false;
    $match_id = null;
}

// Step 5: Test Live Scoring Integration
if ($match_id) {
    echo "<h2>📱 Step 5: Live Scoring Integration</h2>";
    
    try {
        // Create referee session
        $session_token = bin2hex(random_bytes(16));
        
        $stmt = $conn->prepare("
            INSERT INTO referee_sessions (match_id, session_token, status)
            VALUES (?, ?, 'active')
            ON DUPLICATE KEY UPDATE session_token = ?, status = 'active'
        ");
        $stmt->execute([$match_id, $session_token, $session_token]);
        
        echo "<p style='color: green;'>✅ Referee session created</p>";
        echo "<p>Session Token: $session_token</p>";
        
        $referee_url = "/SC_IMS/referee/live-scoring.php?token=" . $session_token;
        echo "<p>Referee URL: <a href='$referee_url' target='_blank'>$referee_url</a></p>";
        
        $test_results['live_scoring'] = true;
        $workflow_steps[] = "✅ Live scoring interface accessible for real-time match management";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Live scoring test failed: " . $e->getMessage() . "</p>";
        $test_results['live_scoring'] = false;
    }
} else {
    $test_results['live_scoring'] = false;
}

// Step 6: Test Public Tournament View
echo "<h2>🌐 Step 6: Public Tournament View</h2>";

try {
    $public_url = "/SC_IMS/public/tournament-bracket.php?event_id=4&sport_id=37";
    
    if (file_exists('../public/tournament-bracket.php')) {
        echo "<p style='color: green;'>✅ Public tournament view available</p>";
        echo "<p>Public URL: <a href='$public_url' target='_blank'>$public_url</a></p>";
        $test_results['public_view'] = true;
        $workflow_steps[] = "✅ Public tournament view provides real-time bracket display for spectators";
    } else {
        echo "<p style='color: red;'>❌ Public tournament view file missing</p>";
        $test_results['public_view'] = false;
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Public view test failed: " . $e->getMessage() . "</p>";
    $test_results['public_view'] = false;
}

// Step 7: Test Admin Interface Integration
echo "<h2>🔧 Step 7: Admin Interface Integration</h2>";

try {
    $admin_url = "/SC_IMS/admin/manage-category.php?event_id=4&sport_id=37&category_id=16";
    
    if (file_exists('manage-category.php')) {
        echo "<p style='color: green;'>✅ Enhanced admin interface available</p>";
        echo "<p>Admin URL: <a href='$admin_url' target='_blank'>$admin_url</a></p>";
        
        // Check for enhanced features in manage-category.php
        $content = file_get_contents('manage-category.php');
        $enhanced_features = [
            'AdvancedTournamentEngine' => 'Advanced Tournament Engine',
            'BracketDisplay' => 'Bracket Display Component',
            'tournament_format_name' => 'Format Name Display',
            'generateTournament()' => 'Tournament Generation Function'
        ];
        
        $features_found = 0;
        foreach ($enhanced_features as $feature => $description) {
            if (strpos($content, $feature) !== false) {
                $features_found++;
                echo "<p style='color: green; margin-left: 20px;'>✅ $description integrated</p>";
            }
        }
        
        if ($features_found == count($enhanced_features)) {
            $test_results['admin_interface'] = true;
            $workflow_steps[] = "✅ Admin interface enhanced with advanced tournament management features";
        } else {
            $test_results['admin_interface'] = false;
        }
    } else {
        echo "<p style='color: red;'>❌ Admin interface file missing</p>";
        $test_results['admin_interface'] = false;
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Admin interface test failed: " . $e->getMessage() . "</p>";
    $test_results['admin_interface'] = false;
}

// Final Results
echo "<h2>📊 Final Integration Test Results</h2>";

$passed_tests = array_sum($test_results);
$total_tests = count($test_results);

echo "<div style='background: " . ($passed_tests == $total_tests ? '#d4edda' : '#f8d7da') . "; padding: 25px; border-radius: 15px; margin: 30px 0; border: 2px solid " . ($passed_tests == $total_tests ? '#c3e6cb' : '#f5c6cb') . ";'>";

if ($passed_tests == $total_tests) {
    echo "<h3 style='color: #155724; margin-top: 0;'>🎉 INTEGRATION TEST PASSED - SYSTEM READY!</h3>";
    echo "<p style='color: #155724; font-size: 1.1rem;'><strong>All $total_tests integration tests passed successfully!</strong></p>";
} else {
    echo "<h3 style='color: #721c24; margin-top: 0;'>⚠️ INTEGRATION TEST INCOMPLETE</h3>";
    echo "<p style='color: #721c24; font-size: 1.1rem;'><strong>$passed_tests of $total_tests tests passed</strong></p>";
}

echo "<h4>🔄 Complete Workflow Verified:</h4>";
echo "<ol>";
foreach ($workflow_steps as $step) {
    echo "<li>$step</li>";
}
echo "</ol>";

echo "<h4>📋 Test Results Breakdown:</h4>";
echo "<ul>";
foreach ($test_results as $test => $result) {
    $status = $result ? '✅ PASSED' : '❌ FAILED';
    $test_name = ucwords(str_replace('_', ' ', $test));
    echo "<li><strong>$test_name:</strong> $status</li>";
}
echo "</ul>";

if ($passed_tests == $total_tests) {
    echo "<h4>🚀 System Capabilities:</h4>";
    echo "<ul>";
    echo "<li>✅ Automatic data inheritance from unified registration system</li>";
    echo "<li>✅ Advanced bracket generation with multiple tournament formats</li>";
    echo "<li>✅ Interactive match management with real-time updates</li>";
    echo "<li>✅ Live scoring interface for referees</li>";
    echo "<li>✅ Real-time public tournament viewing</li>";
    echo "<li>✅ Enhanced admin interface with comprehensive tournament management</li>";
    echo "<li>✅ End-to-end workflow from registration to tournament completion</li>";
    echo "</ul>";
    
    echo "<h4>🔗 Quick Access Links:</h4>";
    echo "<ul>";
    echo "<li><a href='manage-category.php?event_id=4&sport_id=37&category_id=16' target='_blank'>🔧 Admin Tournament Management</a></li>";
    echo "<li><a href='../public/tournament-bracket.php?event_id=4&sport_id=37' target='_blank'>🌐 Public Tournament View</a></li>";
    if (isset($referee_url)) {
        echo "<li><a href='$referee_url' target='_blank'>👨‍⚖️ Referee Live Scoring</a></li>";
    }
    echo "<li><a href='comprehensive-test.php' target='_blank'>📋 Full Test Suite</a></li>";
    echo "</ul>";
}

echo "</div>";

// Performance metrics
echo "<h2>⚡ Performance Metrics</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 10px;'>";
echo "<p><strong>Integration Test Completed:</strong> " . date('Y-m-d H:i:s') . "</p>";
echo "<p><strong>Total Tests:</strong> $total_tests</p>";
echo "<p><strong>Success Rate:</strong> " . round(($passed_tests / $total_tests) * 100, 1) . "%</p>";
echo "<p><strong>System Status:</strong> " . ($passed_tests == $total_tests ? '🟢 OPERATIONAL' : '🟡 NEEDS ATTENTION') . "</p>";
echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
}

h2 {
    color: #34495e;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    margin-top: 40px;
    margin-bottom: 20px;
}

h3, h4 {
    color: #2c3e50;
}

a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
}

a:hover {
    text-decoration: underline;
    color: #2980b9;
}

ul, ol {
    margin: 15px 0;
    padding-left: 25px;
}

li {
    margin: 8px 0;
}

p {
    margin: 10px 0;
}
</style>
