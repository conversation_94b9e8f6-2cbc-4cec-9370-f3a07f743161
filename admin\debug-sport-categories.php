<?php
/**
 * Debug Sport Categories - Investigate Navigation Issue
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🐛 Debug Sport Categories Navigation</h1>";

// Get URL parameters
$event_id = $_GET['event_id'] ?? 0;
$sport_id = $_GET['sport_id'] ?? 0;

echo "<h2>1. URL Parameters</h2>";
echo "<p><strong>event_id:</strong> $event_id</p>";
echo "<p><strong>sport_id:</strong> $sport_id</p>";

if (!$event_id || !$sport_id) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Missing Parameters</h3>";
    echo "<p>event_id and sport_id are required. Redirecting to events.php would occur here.</p>";
    echo "</div>";
    echo "<p><a href='events.php'>← Back to Events</a></p>";
    exit;
}

echo "<h2>2. Event and Sport Information</h2>";

try {
    // Get event and sport information
    $stmt = $conn->prepare("
        SELECT e.name as event_name, s.name as sport_name, es.id as event_sport_id
        FROM events e
        JOIN event_sports es ON e.id = es.event_id
        JOIN sports s ON es.sport_id = s.id
        WHERE e.id = ? AND s.id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();

    if (!$event_sport) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ Event-Sport Relationship Not Found</h3>";
        echo "<p>No relationship found between event_id=$event_id and sport_id=$sport_id</p>";
        echo "</div>";
        
        // Show available relationships
        echo "<h3>Available Event-Sport Relationships:</h3>";
        $stmt = $conn->prepare("
            SELECT es.id, es.event_id, es.sport_id, e.name as event_name, s.name as sport_name
            FROM event_sports es
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            ORDER BY e.name, s.name
        ");
        $stmt->execute();
        $relationships = $stmt->fetchAll();
        
        if (!empty($relationships)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Event ID</th><th>Sport ID</th><th>Event Name</th><th>Sport Name</th><th>Test Link</th></tr>";
            foreach ($relationships as $rel) {
                $test_url = "debug-sport-categories.php?event_id={$rel['event_id']}&sport_id={$rel['sport_id']}";
                echo "<tr>";
                echo "<td>{$rel['event_id']}</td>";
                echo "<td>{$rel['sport_id']}</td>";
                echo "<td>{$rel['event_name']}</td>";
                echo "<td>{$rel['sport_name']}</td>";
                echo "<td><a href='$test_url'>Test</a></td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No event-sport relationships found in database.</p>";
        }
        exit;
    }

    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
    echo "<h3>✅ Event-Sport Found</h3>";
    echo "<p><strong>Event:</strong> {$event_sport['event_name']}</p>";
    echo "<p><strong>Sport:</strong> {$event_sport['sport_name']}</p>";
    echo "<p><strong>Event-Sport ID:</strong> {$event_sport['event_sport_id']}</p>";
    echo "</div>";

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
    exit;
}

echo "<h2>3. Sport Categories</h2>";

try {
    // Get all categories for this sport in this event
    $stmt = $conn->prepare("
        SELECT * FROM sport_categories 
        WHERE event_sport_id = ? 
        ORDER BY category_name ASC
    ");
    $stmt->execute([$event_sport['event_sport_id']]);
    $categories = $stmt->fetchAll();

    if (empty($categories)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<h3>⚠️ No Categories Found</h3>";
        echo "<p>No categories found for event_sport_id = {$event_sport['event_sport_id']}</p>";
        echo "</div>";
        
        // Show all categories in database
        echo "<h3>All Categories in Database:</h3>";
        $stmt = $conn->prepare("
            SELECT sc.*, es.event_id, es.sport_id, e.name as event_name, s.name as sport_name
            FROM sport_categories sc
            JOIN event_sports es ON sc.event_sport_id = es.id
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            ORDER BY e.name, s.name, sc.category_name
        ");
        $stmt->execute();
        $all_categories = $stmt->fetchAll();
        
        if (!empty($all_categories)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
            echo "<tr><th>Category ID</th><th>Category Name</th><th>Event</th><th>Sport</th><th>Event ID</th><th>Sport ID</th><th>Test Link</th></tr>";
            foreach ($all_categories as $cat) {
                $test_url = "debug-sport-categories.php?event_id={$cat['event_id']}&sport_id={$cat['sport_id']}";
                echo "<tr>";
                echo "<td>{$cat['id']}</td>";
                echo "<td>{$cat['category_name']}</td>";
                echo "<td>{$cat['event_name']}</td>";
                echo "<td>{$cat['sport_name']}</td>";
                echo "<td>{$cat['event_id']}</td>";
                echo "<td>{$cat['sport_id']}</td>";
                echo "<td><a href='$test_url'>Test</a></td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p>No categories found in database at all.</p>";
            echo "<p><a href='comprehensive-database-fix.php'>Create Test Data</a></p>";
        }
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Categories Found</h3>";
        echo "<p>Found " . count($categories) . " categories for this sport.</p>";
        echo "</div>";

        echo "<h3>Category Details and Navigation Links:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Category ID</th><th>Category Name</th><th>Type</th><th>Navigation Link</th><th>Test Link</th></tr>";
        
        foreach ($categories as $category) {
            $manage_url = "manage-category.php?event_id=$event_id&sport_id=$sport_id&category_id={$category['id']}";
            $debug_url = "manage-category.php?event_id=$event_id&sport_id=$sport_id&category_id={$category['id']}&debug=1";
            
            echo "<tr>";
            echo "<td>{$category['id']}</td>";
            echo "<td>{$category['category_name']}</td>";
            echo "<td>{$category['category_type']}</td>";
            echo "<td><a href='$manage_url' target='_blank'>Normal Link</a></td>";
            echo "<td><a href='$debug_url' target='_blank'>Debug Link</a></td>";
            echo "</tr>";
        }
        echo "</table>";

        echo "<h3>Simulated sport-categories.php Table:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Category Name</th><th>Type</th><th>Referee</th><th>Email</th><th>Venue</th><th>Actions</th></tr>";
        
        foreach ($categories as $category) {
            $manage_url = "manage-category.php?event_id=$event_id&sport_id=$sport_id&category_id={$category['id']}";
            
            echo "<tr>";
            echo "<td>";
            echo "<strong>";
            echo "<a href='$manage_url' style='color: #007bff; text-decoration: none; font-weight: 600;' title='Manage {$category['category_name']}'>";
            echo "🏆 {$category['category_name']}";
            echo "</a>";
            echo "</strong>";
            echo "</td>";
            echo "<td><span style='background: #e3f2fd; color: #1976d2; padding: 4px 8px; border-radius: 12px; font-size: 0.75rem;'>" . ucfirst($category['category_type']) . "</span></td>";
            echo "<td>" . ($category['referee_name'] ?? 'N/A') . "</td>";
            echo "<td>" . ($category['referee_email'] ?? 'N/A') . "</td>";
            echo "<td>" . ($category['venue'] ?? 'N/A') . "</td>";
            echo "<td>Edit | Delete</td>";
            echo "</tr>";
        }
        echo "</table>";
    }

} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>4. Current URL for Testing</h2>";
$current_url = "sport-categories.php?event_id=$event_id&sport_id=$sport_id";
echo "<p><strong>Current sport-categories.php URL:</strong></p>";
echo "<p><a href='$current_url' target='_blank'>$current_url</a></p>";

echo "<h2>5. Recommendations</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<h4>Next Steps:</h4>";
echo "<ul>";
echo "<li>If categories are found above, test the navigation links</li>";
echo "<li>If no categories found, run the database fix to create test data</li>";
echo "<li>Check if the actual sport-categories.php page is loading the same data</li>";
echo "<li>Verify that the HTML links are being generated correctly</li>";
echo "</ul>";
echo "</div>";
?>
