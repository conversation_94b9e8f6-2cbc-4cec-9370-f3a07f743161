<?php
/**
 * Debug Category Page - Simple test to check if basic functionality works
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get URL parameters
$event_id = $_GET['event_id'] ?? 4;
$sport_id = $_GET['sport_id'] ?? 40;
$category_id = $_GET['category_id'] ?? 15;

echo "<!DOCTYPE html>";
echo "<html lang='en'>";
echo "<head>";
echo "<meta charset='UTF-8'>";
echo "<meta name='viewport' content='width=device-width, initial-scale=1.0'>";
echo "<title>Debug Category Page</title>";
echo "<style>";
echo "body { font-family: Arial, sans-serif; margin: 20px; }";
echo ".btn { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }";
echo ".btn:hover { background: #0056b3; }";
echo ".tab-button { padding: 10px 20px; margin: 5px; background: #f8f9fa; border: 1px solid #dee2e6; cursor: pointer; }";
echo ".tab-button.active { background: #007bff; color: white; }";
echo ".tab-content { display: none; padding: 20px; border: 1px solid #dee2e6; margin-top: 10px; }";
echo ".tab-content.active { display: block; }";
echo ".modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }";
echo ".modal.show { display: flex; align-items: center; justify-content: center; }";
echo ".modal-dialog { background: white; padding: 20px; border-radius: 8px; max-width: 500px; width: 90%; }";
echo ".form-control { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }";
echo "</style>";
echo "</head>";
echo "<body>";

echo "<h1>🔧 Debug Category Page</h1>";

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h2>Parameters</h2>";
echo "<p><strong>Event ID:</strong> $event_id</p>";
echo "<p><strong>Sport ID:</strong> $sport_id</p>";
echo "<p><strong>Category ID:</strong> $category_id</p>";
echo "</div>";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h2>Tab Test</h2>";
echo "<div>";
echo "<button class='tab-button active' onclick='showTab(\"overview\", this)'>📋 Overview</button>";
echo "<button class='tab-button' onclick='showTab(\"fixtures\", this)'>🏆 Fixtures</button>";
echo "<button class='tab-button' onclick='showTab(\"standings\", this)'>📊 Standings</button>";
echo "</div>";

echo "<div id='overview-tab' class='tab-content active'>";
echo "<h3>Overview Tab</h3>";
echo "<p>This is the overview tab content.</p>";
echo "</div>";

echo "<div id='fixtures-tab' class='tab-content'>";
echo "<h3>Fixtures Tab</h3>";
echo "<p>This is the fixtures tab content.</p>";
echo "</div>";

echo "<div id='standings-tab' class='tab-content'>";
echo "<h3>Standings Tab</h3>";
echo "<p>This is the standings tab content.</p>";
echo "</div>";
echo "</div>";

echo "<div style='background: #f0f8f0; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h2>Modal Test</h2>";
echo "<button class='btn' onclick='openTestModal()'>✏️ Open Test Modal</button>";
echo "<p id='modal-status'>Modal status: Closed</p>";
echo "</div>";

echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px;'>";
echo "<h2>PHP Test</h2>";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>";
echo "<p><strong>Session Status:</strong> " . (session_status() === PHP_SESSION_ACTIVE ? 'Active' : 'Inactive') . "</p>";
echo "<p><strong>Current Time:</strong> " . date('Y-m-d H:i:s') . "</p>";

// Test database connection
try {
    $database = new Database();
    $conn = $database->getConnection();
    echo "<p><strong>Database:</strong> ✅ Connected</p>";
} catch (Exception $e) {
    echo "<p><strong>Database:</strong> ❌ Error: " . $e->getMessage() . "</p>";
}

// Test CSRF token
try {
    $token = generateCSRFToken();
    echo "<p><strong>CSRF Token:</strong> ✅ Generated (Length: " . strlen($token) . ")</p>";
} catch (Exception $e) {
    echo "<p><strong>CSRF Token:</strong> ❌ Error: " . $e->getMessage() . "</p>";
}

echo "</div>";

// Test Modal
echo "<div id='testModal' class='modal'>";
echo "<div class='modal-dialog'>";
echo "<h3>Test Modal</h3>";
echo "<p>This is a test modal.</p>";
echo "<input type='text' class='form-control' placeholder='Test input' id='test-input'>";
echo "<div style='margin-top: 15px;'>";
echo "<button class='btn' onclick='closeTestModal()'>Close</button>";
echo "</div>";
echo "</div>";
echo "</div>";

echo "<script>";
echo "console.log('🚀 Debug page JavaScript loaded');";

echo "function showTab(tabName, clickedButton) {";
echo "    console.log('showTab called:', tabName);";
echo "    const tabContents = document.querySelectorAll('.tab-content');";
echo "    tabContents.forEach(content => content.classList.remove('active'));";
echo "    const tabButtons = document.querySelectorAll('.tab-button');";
echo "    tabButtons.forEach(button => button.classList.remove('active'));";
echo "    const targetTab = document.getElementById(tabName + '-tab');";
echo "    if (targetTab) {";
echo "        targetTab.classList.add('active');";
echo "        console.log('✅ Tab switched to:', tabName);";
echo "    }";
echo "    if (clickedButton) clickedButton.classList.add('active');";
echo "}";

echo "function openTestModal() {";
echo "    console.log('openTestModal called');";
echo "    const modal = document.getElementById('testModal');";
echo "    modal.style.display = 'flex';";
echo "    modal.classList.add('show');";
echo "    document.getElementById('modal-status').textContent = 'Modal status: Open';";
echo "}";

echo "function closeTestModal() {";
echo "    console.log('closeTestModal called');";
echo "    const modal = document.getElementById('testModal');";
echo "    modal.classList.remove('show');";
echo "    setTimeout(() => modal.style.display = 'none', 300);";
echo "    document.getElementById('modal-status').textContent = 'Modal status: Closed';";
echo "}";

echo "window.onclick = function(event) {";
echo "    const modal = document.getElementById('testModal');";
echo "    if (event.target == modal) closeTestModal();";
echo "};";

echo "</script>";
echo "</body>";
echo "</html>";
?>
