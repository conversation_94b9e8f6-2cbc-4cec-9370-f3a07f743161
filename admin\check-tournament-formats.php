<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Tournament Formats Analysis</h2>";

try {
    // Get all tournament formats
    $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY sport_type_category, min_participants, name");
    $stmt->execute();
    $formats = $stmt->fetchAll();

    echo "<h3>Current Tournament Formats:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin-bottom: 20px;'>";
    echo "<tr style='background: #f0f0f0;'>";
    echo "<th>ID</th><th>Name</th><th>Code</th><th>Sport Type</th><th>Min Participants</th><th>Max Participants</th><th>Algorithm</th>";
    echo "</tr>";

    $high_min_formats = [];
    foreach ($formats as $format) {
        $min_participants = $format['min_participants'] ?? 2;
        $row_color = $min_participants > 2 ? 'background: #ffe6e6;' : '';

        echo "<tr style='$row_color'>";
        echo "<td>" . $format['id'] . "</td>";
        echo "<td>" . htmlspecialchars($format['name']) . "</td>";
        echo "<td>" . htmlspecialchars($format['code']) . "</td>";
        echo "<td>" . htmlspecialchars($format['sport_type_category'] ?? $format['sport_types'] ?? 'N/A') . "</td>";
        echo "<td><strong>" . $min_participants . "</strong></td>";
        echo "<td>" . ($format['max_participants'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($format['algorithm_class'] ?? 'N/A') . "</td>";
        echo "</tr>";

        if ($min_participants > 2) {
            $high_min_formats[] = $format;
        }
    }
    echo "</table>";

    // Show formats that need fixing
    if (!empty($high_min_formats)) {
        echo "<h3 style='color: red;'>Formats with Min Participants > 2 (Need Fixing):</h3>";
        echo "<ul>";
        foreach ($high_min_formats as $format) {
            echo "<li><strong>" . htmlspecialchars($format['name']) . "</strong> (Code: " . htmlspecialchars($format['code']) . ") - Min: " . $format['min_participants'] . "</li>";
        }
        echo "</ul>";
    }

    // Check for judged sport formats
    echo "<h3>Judged Sport Formats:</h3>";
    $judged_formats = array_filter($formats, function($f) {
        $category = $f['sport_type_category'] ?? $f['sport_types'] ?? '';
        return strpos(strtolower($category), 'judged') !== false ||
               strpos(strtolower($category), 'performance') !== false ||
               strpos(strtolower($f['name']), 'judged') !== false ||
               strpos(strtolower($f['name']), 'talent') !== false ||
               strpos(strtolower($f['name']), 'performance') !== false;
    });

    if (!empty($judged_formats)) {
        echo "<ul>";
        foreach ($judged_formats as $format) {
            echo "<li><strong>" . htmlspecialchars($format['name']) . "</strong> - Min: " . ($format['min_participants'] ?? 2) . " participants</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: red;'><strong>No judged sport formats found!</strong> This needs to be fixed.</p>";
    }

    // Check what format is assigned to category 24
    echo "<h3>Category 24 Current Assignment:</h3>";
    $stmt = $conn->prepare("
        SELECT sc.name as category_name, tf.name as format_name, tf.min_participants, tf.sport_type_category
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE sc.id = 24
    ");
    $stmt->execute();
    $category_info = $stmt->fetch();

    if ($category_info) {
        echo "<p><strong>Category:</strong> " . htmlspecialchars($category_info['category_name']) . "</p>";
        echo "<p><strong>Current Format:</strong> " . htmlspecialchars($category_info['format_name'] ?? 'Not Set') . "</p>";
        echo "<p><strong>Min Participants:</strong> " . ($category_info['min_participants'] ?? 'Not Set') . "</p>";
        echo "<p><strong>Sport Type:</strong> " . htmlspecialchars($category_info['sport_type_category'] ?? 'Not Set') . "</p>";
    }

} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
}
?>
