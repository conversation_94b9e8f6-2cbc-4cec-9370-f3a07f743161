<?php
/**
 * Generate Matches AJAX Handler
 * Creates tournament matches for a category
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$action = $_POST['action'] ?? '';

try {
    if ($action === 'generate_matches') {
        $category_id = $_POST['category_id'] ?? null;
        $event_id = $_POST['event_id'] ?? null;
        
        if (!$category_id || !$event_id) {
            throw new Exception('Missing category or event ID');
        }
        
        // Get participants for this category
        $stmt = $conn->prepare("
            SELECT d.id, d.name 
            FROM departments d
            JOIN event_department_registrations edr ON d.id = edr.department_id
            WHERE edr.event_id = ?
            ORDER BY d.name
        ");
        $stmt->execute([$event_id]);
        $participants = $stmt->fetchAll();
        
        if (count($participants) < 2) {
            throw new Exception('Need at least 2 participants to generate matches');
        }
        
        // Check if matches already exist
        $check_stmt = $conn->prepare("SELECT COUNT(*) as count FROM matches WHERE sport_category_id = ?");
        $check_stmt->execute([$category_id]);
        $existing_matches = $check_stmt->fetch()['count'];
        
        if ($existing_matches > 0) {
            throw new Exception('Matches already exist for this category');
        }
        
        // Generate round-robin matches
        $matches_created = 0;
        $round = 1;
        $match_number = 1;
        
        for ($i = 0; $i < count($participants); $i++) {
            for ($j = $i + 1; $j < count($participants); $j++) {
                // Create new match
                $insert_stmt = $conn->prepare("
                    INSERT INTO matches 
                    (sport_category_id, team_a_id, team_b_id, round_number, match_number, status, created_at)
                    VALUES (?, ?, ?, ?, ?, 'scheduled', NOW())
                ");
                $insert_stmt->execute([
                    $category_id,
                    $participants[$i]['id'],
                    $participants[$j]['id'],
                    $round,
                    $match_number
                ]);
                $matches_created++;
                $match_number++;
            }
        }
        
        // Log admin activity
        logAdminActivity('GENERATE_MATCHES', 'matches', null, null, [
            'category_id' => $category_id,
            'matches_created' => $matches_created,
            'participants_count' => count($participants)
        ]);
        
        echo json_encode([
            'success' => true,
            'message' => "Generated $matches_created matches successfully",
            'matches_created' => $matches_created,
            'participants_count' => count($participants)
        ]);
        
    } else {
        throw new Exception('Invalid action specified');
    }
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
