<?php
/**
 * Tournament Bracket Generator
 * Generates tournament brackets using configured algorithms
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/tournament_manager.php';
require_once '../../includes/tournament_algorithms.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'generate_tournament':
            handleGenerateTournament($conn, $input);
            break;
        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Generate tournament brackets
 */
function handleGenerateTournament($conn, $input) {
    $eventId = $input['event_id'] ?? '';
    $sportId = $input['sport_id'] ?? '';
    $categoryId = $input['category_id'] ?? '';
    $autoGenerated = $input['auto_generated'] ?? false;

    if (empty($eventId) || empty($sportId) || empty($categoryId)) {
        throw new Exception('Missing required parameters');
    }

    try {
        // Get event sport information
        $stmt = $conn->prepare("
            SELECT 
                es.*,
                tf.name as format_name,
                tf.code as format_code,
                tf.algorithm_class,
                tf.min_participants,
                tf.max_participants
            FROM event_sports es
            LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
            WHERE es.event_id = ? AND es.sport_id = ?
        ");
        $stmt->execute([$eventId, $sportId]);
        $eventSport = $stmt->fetch();

        if (!$eventSport) {
            throw new Exception('Event sport not found');
        }

        // Check if tournament format is configured
        if (empty($eventSport['tournament_format_id'])) {
            throw new Exception('Tournament format not configured for this sport');
        }

        // Get registered participants (departments) - check both new and legacy registration systems
        $stmt = $conn->prepare("
            SELECT
                d.id,
                d.name,
                d.abbreviation,
                d.color_code,
                r.id as registration_id,
                JSON_LENGTH(r.participants) as total_participants
            FROM departments d
            JOIN registrations r ON d.id = r.department_id
            WHERE r.event_sport_id = ? AND r.status IN ('confirmed', 'registered', 'approved')
            ORDER BY d.name
        ");
        $stmt->execute([$eventSport['id']]);
        $participants = $stmt->fetchAll();

        // If no participants found in legacy system, try new system
        if (empty($participants)) {
            $stmt = $conn->prepare("
                SELECT
                    d.id,
                    d.name,
                    d.abbreviation,
                    d.color_code,
                    edr.id as registration_id,
                    edr.total_participants
                FROM departments d
                JOIN event_department_registrations edr ON d.id = edr.department_id
                WHERE edr.event_id = ? AND edr.status = 'approved'
                ORDER BY d.name
            ");
            $stmt->execute([$eventId]);
            $participants = $stmt->fetchAll();
        }

        if (count($participants) < $eventSport['min_participants']) {
            throw new Exception("Not enough participants. Minimum required: {$eventSport['min_participants']}, Current: " . count($participants));
        }

        // Check if tournament already exists
        $stmt = $conn->prepare("
            SELECT id FROM tournament_structures 
            WHERE event_sport_id = ? AND status != 'cancelled'
        ");
        $stmt->execute([$eventSport['id']]);
        $existingTournament = $stmt->fetch();

        if ($existingTournament) {
            throw new Exception('Tournament already exists for this sport');
        }

        // Create tournament manager with enhanced error handling
        $tournamentManager = new TournamentManager($conn);

        // Generate tournament name
        $tournamentName = "Tournament - " . date('Y-m-d H:i:s');

        // Configuration for tournament generation with database-driven settings
        $config = [
            'seeding_method' => 'random',
            'bracket_seeding' => true,
            'auto_generated' => $autoGenerated,
            'comprehensive_validation' => true,
            'fallback_enabled' => true
        ];

        // Validate prerequisites before tournament creation
        $validationResult = validateTournamentPrerequisites($conn, $eventSport, $participants);
        if (!$validationResult['valid']) {
            throw new Exception($validationResult['message']);
        }

        // Create tournament with database-driven format selection
        // Note: formatId is used as fallback, actual format selected dynamically
        $tournamentId = $tournamentManager->createTournament(
            $eventSport['id'],
            $eventSport['tournament_format_id'] ?? 1, // Fallback format ID
            $tournamentName,
            $config
        );

        // Update category status if auto-generated
        if ($autoGenerated) {
            $stmt = $conn->prepare("
                UPDATE sport_categories 
                SET status = 'ongoing' 
                WHERE id = ?
            ");
            $stmt->execute([$categoryId]);
        }

        // Log admin activity
        if (function_exists('logAdminActivity')) {
            logAdminActivity('GENERATE_TOURNAMENT', 'tournament_structures', $tournamentId, null, [
                'category_id' => $categoryId,
                'participant_count' => count($participants),
                'format' => $eventSport['format_name'],
                'auto_generated' => $autoGenerated
            ]);
        }

        echo json_encode([
            'success' => true,
            'message' => 'Tournament brackets generated successfully!',
            'tournament_id' => $tournamentId,
            'participant_count' => count($participants),
            'format' => $eventSport['format_name'],
            'auto_generated' => $autoGenerated
        ]);

    } catch (Exception $e) {
        throw $e;
    }
}

/**
 * Validate tournament prerequisites for seamless generation
 */
function validateTournamentPrerequisites($conn, $eventSport, $participants) {
    $errors = [];

    // Check minimum participants
    if (count($participants) < 2) {
        $errors[] = "At least 2 participants required for tournament";
    }

    // Check event sport configuration
    if (empty($eventSport['id'])) {
        $errors[] = "Invalid event sport configuration";
    }

    // Check tournament format availability
    if (!empty($eventSport['tournament_format_id'])) {
        $stmt = $conn->prepare("SELECT id FROM tournament_formats WHERE id = ?");
        $stmt->execute([$eventSport['tournament_format_id']]);
        if (!$stmt->fetch()) {
            $errors[] = "Configured tournament format not found in database";
        }
    }

    // Check for existing active tournaments
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM tournament_structures
        WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')
    ");
    $stmt->execute([$eventSport['id']]);
    $result = $stmt->fetch();
    if ($result['count'] > 0) {
        $errors[] = "Active tournament already exists for this event sport";
    }

    // Validate participant data integrity
    foreach ($participants as $participant) {
        if (empty($participant['id']) || empty($participant['name'])) {
            $errors[] = "Invalid participant data detected";
            break;
        }
    }

    return [
        'valid' => empty($errors),
        'message' => empty($errors) ? 'All prerequisites validated' : implode('; ', $errors),
        'errors' => $errors
    ];
}
