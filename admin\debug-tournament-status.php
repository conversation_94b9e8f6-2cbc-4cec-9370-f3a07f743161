<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$event_sport_id = 52; // From our tests
$category_id = 16;

echo "<h2>🔍 Tournament Status Debug</h2>";

echo "<h3>1. Check Tournament Structures</h3>";
$stmt = $conn->prepare("
    SELECT ts.*, tf.name as format_name
    FROM tournament_structures ts
    LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
    WHERE ts.event_sport_id = ?
    ORDER BY ts.created_at DESC
");
$stmt->execute([$event_sport_id]);
$tournaments = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($tournaments) {
    echo "<p>✅ Found " . count($tournaments) . " tournament(s):</p>";
    foreach ($tournaments as $tournament) {
        echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 10px 0;'>";
        echo "<p><strong>ID:</strong> " . $tournament['id'] . "</p>";
        echo "<p><strong>Status:</strong> " . $tournament['status'] . "</p>";
        echo "<p><strong>Format:</strong> " . ($tournament['format_name'] ?? 'Unknown') . "</p>";
        echo "<p><strong>Created:</strong> " . $tournament['created_at'] . "</p>";
        echo "<p><strong>Has Bracket Data:</strong> " . (!empty($tournament['bracket_data']) ? 'Yes' : 'No') . "</p>";
        
        if (!empty($tournament['bracket_data'])) {
            $bracket_data = json_decode($tournament['bracket_data'], true);
            echo "<p><strong>Bracket Format:</strong> " . ($bracket_data['format'] ?? 'Unknown') . "</p>";
        }
        echo "</div>";
    }
} else {
    echo "<p>❌ No tournaments found for event_sport_id: $event_sport_id</p>";
}

echo "<h3>2. Check Tournament Matches</h3>";
if ($tournaments) {
    $tournament_id = $tournaments[0]['id'];
    $stmt = $conn->prepare("
        SELECT tm.*, d1.name as team1_name, d2.name as team2_name
        FROM tournament_matches tm
        LEFT JOIN departments d1 ON tm.team1_id = d1.id
        LEFT JOIN departments d2 ON tm.team2_id = d2.id
        WHERE tm.tournament_id = ?
        ORDER BY tm.round_number, tm.match_number
    ");
    $stmt->execute([$tournament_id]);
    $matches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($matches) {
        echo "<p>✅ Found " . count($matches) . " match(es):</p>";
        foreach ($matches as $match) {
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
            echo "<p><strong>Round " . $match['round_number'] . ", Match " . $match['match_number'] . ":</strong> ";
            echo ($match['team1_name'] ?? 'TBD') . " vs " . ($match['team2_name'] ?? 'TBD');
            echo " (Status: " . $match['status'] . ")</p>";
            echo "</div>";
        }
    } else {
        echo "<p>❌ No matches found for tournament_id: $tournament_id</p>";
    }
}

echo "<h3>3. Check Participants</h3>";
$stmt = $conn->prepare("
    SELECT COUNT(DISTINCT edr.department_id) as participant_count
    FROM event_department_registrations edr
    JOIN event_sports es ON edr.event_id = es.event_id
    WHERE es.id = ? AND edr.status IN ('pending', 'approved')
");
$stmt->execute([$event_sport_id]);
$participant_data = $stmt->fetch(PDO::FETCH_ASSOC);
echo "<p><strong>Registered Participants:</strong> " . $participant_data['participant_count'] . "</p>";

echo "<h3>4. Check Category Requirements</h3>";
$stmt = $conn->prepare("
    SELECT
        sc.*,
        es.tournament_format_id,
        tf.name as format_name,
        tf.min_participants as format_min_participants
    FROM sport_categories sc
    JOIN event_sports es ON sc.event_sport_id = es.id
    LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
    WHERE sc.id = ?
");
$stmt->execute([$category_id]);
$category = $stmt->fetch(PDO::FETCH_ASSOC);

if ($category) {
    echo "<p><strong>Tournament Format:</strong> " . ($category['format_name'] ?? 'Not set') . "</p>";
    echo "<p><strong>Min Participants:</strong> " . ($category['format_min_participants'] ?? 'Not set') . "</p>";
    echo "<p><strong>Referee:</strong> " . ($category['referee_name'] ?? 'Not set') . "</p>";
    
    $has_format = !empty($category['tournament_format_id']);
    $has_referee = !empty($category['referee_name']);
    $has_participants = $participant_data['participant_count'] >= ($category['format_min_participants'] ?? 2);
    
    echo "<p><strong>Requirements Status:</strong></p>";
    echo "<ul>";
    echo "<li>Format: " . ($has_format ? '✅' : '❌') . "</li>";
    echo "<li>Referee: " . ($has_referee ? '✅' : '❌') . "</li>";
    echo "<li>Participants: " . ($has_participants ? '✅' : '❌') . "</li>";
    echo "</ul>";
    
    if ($has_format && $has_referee && $has_participants) {
        echo "<p style='color: green;'>🎉 All requirements met - Tournament should auto-generate!</p>";
    } else {
        echo "<p style='color: orange;'>⏳ Some requirements missing</p>";
    }
}

echo "<h3>5. Manual Auto-Generation Test</h3>";
if ($category && !$tournaments) {
    echo "<p>Attempting manual auto-generation...</p>";
    
    try {
        require_once '../includes/advanced_tournament_engine.php';
        require_once '../includes/tournament_algorithms_advanced.php';
        
        $engine = new AdvancedTournamentEngine($conn);
        $result = $engine->generateTournament($event_sport_id, $category_id, [
            'seeding_method' => 'random',
            'third_place_playoff' => false,
            'scoring_config' => [
                'points_win' => 3,
                'points_draw' => 1,
                'points_loss' => 0
            ]
        ]);

        if ($result['success']) {
            echo "<p style='color: green;'>✅ Manual generation successful!</p>";
            echo "<p><strong>Tournament ID:</strong> " . $result['tournament_id'] . "</p>";
            echo "<p><strong>Format:</strong> " . $result['format'] . "</p>";
            echo "<p><strong>Participants:</strong> " . $result['participants_count'] . "</p>";
        } else {
            echo "<p style='color: red;'>❌ Manual generation failed: " . $result['message'] . "</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
    }
}

echo "<p><a href='manage-category.php?event_id=4&sport_id=37&category_id=16'>← Back to Category Management</a></p>";
?>
