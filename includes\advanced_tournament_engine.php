<?php
/**
 * Advanced Tournament Bracket Generation Engine
 * Supports multiple tournament formats with automatic data inheritance
 */

class AdvancedTournamentEngine {
    private $conn;
    private $algorithms = [];
    
    public function __construct($conn) {
        $this->conn = $conn;
        $this->initializeAlgorithms();
    }
    
    private function initializeAlgorithms() {
        $this->algorithms = [
            'single_elimination' => new SingleEliminationAlgorithm($this->conn),
            'double_elimination' => new DoubleEliminationAlgorithm($this->conn),
            'round_robin' => new RoundRobinAlgorithm($this->conn),
            'multi_stage' => new MultiStageAlgorithm($this->conn),
            'swiss_system' => new SwissSystemAlgorithm($this->conn),
            'judged_rounds' => new JudgedRoundsAlgorithm($this->conn),
            'performance_competition' => new PerformanceCompetitionAlgorithm($this->conn),
            'talent_showcase' => new TalentShowcaseAlgorithm($this->conn)
        ];
    }
    
    /**
     * Generate tournament bracket with automatic data inheritance
     */
    public function generateTournament($eventSportId, $categoryId = null, $config = []) {
        try {
            $this->conn->beginTransaction();
            
            // Get tournament format from event_sports configuration
            $format = $this->getTournamentFormat($eventSportId);
            if (!$format) {
                throw new Exception('No tournament format configured for this sport');
            }
            
            // Get participants from unified registration system
            $participants = $this->getEventParticipants($eventSportId);
            $minParticipants = $format['min_participants'] ?? 2;
            if (count($participants) < $minParticipants) {
                throw new Exception("Need at least {$minParticipants} participants. Currently have " . count($participants));
            }
            
            // Create tournament structure
            $tournamentId = $this->createTournamentStructure($eventSportId, $format, $participants, $config);
            
            // Generate bracket using appropriate algorithm
            $algorithm = $this->getAlgorithm($format['code']);
            $bracket = $algorithm->generateBracket($participants, array_merge($config, [
                'tournament_id' => $tournamentId,
                'format' => $format
            ]));
            
            // Save bracket data
            $this->saveBracketData($tournamentId, $bracket);
            
            // Create matches
            $this->createMatches($tournamentId, $bracket);
            
            $this->conn->commit();
            
            return [
                'success' => true,
                'tournament_id' => $tournamentId,
                'bracket' => $bracket,
                'participants_count' => count($participants),
                'format' => $format['name']
            ];
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Get tournament format from event_sports configuration
     */
    private function getTournamentFormat($eventSportId) {
        $stmt = $this->conn->prepare("
            SELECT tf.*, es.tournament_format_id
            FROM event_sports es
            LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
            WHERE es.id = ?
        ");
        $stmt->execute([$eventSportId]);
        $format = $stmt->fetch(PDO::FETCH_ASSOC);

        // If no format is configured, use default single elimination
        if (!$format || !$format['tournament_format_id']) {
            $stmt = $this->conn->prepare("
                SELECT * FROM tournament_formats
                WHERE code = 'single_elimination'
                ORDER BY id LIMIT 1
            ");
            $stmt->execute();
            $defaultFormat = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$defaultFormat) {
                // Create default format if it doesn't exist
                $stmt = $this->conn->prepare("
                    INSERT INTO tournament_formats
                    (name, code, description, min_participants, max_participants, rounds_formula, matches_formula, algorithm_class)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([
                    'Single Elimination',
                    'single_elimination',
                    'Standard single elimination tournament',
                    2,
                    64,
                    'ceil(log(n, 2))',
                    'n - 1',
                    'SingleEliminationAlgorithm'
                ]);

                $formatId = $this->conn->lastInsertId();
                $stmt = $this->conn->prepare("SELECT * FROM tournament_formats WHERE id = ?");
                $stmt->execute([$formatId]);
                $defaultFormat = $stmt->fetch(PDO::FETCH_ASSOC);
            }

            return $defaultFormat;
        }

        return $format;
    }
    
    /**
     * Get participants from unified registration system
     */
    private function getEventParticipants($eventSportId) {
        // Use the unified registration system
        $stmt = $this->conn->prepare("
            SELECT DISTINCT
                d.id,
                d.name,
                d.abbreviation,
                d.color_code,
                edr.status,
                edr.created_at as registration_date,
                edr.contact_person,
                edr.contact_email
            FROM event_department_registrations edr
            JOIN event_sports es ON edr.event_id = es.event_id
            JOIN departments d ON edr.department_id = d.id
            WHERE es.id = ? AND edr.status IN ('pending', 'approved')
            ORDER BY d.name
        ");
        $stmt->execute([$eventSportId]);
        $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // If no participants found, use fallback system (all active departments)
        if (empty($participants)) {
            $stmt = $this->conn->prepare("
                SELECT
                    d.id,
                    d.name,
                    d.abbreviation,
                    d.color_code,
                    'available' as status,
                    NOW() as registration_date,
                    NULL as contact_person,
                    NULL as contact_email
                FROM departments d
                WHERE d.status = 'active'
                ORDER BY d.name
                LIMIT 10
            ");
            $stmt->execute();
            $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }

        return $participants;
    }
    
    /**
     * Create tournament structure in database
     */
    private function createTournamentStructure($eventSportId, $format, $participants, $config) {
        // Check if tournament already exists
        $stmt = $this->conn->prepare("
            SELECT id FROM tournament_structures 
            WHERE event_sport_id = ? AND status NOT IN ('completed', 'cancelled')
        ");
        $stmt->execute([$eventSportId]);
        $existing = $stmt->fetch();
        
        if ($existing) {
            return $existing['id'];
        }
        
        $stmt = $this->conn->prepare("
            INSERT INTO tournament_structures 
            (event_sport_id, tournament_format_id, name, participant_count, seeding_method, 
             scoring_config, status, total_rounds, bracket_data)
            VALUES (?, ?, ?, ?, ?, ?, 'setup', ?, ?)
        ");
        
        $tournamentName = "Tournament - " . date('Y-m-d H:i:s');
        $totalRounds = $this->calculateRounds($format, count($participants));
        
        $stmt->execute([
            $eventSportId,
            $format['id'],
            $tournamentName,
            count($participants),
            $config['seeding_method'] ?? 'random',
            json_encode($config['scoring_config'] ?? []),
            $totalRounds,
            json_encode([])
        ]);
        
        return $this->conn->lastInsertId();
    }
    
    /**
     * Calculate number of rounds based on format and participants
     */
    private function calculateRounds($format, $participantCount) {
        if (!empty($format['rounds_formula'])) {
            $formula = str_replace('n', $participantCount, $format['rounds_formula']);
            return eval("return $formula;");
        }
        
        // Default calculation based on format
        switch ($format['code']) {
            case 'single_elimination':
                return ceil(log($participantCount, 2));
            case 'double_elimination':
                return ceil(log($participantCount, 2)) * 2 - 1;
            case 'round_robin':
                return 1;
            case 'multi_stage':
                return ceil(log($participantCount / 4, 2)) + 2; // Group stage + knockout
            default:
                return ceil(log($participantCount, 2));
        }
    }
    
    /**
     * Get algorithm instance for format
     */
    private function getAlgorithm($formatCode) {
        if (!isset($this->algorithms[$formatCode])) {
            // Default to single elimination
            return $this->algorithms['single_elimination'];
        }
        return $this->algorithms[$formatCode];
    }
    
    /**
     * Save bracket data to tournament structure
     */
    private function saveBracketData($tournamentId, $bracket) {
        $stmt = $this->conn->prepare("
            UPDATE tournament_structures 
            SET bracket_data = ?, status = 'in_progress', updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([json_encode($bracket), $tournamentId]);
    }
    
    /**
     * Create matches from bracket data
     */
    private function createMatches($tournamentId, $bracket) {
        // Clear existing matches
        $stmt = $this->conn->prepare("DELETE FROM matches WHERE tournament_structure_id = ?");
        $stmt->execute([$tournamentId]);
        
        // Create new matches from bracket
        foreach ($bracket['rounds'] as $roundNumber => $round) {
            foreach ($round['matches'] as $matchNumber => $match) {
                $this->createMatch($tournamentId, $roundNumber + 1, $matchNumber + 1, $match);
            }
        }
    }
    
    /**
     * Create individual match
     */
    private function createMatch($tournamentId, $roundNumber, $matchNumber, $matchData) {
        $stmt = $this->conn->prepare("
            INSERT INTO matches 
            (tournament_structure_id, round_number, match_number, team1_id, team2_id, 
             status, bracket_position, is_bye_match, created_at)
            VALUES (?, ?, ?, ?, ?, 'pending', ?, ?, NOW())
        ");
        
        $stmt->execute([
            $tournamentId,
            $roundNumber,
            $matchNumber,
            $matchData['team1_id'] ?? null,
            $matchData['team2_id'] ?? null,
            $matchData['bracket_position'] ?? "R{$roundNumber}M{$matchNumber}",
            $matchData['is_bye'] ?? false
        ]);
    }
}

/**
 * Fisher-Yates Shuffle for reproducible randomization
 */
class TournamentSeeder {
    public static function fisherYatesShuffle($array, $seed = null) {
        if ($seed !== null) {
            mt_srand($seed);
        }
        
        $count = count($array);
        for ($i = $count - 1; $i > 0; $i--) {
            $j = mt_rand(0, $i);
            $temp = $array[$i];
            $array[$i] = $array[$j];
            $array[$j] = $temp;
        }
        
        return $array;
    }
    
    public static function seedParticipants($participants, $method = 'random', $seed = null) {
        switch ($method) {
            case 'random':
                return self::fisherYatesShuffle($participants, $seed);
            case 'ranking':
                // Sort by some ranking criteria (could be previous performance)
                usort($participants, function($a, $b) {
                    return ($a['ranking'] ?? 0) <=> ($b['ranking'] ?? 0);
                });
                return $participants;
            case 'manual':
                // Keep original order for manual seeding
                return $participants;
            default:
                return $participants;
        }
    }
}
?>
