<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🔍 Debug Tournament Bracket Generation Failure</h1>";
echo "<p>Let's find out exactly why the bracket generation is failing...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get the exact parameters from the URL
    $event_id = $_GET['event_id'] ?? 4;
    $sport_id = $_GET['sport_id'] ?? 37;
    $category_id = $_GET['category_id'] ?? 15;
    
    echo "<h2>🎯 Testing Parameters</h2>";
    echo "<p><strong>Event ID:</strong> {$event_id}</p>";
    echo "<p><strong>Sport ID:</strong> {$sport_id}</p>";
    echo "<p><strong>Category ID:</strong> {$category_id}</p>";
    
    echo "<h2>1. Check Event Sport Configuration</h2>";
    
    $stmt = $conn->prepare("
        SELECT es.*, e.name as event_name, s.name as sport_name,
               tf.name as format_name, tf.algorithm_class, tf.min_participants
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    
    if (!$event_sport) {
        echo "<p style='color: red;'>❌ Event sport not found!</p>";
        echo "<p>Available event sports:</p>";
        
        $stmt = $conn->prepare("
            SELECT es.*, e.name as event_name, s.name as sport_name
            FROM event_sports es
            JOIN events e ON es.event_id = e.id
            JOIN sports s ON es.sport_id = s.id
            ORDER BY es.event_id, es.sport_id
        ");
        $stmt->execute();
        $all_event_sports = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Event ID</th><th>Sport ID</th><th>Event</th><th>Sport</th></tr>";
        foreach ($all_event_sports as $es) {
            echo "<tr>";
            echo "<td>{$es['event_id']}</td>";
            echo "<td>{$es['sport_id']}</td>";
            echo "<td>{$es['event_name']}</td>";
            echo "<td>{$es['sport_name']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Event Sport Found: {$event_sport['event_name']} - {$event_sport['sport_name']}</p>";
    echo "<p><strong>Tournament Format:</strong> " . ($event_sport['format_name'] ?? 'Not configured') . "</p>";
    echo "<p><strong>Min Participants:</strong> " . ($event_sport['min_participants'] ?? 'Not set') . "</p>";
    
    echo "<h2>2. Check Participants (Legacy System)</h2>";
    
    $stmt = $conn->prepare("
        SELECT r.*, d.name as department_name
        FROM registrations r
        JOIN departments d ON r.department_id = d.id
        WHERE r.event_sport_id = ? AND r.status IN ('confirmed', 'registered', 'approved')
        ORDER BY d.name
    ");
    $stmt->execute([$event_sport['id']]);
    $legacy_participants = $stmt->fetchAll();
    
    echo "<p><strong>Legacy Participants Found:</strong> " . count($legacy_participants) . "</p>";
    
    if (!empty($legacy_participants)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>ID</th><th>Department</th><th>Status</th><th>Participants Count</th>";
        echo "</tr>";
        foreach ($legacy_participants as $p) {
            $participant_list = json_decode($p['participants'], true) ?: [];
            echo "<tr>";
            echo "<td>{$p['id']}</td>";
            echo "<td>{$p['department_name']}</td>";
            echo "<td>{$p['status']}</td>";
            echo "<td>" . count($participant_list) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>3. Check Participants (New System)</h2>";
    
    $stmt = $conn->prepare("
        SELECT d.id, d.name, d.abbreviation, d.color_code,
               edr.id as registration_id, edr.total_participants, edr.status
        FROM departments d
        JOIN event_department_registrations edr ON d.id = edr.department_id
        WHERE edr.event_id = ? AND edr.status = 'approved'
        ORDER BY d.name
    ");
    $stmt->execute([$event_id]);
    $new_participants = $stmt->fetchAll();
    
    echo "<p><strong>New System Participants Found:</strong> " . count($new_participants) . "</p>";
    
    if (!empty($new_participants)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>ID</th><th>Department</th><th>Status</th><th>Total Participants</th>";
        echo "</tr>";
        foreach ($new_participants as $p) {
            echo "<tr>";
            echo "<td>{$p['id']}</td>";
            echo "<td>{$p['name']}</td>";
            echo "<td>{$p['status']}</td>";
            echo "<td>{$p['total_participants']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>4. Check Existing Tournaments</h2>";
    
    $stmt = $conn->prepare("
        SELECT * FROM tournament_structures 
        WHERE event_sport_id = ? 
        ORDER BY created_at DESC
    ");
    $stmt->execute([$event_sport['id']]);
    $existing_tournaments = $stmt->fetchAll();
    
    echo "<p><strong>Existing Tournaments:</strong> " . count($existing_tournaments) . "</p>";
    
    if (!empty($existing_tournaments)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>ID</th><th>Name</th><th>Status</th><th>Participants</th><th>Created</th><th>Action</th>";
        echo "</tr>";
        foreach ($existing_tournaments as $t) {
            echo "<tr>";
            echo "<td>{$t['id']}</td>";
            echo "<td>{$t['name']}</td>";
            echo "<td>{$t['status']}</td>";
            echo "<td>{$t['participant_count']}</td>";
            echo "<td>{$t['created_at']}</td>";
            echo "<td>";
            if ($t['status'] != 'cancelled') {
                echo "<a href='?cancel_tournament={$t['id']}&event_id={$event_id}&sport_id={$sport_id}&category_id={$category_id}' style='background: #dc3545; color: white; padding: 2px 8px; text-decoration: none; border-radius: 3px; font-size: 12px;'>Cancel</a>";
            }
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>5. Simulate AJAX Request</h2>";
    
    // Simulate the exact AJAX request
    $ajax_data = [
        'action' => 'generate_tournament',
        'event_id' => $event_id,
        'sport_id' => $sport_id,
        'category_id' => $category_id,
        'auto_generated' => false
    ];
    
    echo "<p>Simulating AJAX request with data:</p>";
    echo "<pre>" . json_encode($ajax_data, JSON_PRETTY_PRINT) . "</pre>";
    
    // Capture the AJAX response
    ob_start();
    
    // Set up the environment for the AJAX script
    $_SERVER['REQUEST_METHOD'] = 'POST';
    $_SERVER['CONTENT_TYPE'] = 'application/json';
    
    // Mock the input
    $mock_input = json_encode($ajax_data);
    
    // Temporarily override file_get_contents for php://input
    $original_input = file_get_contents('php://input');
    
    try {
        // Include the AJAX script with error handling
        $ajax_file = 'ajax/tournament-bracket-generator.php';
        
        if (file_exists($ajax_file)) {
            // Create a temporary file with our mock input
            $temp_file = tempnam(sys_get_temp_dir(), 'ajax_input');
            file_put_contents($temp_file, $mock_input);
            
            // Override php://input temporarily
            ini_set('auto_prepend_file', '');
            
            // Capture output
            ob_start();
            
            // Set up POST data
            $_POST = [];
            
            // Include the file
            include $ajax_file;
            
            $ajax_output = ob_get_contents();
            ob_end_clean();
            
            // Clean up
            unlink($temp_file);
            
        } else {
            $ajax_output = "AJAX file not found: {$ajax_file}";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        $ajax_output = "AJAX Error: " . $e->getMessage() . "\nStack trace: " . $e->getTraceAsString();
    }
    
    echo "<h3>AJAX Response:</h3>";
    echo "<pre style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; border-radius: 4px; max-height: 400px; overflow-y: auto;'>";
    echo htmlspecialchars($ajax_output);
    echo "</pre>";
    
    // Try to parse as JSON
    $ajax_result = json_decode($ajax_output, true);
    if ($ajax_result) {
        if (isset($ajax_result['success'])) {
            if ($ajax_result['success']) {
                echo "<p style='color: green;'>✅ AJAX returned success</p>";
            } else {
                echo "<p style='color: red;'>❌ AJAX returned error: " . ($ajax_result['message'] ?? 'Unknown error') . "</p>";
            }
        }
    }
    
    echo "<h2>6. Diagnosis & Recommendations</h2>";
    
    $issues = [];
    $recommendations = [];
    
    // Check for common issues
    if (!$event_sport['tournament_format_id']) {
        $issues[] = "No tournament format configured";
        $recommendations[] = "Configure a tournament format for this event sport";
    }
    
    if (count($legacy_participants) == 0 && count($new_participants) == 0) {
        $issues[] = "No participants found in either registration system";
        $recommendations[] = "Add participant registrations for this event sport";
    }
    
    if (count($legacy_participants) < 2 && count($new_participants) < 2) {
        $issues[] = "Insufficient participants (need at least 2)";
        $recommendations[] = "Add more participant registrations";
    }
    
    $active_tournaments = array_filter($existing_tournaments, function($t) {
        return $t['status'] != 'cancelled' && $t['status'] != 'completed';
    });
    
    if (count($active_tournaments) > 0) {
        $issues[] = "Active tournament already exists";
        $recommendations[] = "Cancel existing tournament or wait for it to complete";
    }
    
    if (empty($issues)) {
        echo "<p style='color: green;'>✅ No obvious issues found. The system should be able to generate tournaments.</p>";
    } else {
        echo "<h3>Issues Found:</h3>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li style='color: red;'>❌ {$issue}</li>";
        }
        echo "</ul>";
        
        echo "<h3>Recommendations:</h3>";
        echo "<ul>";
        foreach ($recommendations as $rec) {
            echo "<li style='color: blue;'>💡 {$rec}</li>";
        }
        echo "</ul>";
    }
    
    // Handle tournament cancellation
    if (isset($_GET['cancel_tournament'])) {
        $tournament_id = $_GET['cancel_tournament'];
        $stmt = $conn->prepare("UPDATE tournament_structures SET status = 'cancelled' WHERE id = ?");
        $stmt->execute([$tournament_id]);
        echo "<p style='color: green;'>✅ Tournament {$tournament_id} has been cancelled</p>";
        echo "<p><a href='debug-bracket-failure.php?event_id={$event_id}&sport_id={$sport_id}&category_id={$category_id}'>Refresh Page</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Fatal Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}
?>
