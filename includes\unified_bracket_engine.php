<?php
/**
 * Unified Tournament Bracket Generation Engine
 * SC_IMS Sports Competition and Event Management System
 * 
 * Comprehensive bracket generation system with format-specific configuration,
 * advanced seeding algorithms, and consistent behavior across all sport categories.
 */

require_once 'tournament_manager.php';
require_once 'tournament_algorithms.php';
require_once 'tournament_format_selector.php';
require_once 'tournament_algorithm_factory.php';

class UnifiedBracketEngine {
    private $conn;
    private $tournamentManager;
    private $formatSelector;
    private $algorithmFactory;
    private $currentEventSportId;
    
    public function __construct($conn) {
        $this->conn = $conn;
        $this->tournamentManager = new TournamentManager($conn);
        $this->formatSelector = new TournamentFormatSelector($conn);
        $this->algorithmFactory = new TournamentAlgorithmFactory($conn);
    }
    
    /**
     * Generate tournament bracket for a sport category using existing event participants
     */
    public function generateBracketForCategory($eventSportId, $config = []) {
        // Safety check: if there's an orphaned transaction, close it
        if ($this->conn->inTransaction()) {
            try {
                $this->conn->rollBack();
            } catch (Exception $e) {
                // Ignore rollback errors for orphaned transactions
            }
        }

        // Check if we're already in a transaction
        $inTransaction = $this->conn->inTransaction();

        try {
            if (!$inTransaction) {
                $this->conn->beginTransaction();
            }

            // Store current event sport ID for use in other methods
            $this->currentEventSportId = $eventSportId;

            // Get event sport information
            $eventSport = $this->getEventSportInfo($eventSportId);
            if (!$eventSport) {
                throw new Exception('Event sport not found');
            }

            // Get existing participants from unified registration system
            $participants = $this->getEventParticipants($eventSportId);
            if (count($participants) < 2) {
                throw new Exception('At least 2 participants required for tournament generation');
            }

            // Check if tournament already exists
            if ($this->tournamentExists($eventSportId)) {
                throw new Exception('Tournament already exists for this category');
            }

            // Select optimal tournament format
            $format = $this->selectTournamentFormat($eventSport, count($participants), $config);

            // Validate format compatibility
            $this->validateFormatCompatibility($format, count($participants));

            // Generate tournament configuration
            $tournamentConfig = $this->generateTournamentConfig($format, $config);

            // Create tournament structure
            $tournamentId = $this->createTournamentStructure($eventSportId, $format, $tournamentConfig);

            // Generate bracket using appropriate algorithm
            $bracket = $this->generateBracket($format, $participants, $tournamentConfig);

            // Save bracket data and create matches
            $this->saveBracketData($tournamentId, $bracket);
            $this->createTournamentMatches($tournamentId, $bracket);

            // Create tournament participants entries
            $this->createTournamentParticipants($tournamentId, $participants);

            if (!$inTransaction) {
                $this->conn->commit();
            }

            return [
                'success' => true,
                'tournament_id' => $tournamentId,
                'format' => $format['name'],
                'participant_count' => count($participants),
                'total_rounds' => $bracket['rounds'],
                'total_matches' => $this->calculateTotalMatches($bracket),
                'bracket_data' => $bracket
            ];

        } catch (Exception $e) {
            if (!$inTransaction && $this->conn->inTransaction()) {
                $this->conn->rollBack();
            }
            throw $e;
        }
    }
    
    /**
     * Get event sport information with tournament format
     */
    private function getEventSportInfo($eventSportId) {
        $sql = "SELECT es.*, s.name as sport_name, s.type as sport_type, 
                       tf.name as format_name, tf.code as format_code
                FROM event_sports es
                JOIN sports s ON es.sport_id = s.id
                LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
                WHERE es.id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$eventSportId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get participants using unified registration system
     */
    public function getEventParticipants($eventSportId) {
        // Try unified registration system first (sport-specific participations)
        try {
            $sql = "SELECT
                        dsp.id,
                        d.name as team_name,
                        edr.department_id,
                        d.name as department_name,
                        d.color_code,
                        dsp.status,
                        edr.created_at as registration_date
                    FROM event_department_registrations edr
                    JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
                    JOIN departments d ON edr.department_id = d.id
                    WHERE dsp.event_sport_id = ? AND edr.status IN ('approved', 'pending') AND dsp.status IN ('confirmed', 'registered')
                    ORDER BY edr.created_at";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$eventSportId]);
            $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (!empty($participants)) {
                return $participants;
            }
        } catch (Exception $e) {
            // Tables don't exist, continue with fallback
        }

        // Try legacy registrations system
        try {
            $sql = "SELECT r.id, r.department_id, r.team_name, d.name as department_name, d.color_code,
                           r.status, r.registration_date as created_at
                    FROM registrations r
                    JOIN departments d ON r.department_id = d.id
                    WHERE r.event_sport_id = ? AND r.status IN ('confirmed', 'approved', 'registered')
                    ORDER BY r.registration_date";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$eventSportId]);
            $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);

            if (!empty($participants)) {
                // Format participants to match expected structure
                foreach ($participants as &$participant) {
                    $participant['team_name'] = $participant['team_name'] ?: $participant['department_name'];
                }
                return $participants;
            }
        } catch (Exception $e) {
            // Continue to next fallback
        }

        // Final fallback: Get all departments registered for the event
        try {
            // Get event_id from event_sport_id
            $sql = "SELECT event_id FROM event_sports WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$eventSportId]);
            $eventInfo = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($eventInfo) {
                $sql = "SELECT d.id, d.name as department_name, d.name as team_name, d.color_code,
                               edr.id as registration_id, edr.status, edr.created_at,
                               d.id as department_id
                        FROM departments d
                        JOIN event_department_registrations edr ON d.id = edr.department_id
                        WHERE edr.event_id = ? AND edr.status IN ('approved', 'pending')
                        ORDER BY d.name";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$eventInfo['event_id']]);
                $participants = $stmt->fetchAll(PDO::FETCH_ASSOC);

                if (!empty($participants)) {
                    return $participants;
                }
            }
        } catch (Exception $e) {
            // Final fallback failed
        }

        // Auto-registration fallback: Get departments that should be automatically available
        // This creates seamless tournament participation for departments added to events
        try {
            // Get event info to find departments associated with this event
            $sql = "SELECT event_id FROM event_sports WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$eventSportId]);
            $eventInfo = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($eventInfo) {
                // Get all active departments - auto-eligible for tournaments
                $sql = "SELECT d.id, d.name as department_name, d.name as team_name, d.color_code,
                               d.id as department_id, 'auto_registered' as status,
                               NOW() as created_at
                        FROM departments d
                        WHERE d.status = 'active'
                        ORDER BY d.name
                        LIMIT 10"; // Reasonable limit for tournament generation
                $stmt = $this->conn->prepare($sql);
                $stmt->execute();
                $departments = $stmt->fetchAll(PDO::FETCH_ASSOC);

                // Only return if we have enough for a tournament (2+ departments)
                if (count($departments) >= 2) {
                    return $departments;
                }
            }
        } catch (Exception $e) {
            // Auto-registration failed
        }

        return [];
    }
    
    /**
     * Check if tournament already exists for this event sport
     */
    private function tournamentExists($eventSportId) {
        $sql = "SELECT id FROM tournament_structures 
                WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$eventSportId]);
        return $stmt->fetch() !== false;
    }
    
    /**
     * Select optimal tournament format based on sport type and participant count
     */
    private function selectTournamentFormat($eventSport, $participantCount, $config) {
        // Use configured format if available
        if (!empty($eventSport['tournament_format_id'])) {
            $sql = "SELECT * FROM tournament_formats WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$eventSport['tournament_format_id']]);
            $format = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($format && $this->isFormatSuitableForParticipants($format, $participantCount)) {
                return $format;
            }
        }
        
        // Auto-select format using format selector
        return $this->formatSelector->selectFormat(
            $eventSport['event_id'],
            $eventSport['sport_id'],
            $participantCount
        );
    }
    
    /**
     * Validate format compatibility with participant count
     */
    private function validateFormatCompatibility($format, $participantCount) {
        if ($participantCount < ($format['min_participants'] ?? 2)) {
            throw new Exception("Insufficient participants. Format requires at least {$format['min_participants']} participants.");
        }
        
        if (!empty($format['max_participants']) && $participantCount > $format['max_participants']) {
            throw new Exception("Too many participants. Format supports maximum {$format['max_participants']} participants.");
        }
    }
    
    /**
     * Check if format is suitable for given participant count
     */
    private function isFormatSuitableForParticipants($format, $participantCount) {
        $minParticipants = $format['min_participants'] ?? 2;
        $maxParticipants = $format['max_participants'] ?? null;
        
        return $participantCount >= $minParticipants && 
               ($maxParticipants === null || $participantCount <= $maxParticipants);
    }
    
    /**
     * Generate tournament configuration based on format and user preferences
     */
    private function generateTournamentConfig($format, $userConfig) {
        // Parse format configuration
        $formatConfig = json_decode($format['configuration'] ?? '{}', true);
        
        // Merge with user configuration
        $config = array_merge([
            'seeding_method' => 'random',
            'bracket_seeding' => true,
            'auto_generated' => true,
            'comprehensive_validation' => true,
            'fallback_enabled' => true
        ], $formatConfig, $userConfig);
        
        return $config;
    }
    
    /**
     * Create tournament structure in database
     */
    private function createTournamentStructure($eventSportId, $format, $config) {
        $sql = "INSERT INTO tournament_structures
                (event_sport_id, tournament_format_id, name, participant_count, 
                 seeding_method, scoring_config, status)
                VALUES (?, ?, ?, ?, ?, ?, 'setup')";
        
        $tournamentName = "Tournament - " . date('Y-m-d H:i:s');
        $participantCount = count($this->getEventParticipants($eventSportId));
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([
            $eventSportId,
            $format['id'],
            $tournamentName,
            $participantCount,
            $config['seeding_method'] ?? 'random',
            json_encode($config['scoring_config'] ?? [])
        ]);
        
        return $this->conn->lastInsertId();
    }

    /**
     * Create tournament participants entries - simplified direct department linking
     */
    private function createTournamentParticipants($tournamentId, $participants) {
        foreach ($participants as $participant) {
            $departmentId = $participant['department_id'] ?? $participant['id'];
            $teamName = $participant['team_name'] ?? $participant['department_name'];

            $sql = "INSERT INTO tournament_participants (tournament_structure_id, department_id, team_name, current_status, created_at)
                    VALUES (?, ?, ?, 'active', NOW())";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([
                $tournamentId,
                $departmentId,
                $teamName
            ]);
        }
    }



    /**
     * Generate bracket using appropriate algorithm
     */
    private function generateBracket($format, $participants, $config) {
        $algorithm = $this->algorithmFactory->createRobustAlgorithm(
            $format,
            count($participants),
            null, // Tournament ID will be set later
            $config
        );
        
        return $algorithm->generateBracket($participants, $config);
    }
    
    /**
     * Save bracket data to tournament structure
     */
    private function saveBracketData($tournamentId, $bracket) {
        $sql = "UPDATE tournament_structures 
                SET bracket_data = ?, total_rounds = ?, status = 'in_progress'
                WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([
            json_encode($bracket),
            $bracket['rounds'],
            $tournamentId
        ]);
    }
    
    /**
     * Create tournament matches based on bracket structure
     */
    private function createTournamentMatches($tournamentId, $bracket) {
        require_once 'match_scheduler.php';

        $matchScheduler = new MatchScheduler($this->conn, $tournamentId);
        return $matchScheduler->createTournamentMatches($tournamentId, $bracket);
    }
    
    /**
     * Calculate total matches in bracket
     */
    private function calculateTotalMatches($bracket) {
        if (isset($bracket['structure']) && is_array($bracket['structure'])) {
            // Count matches in structure
            return $this->countMatchesInStructure($bracket['structure']);
        }
        
        // Fallback calculation
        $participantCount = count($bracket['participants'] ?? []);
        switch ($bracket['format']) {
            case 'single_elimination':
                return $participantCount - 1;
            case 'double_elimination':
                return 2 * $participantCount - 2;
            case 'round_robin':
                return $participantCount * ($participantCount - 1) / 2;
            default:
                return $participantCount - 1;
        }
    }
    
    /**
     * Count matches in bracket structure
     */
    private function countMatchesInStructure($structure) {
        $count = 0;
        
        if (is_array($structure)) {
            foreach ($structure as $item) {
                if (is_array($item)) {
                    $count += $this->countMatchesInStructure($item);
                } elseif (isset($item['matches'])) {
                    $count += count($item['matches']);
                }
            }
        }
        
        return $count;
    }
}
?>
