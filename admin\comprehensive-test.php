<?php
/**
 * Comprehensive Tournament System Test
 * Tests all components of the enhanced tournament management system
 */

require_once '../config/database.php';
require_once 'auth.php';

requireAdmin();

$database = new Database();
$conn = $database->getConnection();

echo "<h1>🏆 Enhanced Tournament System Test</h1>";

$test_results = [];

// Test 1: Database Schema Verification
echo "<h2>📋 Test 1: Database Schema Verification</h2>";
try {
    $required_tables = [
        'tournament_formats',
        'tournament_structures', 
        'tournament_participants',
        'event_department_registrations',
        'department_sport_participations',
        'matches'
    ];
    
    $schema_passed = true;
    foreach ($required_tables as $table) {
        $stmt = $conn->prepare("SHOW TABLES LIKE ?");
        $stmt->execute([$table]);
        if ($stmt->fetch()) {
            echo "<p style='color: green;'>✅ Table '$table' exists</p>";
        } else {
            echo "<p style='color: red;'>❌ Table '$table' missing</p>";
            $schema_passed = false;
        }
    }
    
    $test_results['schema'] = $schema_passed;
    echo "<p style='color: " . ($schema_passed ? 'green' : 'red') . ";'><strong>" . ($schema_passed ? '✅ Schema Test PASSED' : '❌ Schema Test FAILED') . "</strong></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Schema Test FAILED: " . $e->getMessage() . "</p>";
    $test_results['schema'] = false;
}

// Test 2: Tournament Format Configuration
echo "<h2>🎯 Test 2: Tournament Format Configuration</h2>";
try {
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats");
    $stmt->execute();
    $format_count = $stmt->fetch()['count'];
    
    if ($format_count > 0) {
        echo "<p style='color: green;'>✅ Found $format_count tournament formats</p>";
        
        // Show sample formats
        $stmt = $conn->prepare("SELECT name, code, sport_types FROM tournament_formats LIMIT 5");
        $stmt->execute();
        $formats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
        echo "<tr style='background: #3498db; color: white;'><th>Name</th><th>Code</th><th>Sport Types</th></tr>";
        foreach ($formats as $format) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($format['name']) . "</td>";
            echo "<td>" . htmlspecialchars($format['code']) . "</td>";
            echo "<td>" . htmlspecialchars($format['sport_types'] ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        $test_results['formats'] = true;
        echo "<p style='color: green;'><strong>✅ Tournament Formats Test PASSED</strong></p>";
    } else {
        echo "<p style='color: red;'>❌ No tournament formats found</p>";
        $test_results['formats'] = false;
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Tournament Formats Test FAILED: " . $e->getMessage() . "</p>";
    $test_results['formats'] = false;
}

// Test 3: Unified Registration System
echo "<h2>👥 Test 3: Unified Registration System</h2>";
try {
    // Check for event 4 registrations
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count 
        FROM event_department_registrations 
        WHERE event_id = 4
    ");
    $stmt->execute();
    $reg_count = $stmt->fetch()['count'];
    
    if ($reg_count > 0) {
        echo "<p style='color: green;'>✅ Found $reg_count department registrations for Event 4</p>";
        
        // Check sport participations
        $stmt = $conn->prepare("
            SELECT COUNT(*) as count 
            FROM department_sport_participations dsp
            JOIN event_department_registrations edr ON dsp.event_department_registration_id = edr.id
            WHERE edr.event_id = 4
        ");
        $stmt->execute();
        $part_count = $stmt->fetch()['count'];
        
        echo "<p style='color: green;'>✅ Found $part_count sport participations</p>";
        
        // Show sample registrations
        $stmt = $conn->prepare("
            SELECT d.name as department_name, edr.status, COUNT(dsp.id) as sports_count
            FROM event_department_registrations edr
            JOIN departments d ON edr.department_id = d.id
            LEFT JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
            WHERE edr.event_id = 4
            GROUP BY edr.id, d.name, edr.status
            LIMIT 5
        ");
        $stmt->execute();
        $registrations = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (!empty($registrations)) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0; width: 100%;'>";
            echo "<tr style='background: #3498db; color: white;'><th>Department</th><th>Status</th><th>Sports Count</th></tr>";
            foreach ($registrations as $reg) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($reg['department_name']) . "</td>";
                echo "<td>" . htmlspecialchars($reg['status']) . "</td>";
                echo "<td>" . $reg['sports_count'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        $test_results['registration'] = true;
        echo "<p style='color: green;'><strong>✅ Registration System Test PASSED</strong></p>";
    } else {
        echo "<p style='color: orange;'>⚠️ No registrations found for Event 4</p>";
        $test_results['registration'] = false;
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Registration System Test FAILED: " . $e->getMessage() . "</p>";
    $test_results['registration'] = false;
}

// Test 4: File Structure
echo "<h2>📁 Test 4: File Structure</h2>";
try {
    $required_files = [
        '../includes/advanced_tournament_engine.php' => 'Advanced Tournament Engine',
        '../includes/tournament_algorithms_advanced.php' => 'Tournament Algorithms',
        'ajax/advanced-tournament-management.php' => 'Advanced AJAX Handler',
        'includes/bracket_display.php' => 'Bracket Display Component',
        '../public/tournament-bracket.php' => 'Public Tournament View',
        '../referee/live-scoring.php' => 'Referee Live Scoring'
    ];
    
    $files_passed = true;
    foreach ($required_files as $file => $description) {
        if (file_exists($file)) {
            echo "<p style='color: green;'>✅ $description ($file)</p>";
        } else {
            echo "<p style='color: red;'>❌ $description missing ($file)</p>";
            $files_passed = false;
        }
    }
    
    $test_results['files'] = $files_passed;
    echo "<p style='color: " . ($files_passed ? 'green' : 'red') . ";'><strong>" . ($files_passed ? '✅ File Structure Test PASSED' : '❌ File Structure Test FAILED') . "</strong></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ File Structure Test FAILED: " . $e->getMessage() . "</p>";
    $test_results['files'] = false;
}

// Test 5: Tournament Management Interface
echo "<h2>🖥️ Test 5: Tournament Management Interface</h2>";
try {
    // Check if manage-category.php has been enhanced
    $manage_category_content = file_get_contents('manage-category.php');
    
    $required_features = [
        'AdvancedTournamentEngine' => 'Advanced Tournament Engine Integration',
        'BracketDisplay' => 'Bracket Display Component',
        'generateTournament()' => 'Tournament Generation Function',
        'editMatch(' => 'Match Editing Function',
        'tournament_format_name' => 'Format Name Display'
    ];
    
    $interface_passed = true;
    foreach ($required_features as $feature => $description) {
        if (strpos($manage_category_content, $feature) !== false) {
            echo "<p style='color: green;'>✅ $description</p>";
        } else {
            echo "<p style='color: red;'>❌ $description missing</p>";
            $interface_passed = false;
        }
    }
    
    $test_results['interface'] = $interface_passed;
    echo "<p style='color: " . ($interface_passed ? 'green' : 'red') . ";'><strong>" . ($interface_passed ? '✅ Interface Test PASSED' : '❌ Interface Test FAILED') . "</strong></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Interface Test FAILED: " . $e->getMessage() . "</p>";
    $test_results['interface'] = false;
}

// Final Results Summary
echo "<h2>📊 Test Results Summary</h2>";
$passed_tests = array_sum($test_results);
$total_tests = count($test_results);

echo "<div style='background: " . ($passed_tests == $total_tests ? '#d4edda' : '#f8d7da') . "; padding: 20px; border-radius: 10px; margin: 20px 0; border: 1px solid " . ($passed_tests == $total_tests ? '#c3e6cb' : '#f5c6cb') . ";'>";
echo "<h3 style='margin-top: 0;'>Overall Result: " . ($passed_tests == $total_tests ? '✅ ALL TESTS PASSED' : '⚠️ SOME TESTS FAILED') . "</h3>";
echo "<p><strong>Passed:</strong> $passed_tests / $total_tests tests</p>";

echo "<h4>Test Details:</h4>";
echo "<ul>";
foreach ($test_results as $test => $result) {
    $status = $result ? '✅ PASSED' : '❌ FAILED';
    echo "<li><strong>" . ucfirst($test) . ":</strong> $status</li>";
}
echo "</ul>";

if ($passed_tests == $total_tests) {
    echo "<h4>🎉 System Ready!</h4>";
    echo "<p>The enhanced tournament management system is fully operational with:</p>";
    echo "<ul>";
    echo "<li>✅ Automatic data inheritance from unified registration system</li>";
    echo "<li>✅ Advanced bracket generation with multiple tournament formats</li>";
    echo "<li>✅ Interactive match management interface</li>";
    echo "<li>✅ Live scoring capabilities for referees</li>";
    echo "<li>✅ Real-time public tournament viewing</li>";
    echo "</ul>";
    
    echo "<h4>🔗 Quick Links:</h4>";
    echo "<ul>";
    echo "<li><a href='manage-category.php?event_id=4&sport_id=37&category_id=16' target='_blank'>Admin Tournament Management</a></li>";
    echo "<li><a href='../public/tournament-bracket.php?event_id=4&sport_id=37' target='_blank'>Public Tournament View</a></li>";
    echo "<li><a href='debug-event-participants.php?event_id=4&sport_id=37' target='_blank'>Debug Participants</a></li>";
    echo "</ul>";
} else {
    echo "<h4>⚠️ Issues Found</h4>";
    echo "<p>Please address the failed tests before using the system in production.</p>";
}

echo "</div>";
?>

<style>
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    background: #f8f9fa;
    line-height: 1.6;
}

h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5rem;
}

h2 {
    color: #34495e;
    border-bottom: 3px solid #3498db;
    padding-bottom: 10px;
    margin-top: 40px;
    margin-bottom: 20px;
}

h3, h4 {
    color: #2c3e50;
}

table {
    width: 100%;
    margin: 15px 0;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

th, td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

th {
    background: #3498db;
    color: white;
    font-weight: 600;
}

tr:nth-child(even) {
    background: #f8f9fa;
}

tr:hover {
    background: #e8f4f8;
}

a {
    color: #3498db;
    text-decoration: none;
    font-weight: 500;
}

a:hover {
    text-decoration: underline;
    color: #2980b9;
}

p {
    margin: 8px 0;
}

ul {
    margin: 10px 0;
    padding-left: 20px;
}

li {
    margin: 5px 0;
}
</style>
