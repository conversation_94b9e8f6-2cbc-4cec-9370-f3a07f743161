<?php
/**
 * Instant Fix Navigation
 * One-click solution to fix category navigation
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Auto-fix if requested
if (isset($_GET['autofix']) && $_GET['autofix'] === '1') {
    try {
        $conn->beginTransaction();
        
        // Step 1: Ensure basic data exists
        $event_count = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
        if ($event_count == 0) {
            $conn->exec("INSERT INTO events (name, description, start_date, end_date, venue, status) VALUES ('Test Event', 'Test event', CURDATE(), DATE_ADD(CURDATE(), INTERVAL 7 DAY), 'Test Venue', 'active')");
        }
        
        $sport_count = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
        if ($sport_count == 0) {
            $conn->exec("INSERT INTO sports (name, type, description) VALUES ('Basketball', 'traditional', 'Test sport')");
        }
        
        $es_count = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
        if ($es_count == 0) {
            $conn->exec("INSERT INTO event_sports (event_id, sport_id) SELECT e.id, s.id FROM events e, sports s LIMIT 1");
        }
        
        // Step 2: Create categories if none exist
        $cat_count = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
        if ($cat_count == 0) {
            $stmt = $conn->prepare("INSERT INTO sport_categories (event_sport_id, category_name, category_type, referee_name, venue) VALUES (?, ?, ?, ?, ?)");
            
            // Get the first event_sport_id
            $es_id = $conn->query("SELECT id FROM event_sports LIMIT 1")->fetchColumn();
            
            $categories = [
                ['Men\'s Basketball', 'men'],
                ['Women\'s Basketball', 'women'],
                ['Mixed Basketball', 'mixed']
            ];
            
            foreach ($categories as $cat) {
                $stmt->execute([$es_id, $cat[0], $cat[1], 'Test Referee', 'Sports Hall']);
            }
        }
        
        // Step 3: Create departments if none exist
        $dept_count = $conn->query("SELECT COUNT(*) FROM departments")->fetchColumn();
        if ($dept_count == 0) {
            $departments = [
                ['Computer Science', 'CS', '#007bff'],
                ['Engineering', 'ENG', '#28a745'],
                ['Business', 'BUS', '#ffc107']
            ];
            
            $stmt = $conn->prepare("INSERT INTO departments (name, abbreviation, color_code) VALUES (?, ?, ?)");
            foreach ($departments as $dept) {
                $stmt->execute($dept);
            }
        }
        
        // Step 4: Create registrations if none exist
        $reg_count = $conn->query("SELECT COUNT(*) FROM event_department_registrations")->fetchColumn();
        if ($reg_count == 0) {
            $conn->exec("INSERT INTO event_department_registrations (event_id, department_id, registration_date) SELECT e.id, d.id, CURDATE() FROM events e CROSS JOIN departments d");
        }
        
        $conn->commit();
        
        header('Location: instant-fix-navigation.php?success=1');
        exit;
        
    } catch (Exception $e) {
        $conn->rollback();
        header('Location: instant-fix-navigation.php?error=' . urlencode($e->getMessage()));
        exit;
    }
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Instant Fix Navigation</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>⚡ Instant Fix Navigation</h1>
        <p>One-click solution to fix category navigation issues</p>
        
        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success">
                <h4>✅ Fix Applied Successfully!</h4>
                <p>Test data has been created. Navigation should work now.</p>
            </div>
        <?php endif; ?>
        
        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger">
                <h4>❌ Error Occurred</h4>
                <p><?php echo htmlspecialchars($_GET['error']); ?></p>
            </div>
        <?php endif; ?>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>Current Status</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    $events = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
                    $sports = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
                    $event_sports = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
                    $categories = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
                    $departments = $conn->query("SELECT COUNT(*) FROM departments")->fetchColumn();
                    $registrations = $conn->query("SELECT COUNT(*) FROM event_department_registrations")->fetchColumn();
                    
                    echo "<div class='row'>";
                    echo "<div class='col-md-2'><div class='text-center'><h4>$events</h4><small>Events</small></div></div>";
                    echo "<div class='col-md-2'><div class='text-center'><h4>$sports</h4><small>Sports</small></div></div>";
                    echo "<div class='col-md-2'><div class='text-center'><h4>$event_sports</h4><small>Event-Sports</small></div></div>";
                    echo "<div class='col-md-2'><div class='text-center'><h4>$categories</h4><small>Categories</small></div></div>";
                    echo "<div class='col-md-2'><div class='text-center'><h4>$departments</h4><small>Departments</small></div></div>";
                    echo "<div class='col-md-2'><div class='text-center'><h4>$registrations</h4><small>Registrations</small></div></div>";
                    echo "</div>";
                    
                    $all_good = ($events > 0 && $sports > 0 && $event_sports > 0 && $categories > 0 && $departments > 0 && $registrations > 0);
                    
                    if ($all_good) {
                        echo "<div class='alert alert-success mt-3'>";
                        echo "<h5>✅ All Required Data Present</h5>";
                        echo "<p>Navigation should work. Test the links below.</p>";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-warning mt-3'>";
                        echo "<h5>⚠️ Missing Required Data</h5>";
                        echo "<p>Some data is missing. Click the fix button below.</p>";
                        echo "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Database Error: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>Quick Fix</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5>🔧 What This Fix Does:</h5>
                    <ul>
                        <li>Creates test events, sports, and categories if missing</li>
                        <li>Establishes proper database relationships</li>
                        <li>Creates sample departments and registrations</li>
                        <li>Ensures category navigation will work</li>
                    </ul>
                </div>
                
                <a href="instant-fix-navigation.php?autofix=1" class="btn btn-success btn-lg">
                    <i class="fas fa-magic"></i> Apply Instant Fix
                </a>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3>Test Navigation</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    // Get working test links
                    $stmt = $conn->prepare("
                        SELECT 
                            sc.id as category_id,
                            sc.category_name,
                            es.event_id,
                            es.sport_id,
                            e.name as event_name,
                            s.name as sport_name
                        FROM sport_categories sc
                        JOIN event_sports es ON sc.event_sport_id = es.id
                        JOIN events e ON es.event_id = e.id
                        JOIN sports s ON es.sport_id = s.id
                        ORDER BY sc.id
                        LIMIT 5
                    ");
                    $stmt->execute();
                    $test_links = $stmt->fetchAll();
                    
                    if ($test_links) {
                        echo "<h5>✅ Working Test Links:</h5>";
                        echo "<div class='row'>";
                        
                        foreach ($test_links as $link) {
                            $categories_url = "sport-categories.php?event_id={$link['event_id']}&sport_id={$link['sport_id']}";
                            $manage_url = "manage-category.php?event_id={$link['event_id']}&sport_id={$link['sport_id']}&category_id={$link['category_id']}";
                            
                            echo "<div class='col-md-6 mb-3'>";
                            echo "<div class='card'>";
                            echo "<div class='card-body'>";
                            echo "<h6 class='card-title'>" . htmlspecialchars($link['category_name']) . "</h6>";
                            echo "<p class='card-text'><small class='text-muted'>" . htmlspecialchars($link['event_name']) . " - " . htmlspecialchars($link['sport_name']) . "</small></p>";
                            echo "<div class='d-flex gap-2'>";
                            echo "<a href='$categories_url' class='btn btn-primary btn-sm' target='_blank'>";
                            echo "<i class='fas fa-list'></i> Categories Page";
                            echo "</a>";
                            echo "<a href='$manage_url' class='btn btn-success btn-sm' target='_blank'>";
                            echo "<i class='fas fa-cog'></i> Direct Link";
                            echo "</a>";
                            echo "</div>";
                            echo "<p class='mt-2'><small class='text-info'>Click 'Categories Page' then click on the category name to test navigation</small></p>";
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                        }
                        
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-warning'>";
                        echo "<h5>No Test Links Available</h5>";
                        echo "<p>Apply the instant fix first to create test data.</p>";
                        echo "</div>";
                    }
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>Error: " . $e->getMessage() . "</div>";
                }
                ?>
            </div>
        </div>
    </div>
</body>
</html>
