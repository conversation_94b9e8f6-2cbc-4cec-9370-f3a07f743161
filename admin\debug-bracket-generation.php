<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🔍 Debug Tournament Bracket Generation</h1>";
echo "<p>Investigating why tournament bracket generation is failing...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Test the exact same parameters that would be used in the real request
    $event_id = 4;
    $sport_id = 37;
    $category_id = 15;
    
    echo "<h2>1. Check Event Sport Configuration</h2>";
    
    $stmt = $conn->prepare("
        SELECT es.*, e.name as event_name, s.name as sport_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    
    if (!$event_sport) {
        echo "<p style='color: red;'>❌ Event sport not found!</p>";
        exit;
    }
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><td><strong>Event Sport ID</strong></td><td>{$event_sport['id']}</td></tr>";
    echo "<tr><td><strong>Event</strong></td><td>{$event_sport['event_name']}</td></tr>";
    echo "<tr><td><strong>Sport</strong></td><td>{$event_sport['sport_name']}</td></tr>";
    echo "<tr><td><strong>Tournament Format ID</strong></td><td>{$event_sport['tournament_format_id']}</td></tr>";
    echo "<tr><td><strong>Status</strong></td><td>{$event_sport['status']}</td></tr>";
    echo "</table>";
    
    echo "<h2>2. Check Participants</h2>";
    
    $stmt = $conn->prepare("
        SELECT r.*, d.name as department_name
        FROM registrations r
        JOIN departments d ON r.department_id = d.id
        WHERE r.event_sport_id = ? AND r.status IN ('confirmed', 'registered')
    ");
    $stmt->execute([$event_sport['id']]);
    $participants = $stmt->fetchAll();
    
    echo "<p><strong>Participant Count:</strong> " . count($participants) . "</p>";
    
    if (empty($participants)) {
        echo "<p style='color: red;'>❌ No participants found!</p>";
        exit;
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th>ID</th><th>Department</th><th>Team Name</th><th>Status</th><th>Participants</th>";
    echo "</tr>";
    foreach ($participants as $p) {
        $participant_list = json_decode($p['participants'], true) ?: [];
        echo "<tr>";
        echo "<td>{$p['id']}</td>";
        echo "<td>{$p['department_name']}</td>";
        echo "<td>" . ($p['team_name'] ?: 'N/A') . "</td>";
        echo "<td>{$p['status']}</td>";
        echo "<td>" . count($participant_list) . " people</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>3. Check Tournament Format</h2>";
    
    $stmt = $conn->prepare("SELECT * FROM tournament_formats WHERE id = ?");
    $stmt->execute([$event_sport['tournament_format_id']]);
    $format = $stmt->fetch();
    
    if (!$format) {
        echo "<p style='color: red;'>❌ Tournament format not found!</p>";
        exit;
    }
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><td><strong>Format ID</strong></td><td>{$format['id']}</td></tr>";
    echo "<tr><td><strong>Name</strong></td><td>{$format['name']}</td></tr>";
    echo "<tr><td><strong>Code</strong></td><td>{$format['code']}</td></tr>";
    echo "<tr><td><strong>Algorithm Class</strong></td><td>" . ($format['algorithm_class'] ?? 'NULL') . "</td></tr>";
    echo "<tr><td><strong>Min Participants</strong></td><td>{$format['min_participants']}</td></tr>";
    echo "</table>";
    
    echo "<h2>4. Check Algorithm Class Availability</h2>";
    
    $algorithm_class = $format['algorithm_class'] ?? 'SingleEliminationAlgorithm';
    
    try {
        require_once '../includes/tournament_algorithms.php';
        
        if (class_exists($algorithm_class)) {
            echo "<p style='color: green;'>✅ Algorithm class '{$algorithm_class}' exists</p>";
            
            // Test creating an instance
            $algorithm = new $algorithm_class();
            echo "<p style='color: green;'>✅ Algorithm instance created successfully</p>";
            
            // Test bracket generation
            echo "<h3>Testing Bracket Generation:</h3>";
            
            $test_participants = [];
            foreach ($participants as $i => $p) {
                $test_participants[] = [
                    'id' => $p['id'],
                    'name' => $p['department_name'],
                    'seed' => $i + 1
                ];
            }
            
            echo "<p>Test participants prepared: " . count($test_participants) . "</p>";
            
            try {
                $bracket = $algorithm->generateBracket($test_participants, ['seeding_method' => 'random']);
                echo "<p style='color: green;'>✅ Bracket generated successfully!</p>";
                echo "<pre>" . json_encode($bracket, JSON_PRETTY_PRINT) . "</pre>";
                
            } catch (Exception $e) {
                echo "<p style='color: red;'>❌ Bracket generation failed: " . $e->getMessage() . "</p>";
                echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
            }
            
        } else {
            echo "<p style='color: red;'>❌ Algorithm class '{$algorithm_class}' does not exist!</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error loading algorithm: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>5. Check Existing Tournaments</h2>";
    
    $stmt = $conn->prepare("
        SELECT * FROM tournament_structures 
        WHERE event_sport_id = ? 
        ORDER BY created_at DESC
    ");
    $stmt->execute([$event_sport['id']]);
    $existing_tournaments = $stmt->fetchAll();
    
    if (empty($existing_tournaments)) {
        echo "<p style='color: blue;'>ℹ️ No existing tournaments found</p>";
    } else {
        echo "<p style='color: orange;'>⚠️ Found " . count($existing_tournaments) . " existing tournaments:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>ID</th><th>Name</th><th>Status</th><th>Participants</th><th>Created</th>";
        echo "</tr>";
        foreach ($existing_tournaments as $t) {
            echo "<tr>";
            echo "<td>{$t['id']}</td>";
            echo "<td>{$t['name']}</td>";
            echo "<td>{$t['status']}</td>";
            echo "<td>{$t['participant_count']}</td>";
            echo "<td>{$t['created_at']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>6. Test Tournament Manager</h2>";
    
    try {
        require_once '../includes/tournament_manager.php';
        $tournamentManager = new TournamentManager($conn);
        echo "<p style='color: green;'>✅ TournamentManager loaded successfully</p>";
        
        // Test getting participants
        $manager_participants = $tournamentManager->getEventSportParticipants($event_sport['id']);
        echo "<p>TournamentManager found " . count($manager_participants) . " participants</p>";
        
        if (count($manager_participants) >= 2) {
            echo "<p style='color: green;'>✅ Sufficient participants for tournament creation</p>";
        } else {
            echo "<p style='color: red;'>❌ Insufficient participants</p>";
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ TournamentManager error: " . $e->getMessage() . "</p>";
        echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
    }
    
    echo "<h2>7. Simulate Tournament Creation</h2>";
    
    if (count($participants) >= 2 && $format && class_exists($algorithm_class)) {
        echo "<p style='color: blue;'>🧪 Attempting to simulate tournament creation...</p>";
        
        try {
            // Check for existing active tournaments first
            $stmt = $conn->prepare("
                SELECT COUNT(*) as count
                FROM tournament_structures
                WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')
            ");
            $stmt->execute([$event_sport['id']]);
            $active_count = $stmt->fetch()['count'];
            
            if ($active_count > 0) {
                echo "<p style='color: orange;'>⚠️ Active tournament already exists - this might be the issue!</p>";
                echo "<p>The system prevents creating multiple active tournaments for the same event sport.</p>";
                
                // Show option to cancel existing tournaments
                echo "<h3>Existing Active Tournaments:</h3>";
                $stmt = $conn->prepare("
                    SELECT * FROM tournament_structures 
                    WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')
                ");
                $stmt->execute([$event_sport['id']]);
                $active_tournaments = $stmt->fetchAll();
                
                foreach ($active_tournaments as $t) {
                    echo "<p>Tournament ID {$t['id']}: {$t['name']} (Status: {$t['status']})</p>";
                    echo "<p><a href='?cancel_tournament={$t['id']}' style='background: #dc3545; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Cancel This Tournament</a></p>";
                }
                
            } else {
                echo "<p style='color: green;'>✅ No active tournaments found - creation should be possible</p>";
                
                // Test the actual creation process
                $config = [
                    'seeding_method' => 'random',
                    'bracket_seeding' => true,
                    'auto_generated' => true
                ];
                
                $tournament_name = "Test Tournament - " . date('Y-m-d H:i:s');
                
                echo "<p>Attempting to create tournament with:</p>";
                echo "<ul>";
                echo "<li>Event Sport ID: {$event_sport['id']}</li>";
                echo "<li>Format ID: {$format['id']}</li>";
                echo "<li>Name: {$tournament_name}</li>";
                echo "<li>Participants: " . count($participants) . "</li>";
                echo "</ul>";
                
                // This would be the actual creation call
                echo "<p style='color: blue;'>ℹ️ Ready for tournament creation - all prerequisites met!</p>";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Simulation failed: " . $e->getMessage() . "</p>";
            echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Prerequisites not met for tournament creation</p>";
    }
    
    // Handle tournament cancellation if requested
    if (isset($_GET['cancel_tournament'])) {
        $tournament_id = $_GET['cancel_tournament'];
        $stmt = $conn->prepare("UPDATE tournament_structures SET status = 'cancelled' WHERE id = ?");
        $stmt->execute([$tournament_id]);
        echo "<p style='color: green;'>✅ Tournament {$tournament_id} has been cancelled</p>";
        echo "<p><a href='debug-bracket-generation.php'>Refresh Page</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}
?>
