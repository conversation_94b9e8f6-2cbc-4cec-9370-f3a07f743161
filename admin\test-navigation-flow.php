<?php
/**
 * Test Navigation Flow
 * Test the complete navigation from sport-categories.php to manage-category.php
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🧪 Navigation Flow Test</h1>";

try {
    // Get available navigation paths
    $stmt = $conn->prepare("
        SELECT 
            sc.id as category_id,
            sc.category_name,
            sc.category_type,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        ORDER BY e.name, s.name, sc.category_name
        LIMIT 10
    ");
    $stmt->execute();
    $navigation_paths = $stmt->fetchAll();
    
    if (empty($navigation_paths)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107;'>";
        echo "<h3>⚠️ No Navigation Paths Available</h3>";
        echo "<p>No sport categories found for testing navigation. Please create test data first.</p>";
        echo "<a href='check-navigation-data.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Check & Create Test Data</a>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745;'>";
        echo "<h3>✅ Navigation Paths Available</h3>";
        echo "<p>Found " . count($navigation_paths) . " navigation paths for testing.</p>";
        echo "</div>";
        
        echo "<h2>🔗 Test Navigation Links</h2>";
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px;'>";
        
        foreach ($navigation_paths as $path) {
            $categories_url = "sport-categories.php?event_id={$path['event_id']}&sport_id={$path['sport_id']}";
            $manage_url = "manage-category.php?event_id={$path['event_id']}&sport_id={$path['sport_id']}&category_id={$path['category_id']}";
            $debug_url = "manage-category.php?event_id={$path['event_id']}&sport_id={$path['sport_id']}&category_id={$path['category_id']}&debug=1";
            
            echo "<div style='border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; background: white;'>";
            echo "<h4 style='margin-top: 0; color: #007bff;'>{$path['event_name']} → {$path['sport_name']}</h4>";
            echo "<p><strong>Category:</strong> {$path['category_name']} ({$path['category_type']})</p>";
            
            echo "<div style='display: flex; flex-direction: column; gap: 8px;'>";
            echo "<a href='$categories_url' target='_blank' style='background: #007bff; color: white; padding: 8px 12px; text-decoration: none; border-radius: 4px; text-align: center;'>";
            echo "📋 Step 1: Categories List";
            echo "</a>";
            
            echo "<a href='$manage_url' target='_blank' style='background: #28a745; color: white; padding: 8px 12px; text-decoration: none; border-radius: 4px; text-align: center;'>";
            echo "⚙️ Step 2: Manage Category";
            echo "</a>";
            
            echo "<a href='$debug_url' target='_blank' style='background: #ffc107; color: black; padding: 8px 12px; text-decoration: none; border-radius: 4px; text-align: center;'>";
            echo "🐛 Debug Mode";
            echo "</a>";
            echo "</div>";
            
            echo "<div style='margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 0.9em;'>";
            echo "<strong>Test Flow:</strong><br>";
            echo "1. Click 'Categories List' to see the sport-categories.php page<br>";
            echo "2. Click on the category name in the table<br>";
            echo "3. Verify it navigates to manage-category.php with 3 tabs<br>";
            echo "4. Use 'Debug Mode' if there are issues";
            echo "</div>";
            echo "</div>";
        }
        echo "</div>";
        
        // Test specific navigation scenarios
        echo "<h2>🎯 Specific Test Scenarios</h2>";
        
        echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; margin-bottom: 20px;'>";
        echo "<h4>Test Scenario 1: Valid Navigation</h4>";
        echo "<p>Test with the first available category:</p>";
        if (!empty($navigation_paths)) {
            $first_path = $navigation_paths[0];
            $test_url = "manage-category.php?event_id={$first_path['event_id']}&sport_id={$first_path['sport_id']}&category_id={$first_path['category_id']}";
            echo "<a href='$test_url' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Valid Navigation</a>";
            echo "<p><small>URL: $test_url</small></p>";
        }
        echo "</div>";
        
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin-bottom: 20px;'>";
        echo "<h4>Test Scenario 2: Invalid Parameters</h4>";
        echo "<p>Test with invalid parameters to verify error handling:</p>";
        $invalid_url = "manage-category.php?event_id=999&sport_id=999&category_id=999&debug=1";
        echo "<a href='$invalid_url' target='_blank' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Invalid Parameters</a>";
        echo "<p><small>URL: $invalid_url</small></p>";
        echo "</div>";
        
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545;'>";
        echo "<h4>Test Scenario 3: Missing Parameters</h4>";
        echo "<p>Test with missing parameters:</p>";
        $missing_url = "manage-category.php?debug=1";
        echo "<a href='$missing_url' target='_blank' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Missing Parameters</a>";
        echo "<p><small>URL: $missing_url</small></p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<h2>📝 Navigation Test Checklist</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h4>Expected Behavior:</h4>";
echo "<ol>";
echo "<li><strong>sport-categories.php</strong> should display a table with clickable category names</li>";
echo "<li><strong>Category name links</strong> should point to manage-category.php with correct parameters</li>";
echo "<li><strong>manage-category.php</strong> should display without redirecting to events.php</li>";
echo "<li><strong>Three tabs</strong> (Overview, Fixtures, Standings) should be visible and functional</li>";
echo "<li><strong>Tab switching</strong> should work smoothly with JavaScript</li>";
echo "<li><strong>AJAX functionality</strong> should work for real-time updates</li>";
echo "</ol>";

echo "<h4>Common Issues to Check:</h4>";
echo "<ul>";
echo "<li>Category links pointing to wrong file (manage-category-new.php vs manage-category.php)</li>";
echo "<li>Missing or invalid URL parameters</li>";
echo "<li>Database query not finding the category</li>";
echo "<li>JavaScript errors preventing tab functionality</li>";
echo "<li>Missing CSS/JS includes</li>";
echo "</ul>";
echo "</div>";
?>
