<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🤖 Automatic Tournament Generation Test</h1>";
echo "<p>Testing the new automatic tournament generation system...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Test parameters
    $event_id = 4;
    $sport_id = 37;
    $category_id = 15;
    
    echo "<h2>1. Check Current State</h2>";
    
    // Get event sport
    $stmt = $conn->prepare("
        SELECT es.*, tf.name as format_name
        FROM event_sports es
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    
    if (!$event_sport) {
        echo "<p style='color: red;'>❌ Event sport not found</p>";
        exit;
    }
    
    echo "<p style='color: green;'>✅ Event Sport ID: {$event_sport['id']}</p>";
    echo "<p>Tournament Format: " . ($event_sport['format_name'] ?? 'Not configured') . "</p>";
    
    // Check participants
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM registrations r
        WHERE r.event_sport_id = ? AND r.status IN ('confirmed', 'registered', 'approved')
    ");
    $stmt->execute([$event_sport['id']]);
    $participant_count = $stmt->fetch()['count'];
    
    echo "<p>Participants: {$participant_count}</p>";
    
    // Check existing tournaments
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM tournament_structures
        WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')
    ");
    $stmt->execute([$event_sport['id']]);
    $active_tournament_count = $stmt->fetch()['count'];
    
    echo "<p>Active Tournaments: {$active_tournament_count}</p>";
    
    echo "<h2>2. Auto-Generation Conditions</h2>";
    
    $conditions_met = [];
    $conditions_failed = [];
    
    if ($participant_count >= 2) {
        $conditions_met[] = "✅ Sufficient participants ({$participant_count} >= 2)";
    } else {
        $conditions_failed[] = "❌ Insufficient participants ({$participant_count} < 2)";
    }
    
    if ($event_sport['tournament_format_id']) {
        $conditions_met[] = "✅ Tournament format configured";
    } else {
        $conditions_failed[] = "❌ No tournament format configured";
    }
    
    if ($active_tournament_count == 0) {
        $conditions_met[] = "✅ No active tournaments exist";
    } else {
        $conditions_failed[] = "❌ Active tournament already exists";
    }
    
    foreach ($conditions_met as $condition) {
        echo "<p style='color: green;'>{$condition}</p>";
    }
    
    foreach ($conditions_failed as $condition) {
        echo "<p style='color: red;'>{$condition}</p>";
    }
    
    echo "<h2>3. Expected Behavior</h2>";
    
    if (empty($conditions_failed)) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🎯 AUTO-GENERATION SHOULD OCCUR</h3>";
        echo "<p>All conditions are met. When you visit the category management page, a tournament should be automatically generated.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 2px solid #ffc107; margin: 20px 0;'>";
        echo "<h3 style='color: #856404; margin-top: 0;'>⚠️ AUTO-GENERATION WILL NOT OCCUR</h3>";
        echo "<p>Some conditions are not met. Fix the issues above for auto-generation to work.</p>";
        echo "</div>";
    }
    
    echo "<h2>4. Test the System</h2>";
    
    if (!empty($conditions_failed)) {
        echo "<h3>Fix Issues First:</h3>";
        
        if ($participant_count < 2) {
            echo "<p><a href='add-test-participants.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Add More Participants</a></p>";
        }
        
        if (!$event_sport['tournament_format_id']) {
            echo "<p><a href='fix-tournament-format-constraints.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Fix Tournament Format</a></p>";
        }
        
        if ($active_tournament_count > 0) {
            echo "<p><a href='?cancel_tournaments=1' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Cancel Active Tournaments</a></p>";
        }
    }
    
    echo "<h3>Test Auto-Generation:</h3>";
    echo "<p><a href='manage-category.php?category_id={$category_id}&event_id={$event_id}&sport_id={$sport_id}' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🚀 Go to Category Management (Auto-Generate)</a></p>";
    
    echo "<h2>5. How It Works</h2>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6; margin: 20px 0;'>";
    echo "<h4>Automatic Tournament Generation Process:</h4>";
    echo "<ol>";
    echo "<li><strong>Page Load:</strong> When manage-category.php loads, it checks conditions</li>";
    echo "<li><strong>Condition Check:</strong> Verifies participants (≥2), tournament format, and no active tournaments</li>";
    echo "<li><strong>Auto-Generation:</strong> If conditions are met, creates tournament automatically</li>";
    echo "<li><strong>Display:</strong> Shows matches immediately without manual intervention</li>";
    echo "<li><strong>User Experience:</strong> No 'Generate Matches' button needed - everything is automatic</li>";
    echo "</ol>";
    echo "</div>";
    
    // Handle tournament cancellation
    if (isset($_GET['cancel_tournaments'])) {
        $stmt = $conn->prepare("UPDATE tournament_structures SET status = 'cancelled' WHERE event_sport_id = ? AND status NOT IN ('cancelled', 'completed')");
        $stmt->execute([$event_sport['id']]);
        $cancelled = $stmt->rowCount();
        echo "<p style='color: green;'>✅ Cancelled {$cancelled} active tournaments</p>";
        echo "<p><a href='test-auto-tournament.php'>Refresh and test again</a></p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace: " . $e->getTraceAsString() . "</p>";
}
?>
