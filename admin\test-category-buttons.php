<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Category Buttons</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .btn { padding: 10px 20px; margin: 10px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        .btn:hover { background: #0056b3; }
        .tab-button { padding: 10px 20px; margin: 5px; background: #f8f9fa; border: 1px solid #dee2e6; cursor: pointer; }
        .tab-button.active { background: #007bff; color: white; }
        .tab-content { display: none; padding: 20px; border: 1px solid #dee2e6; margin-top: 10px; }
        .tab-content.active { display: block; }
        .modal { display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; }
        .modal.show { display: flex; align-items: center; justify-content: center; }
        .modal-dialog { background: white; padding: 20px; border-radius: 8px; max-width: 500px; width: 90%; }
        .form-control { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🧪 Category Button Functionality Test</h1>
    
    <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2>Tab Navigation Test</h2>
        <div>
            <button class="tab-button active" onclick="showTab('overview', this)">
                📋 Overview
            </button>
            <button class="tab-button" onclick="showTab('fixtures', this)">
                🏆 Fixtures
            </button>
            <button class="tab-button" onclick="showTab('standings', this)">
                📊 Standings
            </button>
        </div>
        
        <div id="overview-tab" class="tab-content active">
            <h3>Overview Tab Content</h3>
            <p>This is the overview tab. It should be visible by default.</p>
        </div>
        
        <div id="fixtures-tab" class="tab-content">
            <h3>Fixtures Tab Content</h3>
            <p>This is the fixtures tab. Click the Fixtures button to see this.</p>
        </div>
        
        <div id="standings-tab" class="tab-content">
            <h3>Standings Tab Content</h3>
            <p>This is the standings tab. Click the Standings button to see this.</p>
        </div>
    </div>
    
    <div style="background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
        <h2>Modal Test</h2>
        <button class="btn" onclick="openTestModal()">
            ✏️ Open Test Modal
        </button>
        <p id="modal-status">Modal status: Closed</p>
    </div>
    
    <div style="background: #f0f8f0; padding: 20px; border-radius: 8px;">
        <h2>JavaScript Console Test</h2>
        <button class="btn" onclick="testConsole()">
            🔍 Test Console Output
        </button>
        <div id="console-output" style="background: #fff; padding: 10px; margin-top: 10px; border: 1px solid #ddd; border-radius: 4px; font-family: monospace; min-height: 50px;"></div>
    </div>

    <!-- Test Modal -->
    <div id="testModal" class="modal">
        <div class="modal-dialog">
            <h3>Test Modal</h3>
            <p>This is a test modal to verify modal functionality.</p>
            <input type="text" class="form-control" placeholder="Test input field" id="test-input">
            <div style="margin-top: 15px;">
                <button class="btn" onclick="closeTestModal()">Close Modal</button>
                <button class="btn" onclick="testModalInput()">Test Input</button>
            </div>
        </div>
    </div>

    <script>
        console.log('🚀 JavaScript loaded successfully!');
        
        // Tab switching functionality
        function showTab(tabName, clickedButton) {
            console.log('showTab called with:', tabName, clickedButton);
            
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => {
                content.classList.remove('active');
            });

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => {
                button.classList.remove('active');
            });

            // Show selected tab content
            const targetTab = document.getElementById(tabName + '-tab');
            if (targetTab) {
                targetTab.classList.add('active');
                console.log('✅ Tab switched to:', tabName);
            } else {
                console.error('❌ Tab not found:', tabName + '-tab');
            }

            // Add active class to clicked button
            if (clickedButton) {
                clickedButton.classList.add('active');
            }
        }
        
        // Modal functions
        function openTestModal() {
            console.log('openTestModal called');
            const modal = document.getElementById('testModal');
            modal.style.display = 'flex';
            modal.classList.add('show');
            document.getElementById('modal-status').textContent = 'Modal status: Open';
            console.log('✅ Modal opened');
        }
        
        function closeTestModal() {
            console.log('closeTestModal called');
            const modal = document.getElementById('testModal');
            modal.classList.remove('show');
            setTimeout(() => {
                modal.style.display = 'none';
            }, 300);
            document.getElementById('modal-status').textContent = 'Modal status: Closed';
            console.log('✅ Modal closed');
        }
        
        function testModalInput() {
            const input = document.getElementById('test-input');
            const value = input.value;
            alert('Input value: ' + value);
            console.log('Input value:', value);
        }
        
        function testConsole() {
            const output = document.getElementById('console-output');
            const timestamp = new Date().toLocaleTimeString();
            
            console.log('🧪 Console test at', timestamp);
            console.log('✅ All functions are working');
            console.log('📋 Tab function:', typeof showTab);
            console.log('🔧 Modal functions:', typeof openTestModal, typeof closeTestModal);
            
            output.innerHTML = `
                <div>🧪 Console test at ${timestamp}</div>
                <div>✅ showTab function: ${typeof showTab}</div>
                <div>✅ openTestModal function: ${typeof openTestModal}</div>
                <div>✅ closeTestModal function: ${typeof closeTestModal}</div>
                <div>📊 All functions are properly defined!</div>
            `;
        }
        
        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('testModal');
            if (event.target == modal) {
                closeTestModal();
            }
        };
        
        // Test on page load
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🎯 DOM Content Loaded');
            testConsole();
        });
    </script>
</body>
</html>
