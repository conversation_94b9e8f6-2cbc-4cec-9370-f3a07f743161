<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🔧 Fix Tournament Format Configuration</h1>";
echo "<p>Configuring tournament format for event sports...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    $event_id = $_GET['event_id'] ?? 4;
    $sport_id = $_GET['sport_id'] ?? 37;
    
    echo "<h2>1. Current Event Sport Configuration</h2>";
    
    // Get current event sport configuration
    $stmt = $conn->prepare("
        SELECT 
            es.*,
            e.name as event_name,
            s.name as sport_name,
            s.type as sport_type,
            tf.name as format_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    
    if (!$event_sport) {
        echo "<p style='color: red;'>❌ Event sport not found</p>";
        exit;
    }
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
    echo "<p><strong>Event:</strong> {$event_sport['event_name']}</p>";
    echo "<p><strong>Sport:</strong> {$event_sport['sport_name']} ({$event_sport['sport_type']})</p>";
    echo "<p><strong>Current Format ID:</strong> " . ($event_sport['tournament_format_id'] ?? 'NULL') . "</p>";
    echo "<p><strong>Current Format:</strong> " . ($event_sport['format_name'] ?? 'Not configured') . "</p>";
    echo "</div>";
    
    echo "<h2>2. Available Tournament Formats</h2>";
    
    // Get available tournament formats
    $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY name");
    $stmt->execute();
    $formats = $stmt->fetchAll();
    
    if (empty($formats)) {
        echo "<p style='color: red;'>❌ No tournament formats found in database</p>";
        echo "<h3>Creating Default Tournament Formats...</h3>";
        
        // Create default tournament formats
        $default_formats = [
            [
                'name' => 'Single Elimination',
                'description' => 'Traditional knockout tournament where teams/participants are eliminated after one loss',
                'code' => 'single_elimination',
                'algorithm_class' => 'SingleEliminationAlgorithm',
                'rounds_formula' => 'ceil(log2(n))',
                'matches_formula' => 'n - 1',
                'min_participants' => 2,
                'max_participants' => 128
            ],
            [
                'name' => 'Round Robin',
                'description' => 'Every team/participant plays every other team/participant once',
                'code' => 'round_robin',
                'algorithm_class' => 'RoundRobinAlgorithm',
                'rounds_formula' => 'n - 1',
                'matches_formula' => 'n * (n - 1) / 2',
                'min_participants' => 2,
                'max_participants' => 16
            ],
            [
                'name' => 'Double Elimination',
                'description' => 'Teams/participants must lose twice to be eliminated',
                'code' => 'double_elimination',
                'algorithm_class' => 'DoubleEliminationAlgorithm',
                'rounds_formula' => '2 * ceil(log2(n)) - 1',
                'matches_formula' => '2 * n - 2',
                'min_participants' => 2,
                'max_participants' => 64
            ]
        ];
        
        foreach ($default_formats as $format) {
            $stmt = $conn->prepare("
                INSERT INTO tournament_formats 
                (name, description, code, algorithm_class, rounds_formula, matches_formula, min_participants, max_participants)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $format['name'],
                $format['description'],
                $format['code'],
                $format['algorithm_class'],
                $format['rounds_formula'],
                $format['matches_formula'],
                $format['min_participants'],
                $format['max_participants']
            ]);
        }
        
        echo "<p style='color: green;'>✅ Created default tournament formats</p>";
        
        // Refresh formats list
        $stmt = $conn->prepare("SELECT * FROM tournament_formats ORDER BY name");
        $stmt->execute();
        $formats = $stmt->fetchAll();
    }
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th>ID</th><th>Name</th><th>Description</th><th>Min Participants</th><th>Action</th>";
    echo "</tr>";
    
    foreach ($formats as $format) {
        echo "<tr>";
        echo "<td>{$format['id']}</td>";
        echo "<td>{$format['name']}</td>";
        echo "<td>" . substr($format['description'], 0, 50) . "...</td>";
        echo "<td>{$format['min_participants']}</td>";
        echo "<td>";
        if ($format['id'] == $event_sport['tournament_format_id']) {
            echo "<span style='color: green; font-weight: bold;'>CURRENT</span>";
        } else {
            echo "<a href='?event_id={$event_id}&sport_id={$sport_id}&set_format={$format['id']}' style='background: #007bff; color: white; padding: 2px 8px; text-decoration: none; border-radius: 3px; font-size: 12px;'>Set Format</a>";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Handle format setting
    if (isset($_GET['set_format'])) {
        $format_id = $_GET['set_format'];
        
        echo "<h3>Setting Tournament Format...</h3>";
        
        $stmt = $conn->prepare("UPDATE event_sports SET tournament_format_id = ? WHERE event_id = ? AND sport_id = ?");
        $stmt->execute([$format_id, $event_id, $sport_id]);
        
        // Get format name
        $stmt = $conn->prepare("SELECT name FROM tournament_formats WHERE id = ?");
        $stmt->execute([$format_id]);
        $format_name = $stmt->fetchColumn();
        
        echo "<p style='color: green;'>✅ Tournament format set to: {$format_name}</p>";
        echo "<p><a href='fix-tournament-format.php?event_id={$event_id}&sport_id={$sport_id}'>Refresh Page</a></p>";
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🎉 Format Configuration Complete!</h3>";
        echo "<p>Tournament format has been configured successfully.</p>";
        echo "<p><a href='tournament-diagnostic-tool.php?event_id={$event_id}&sport_id={$sport_id}&category_id=15' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🔍 Run Diagnostic</a></p>";
        echo "<p><a href='manage-category.php?category_id=15&event_id={$event_id}&sport_id={$sport_id}' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-left: 10px;'>🚀 Test Auto-Generation</a></p>";
        echo "</div>";
    }
    
    echo "<h2>3. Recommendations</h2>";
    
    if (!$event_sport['tournament_format_id']) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 8px; border: 2px solid #ffc107; margin: 20px 0;'>";
        echo "<h4 style='color: #856404; margin-top: 0;'>⚠️ Action Required</h4>";
        echo "<p>No tournament format is currently configured for this event sport.</p>";
        echo "<p><strong>Recommended:</strong> Set 'Single Elimination' format for most sports.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h4 style='color: #155724; margin-top: 0;'>✅ Format Configured</h4>";
        echo "<p>Tournament format is properly configured.</p>";
        echo "</div>";
    }
    
    echo "<h3>Format Recommendations by Sport Type:</h3>";
    echo "<ul>";
    echo "<li><strong>Team Sports (Basketball, Volleyball, etc.):</strong> Single Elimination or Double Elimination</li>";
    echo "<li><strong>Individual Sports (Athletics, Swimming):</strong> Round Robin or Single Elimination</li>";
    echo "<li><strong>Academic Competitions:</strong> Round Robin or Swiss System</li>";
    echo "<li><strong>Large Tournaments (16+ participants):</strong> Single Elimination</li>";
    echo "<li><strong>Small Tournaments (2-8 participants):</strong> Round Robin</li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
