<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🔧 Tournament Management Diagnostic Tool</h1>";
echo "<p>Comprehensive analysis of tournament system configuration and requirements...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get parameters from URL or use defaults
    $event_id = $_GET['event_id'] ?? 4;
    $sport_id = $_GET['sport_id'] ?? 37;
    $category_id = $_GET['category_id'] ?? 15;
    
    echo "<div style='background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;'>";
    echo "<h3>🎯 Diagnostic Parameters</h3>";
    echo "<p><strong>Event ID:</strong> {$event_id} | <strong>Sport ID:</strong> {$sport_id} | <strong>Category ID:</strong> {$category_id}</p>";
    echo "</div>";
    
    $issues = [];
    $recommendations = [];
    $status_checks = [];
    
    // ===== 1. SPORT CATEGORY FORMAT DISPLAY ANALYSIS =====
    echo "<h2>1. 🏷️ Sport Category Format Display Analysis</h2>";
    
    // Get category information
    $stmt = $conn->prepare("
        SELECT
            sc.*,
            e.name as event_name,
            s.name as sport_name,
            s.type as sport_type,
            es.id as event_sport_id,
            es.tournament_format_id,
            es.bracket_type,
            tf.name as format_name,
            tf.description as format_description,
            tf.code as format_code,
            tf.algorithm_class,
            tf.min_participants,
            tf.max_participants
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE sc.id = ? AND es.event_id = ? AND s.id = ?
    ");
    $stmt->execute([$category_id, $event_id, $sport_id]);
    $category = $stmt->fetch();
    
    if (!$category) {
        $issues[] = "❌ Category not found with given parameters";
        echo "<p style='color: red;'>❌ Category not found</p>";
    } else {
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;'>";
        echo "<h4>Category Information:</h4>";
        echo "<p><strong>Category Name:</strong> {$category['category_name']}</p>";
        echo "<p><strong>Sport:</strong> {$category['sport_name']} ({$category['sport_type']})</p>";
        echo "<p><strong>Event:</strong> {$category['event_name']}</p>";
        echo "<p><strong>Event Sport ID:</strong> {$category['event_sport_id']}</p>";
        echo "</div>";
        
        // Check tournament format configuration
        echo "<h3>Tournament Format Configuration:</h3>";
        
        if ($category['tournament_format_id']) {
            echo "<p style='color: green;'>✅ Tournament Format ID: {$category['tournament_format_id']}</p>";
            echo "<p style='color: green;'>✅ Format Name: {$category['format_name']}</p>";
            echo "<p style='color: green;'>✅ Format Code: {$category['format_code']}</p>";
            echo "<p style='color: green;'>✅ Algorithm Class: {$category['algorithm_class']}</p>";
            echo "<p style='color: green;'>✅ Min Participants: {$category['min_participants']}</p>";
            $status_checks['format_configured'] = true;
        } else {
            $issues[] = "❌ No tournament format configured for this event sport";
            echo "<p style='color: red;'>❌ No tournament format configured</p>";
            
            if ($category['bracket_type']) {
                echo "<p style='color: orange;'>⚠️ Legacy bracket type found: {$category['bracket_type']}</p>";
                $recommendations[] = "Update event sport to use new tournament format system";
            }
            $status_checks['format_configured'] = false;
        }
    }
    
    // ===== 2. PARTICIPANT ANALYSIS =====
    echo "<h2>2. 👥 Participant Analysis</h2>";
    
    if ($category) {
        // Check participants in both systems
        $stmt = $conn->prepare("
            SELECT 
                d.id,
                d.name,
                d.abbreviation,
                r.id as registration_id,
                r.status,
                JSON_LENGTH(r.participants) as participant_count
            FROM departments d
            JOIN registrations r ON d.id = r.department_id
            WHERE r.event_sport_id = ? AND r.status IN ('confirmed', 'registered', 'approved')
            ORDER BY d.name
        ");
        $stmt->execute([$category['event_sport_id']]);
        $participants = $stmt->fetchAll();
        
        echo "<h3>Registered Participants:</h3>";
        echo "<p><strong>Total Departments:</strong> " . count($participants) . "</p>";
        
        if (count($participants) >= 2) {
            echo "<p style='color: green;'>✅ Sufficient participants for tournament</p>";
            $status_checks['sufficient_participants'] = true;
        } else {
            $issues[] = "❌ Insufficient participants (need at least 2, have " . count($participants) . ")";
            echo "<p style='color: red;'>❌ Insufficient participants</p>";
            $status_checks['sufficient_participants'] = false;
        }
        
        if (!empty($participants)) {
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f5f5f5;'>";
            echo "<th>Department</th><th>Status</th><th>Participants</th>";
            echo "</tr>";
            foreach ($participants as $p) {
                echo "<tr>";
                echo "<td>{$p['name']}</td>";
                echo "<td>{$p['status']}</td>";
                echo "<td>{$p['participant_count']}</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
    
    // ===== 3. DATABASE STRUCTURE VALIDATION =====
    echo "<h2>3. 🗄️ Database Structure Validation</h2>";
    
    $required_tables = [
        'tournament_structures' => ['id', 'event_sport_id', 'tournament_format_id', 'name', 'status'],
        'tournament_participants' => ['id', 'tournament_structure_id', 'registration_id'],
        'tournament_rounds' => ['id', 'tournament_structure_id', 'round_number'],
        'matches' => ['id', 'tournament_structure_id', 'event_sport_id', 'team1_id', 'team2_id']
    ];
    
    foreach ($required_tables as $table => $required_columns) {
        echo "<h4>Table: {$table}</h4>";
        
        try {
            $stmt = $conn->query("DESCRIBE {$table}");
            $columns = $stmt->fetchAll();
            $existing_columns = array_column($columns, 'Field');
            
            echo "<p style='color: green;'>✅ Table exists</p>";
            
            $missing_columns = array_diff($required_columns, $existing_columns);
            if (empty($missing_columns)) {
                echo "<p style='color: green;'>✅ All required columns present</p>";
                $status_checks["table_{$table}"] = true;
            } else {
                $issues[] = "❌ Missing columns in {$table}: " . implode(', ', $missing_columns);
                echo "<p style='color: red;'>❌ Missing columns: " . implode(', ', $missing_columns) . "</p>";
                $status_checks["table_{$table}"] = false;
            }
            
        } catch (Exception $e) {
            $issues[] = "❌ Table {$table} does not exist";
            echo "<p style='color: red;'>❌ Table does not exist</p>";
            $status_checks["table_{$table}"] = false;
        }
    }
    
    // ===== 4. EXISTING TOURNAMENT CHECK =====
    echo "<h2>4. 🏆 Existing Tournament Check</h2>";
    
    if ($category) {
        $stmt = $conn->prepare("
            SELECT id, name, status, participant_count, created_at
            FROM tournament_structures 
            WHERE event_sport_id = ?
            ORDER BY created_at DESC
        ");
        $stmt->execute([$category['event_sport_id']]);
        $tournaments = $stmt->fetchAll();
        
        if (empty($tournaments)) {
            echo "<p style='color: green;'>✅ No existing tournaments - ready for auto-generation</p>";
            $status_checks['no_existing_tournament'] = true;
        } else {
            echo "<p><strong>Existing Tournaments:</strong></p>";
            echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
            echo "<tr style='background: #f5f5f5;'>";
            echo "<th>ID</th><th>Name</th><th>Status</th><th>Participants</th><th>Created</th><th>Action</th>";
            echo "</tr>";
            
            $active_tournaments = 0;
            foreach ($tournaments as $t) {
                echo "<tr>";
                echo "<td>{$t['id']}</td>";
                echo "<td>{$t['name']}</td>";
                echo "<td>{$t['status']}</td>";
                echo "<td>{$t['participant_count']}</td>";
                echo "<td>{$t['created_at']}</td>";
                echo "<td>";
                if ($t['status'] != 'cancelled' && $t['status'] != 'completed') {
                    $active_tournaments++;
                    echo "<a href='?cancel_tournament={$t['id']}&event_id={$event_id}&sport_id={$sport_id}&category_id={$category_id}' style='background: #dc3545; color: white; padding: 2px 8px; text-decoration: none; border-radius: 3px; font-size: 12px;'>Cancel</a>";
                }
                echo "</td>";
                echo "</tr>";
            }
            echo "</table>";
            
            if ($active_tournaments > 0) {
                $issues[] = "❌ {$active_tournaments} active tournament(s) exist - preventing auto-generation";
                echo "<p style='color: red;'>❌ Active tournaments prevent auto-generation</p>";
                $status_checks['no_existing_tournament'] = false;
            } else {
                echo "<p style='color: green;'>✅ No active tournaments - ready for auto-generation</p>";
                $status_checks['no_existing_tournament'] = true;
            }
        }
    }
    
    // ===== 5. AUTO-GENERATION REQUIREMENTS CHECKLIST =====
    echo "<h2>5. ✅ Auto-Generation Requirements Checklist</h2>";
    
    $requirements = [
        'Category Found' => isset($category) && $category !== false,
        'Tournament Format Configured' => $status_checks['format_configured'] ?? false,
        'Sufficient Participants (≥2)' => $status_checks['sufficient_participants'] ?? false,
        'Tournament Structures Table' => $status_checks['table_tournament_structures'] ?? false,
        'Tournament Participants Table' => $status_checks['table_tournament_participants'] ?? false,
        'Tournament Rounds Table' => $status_checks['table_tournament_rounds'] ?? false,
        'Matches Table Updated' => $status_checks['table_matches'] ?? false,
        'No Active Tournaments' => $status_checks['no_existing_tournament'] ?? false
    ];
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th>Requirement</th><th>Status</th>";
    echo "</tr>";
    
    $all_requirements_met = true;
    foreach ($requirements as $requirement => $status) {
        echo "<tr>";
        echo "<td>{$requirement}</td>";
        if ($status) {
            echo "<td style='color: green; text-align: center;'>✅ PASS</td>";
        } else {
            echo "<td style='color: red; text-align: center;'>❌ FAIL</td>";
            $all_requirements_met = false;
        }
        echo "</tr>";
    }
    echo "</table>";
    
    // ===== 6. FINAL DIAGNOSIS AND RECOMMENDATIONS =====
    echo "<h2>6. 🎯 Final Diagnosis and Recommendations</h2>";
    
    if ($all_requirements_met) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🎉 ALL REQUIREMENTS MET!</h3>";
        echo "<p>Auto-generation should work. If it's still failing, there may be a code issue.</p>";
        echo "<p><a href='manage-category.php?category_id={$category_id}&event_id={$event_id}&sport_id={$sport_id}' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🚀 Test Auto-Generation</a></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
        echo "<h3 style='color: #721c24; margin-top: 0;'>❌ REQUIREMENTS NOT MET</h3>";
        echo "<p>The following issues must be resolved:</p>";
        echo "<ul>";
        foreach ($issues as $issue) {
            echo "<li>{$issue}</li>";
        }
        echo "</ul>";
        echo "</div>";
        
        echo "<h3>🔧 Quick Fixes:</h3>";
        echo "<ul>";
        if (!($status_checks['format_configured'] ?? false)) {
            echo "<li><a href='fix-tournament-format.php?event_id={$event_id}&sport_id={$sport_id}' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Configure Tournament Format</a></li>";
        }
        if (!($status_checks['sufficient_participants'] ?? false)) {
            echo "<li><a href='add-test-participants.php' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Add More Participants</a></li>";
        }
        if (!($status_checks['table_tournament_structures'] ?? false)) {
            echo "<li><a href='check-tournament-tables.php' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Fix Database Tables</a></li>";
        }
        echo "</ul>";
    }
    
    // Handle tournament cancellation
    if (isset($_GET['cancel_tournament'])) {
        $tournament_id = $_GET['cancel_tournament'];
        $stmt = $conn->prepare("UPDATE tournament_structures SET status = 'cancelled' WHERE id = ?");
        $stmt->execute([$tournament_id]);
        echo "<p style='color: green;'>✅ Tournament {$tournament_id} has been cancelled</p>";
        echo "<p><a href='tournament-diagnostic-tool.php?event_id={$event_id}&sport_id={$sport_id}&category_id={$category_id}'>Refresh Diagnostic</a></p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Diagnostic Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
