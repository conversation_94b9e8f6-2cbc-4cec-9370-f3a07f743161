<?php
require_once '../includes/config.php';
require_once '../includes/functions.php';

// Check if user is admin
requireAdmin();

header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['match_id'])) {
        throw new Exception('Missing match ID');
    }
    
    $match_id = $input['match_id'];
    
    // Get match details
    $stmt = $pdo->prepare("
        SELECT 
            m.*,
            es.sport_id,
            es.event_id,
            s.name as sport_name,
            e.name as event_name,
            t1.name as team1_name,
            t2.name as team2_name,
            es.referee_name,
            es.referee_contact
        FROM tournament_matches m
        JOIN event_sports es ON m.event_sport_id = es.id
        JOIN sports s ON es.sport_id = s.id
        JOIN events e ON es.event_id = e.id
        LEFT JOIN tournament_participants t1 ON m.team1_id = t1.id
        LEFT JOIN tournament_participants t2 ON m.team2_id = t2.id
        WHERE m.id = ?
    ");
    
    $stmt->execute([$match_id]);
    $match = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$match) {
        throw new Exception('Match not found');
    }
    
    // Generate referee session token
    $referee_token = bin2hex(random_bytes(32));
    
    // Store referee session
    $stmt = $pdo->prepare("
        INSERT INTO referee_sessions (match_id, token, created_at, expires_at, status)
        VALUES (?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), 'active')
        ON DUPLICATE KEY UPDATE
        token = VALUES(token),
        created_at = NOW(),
        expires_at = DATE_ADD(NOW(), INTERVAL 24 HOUR),
        status = 'active'
    ");
    
    $stmt->execute([$match_id, $referee_token]);
    
    // Update match status to indicate it's been sent to referee
    $stmt = $pdo->prepare("
        UPDATE tournament_matches 
        SET status = 'in_progress', 
            referee_notified_at = NOW(),
            updated_at = NOW()
        WHERE id = ?
    ");
    
    $stmt->execute([$match_id]);
    
    // Generate referee URL
    $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . 
                '://' . $_SERVER['HTTP_HOST'];
    $referee_url = $base_url . '/referee/match.php?token=' . $referee_token;
    
    // Prepare notification data
    $notification_data = [
        'match_id' => $match_id,
        'event_name' => $match['event_name'],
        'sport_name' => $match['sport_name'],
        'team1_name' => $match['team1_name'] ?? 'TBD',
        'team2_name' => $match['team2_name'] ?? 'TBD',
        'referee_name' => $match['referee_name'],
        'referee_contact' => $match['referee_contact'],
        'referee_url' => $referee_url,
        'scheduled_time' => $match['scheduled_time']
    ];
    
    // Send notification to referee (if contact info available)
    $notification_sent = false;
    if (!empty($match['referee_contact'])) {
        $notification_sent = sendRefereeNotification($notification_data);
    }
    
    // Log admin activity
    logAdminActivity($_SESSION['admin_id'], 'send_to_referee', 'tournament_match', $match_id, [
        'match_id' => $match_id,
        'referee_name' => $match['referee_name'],
        'referee_contact' => $match['referee_contact'],
        'notification_sent' => $notification_sent,
        'referee_url' => $referee_url
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Match sent to referee successfully',
        'referee_url' => $referee_url,
        'notification_sent' => $notification_sent,
        'referee_name' => $match['referee_name']
    ]);
    
} catch (Exception $e) {
    error_log("Error in send_to_referee.php: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Error sending to referee: ' . $e->getMessage()
    ]);
}

/**
 * Send notification to referee
 */
function sendRefereeNotification($data) {
    try {
        // Create notification message
        $message = "SC_IMS Tournament Match Assignment\n\n";
        $message .= "Event: {$data['event_name']}\n";
        $message .= "Sport: {$data['sport_name']}\n";
        $message .= "Match: {$data['team1_name']} vs {$data['team2_name']}\n";
        
        if ($data['scheduled_time']) {
            $message .= "Scheduled: " . date('M j, Y g:i A', strtotime($data['scheduled_time'])) . "\n";
        }
        
        $message .= "\nReferee Access Link:\n{$data['referee_url']}\n\n";
        $message .= "This link is valid for 24 hours.\n";
        $message .= "Use this link to access the live scoring interface for this match.";
        
        // Check if contact is email or phone
        $contact = $data['referee_contact'];
        
        if (filter_var($contact, FILTER_VALIDATE_EMAIL)) {
            // Send email notification
            return sendEmailNotification($contact, $data['referee_name'], $message, $data);
        } elseif (preg_match('/^\+?[\d\s\-\(\)]+$/', $contact)) {
            // Send SMS notification (if SMS service is configured)
            return sendSMSNotification($contact, $message);
        }
        
        return false;
        
    } catch (Exception $e) {
        error_log("Error sending referee notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Send email notification to referee
 */
function sendEmailNotification($email, $referee_name, $message, $data) {
    try {
        $subject = "SC_IMS Match Assignment - {$data['sport_name']}";
        
        $html_message = "
        <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
            <div style='background: linear-gradient(135deg, #007bff, #0056b3); color: white; padding: 20px; text-align: center;'>
                <h2 style='margin: 0;'>SC_IMS Tournament Match Assignment</h2>
            </div>
            
            <div style='padding: 20px; background: #f8f9fa;'>
                <p>Dear {$referee_name},</p>
                
                <p>You have been assigned to referee the following match:</p>
                
                <div style='background: white; padding: 15px; border-radius: 8px; margin: 15px 0;'>
                    <h3 style='color: #007bff; margin-top: 0;'>{$data['event_name']}</h3>
                    <p><strong>Sport:</strong> {$data['sport_name']}</p>
                    <p><strong>Match:</strong> {$data['team1_name']} vs {$data['team2_name']}</p>";
        
        if ($data['scheduled_time']) {
            $html_message .= "<p><strong>Scheduled:</strong> " . date('M j, Y g:i A', strtotime($data['scheduled_time'])) . "</p>";
        }
        
        $html_message .= "
                </div>
                
                <div style='text-align: center; margin: 20px 0;'>
                    <a href='{$data['referee_url']}' style='background: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;'>
                        Access Referee Interface
                    </a>
                </div>
                
                <p style='color: #6c757d; font-size: 0.9em;'>
                    This link is valid for 24 hours. Use it to access the live scoring interface for this match.
                </p>
            </div>
            
            <div style='background: #343a40; color: white; padding: 15px; text-align: center; font-size: 0.8em;'>
                SC_IMS - Sports Competition Information Management System
            </div>
        </div>";
        
        $headers = [
            'MIME-Version: 1.0',
            'Content-type: text/html; charset=UTF-8',
            'From: SC_IMS <<EMAIL>>',
            'Reply-To: <EMAIL>',
            'X-Mailer: PHP/' . phpversion()
        ];
        
        return mail($email, $subject, $html_message, implode("\r\n", $headers));
        
    } catch (Exception $e) {
        error_log("Error sending email notification: " . $e->getMessage());
        return false;
    }
}

/**
 * Send SMS notification to referee
 */
function sendSMSNotification($phone, $message) {
    // SMS implementation would depend on your SMS service provider
    // This is a placeholder for SMS functionality
    
    // Example for Twilio, Nexmo, or other SMS services:
    /*
    try {
        // Initialize SMS service
        $sms_service = new SMSService();
        return $sms_service->send($phone, $message);
    } catch (Exception $e) {
        error_log("Error sending SMS notification: " . $e->getMessage());
        return false;
    }
    */
    
    // For now, just log the SMS attempt
    error_log("SMS notification would be sent to {$phone}: " . substr($message, 0, 100) . "...");
    return true; // Return true for demo purposes
}
?>
