<?php
/**
 * Dynamic Tournament Format Selection Engine
 * SC_IMS Sports Competition and Event Management System
 * 
 * Automatically selects appropriate tournament format based on:
 * - Sport configuration from event_sports.tournament_format_id
 * - Actual number of participating departments
 * - Tournament format rules from tournament_formats table
 */

class TournamentFormatSelector {
    private $conn;
    
    public function __construct($conn) {
        $this->conn = $conn;
    }
    
    /**
     * Select the optimal tournament format for given parameters
     */
    public function selectFormat($eventId, $sportId, $participantCount, $categoryId = null) {
        try {
            // Step 1: Check if format is explicitly configured in event_sports
            $configuredFormat = $this->getConfiguredFormat($eventId, $sportId);
            
            if ($configuredFormat) {
                // Validate configured format against participant count
                if ($this->validateFormatForParticipants($configuredFormat, $participantCount)) {
                    return $this->enrichFormatData($configuredFormat, $participantCount);
                } else {
                    // Log warning but continue with automatic selection
                    error_log("Configured format {$configuredFormat['code']} not suitable for {$participantCount} participants. Auto-selecting...");
                }
            }
            
            // Step 2: Auto-select based on sport type and participant count
            $sportTypeCategory = $this->getSportTypeCategory($sportId);
            $autoSelectedFormat = $this->autoSelectFormat($sportTypeCategory, $participantCount);
            
            if ($autoSelectedFormat) {
                return $this->enrichFormatData($autoSelectedFormat, $participantCount);
            }
            
            // Step 3: Fallback to default format
            return $this->getFallbackFormat($participantCount);
            
        } catch (Exception $e) {
            error_log("Tournament format selection error: " . $e->getMessage());
            return $this->getFallbackFormat($participantCount);
        }
    }
    
    /**
     * Get explicitly configured tournament format from event_sports
     */
    private function getConfiguredFormat($eventId, $sportId) {
        $stmt = $this->conn->prepare("
            SELECT tf.* 
            FROM event_sports es
            JOIN tournament_formats tf ON es.tournament_format_id = tf.id
            WHERE es.event_id = ? AND es.sport_id = ?
        ");
        $stmt->execute([$eventId, $sportId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Get sport type category for format selection
     */
    private function getSportTypeCategory($sportId) {
        $stmt = $this->conn->prepare("
            SELECT st.category 
            FROM sports s 
            LEFT JOIN sport_types st ON s.sport_type_id = st.id 
            WHERE s.id = ?
        ");
        $stmt->execute([$sportId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result ? $result['category'] : 'traditional';
    }
    
    /**
     * Automatically select format based on sport type and participant count
     */
    private function autoSelectFormat($sportTypeCategory, $participantCount) {
        // Define selection rules based on sport type and participant count
        $selectionRules = [
            'traditional' => [
                'small' => ['single_elimination', 'round_robin'],  // 2-8 participants
                'medium' => ['single_elimination', 'double_elimination'], // 9-16 participants
                'large' => ['single_elimination', 'multi_stage'] // 17+ participants
            ],
            'academic' => [
                'small' => ['round_robin', 'single_elimination'],
                'medium' => ['swiss_system', 'single_elimination'],
                'large' => ['swiss_system', 'multi_stage']
            ],
            'individual' => [
                'small' => ['single_elimination', 'round_robin'],
                'medium' => ['single_elimination', 'double_elimination'],
                'large' => ['single_elimination', 'multi_stage']
            ],
            'judged' => [
                'small' => ['round_robin', 'single_elimination'],
                'medium' => ['single_elimination', 'round_robin'],
                'large' => ['single_elimination', 'multi_stage']
            ]
        ];
        
        // Determine size category
        $sizeCategory = $this->getParticipantSizeCategory($participantCount);
        
        // Get preferred formats for this sport type and size
        $preferredFormats = $selectionRules[$sportTypeCategory][$sizeCategory] ?? 
                           $selectionRules['traditional'][$sizeCategory];
        
        // Try each preferred format in order
        foreach ($preferredFormats as $formatCode) {
            $format = $this->getFormatByCode($formatCode);
            if ($format && $this->validateFormatForParticipants($format, $participantCount)) {
                return $format;
            }
        }
        
        return null;
    }
    
    /**
     * Categorize participant count into size groups
     */
    private function getParticipantSizeCategory($participantCount) {
        if ($participantCount <= 8) return 'small';
        if ($participantCount <= 16) return 'medium';
        return 'large';
    }
    
    /**
     * Get tournament format by code
     */
    private function getFormatByCode($code) {
        $stmt = $this->conn->prepare("
            SELECT * FROM tournament_formats 
            WHERE code = ? 
            ORDER BY id LIMIT 1
        ");
        $stmt->execute([$code]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    /**
     * Validate if format is suitable for given participant count
     */
    private function validateFormatForParticipants($format, $participantCount) {
        // Check minimum participants
        if ($participantCount < $format['min_participants']) {
            return false;
        }
        
        // Check maximum participants (if set)
        if ($format['max_participants'] && $participantCount > $format['max_participants']) {
            return false;
        }
        
        // Additional validation based on format type
        switch ($format['code']) {
            case 'round_robin':
                // Round robin becomes unwieldy with too many participants
                return $participantCount <= 16;
                
            case 'swiss_system':
                // Swiss system needs even number of participants ideally
                return $participantCount >= 4;
                
            case 'multi_stage':
                // Multi-stage needs enough participants for groups
                return $participantCount >= 8;
                
            default:
                return true;
        }
    }
    
    /**
     * Enrich format data with calculated values
     */
    private function enrichFormatData($format, $participantCount) {
        $calculator = new TournamentFormulaCalculator($this->conn);
        
        $format['calculated_rounds'] = $calculator->calculateRounds($format, $participantCount);
        $format['calculated_matches'] = $calculator->calculateMatches($format, $participantCount);
        $format['participant_count'] = $participantCount;
        $format['selection_reason'] = $this->getSelectionReason($format, $participantCount);
        
        return $format;
    }
    
    /**
     * Get fallback format when no suitable format is found
     */
    private function getFallbackFormat($participantCount) {
        // Always fall back to single elimination as it works for any number of participants
        $format = $this->getFormatByCode('single_elimination');
        if ($format) {
            return $this->enrichFormatData($format, $participantCount);
        }
        
        // Ultimate fallback - create minimal format data
        return [
            'id' => null,
            'name' => 'Single Elimination (Fallback)',
            'code' => 'single_elimination',
            'algorithm_class' => 'SingleEliminationAlgorithm',
            'rounds_formula' => 'ceil(log2(n))',
            'matches_formula' => 'n-1',
            'calculated_rounds' => ceil(log($participantCount, 2)),
            'calculated_matches' => $participantCount - 1,
            'participant_count' => $participantCount,
            'selection_reason' => 'Fallback format - no suitable format found',
            'configuration' => '{"bracket_seeding": true}'
        ];
    }
    
    /**
     * Get human-readable reason for format selection
     */
    private function getSelectionReason($format, $participantCount) {
        if (isset($format['configured'])) {
            return "Explicitly configured for this event/sport";
        }
        
        $sizeCategory = $this->getParticipantSizeCategory($participantCount);
        return "Auto-selected for {$sizeCategory} group ({$participantCount} participants)";
    }
    
    /**
     * Get all available formats for a sport type
     */
    public function getAvailableFormats($sportTypeCategory, $participantCount = null) {
        $sql = "SELECT * FROM tournament_formats 
                WHERE sport_type_category = ? OR sport_type_category = 'all'
                ORDER BY name";
        
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$sportTypeCategory]);
        $formats = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Filter by participant count if provided
        if ($participantCount !== null) {
            $formats = array_filter($formats, function($format) use ($participantCount) {
                return $this->validateFormatForParticipants($format, $participantCount);
            });
        }
        
        return $formats;
    }
}
?>
