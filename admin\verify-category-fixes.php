<?php
/**
 * Verify Category Fixes
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>✅ Verify Category Fixes</h1>";

$event_id = 4;
$sport_id = 40;
$category_id = 14;

try {
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
    echo "<h2>Verification Results for Men's Singles A</h2>";
    echo "<p>Checking if both tournament format and participants issues are resolved.</p>";
    echo "</div>";
    
    echo "<h2>1. Tournament Format Verification</h2>";
    
    // Check sport bracket format
    $stmt = $conn->prepare("
        SELECT 
            s.name as sport_name,
            s.bracket_format,
            s.type as sport_type
        FROM sports s 
        WHERE s.id = ?
    ");
    $stmt->execute([$sport_id]);
    $sport = $stmt->fetch();
    
    if ($sport) {
        $format_display = ucwords(str_replace('_', ' ', $sport['bracket_format']));
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Tournament Format Fixed</h3>";
        echo "<ul>";
        echo "<li><strong>Sport:</strong> {$sport['sport_name']}</li>";
        echo "<li><strong>Type:</strong> {$sport['sport_type']}</li>";
        echo "<li><strong>Format:</strong> $format_display</li>";
        echo "<li><strong>Database Value:</strong> {$sport['bracket_format']}</li>";
        echo "</ul>";
        echo "</div>";
        
        // Check if this matches what should be displayed
        if ($sport['bracket_format'] === 'single_elimination') {
            echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>✅ <strong>Correct!</strong> Badminton is now using Single Elimination format as expected.</p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>❌ <strong>Issue:</strong> Expected 'single_elimination' but got '{$sport['bracket_format']}'</p>";
            echo "</div>";
        }
    }
    
    echo "<h2>2. Participants Verification</h2>";
    
    // Check registered departments
    $stmt = $conn->prepare("
        SELECT 
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            edr.registration_date,
            edr.status
        FROM departments d
        JOIN event_department_registrations edr ON d.id = edr.department_id
        WHERE edr.event_id = ?
        ORDER BY d.name
    ");
    $stmt->execute([$event_id]);
    $participants = $stmt->fetchAll();
    
    $participant_count = count($participants);
    
    if ($participant_count > 0) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Participants Found</h3>";
        echo "<p><strong>Total Registered Departments:</strong> $participant_count</p>";
        echo "</div>";
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>📋 Registered Departments:</h4>";
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 10px;'>";
        
        foreach ($participants as $participant) {
            $color = $participant['color_code'] ?? '#6c757d';
            echo "<div style='background: white; padding: 10px; border-radius: 5px; border-left: 4px solid $color;'>";
            echo "<strong>{$participant['name']}</strong><br>";
            echo "<small>{$participant['abbreviation']} • {$participant['status']}</small><br>";
            echo "<small>Registered: {$participant['registration_date']}</small>";
            echo "</div>";
        }
        
        echo "</div>";
        echo "</div>";
        
        echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Excellent!</strong> The category now has $participant_count registered participants.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ No Participants Found</h3>";
        echo "<p>The event_department_registrations table may be empty or the registration process failed.</p>";
        echo "</div>";
    }
    
    echo "<h2>3. Category Page Data Verification</h2>";
    
    // Simulate what the category page will show
    $stmt = $conn->prepare("
        SELECT 
            sc.*,
            e.name as event_name,
            s.name as sport_name,
            s.bracket_format,
            es.id as event_sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ?
    ");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();
    
    if ($category) {
        // Simulate tournament format logic from manage-category.php
        $tournament_format = [
            'format_name' => ucwords(str_replace('_', ' ', $category['bracket_format'])),
            'format_description' => 'Default format for this sport',
            'format_code' => $category['bracket_format'],
            'tournament_status' => 'setup'
        ];
        
        echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px;'>";
        echo "<h3>📊 Category Page Preview</h3>";
        echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px;'>";
        
        echo "<div>";
        echo "<h4>Basic Details</h4>";
        echo "<p><strong>Category Name:</strong> {$category['category_name']}</p>";
        echo "<p><strong>Category Type:</strong> {$category['category_type']}</p>";
        echo "<p><strong>Sport:</strong> {$category['sport_name']}</p>";
        echo "<p><strong>Event:</strong> {$category['event_name']}</p>";
        echo "<p><strong>Tournament Format:</strong> <span style='color: #007bff; font-weight: 600;'>{$tournament_format['format_name']}</span></p>";
        echo "</div>";
        
        echo "<div>";
        echo "<h4>Statistics</h4>";
        echo "<p><strong>Participants:</strong> $participant_count</p>";
        echo "<p><strong>Total Matches:</strong> 0 (not yet generated)</p>";
        echo "<p><strong>Completed:</strong> 0</p>";
        echo "<p><strong>Pending:</strong> 0</p>";
        echo "</div>";
        
        echo "</div>";
        echo "</div>";
    }
    
    echo "<h2>4. Overall Status</h2>";
    
    $format_fixed = ($sport['bracket_format'] === 'single_elimination');
    $participants_fixed = ($participant_count > 0);
    
    if ($format_fixed && $participants_fixed) {
        echo "<div style='background: #d4edda; padding: 25px; border-radius: 8px; border-left: 4px solid #28a745; text-align: center;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🎉 All Issues Fixed!</h3>";
        echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;'>";
        
        echo "<div style='background: rgba(21, 87, 36, 0.1); padding: 15px; border-radius: 5px;'>";
        echo "<h4 style='color: #155724;'>✅ Tournament Format</h4>";
        echo "<p>Now showing: <strong>Single Elimination</strong></p>";
        echo "<p>Perfect for Badminton competitions!</p>";
        echo "</div>";
        
        echo "<div style='background: rgba(21, 87, 36, 0.1); padding: 15px; border-radius: 5px;'>";
        echo "<h4 style='color: #155724;'>✅ Participants</h4>";
        echo "<p>Now showing: <strong>$participant_count Departments</strong></p>";
        echo "<p>Ready for tournament creation!</p>";
        echo "</div>";
        
        echo "</div>";
        echo "<p style='font-size: 1.1rem; font-weight: 600; color: #155724;'>";
        echo "The category page should now display correctly with proper format and participants!";
        echo "</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border-left: 4px solid #dc3545;'>";
        echo "<h3 style='color: #721c24;'>⚠️ Some Issues Remain</h3>";
        echo "<ul>";
        if (!$format_fixed) {
            echo "<li>❌ Tournament format still needs fixing</li>";
        }
        if (!$participants_fixed) {
            echo "<li>❌ Participants still need to be added</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<h2>5. Test Links</h2>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='manage-category.php?event_id=$event_id&sport_id=$sport_id&category_id=$category_id' target='_blank' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "🎯 View Fixed Category Page";
    echo "</a>";
    
    echo "<a href='debug-category-issues.php' target='_blank' style='background: #6c757d; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "🔍 Debug Details";
    echo "</a>";
    
    echo "<a href='sport-categories.php?event_id=$event_id&sport_id=$sport_id' target='_blank' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "📋 Back to Categories List";
    echo "</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Verification Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
