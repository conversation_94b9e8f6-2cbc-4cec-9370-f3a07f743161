<?php
/**
 * View Error Logs
 * Check what's happening with category navigation
 */

require_once 'auth.php';
requireAdmin();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Error Logs</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>📋 Error Logs</h1>
        <p>Check what's happening with category navigation</p>
        
        <div class="card">
            <div class="card-header">
                <h3>Recent PHP Error Log Entries</h3>
                <button class="btn btn-sm btn-secondary" onclick="location.reload()">Refresh</button>
            </div>
            <div class="card-body">
                <?php
                // Try to find PHP error log
                $log_files = [
                    ini_get('error_log'),
                    '/var/log/apache2/error.log',
                    '/var/log/nginx/error.log',
                    '/tmp/php_errors.log',
                    'C:\\xampp\\apache\\logs\\error.log',
                    'C:\\wamp\\logs\\php_error.log'
                ];
                
                $found_logs = false;
                
                foreach ($log_files as $log_file) {
                    if ($log_file && file_exists($log_file) && is_readable($log_file)) {
                        $found_logs = true;
                        echo "<h5>Log file: $log_file</h5>";
                        
                        // Get last 50 lines
                        $lines = file($log_file);
                        $recent_lines = array_slice($lines, -50);
                        
                        // Filter for manage-category related entries
                        $relevant_lines = array_filter($recent_lines, function($line) {
                            return strpos($line, 'manage-category') !== false;
                        });
                        
                        if ($relevant_lines) {
                            echo "<div class='alert alert-info'>";
                            echo "<h6>Recent manage-category.php entries:</h6>";
                            echo "<pre style='font-size: 0.8rem; max-height: 300px; overflow-y: auto;'>";
                            foreach ($relevant_lines as $line) {
                                echo htmlspecialchars($line);
                            }
                            echo "</pre>";
                            echo "</div>";
                        } else {
                            echo "<div class='alert alert-warning'>No recent manage-category.php entries found in this log.</div>";
                        }
                        break;
                    }
                }
                
                if (!$found_logs) {
                    echo "<div class='alert alert-warning'>";
                    echo "<h5>No accessible log files found</h5>";
                    echo "<p>Tried these locations:</p>";
                    echo "<ul>";
                    foreach ($log_files as $log_file) {
                        if ($log_file) {
                            $exists = file_exists($log_file) ? 'exists' : 'not found';
                            $readable = (file_exists($log_file) && is_readable($log_file)) ? 'readable' : 'not readable';
                            echo "<li><code>$log_file</code> - $exists, $readable</li>";
                        }
                    }
                    echo "</ul>";
                    echo "</div>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h3>Manual Test</h3>
            </div>
            <div class="card-body">
                <p>Click this link to test category navigation and generate log entries:</p>
                <a href="manage-category.php?event_id=1&sport_id=1&category_id=1&debug=1" 
                   class="btn btn-primary" target="_blank">
                   Test Category Navigation (Debug Mode)
                </a>
                
                <p class="mt-3">After clicking the link above, refresh this page to see any new log entries.</p>
                
                <div class="alert alert-info mt-3">
                    <h6>What to look for:</h6>
                    <ul>
                        <li><strong>"manage-category.php accessed"</strong> - Shows the page was reached</li>
                        <li><strong>"manage-category.php REDIRECT"</strong> - Shows why it redirected</li>
                        <li><strong>"manage-category.php database query"</strong> - Shows if category was found</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
