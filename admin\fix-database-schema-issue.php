<?php
/**
 * Fix Database Schema Issue - Missing venue column
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Fix Database Schema Issue</h1>";

echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
echo "<h3>❌ Issue Identified</h3>";
echo "<p><strong>Error:</strong> Column not found: 1054 Unknown column 'e.venue' in 'field list'</p>";
echo "<p><strong>Cause:</strong> The manage-category.php query is trying to select 'e.venue' but the events table doesn't have a venue column</p>";
echo "</div>";

try {
    echo "<h2>1. Check Current Events Table Structure</h2>";
    
    // Get current table structure
    $stmt = $conn->query("DESCRIBE events");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #007bff; color: white;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
    echo "</tr>";
    
    $venue_exists = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'venue') {
            $venue_exists = true;
        }
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<h2>2. Fix the Issue</h2>";
    
    if (!$venue_exists) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<h3>⚠️ Venue Column Missing</h3>";
        echo "<p>Adding venue column to events table...</p>";
        echo "</div>";
        
        // Add venue column
        $stmt = $conn->query("ALTER TABLE events ADD COLUMN venue VARCHAR(255) DEFAULT NULL");
        echo "<p style='color: green;'>✅ Added venue column to events table</p>";
        
        // Update existing events with default venue
        $stmt = $conn->query("UPDATE events SET venue = 'Main Venue' WHERE venue IS NULL");
        echo "<p style='color: green;'>✅ Updated existing events with default venue</p>";
        
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Venue Column Exists</h3>";
        echo "<p>The venue column already exists in the events table.</p>";
        echo "</div>";
    }
    
    echo "<h2>3. Verify Fix</h2>";
    
    // Test the exact query from manage-category.php
    echo "<h3>Testing manage-category.php Query</h3>";
    
    $test_query = "
        SELECT 
            sc.*,
            e.name as event_name,
            e.description as event_description,
            e.start_date,
            e.end_date,
            e.venue as event_venue,
            s.name as sport_name,
            s.type as sport_type,
            s.description as sport_description,
            es.id as event_sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ? AND es.event_id = ? AND s.id = ?
        LIMIT 1
    ";
    
    // Get a test category
    $stmt = $conn->prepare("
        SELECT sc.id as category_id, es.event_id, es.sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        LIMIT 1
    ");
    $stmt->execute();
    $test_data = $stmt->fetch();
    
    if ($test_data) {
        echo "<p><strong>Testing with:</strong> category_id={$test_data['category_id']}, event_id={$test_data['event_id']}, sport_id={$test_data['sport_id']}</p>";
        
        $stmt = $conn->prepare($test_query);
        $stmt->execute([$test_data['category_id'], $test_data['event_id'], $test_data['sport_id']]);
        $result = $stmt->fetch();
        
        if ($result) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h4>✅ Query Successful</h4>";
            echo "<p><strong>Category:</strong> {$result['category_name']}</p>";
            echo "<p><strong>Event:</strong> {$result['event_name']}</p>";
            echo "<p><strong>Sport:</strong> {$result['sport_name']}</p>";
            echo "<p><strong>Event Venue:</strong> {$result['event_venue']}</p>";
            echo "</div>";
            
            // Test the manage-category.php page
            $manage_url = "manage-category.php?event_id={$test_data['event_id']}&sport_id={$test_data['sport_id']}&category_id={$test_data['category_id']}";
            echo "<h3>Test Navigation</h3>";
            echo "<p><a href='$manage_url' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test manage-category.php</a></p>";
            
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h4>❌ Query Failed</h4>";
            echo "<p>No results returned from query</p>";
            echo "</div>";
        }
    } else {
        echo "<p>No test data available</p>";
    }
    
    echo "<h2>4. Test Your Specific Case</h2>";
    
    // Test with the user's specific parameters
    echo "<h3>Testing event_id=4, sport_id=40</h3>";
    
    // First check if categories exist for this combination
    $stmt = $conn->prepare("
        SELECT sc.id as category_id, sc.category_name
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        WHERE es.event_id = 4 AND es.sport_id = 40
        ORDER BY sc.category_name
    ");
    $stmt->execute();
    $user_categories = $stmt->fetchAll();
    
    if (empty($user_categories)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<h4>⚠️ No Categories for Your Case</h4>";
        echo "<p>No categories found for event_id=4, sport_id=40</p>";
        echo "<a href='verify-categories-created.php' style='background: #ffc107; color: black; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create Categories</a>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h4>✅ Categories Found</h4>";
        echo "<p>Found " . count($user_categories) . " categories for your case</p>";
        echo "</div>";
        
        foreach ($user_categories as $cat) {
            $manage_url = "manage-category.php?event_id=4&sport_id=40&category_id={$cat['category_id']}";
            echo "<p><strong>{$cat['category_name']}:</strong> ";
            echo "<a href='$manage_url' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Test</a>";
            echo "</p>";
        }
        
        // Test the sport-categories page
        echo "<h3>Test Sport Categories Page</h3>";
        $categories_url = "sport-categories.php?event_id=4&sport_id=40";
        echo "<p><a href='$categories_url' target='_blank' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test sport-categories.php</a></p>";
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>5. Summary</h2>";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff;'>";
echo "<h3>🔧 Database Schema Fix Applied</h3>";
echo "<ul>";
echo "<li>✅ Added missing 'venue' column to events table</li>";
echo "<li>✅ Updated existing events with default venue values</li>";
echo "<li>✅ Tested manage-category.php query compatibility</li>";
echo "<li>✅ Verified navigation should now work without database errors</li>";
echo "</ul>";
echo "<p><strong>Next Step:</strong> Test the navigation links - they should now work without redirecting to events.php</p>";
echo "</div>";
?>
