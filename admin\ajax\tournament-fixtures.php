<?php
/**
 * Tournament Fixtures Management
 * Handles real-time fixture updates and display
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'get_fixtures':
            handleGetFixtures($conn, $input);
            break;
        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get tournament fixtures for a category
 */
function handleGetFixtures($conn, $input) {
    $eventId = $input['event_id'] ?? '';
    $sportId = $input['sport_id'] ?? '';
    $categoryId = $input['category_id'] ?? '';

    if (empty($eventId) || empty($sportId) || empty($categoryId)) {
        throw new Exception('Missing required parameters');
    }

    // Get tournament structure
    $stmt = $conn->prepare("
        SELECT 
            ts.*,
            tf.name as format_name,
            tf.code as format_code
        FROM tournament_structures ts
        JOIN event_sports es ON ts.event_sport_id = es.id
        LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ? AND ts.status != 'cancelled'
        ORDER BY ts.created_at DESC
        LIMIT 1
    ");
    $stmt->execute([$eventId, $sportId]);
    $tournament = $stmt->fetch();

    if (!$tournament) {
        echo json_encode([
            'success' => true,
            'fixtures' => [],
            'tournament_info' => null,
            'message' => 'No tournament found'
        ]);
        return;
    }

    // Get matches for this tournament
    $stmt = $conn->prepare("
        SELECT 
            m.*,
            ta.name as team_a_name,
            ta.abbreviation as team_a_abbr,
            ta.color_code as team_a_color,
            tb.name as team_b_name,
            tb.abbreviation as team_b_abbr,
            tb.color_code as team_b_color,
            w.name as winner_name,
            w.abbreviation as winner_abbr,
            tr.round_name,
            tr.round_type
        FROM matches m
        LEFT JOIN departments ta ON m.team1_id = ta.id
        LEFT JOIN departments tb ON m.team2_id = tb.id
        LEFT JOIN departments w ON m.winner_id = w.id
        LEFT JOIN tournament_rounds tr ON m.tournament_round_id = tr.id
        WHERE m.tournament_structure_id = ?
        ORDER BY m.round_number, m.match_number
    ");
    $stmt->execute([$tournament['id']]);
    $matches = $stmt->fetchAll();

    // Format matches for display
    $fixtures = [];
    foreach ($matches as $match) {
        $fixtures[] = [
            'id' => $match['id'],
            'round_number' => $match['round_number'],
            'match_number' => $match['match_number'],
            'round_name' => $match['round_name'] ?? "Round {$match['round_number']}",
            'round_type' => $match['round_type'] ?? 'elimination',
            'team_a_id' => $match['team1_id'],
            'team_a_name' => $match['team_a_name'] ?? 'TBD',
            'team_a_abbr' => $match['team_a_abbr'] ?? '',
            'team_a_color' => $match['team_a_color'] ?? '#6c757d',
            'team_a_score' => $match['team_a_score'],
            'team_b_id' => $match['team2_id'],
            'team_b_name' => $match['team_b_name'] ?? 'TBD',
            'team_b_abbr' => $match['team_b_abbr'] ?? '',
            'team_b_color' => $match['team_b_color'] ?? '#6c757d',
            'team_b_score' => $match['team_b_score'],
            'status' => $match['status'],
            'winner_id' => $match['winner_id'],
            'winner_name' => $match['winner_name'],
            'venue' => $match['venue'],
            'scheduled_time' => $match['scheduled_time'],
            'referee_notes' => $match['referee_notes'],
            'is_bye_match' => $match['is_bye_match']
        ];
    }

    // Tournament information
    $tournamentInfo = [
        'id' => $tournament['id'],
        'name' => $tournament['name'],
        'status' => $tournament['status'],
        'format_name' => $tournament['format_name'],
        'format_code' => $tournament['format_code'],
        'participant_count' => $tournament['participant_count'],
        'total_rounds' => $tournament['total_rounds'],
        'current_round' => $tournament['current_round'],
        'seeding_method' => $tournament['seeding_method']
    ];

    echo json_encode([
        'success' => true,
        'fixtures' => $fixtures,
        'tournament_info' => $tournamentInfo,
        'total_matches' => count($fixtures),
        'completed_matches' => count(array_filter($fixtures, function($f) { 
            return $f['status'] === 'completed'; 
        }))
    ]);
}
