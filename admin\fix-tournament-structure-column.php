<?php
require_once 'auth.php';
require_once '../config/database.php';

echo "<h1>🔧 Fix Tournament Structure Column</h1>";
echo "<p>Fixing the missing 'tournament_structure_id' column in the matches table...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>1. Check Current Matches Table Structure</h2>";
    
    // Check current table structure
    $stmt = $conn->query("DESCRIBE matches");
    $columns = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr style='background: #f5f5f5;'>";
    echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th>";
    echo "</tr>";
    
    $has_tournament_structure_id = false;
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "<td>{$column['Extra']}</td>";
        echo "</tr>";
        
        if ($column['Field'] === 'tournament_structure_id') {
            $has_tournament_structure_id = true;
        }
    }
    echo "</table>";
    
    echo "<h2>2. Column Status</h2>";
    
    if ($has_tournament_structure_id) {
        echo "<p style='color: green;'>✅ tournament_structure_id column already exists</p>";
    } else {
        echo "<p style='color: red;'>❌ tournament_structure_id column is missing</p>";
        
        echo "<h3>Adding tournament_structure_id column...</h3>";
        
        // Add the missing column
        $sql = "ALTER TABLE matches ADD COLUMN tournament_structure_id INT NULL AFTER id";
        $conn->exec($sql);
        
        echo "<p style='color: green;'>✅ Added tournament_structure_id column</p>";
        
        // Add foreign key constraint
        try {
            $sql = "ALTER TABLE matches ADD CONSTRAINT fk_matches_tournament_structure 
                    FOREIGN KEY (tournament_structure_id) REFERENCES tournament_structures(id) 
                    ON DELETE CASCADE";
            $conn->exec($sql);
            echo "<p style='color: green;'>✅ Added foreign key constraint</p>";
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠️ Foreign key constraint not added (may already exist): " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<h2>3. Check Tournament Structures Table</h2>";
    
    // Check if tournament_structures table exists
    try {
        $stmt = $conn->query("DESCRIBE tournament_structures");
        $tournament_columns = $stmt->fetchAll();
        
        echo "<p style='color: green;'>✅ tournament_structures table exists</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr style='background: #f5f5f5;'>";
        echo "<th>Field</th><th>Type</th><th>Null</th><th>Key</th>";
        echo "</tr>";
        
        foreach ($tournament_columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "</tr>";
        }
        echo "</table>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ tournament_structures table does not exist</p>";
        echo "<p>Error: " . $e->getMessage() . "</p>";
        
        echo "<h3>Creating tournament_structures table...</h3>";
        
        $sql = "CREATE TABLE tournament_structures (
            id INT AUTO_INCREMENT PRIMARY KEY,
            event_sport_id INT NOT NULL,
            tournament_format_id INT NOT NULL,
            name VARCHAR(255) NOT NULL,
            status ENUM('pending', 'active', 'completed', 'cancelled') DEFAULT 'pending',
            participant_count INT DEFAULT 0,
            total_rounds INT DEFAULT 0,
            current_round INT DEFAULT 1,
            seeding_method VARCHAR(50) DEFAULT 'random',
            bracket_data JSON NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
            FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id)
        )";
        
        $conn->exec($sql);
        echo "<p style='color: green;'>✅ Created tournament_structures table</p>";
    }
    
    echo "<h2>4. Verify Fix</h2>";
    
    // Test the fix by checking if we can query with tournament_structure_id
    try {
        $stmt = $conn->query("SELECT id, tournament_structure_id FROM matches LIMIT 1");
        echo "<p style='color: green;'>✅ tournament_structure_id column is now accessible</p>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Still having issues: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>5. Update Existing Matches (if any)</h2>";
    
    // Check if there are any existing matches without tournament_structure_id
    $stmt = $conn->query("SELECT COUNT(*) as count FROM matches WHERE tournament_structure_id IS NULL");
    $null_count = $stmt->fetch()['count'];
    
    if ($null_count > 0) {
        echo "<p>Found {$null_count} matches without tournament_structure_id</p>";
        echo "<p style='color: orange;'>⚠️ These matches were created before the tournament structure system</p>";
        echo "<p>They will need to be associated with tournament structures manually or regenerated</p>";
    } else {
        echo "<p style='color: green;'>✅ All matches have proper tournament structure references</p>";
    }
    
    echo "<h2>6. Test Auto-Generation</h2>";
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 8px; border: 2px solid #28a745; margin: 20px 0;'>";
    echo "<h3 style='color: #155724; margin-top: 0;'>🎉 Fix Complete!</h3>";
    echo "<p>The tournament_structure_id column has been added to the matches table.</p>";
    echo "<p>Auto-generation should now work properly.</p>";
    echo "</div>";
    
    echo "<h3>Next Steps:</h3>";
    echo "<ul>";
    echo "<li><a href='manage-category.php?category_id=15&event_id=4&sport_id=37' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>🚀 Test Auto-Generation</a></li>";
    echo "<li><a href='test-auto-tournament.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin-left: 10px;'>🧪 Run Tests</a></li>";
    echo "</ul>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 8px; border: 2px solid #dc3545; margin: 20px 0;'>";
    echo "<h3 style='color: #721c24; margin-top: 0;'>❌ Error</h3>";
    echo "<p><strong>Message:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>File:</strong> " . $e->getFile() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}
?>
