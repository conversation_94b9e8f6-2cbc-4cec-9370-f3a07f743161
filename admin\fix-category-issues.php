<?php
/**
 * Fix Category Issues - Tournament Format & Participants
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Fix Category Issues</h1>";

try {
    $conn->beginTransaction();
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
    echo "<h2>Fixing Issues for Men's Singles A Category</h2>";
    echo "<p>This will fix both the tournament format display and add participants.</p>";
    echo "</div>";
    
    // Get the specific category details
    $event_id = 4;
    $sport_id = 40;
    $category_id = 14;
    
    echo "<h2>1. Fix Tournament Format for Badminton</h2>";
    
    // First, check current sport bracket format
    $stmt = $conn->prepare("SELECT name, bracket_format FROM sports WHERE id = ?");
    $stmt->execute([$sport_id]);
    $sport = $stmt->fetch();
    
    if ($sport) {
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Current Sport:</strong> {$sport['name']}</p>";
        echo "<p><strong>Current Format:</strong> {$sport['bracket_format']}</p>";
        echo "</div>";
        
        // Update sport bracket format to appropriate format for badminton
        $correct_format = 'single_elimination'; // Badminton typically uses single elimination
        
        $stmt = $conn->prepare("UPDATE sports SET bracket_format = ? WHERE id = ?");
        $stmt->execute([$correct_format, $sport_id]);
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<p>✅ <strong>Updated sport bracket format to:</strong> $correct_format</p>";
        echo "</div>";
    }
    
    echo "<h2>2. Add Sample Departments for Testing</h2>";
    
    // Check if departments exist
    $stmt = $conn->query("SELECT COUNT(*) as count FROM departments");
    $dept_count = $stmt->fetch()['count'];
    
    if ($dept_count == 0) {
        echo "<p>Creating sample departments...</p>";
        
        $sample_departments = [
            ['College of Engineering', 'COE', '#007bff'],
            ['College of Business', 'COB', '#28a745'],
            ['College of Arts and Sciences', 'CAS', '#dc3545'],
            ['College of Education', 'COEd', '#ffc107'],
            ['College of Medicine', 'COM', '#6f42c1'],
            ['College of Law', 'COL', '#fd7e14']
        ];
        
        foreach ($sample_departments as $dept) {
            $stmt = $conn->prepare("
                INSERT INTO departments (name, abbreviation, color_code, description, status) 
                VALUES (?, ?, ?, ?, 'active')
            ");
            $stmt->execute([$dept[0], $dept[1], $dept[2], "Sample department for testing"]);
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<p>✅ <strong>Created " . count($sample_departments) . " sample departments</strong></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
        echo "<p>📋 <strong>Found $dept_count existing departments</strong></p>";
        echo "</div>";
    }
    
    echo "<h2>3. Register Departments for Event</h2>";
    
    // Check if event_department_registrations table exists
    try {
        $stmt = $conn->query("SHOW TABLES LIKE 'event_department_registrations'");
        $table_exists = $stmt->rowCount() > 0;
        
        if (!$table_exists) {
            echo "<p>Creating event_department_registrations table...</p>";
            
            $sql = "CREATE TABLE IF NOT EXISTS event_department_registrations (
                id INT AUTO_INCREMENT PRIMARY KEY,
                event_id INT NOT NULL,
                department_id INT NOT NULL,
                registration_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status ENUM('pending', 'approved', 'rejected', 'withdrawn') DEFAULT 'approved',
                contact_person VARCHAR(255),
                contact_email VARCHAR(255),
                contact_phone VARCHAR(20),
                notes TEXT,
                total_participants INT DEFAULT 1,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (event_id) REFERENCES events(id) ON DELETE CASCADE,
                FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE,
                UNIQUE KEY unique_event_department (event_id, department_id)
            )";
            $conn->exec($sql);
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<p>✅ <strong>Created event_department_registrations table</strong></p>";
            echo "</div>";
        }
        
        // Get all departments
        $stmt = $conn->query("SELECT id, name, abbreviation FROM departments ORDER BY name");
        $departments = $stmt->fetchAll();
        
        // Register departments for the event
        $registered_count = 0;
        foreach ($departments as $dept) {
            try {
                $stmt = $conn->prepare("
                    INSERT IGNORE INTO event_department_registrations 
                    (event_id, department_id, status, contact_person, total_participants) 
                    VALUES (?, ?, 'approved', 'Test Contact', 1)
                ");
                $stmt->execute([$event_id, $dept['id']]);
                
                if ($stmt->rowCount() > 0) {
                    $registered_count++;
                }
            } catch (Exception $e) {
                // Skip if already registered
            }
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<p>✅ <strong>Registered $registered_count departments for event</strong></p>";
        echo "</div>";
        
        // Show registered departments
        $stmt = $conn->prepare("
            SELECT d.name, d.abbreviation, edr.registration_date
            FROM departments d
            JOIN event_department_registrations edr ON d.id = edr.department_id
            WHERE edr.event_id = ?
            ORDER BY d.name
        ");
        $stmt->execute([$event_id]);
        $registered_depts = $stmt->fetchAll();
        
        if (!empty($registered_depts)) {
            echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<h4>📋 Registered Departments:</h4>";
            echo "<ul>";
            foreach ($registered_depts as $dept) {
                echo "<li><strong>{$dept['name']}</strong> ({$dept['abbreviation']}) - {$dept['registration_date']}</li>";
            }
            echo "</ul>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<p>❌ Error with department registration: " . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "<h2>4. Verify Fixes</h2>";
    
    // Check tournament format
    $stmt = $conn->prepare("SELECT bracket_format FROM sports WHERE id = ?");
    $stmt->execute([$sport_id]);
    $updated_sport = $stmt->fetch();
    
    // Check participants
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM departments d
        JOIN event_department_registrations edr ON d.id = edr.department_id
        WHERE edr.event_id = ?
    ");
    $stmt->execute([$event_id]);
    $participant_count = $stmt->fetch()['count'];
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;'>";
    echo "<h3>✅ Verification Results</h3>";
    echo "<ul>";
    echo "<li><strong>Tournament Format:</strong> " . ucwords(str_replace('_', ' ', $updated_sport['bracket_format'])) . "</li>";
    echo "<li><strong>Registered Participants:</strong> $participant_count departments</li>";
    echo "</ul>";
    echo "</div>";
    
    $conn->commit();
    
    echo "<h2>5. Test the Fixed Category Page</h2>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='manage-category.php?event_id=$event_id&sport_id=$sport_id&category_id=$category_id' target='_blank' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "🚀 Test Fixed Category Page";
    echo "</a>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin-top: 20px;'>";
    echo "<h3>🎯 What Was Fixed</h3>";
    echo "<ol>";
    echo "<li><strong>Tournament Format:</strong> Updated Badminton sport to use 'Single Elimination' format</li>";
    echo "<li><strong>Participants:</strong> Created sample departments and registered them for the event</li>";
    echo "<li><strong>Database Structure:</strong> Ensured event_department_registrations table exists</li>";
    echo "<li><strong>Data Integrity:</strong> Added proper foreign key relationships</li>";
    echo "</ol>";
    echo "<p><strong>Result:</strong> The category page should now show the correct tournament format and display registered participants!</p>";
    echo "</div>";
    
} catch (Exception $e) {
    $conn->rollBack();
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
