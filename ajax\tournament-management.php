<?php
/**
 * Tournament Management AJAX Endpoint
 * SC_IMS Sports Competition and Event Management System
 * 
 * Handles AJAX requests for tournament management operations
 */

require_once '../config/database.php';
require_once '../includes/auth.php';
require_once '../includes/functions.php';
require_once '../includes/tournament_manager.php';

// Ensure admin authentication
requireAdmin();

header('Content-Type: application/json');

try {
    $action = $_POST['action'] ?? $_GET['action'] ?? '';
    
    switch ($action) {
        case 'get_bracket_types':
            handleGetBracketTypes();
            break;
            
        case 'create_tournament':
            handleCreateTournament();
            break;
            
        case 'get_tournament_standings':
            handleGetTournamentStandings();
            break;
            
        case 'get_bracket_visualization':
            handleGetBracketVisualization();
            break;
            
        case 'advance_tournament':
            handleAdvanceTournament();
            break;

        case 'get_tournament_matches':
            handleGetTournamentMatches();
            break;

        case 'get_sport_formats':
            handleGetSportFormats();
            break;

        case 'generate_bracket':
            handleGenerateBracket();
            break;

        default:
            throw new Exception('Invalid action');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get available bracket types for a sport
 */
function handleGetBracketTypes() {
    global $conn;
    
    $sportId = $_GET['sport_id'] ?? '';
    if (empty($sportId)) {
        throw new Exception('Sport ID is required');
    }
    
    $tournamentManager = new TournamentManager($conn);
    
    // Get sport type category
    $sportTypeCategory = $tournamentManager->getSportTypeCategory($sportId);
    
    // Get available formats
    $formats = $tournamentManager->getAvailableFormats($sportTypeCategory);
    
    // Format for select options
    $options = [];
    foreach ($formats as $format) {
        $options[] = [
            'value' => $format['id'],
            'text' => $format['name'],
            'description' => $format['description'],
            'min_participants' => $format['min_participants'],
            'max_participants' => $format['max_participants']
        ];
    }
    
    echo json_encode([
        'success' => true,
        'sport_type_category' => $sportTypeCategory,
        'formats' => $options
    ]);
}

/**
 * Create a new tournament
 */
function handleCreateTournament() {
    global $conn;
    
    $eventSportId = $_POST['event_sport_id'] ?? '';
    $formatId = $_POST['format_id'] ?? '';
    $tournamentName = $_POST['tournament_name'] ?? '';
    $seedingMethod = $_POST['seeding_method'] ?? 'random';
    
    if (empty($eventSportId) || empty($formatId)) {
        throw new Exception('Event sport ID and format ID are required');
    }
    
    $tournamentManager = new TournamentManager($conn);
    
    $config = [
        'seeding_method' => $seedingMethod,
        'scoring_config' => [
            'points_win' => $_POST['points_win'] ?? 3,
            'points_draw' => $_POST['points_draw'] ?? 1,
            'points_loss' => $_POST['points_loss'] ?? 0
        ]
    ];
    
    $tournamentId = $tournamentManager->createTournament($eventSportId, $formatId, $tournamentName, $config);
    
    // Log admin activity
    logAdminActivity($conn, $_SESSION['admin_id'], 'create_tournament', 
        "Created tournament: {$tournamentName} (ID: {$tournamentId})");
    
    echo json_encode([
        'success' => true,
        'message' => 'Tournament created successfully',
        'tournament_id' => $tournamentId
    ]);
}

/**
 * Get tournament standings
 */
function handleGetTournamentStandings() {
    global $conn;

    $eventSportId = $_GET['event_sport_id'] ?? '';
    if (empty($eventSportId)) {
        throw new Exception('Event sport ID is required');
    }

    try {
        // Get tournament structure
        $sql = "SELECT ts.* FROM tournament_structures ts WHERE ts.event_sport_id = ? ORDER BY ts.created_at DESC LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$eventSportId]);
        $tournament = $stmt->fetch();

        if (!$tournament) {
            echo json_encode([
                'success' => true,
                'standings' => [],
                'message' => 'No tournament found'
            ]);
            return;
        }

        // Get tournament participants with their current standings
        $sql = "SELECT
                    tp.*,
                    d.name as department_name,
                    d.abbreviation,
                    COALESCE(tp.points, 0) as points,
                    COALESCE(tp.wins, 0) as wins,
                    COALESCE(tp.losses, 0) as losses,
                    COALESCE(tp.draws, 0) as draws
                FROM tournament_participants tp
                JOIN registrations r ON tp.registration_id = r.id
                JOIN departments d ON r.department_id = d.id
                WHERE tp.tournament_structure_id = ?
                ORDER BY tp.points DESC, tp.wins DESC, tp.losses ASC";

        $stmt = $conn->prepare($sql);
        $stmt->execute([$tournament['id']]);
        $standings = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'standings' => $standings,
            'tournament' => $tournament
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error fetching standings: ' . $e->getMessage()
        ]);
    }
}

/**
 * Get bracket visualization data
 */
function handleGetBracketVisualization() {
    global $conn;

    $eventSportId = $_GET['event_sport_id'] ?? '';
    if (empty($eventSportId)) {
        throw new Exception('Event sport ID is required');
    }

    try {
        // Get tournament structure
        $sql = "SELECT ts.*, tf.name as format_name FROM tournament_structures ts
                JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
                WHERE ts.event_sport_id = ? ORDER BY ts.created_at DESC LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$eventSportId]);
        $tournament = $stmt->fetch();

        if (!$tournament) {
            echo json_encode([
                'success' => false,
                'message' => 'No tournament found for this event sport'
            ]);
            return;
        }

        // Generate bracket HTML
        $bracketHtml = generateBracketHtml($conn, $tournament);

        echo json_encode([
            'success' => true,
            'bracket_html' => $bracketHtml,
            'tournament' => $tournament
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error generating bracket: ' . $e->getMessage()
        ]);
    }
}

/**
 * Generate bracket HTML visualization
 */
function generateBracketHtml($conn, $tournament) {
    try {
        // Get tournament participants - simplified schema
        $sql = "SELECT tp.*, d.name as department_name, d.abbreviation
                FROM tournament_participants tp
                JOIN departments d ON tp.department_id = d.id
                WHERE tp.tournament_structure_id = ?
                ORDER BY tp.seed_number";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$tournament['id']]);
        $participants = $stmt->fetchAll();

        // Get tournament matches
        $sql = "SELECT m.*,
                       d1.name as team1_name, d1.abbreviation as team1_abbr,
                       d2.name as team2_name, d2.abbreviation as team2_abbr,
                       tr.round_name
                FROM matches m
                LEFT JOIN registrations r1 ON m.team1_id = r1.id
                LEFT JOIN departments d1 ON r1.department_id = d1.id
                LEFT JOIN registrations r2 ON m.team2_id = r2.id
                LEFT JOIN departments d2 ON r2.department_id = d2.id
                LEFT JOIN tournament_rounds tr ON m.tournament_round_id = tr.id
                WHERE m.tournament_structure_id = ?
                ORDER BY m.round_number, m.match_number";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$tournament['id']]);
        $matches = $stmt->fetchAll();

        // Generate HTML based on tournament format
        $html = '<div class="tournament-bracket">';
        $html .= '<h4>Tournament Bracket - ' . htmlspecialchars($tournament['format_name']) . '</h4>';

        if (empty($participants)) {
            $html .= '<p>No participants found for this tournament.</p>';
        } else {
            $html .= generateBracketStructure($participants, $matches, $tournament);
        }

        $html .= '</div>';

        return $html;

    } catch (Exception $e) {
        return '<p style="color: #ef4444;">Error generating bracket: ' . htmlspecialchars($e->getMessage()) . '</p>';
    }
}

/**
 * Generate bracket structure HTML
 */
function generateBracketStructure($participants, $matches, $tournament) {
    $html = '<div class="bracket-container">';

    // Simple bracket display for now
    $html .= '<div class="bracket-participants">';
    $html .= '<h5>Participants (' . count($participants) . ')</h5>';
    $html .= '<div class="participants-grid">';

    foreach ($participants as $participant) {
        $status_class = $participant['current_status'] === 'active' ? 'active' : 'eliminated';
        $html .= '<div class="participant-card ' . $status_class . '">';
        $html .= '<div class="participant-seed">#' . ($participant['seed_number'] ?? '?') . '</div>';
        $html .= '<div class="participant-name">' . htmlspecialchars($participant['department_name']) . '</div>';
        $html .= '<div class="participant-record">' . ($participant['wins'] ?? 0) . 'W - ' . ($participant['losses'] ?? 0) . 'L</div>';
        $html .= '</div>';
    }

    $html .= '</div></div>';

    // Matches by round
    if (!empty($matches)) {
        $matchesByRound = [];
        foreach ($matches as $match) {
            $matchesByRound[$match['round_number']][] = $match;
        }

        $html .= '<div class="bracket-rounds">';
        foreach ($matchesByRound as $roundNum => $roundMatches) {
            $html .= '<div class="bracket-round">';
            $html .= '<h6>Round ' . $roundNum . '</h6>';

            foreach ($roundMatches as $match) {
                $html .= '<div class="match-card">';
                $html .= '<div class="match-teams">';
                $html .= '<div class="team">' . htmlspecialchars($match['team1_name'] ?? 'TBD') . '</div>';
                $html .= '<div class="vs">vs</div>';
                $html .= '<div class="team">' . htmlspecialchars($match['team2_name'] ?? 'TBD') . '</div>';
                $html .= '</div>';
                $html .= '<div class="match-status">' . ucfirst($match['status']) . '</div>';
                $html .= '</div>';
            }

            $html .= '</div>';
        }
        $html .= '</div>';
    }

    $html .= '</div>';

    // Add CSS for bracket styling
    $html .= '<style>
        .bracket-container {
            padding: 1rem;
        }
        .participants-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
            margin: 1rem 0;
        }
        .participant-card {
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            text-align: center;
        }
        .participant-card.active {
            border-color: #10b981;
            background: #f0fdf4;
        }
        .participant-card.eliminated {
            border-color: #ef4444;
            background: #fef2f2;
            opacity: 0.7;
        }
        .participant-seed {
            font-weight: bold;
            color: #6b7280;
            font-size: 0.9rem;
        }
        .participant-name {
            font-weight: 600;
            margin: 0.5rem 0;
        }
        .participant-record {
            font-size: 0.8rem;
            color: #6b7280;
        }
        .bracket-rounds {
            margin-top: 2rem;
        }
        .bracket-round {
            margin-bottom: 2rem;
        }
        .bracket-round h6 {
            background: #f3f4f6;
            padding: 0.5rem 1rem;
            border-radius: 6px;
            margin-bottom: 1rem;
        }
        .match-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 1rem;
            margin-bottom: 0.5rem;
        }
        .match-teams {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        .team {
            font-weight: 500;
            flex: 1;
        }
        .vs {
            margin: 0 1rem;
            color: #6b7280;
            font-size: 0.9rem;
        }
        .match-status {
            text-align: center;
            font-size: 0.8rem;
            color: #6b7280;
            text-transform: uppercase;
        }
    </style>';

    return $html;
}

/**
 * Advance tournament to next round
 */
function handleAdvanceTournament() {
    global $conn;
    
    $tournamentId = $_POST['tournament_id'] ?? '';
    if (empty($tournamentId)) {
        throw new Exception('Tournament ID is required');
    }
    
    $tournamentManager = new TournamentManager($conn);
    $result = $tournamentManager->advanceToNextRound($tournamentId);
    
    // Log admin activity
    logAdminActivity($conn, $_SESSION['admin_id'], 'advance_tournament', 
        "Advanced tournament ID: {$tournamentId} - {$result['message']}");
    
    echo json_encode([
        'success' => true,
        'message' => $result['message'],
        'status' => $result['status']
    ]);
}

/**
 * Get tournament matches
 */
function handleGetTournamentMatches() {
    global $conn;

    $eventSportId = $_GET['event_sport_id'] ?? '';
    if (empty($eventSportId)) {
        throw new Exception('Event sport ID is required');
    }

    try {
        // Get tournament structure
        $sql = "SELECT ts.* FROM tournament_structures ts WHERE ts.event_sport_id = ? ORDER BY ts.created_at DESC LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$eventSportId]);
        $tournament = $stmt->fetch();

        if (!$tournament) {
            echo json_encode([
                'success' => true,
                'matches' => [],
                'message' => 'No tournament found'
            ]);
            return;
        }

        // Get matches for this tournament
        $sql = "SELECT
                    m.*,
                    d1.name as team1_name,
                    d2.name as team2_name,
                    tr.round_name
                FROM matches m
                LEFT JOIN registrations r1 ON m.team1_id = r1.id
                LEFT JOIN departments d1 ON r1.department_id = d1.id
                LEFT JOIN registrations r2 ON m.team2_id = r2.id
                LEFT JOIN departments d2 ON r2.department_id = d2.id
                LEFT JOIN tournament_rounds tr ON m.tournament_round_id = tr.id
                WHERE m.tournament_structure_id = ?
                ORDER BY m.round_number, m.match_number";

        $stmt = $conn->prepare($sql);
        $stmt->execute([$tournament['id']]);
        $matches = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'matches' => $matches,
            'tournament' => $tournament
        ]);

    } catch (Exception $e) {
        throw new Exception('Error fetching tournament matches: ' . $e->getMessage());
    }
}

/**
 * Get sport details including type category
 */
function getSportDetails($sportId) {
    global $conn;
    
    $sql = "SELECT s.*, st.category as sport_type_category, st.name as sport_type_name
            FROM sports s
            LEFT JOIN sport_types st ON s.sport_type_id = st.id
            WHERE s.id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$sportId]);
    return $stmt->fetch();
}

/**
 * Validate tournament configuration
 */
function validateTournamentConfig($formatId, $participantCount) {
    global $conn;
    
    $sql = "SELECT min_participants, max_participants FROM tournament_formats WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$formatId]);
    $format = $stmt->fetch();
    
    if (!$format) {
        throw new Exception('Invalid tournament format');
    }
    
    if ($participantCount < $format['min_participants']) {
        throw new Exception("Minimum {$format['min_participants']} participants required for this format");
    }
    
    if ($format['max_participants'] && $participantCount > $format['max_participants']) {
        throw new Exception("Maximum {$format['max_participants']} participants allowed for this format");
    }
    
    return true;
}

/**
 * Get tournament format restrictions for sport type
 */
function getTournamentRestrictions($sportTypeCategory) {
    $restrictions = [
        // Enhanced sport type categories
        'traditional' => [
            'allowed_formats' => ['single_elimination', 'double_elimination', 'round_robin', 'multi_stage', 'elimination_rounds'],
            'default_seeding' => 'random',
            'supports_teams' => true
        ],
        'academic' => [
            'allowed_formats' => ['swiss_system', 'knockout_rounds', 'round_robin'],
            'default_seeding' => 'ranking',
            'supports_teams' => false
        ],
        'judged' => [
            'allowed_formats' => ['judged_rounds', 'talent_showcase', 'elimination_rounds'],
            'default_seeding' => 'manual',
            'supports_teams' => false
        ],
        'performance' => [
            'allowed_formats' => ['talent_showcase', 'judged_rounds', 'best_performance'],
            'default_seeding' => 'manual',
            'supports_teams' => false
        ],
        // Legacy support for backward compatibility
        'individual' => [
            'allowed_formats' => ['elimination_rounds', 'judged_rounds', 'best_performance', 'talent_showcase'],
            'default_seeding' => 'ranking',
            'supports_teams' => false
        ],
        'team' => [
            'allowed_formats' => ['single_elimination', 'double_elimination', 'round_robin', 'multi_stage'],
            'default_seeding' => 'random',
            'supports_teams' => true
        ]
    ];

    return $restrictions[$sportTypeCategory] ?? $restrictions['traditional'];
}

/**
 * Get participant count for event sport
 */
function getParticipantCount($eventSportId) {
    global $conn;
    
    $sql = "SELECT COUNT(*) as count FROM registrations 
            WHERE event_sport_id = ? AND status = 'confirmed'";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$eventSportId]);
    $result = $stmt->fetch();
    
    return $result['count'] ?? 0;
}

/**
 * Check if tournament already exists for event sport
 */
function tournamentExists($eventSportId) {
    global $conn;
    
    $sql = "SELECT COUNT(*) as count FROM tournament_structures WHERE event_sport_id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$eventSportId]);
    $result = $stmt->fetch();
    
    return $result['count'] > 0;
}

/**
 * Get sport formats based on sport type
 */
function handleGetSportFormats() {
    global $conn;

    $sportTypeCategory = $_GET['sport_type_category'] ?? '';
    if (empty($sportTypeCategory)) {
        throw new Exception('Sport type category is required');
    }

    try {
        $sql = "SELECT * FROM tournament_formats WHERE sport_type_category = ? OR sport_type_category = 'all' ORDER BY name";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$sportTypeCategory]);
        $formats = $stmt->fetchAll();

        echo json_encode([
            'success' => true,
            'formats' => $formats
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error fetching sport formats: ' . $e->getMessage()
        ]);
    }
}

/**
 * Generate tournament bracket
 */
function handleGenerateBracket() {
    global $conn;

    $eventSportId = $_POST['event_sport_id'] ?? $_GET['event_sport_id'] ?? '';
    if (empty($eventSportId)) {
        throw new Exception('Event sport ID is required');
    }

    try {
        // Get tournament structure
        $sql = "SELECT ts.* FROM tournament_structures ts WHERE ts.event_sport_id = ? ORDER BY ts.created_at DESC LIMIT 1";
        $stmt = $conn->prepare($sql);
        $stmt->execute([$eventSportId]);
        $tournament = $stmt->fetch();

        if (!$tournament) {
            echo json_encode([
                'success' => false,
                'message' => 'No tournament found for this event sport'
            ]);
            return;
        }

        // Initialize tournament manager
        require_once '../includes/tournament_manager.php';
        $tournamentManager = new TournamentManager($conn);

        // Generate bracket (this would call a method to regenerate/update the bracket)
        // For now, we'll just return success since the bracket is generated during tournament creation
        echo json_encode([
            'success' => true,
            'message' => 'Bracket is ready for viewing',
            'tournament_id' => $tournament['id']
        ]);

    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => 'Error generating bracket: ' . $e->getMessage()
        ]);
    }
}
?>
