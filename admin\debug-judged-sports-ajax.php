<?php
/**
 * Debug Judged Sports AJAX Issue
 * Investigate what's happening with tournament format loading for Judged sports
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>🔍 Debugging Judged Sports AJAX Issue</h2>";

// 1. Check the specific sports that are causing issues
echo "<h3>1. Judged Sports in Database</h3>";
$stmt = $conn->prepare("
    SELECT s.id, s.name, s.type, 
           st.category as sport_type_category,
           st.name as sport_type_name
    FROM sports s
    LEFT JOIN sport_types st ON s.sport_type_id = st.id
    WHERE st.category = 'judged' OR s.type = 'judged'
    ORDER BY s.name
");
$stmt->execute();
$judged_sports = $stmt->fetchAll();

if (empty($judged_sports)) {
    echo "<p style='color: red;'>❌ No Judged sports found in database!</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Type</th><th>Sport Type Category</th><th>Sport Type Name</th></tr>";
    foreach ($judged_sports as $sport) {
        echo "<tr>";
        echo "<td>{$sport['id']}</td>";
        echo "<td>{$sport['name']}</td>";
        echo "<td>" . ($sport['type'] ?? 'N/A') . "</td>";
        echo "<td>" . ($sport['sport_type_category'] ?? 'N/A') . "</td>";
        echo "<td>" . ($sport['sport_type_name'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// 2. Check tournament formats for judged sports
echo "<h3>2. Tournament Formats for Judged Sports</h3>";
$stmt = $conn->prepare("
    SELECT id, name, code, sport_type_category, sport_types
    FROM tournament_formats
    WHERE sport_type_category = 'judged' OR sport_types LIKE '%judged%'
    ORDER BY name
");
$stmt->execute();
$judged_formats = $stmt->fetchAll();

if (empty($judged_formats)) {
    echo "<p style='color: red;'>❌ No tournament formats found for Judged sports!</p>";
} else {
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>ID</th><th>Name</th><th>Code</th><th>Sport Type Category</th><th>Sport Types</th></tr>";
    foreach ($judged_formats as $format) {
        echo "<tr>";
        echo "<td>{$format['id']}</td>";
        echo "<td>{$format['name']}</td>";
        echo "<td>{$format['code']}</td>";
        echo "<td>" . ($format['sport_type_category'] ?? 'N/A') . "</td>";
        echo "<td>" . ($format['sport_types'] ?? 'N/A') . "</td>";
        echo "</tr>";
    }
    echo "</table>";
}

// 3. Test the AJAX endpoint directly
echo "<h3>3. Direct AJAX Endpoint Test</h3>";

// Test different sport type values
$test_types = ['judged', 'academic', 'traditional'];

foreach ($test_types as $sport_type) {
    echo "<h4>Testing sport_type: '{$sport_type}'</h4>";
    
    // Simulate the AJAX call
    $_POST['sport_type'] = $sport_type;
    
    ob_start();
    try {
        // Capture any errors
        error_reporting(E_ALL);
        ini_set('display_errors', 1);
        
        // Include the AJAX file
        include 'ajax/get-tournament-formats.php';
        $response = ob_get_clean();
        
        echo "<div style='background: #f8f9fa; padding: 10px; border: 1px solid #dee2e6; margin: 10px 0;'>";
        echo "<strong>Raw Response:</strong><br>";
        echo "<pre>" . htmlspecialchars($response) . "</pre>";
        echo "</div>";
        
        // Try to decode JSON
        $data = json_decode($response, true);
        if ($data) {
            echo "<div style='background: #d4edda; padding: 10px; border: 1px solid #c3e6cb; margin: 10px 0;'>";
            echo "<strong>Parsed JSON:</strong><br>";
            echo "Success: " . ($data['success'] ? 'true' : 'false') . "<br>";
            if (isset($data['formats'])) {
                echo "Formats found: " . count($data['formats']) . "<br>";
                foreach ($data['formats'] as $format) {
                    echo "- {$format['name']} (ID: {$format['id']})<br>";
                }
            }
            if (isset($data['debug'])) {
                echo "Debug info: " . json_encode($data['debug']) . "<br>";
            }
            if (isset($data['message'])) {
                echo "Message: {$data['message']}<br>";
            }
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
            echo "<strong>❌ Failed to parse JSON response</strong>";
            echo "</div>";
        }
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "<div style='background: #f8d7da; padding: 10px; border: 1px solid #f5c6cb; margin: 10px 0;'>";
        echo "<strong>❌ Exception:</strong> " . $e->getMessage();
        echo "</div>";
    }
    
    unset($_POST['sport_type']);
    echo "<hr>";
}

// 4. Check how sports are loaded in the dropdown
echo "<h3>4. Sport Dropdown Data Analysis</h3>";
$available_sports = getAvailableSports($conn, 1); // Use event ID 1

echo "<h4>Available Sports for Event (showing data-type attributes):</h4>";
echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
echo "<tr><th>ID</th><th>Name</th><th>data-type Value</th><th>Sport Type Category</th><th>Type</th></tr>";
foreach ($available_sports as $sport) {
    $data_type = $sport['sport_type_category'] ?? $sport['type'];
    echo "<tr>";
    echo "<td>{$sport['id']}</td>";
    echo "<td>{$sport['name']}</td>";
    echo "<td style='font-weight: bold; color: blue;'>{$data_type}</td>";
    echo "<td>" . ($sport['sport_type_category'] ?? 'N/A') . "</td>";
    echo "<td>" . ($sport['type'] ?? 'N/A') . "</td>";
    echo "</tr>";
}
echo "</table>";

// 5. Test specific problematic sports
echo "<h3>5. Test Specific Problematic Sports</h3>";
$problematic_sports = ['Mr. and Ms. Intramurals', 'Dance'];

foreach ($problematic_sports as $sport_name) {
    echo "<h4>Testing: {$sport_name}</h4>";
    
    $stmt = $conn->prepare("
        SELECT s.*, st.category as sport_type_category, st.name as sport_type_name
        FROM sports s
        LEFT JOIN sport_types st ON s.sport_type_id = st.id
        WHERE s.name LIKE ?
    ");
    $stmt->execute(["%{$sport_name}%"]);
    $sport = $stmt->fetch();
    
    if ($sport) {
        $sport_type = $sport['sport_type_category'] ?? $sport['type'];
        echo "<p><strong>Found sport:</strong> {$sport['name']}</p>";
        echo "<p><strong>Sport Type:</strong> {$sport_type}</p>";
        echo "<p><strong>Sport Type Category:</strong> " . ($sport['sport_type_category'] ?? 'N/A') . "</p>";
        
        // Test AJAX for this specific sport type
        $_POST['sport_type'] = $sport_type;
        
        ob_start();
        try {
            include 'ajax/get-tournament-formats.php';
            $response = ob_get_clean();
            $data = json_decode($response, true);
            
            if ($data && $data['success']) {
                echo "<p style='color: green;'>✅ AJAX Success: Found " . count($data['formats']) . " formats</p>";
            } else {
                echo "<p style='color: red;'>❌ AJAX Failed: " . ($data['message'] ?? 'Unknown error') . "</p>";
                echo "<p>Raw response: " . htmlspecialchars($response) . "</p>";
            }
        } catch (Exception $e) {
            ob_end_clean();
            echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
        }
        
        unset($_POST['sport_type']);
    } else {
        echo "<p style='color: orange;'>⚠️ Sport '{$sport_name}' not found in database</p>";
    }
}

echo "<h3>6. Recommendations</h3>";
echo "<div style='background: #f0f8ff; padding: 15px; border-left: 4px solid #007bff;'>";
echo "<p><strong>Based on the analysis above, check for these common issues:</strong></p>";
echo "<ul>";
echo "<li>Are Judged sports properly categorized in the sport_types table?</li>";
echo "<li>Are tournament formats properly linked to the 'judged' category?</li>";
echo "<li>Is the AJAX endpoint receiving the correct sport_type parameter?</li>";
echo "<li>Are there any PHP errors in the AJAX response?</li>";
echo "<li>Is the sport type mapping in get-tournament-formats.php correct?</li>";
echo "</ul>";
echo "</div>";
?>
