<?php
/**
 * Debug Category Click Issues
 * Real-time debugging of category navigation
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get URL parameters to see what's being passed
$event_id = $_GET['event_id'] ?? 'NOT_SET';
$sport_id = $_GET['sport_id'] ?? 'NOT_SET';
$category_id = $_GET['category_id'] ?? 'NOT_SET';
$debug = $_GET['debug'] ?? false;

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Category Click</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-info { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🔍 Debug Category Click</h1>
        <p>Real-time debugging of category navigation issues</p>
        
        <div class="debug-info">
            <h3>Current Request Parameters:</h3>
            <table class="table table-sm">
                <tr><td><strong>event_id:</strong></td><td><?php echo htmlspecialchars($event_id); ?></td></tr>
                <tr><td><strong>sport_id:</strong></td><td><?php echo htmlspecialchars($sport_id); ?></td></tr>
                <tr><td><strong>category_id:</strong></td><td><?php echo htmlspecialchars($category_id); ?></td></tr>
                <tr><td><strong>Full URL:</strong></td><td><?php echo htmlspecialchars($_SERVER['REQUEST_URI']); ?></td></tr>
            </table>
        </div>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4>1. Test Actual Categories Page</h4>
                    </div>
                    <div class="card-body">
                        <p>Let's check what categories actually exist and test their links:</p>
                        <div id="categories-test"></div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h4>2. Test manage-category.php Directly</h4>
                    </div>
                    <div class="card-body">
                        <p>Test the manage-category.php page with known good parameters:</p>
                        <div id="direct-test"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h4>3. Live Link Testing</h4>
            </div>
            <div class="card-body">
                <p>Click these links to test navigation. Watch the browser URL to see where you end up:</p>
                <div id="live-links"></div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h4>4. JavaScript Link Interceptor</h4>
            </div>
            <div class="card-body">
                <p>This will intercept category link clicks and show you exactly what's happening:</p>
                <button class="btn btn-primary" onclick="enableLinkInterceptor()">Enable Link Interceptor</button>
                <div id="interceptor-log" class="mt-3"></div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            loadCategoriesTest();
            loadDirectTest();
            loadLiveLinks();
        });
        
        function loadCategoriesTest() {
            const div = document.getElementById('categories-test');
            div.innerHTML = '<p>Loading categories...</p>';
            
            // Get available event-sport combinations
            fetch('ajax/get-test-data.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.event_sports.length > 0) {
                        let html = '<h6>Available Event-Sport Combinations:</h6>';
                        html += '<div class="list-group">';
                        
                        data.event_sports.forEach(es => {
                            const url = `sport-categories.php?event_id=${es.event_id}&sport_id=${es.sport_id}`;
                            html += `
                                <a href="${url}" class="list-group-item list-group-item-action" target="_blank">
                                    <strong>${es.event_name}</strong> - ${es.sport_name}
                                    <br><small class="text-muted">event_id=${es.event_id}, sport_id=${es.sport_id}</small>
                                </a>
                            `;
                        });
                        
                        html += '</div>';
                        html += '<p class="mt-2"><small class="text-info">Click these links to go to the categories page, then click on a category name to test navigation.</small></p>';
                        div.innerHTML = html;
                    } else {
                        div.innerHTML = '<div class="alert alert-warning">No event-sport combinations found</div>';
                    }
                })
                .catch(error => {
                    div.innerHTML = `<div class="alert alert-danger">Error: ${error.message}</div>`;
                });
        }
        
        function loadDirectTest() {
            const div = document.getElementById('direct-test');
            
            // Test direct links to manage-category.php
            let html = '<h6>Direct manage-category.php Tests:</h6>';
            html += '<div class="list-group">';
            
            const testCases = [
                {url: 'manage-category.php?event_id=1&sport_id=1&category_id=1', desc: 'Standard test (1,1,1)'},
                {url: 'manage-category.php?event_id=1&sport_id=2&category_id=2', desc: 'Different IDs (1,2,2)'},
                {url: 'manage-category.php?event_id=2&sport_id=1&category_id=3', desc: 'Mixed IDs (2,1,3)'}
            ];
            
            testCases.forEach(test => {
                html += `
                    <a href="${test.url}" class="list-group-item list-group-item-action" target="_blank">
                        <strong>${test.desc}</strong>
                        <br><small class="text-muted">${test.url}</small>
                    </a>
                `;
            });
            
            html += '</div>';
            html += '<p class="mt-2"><small class="text-info">These should either load the category management page or redirect to events.php if the category doesn\'t exist.</small></p>';
            
            div.innerHTML = html;
        }
        
        function loadLiveLinks() {
            const div = document.getElementById('live-links');
            div.innerHTML = '<p>Loading live category links...</p>';
            
            // Get actual categories from database
            fetch('check-database-structure.php')
                .then(response => response.text())
                .then(html => {
                    // Extract category information from the response
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(html, 'text/html');
                    const categoryTable = doc.querySelector('h3:contains("Sport Categories")');
                    
                    // For now, create some test links
                    let linksHtml = '<h6>Test Category Links:</h6>';
                    linksHtml += '<div class="alert alert-warning">';
                    linksHtml += '<p><strong>Instructions:</strong></p>';
                    linksHtml += '<ol>';
                    linksHtml += '<li>First, go to a sport-categories.php page using the links in section 1</li>';
                    linksHtml += '<li>Look for category names in the table</li>';
                    linksHtml += '<li>Right-click on a category name and "Inspect Element"</li>';
                    linksHtml += '<li>Check the href attribute of the link</li>';
                    linksHtml += '<li>Copy that exact URL and test it directly</li>';
                    linksHtml += '</ol>';
                    linksHtml += '</div>';
                    
                    div.innerHTML = linksHtml;
                })
                .catch(error => {
                    div.innerHTML = `<div class="alert alert-danger">Error loading links: ${error.message}</div>`;
                });
        }
        
        function enableLinkInterceptor() {
            const logDiv = document.getElementById('interceptor-log');
            logDiv.innerHTML = '<div class="alert alert-info">Link interceptor enabled. Now go to a sport-categories.php page and click on category names. The clicks will be logged here.</div>';
            
            // Open sport-categories page in new window with interceptor
            const newWindow = window.open('sport-categories.php?event_id=1&sport_id=1', '_blank');
            
            // Inject interceptor script into the new window
            newWindow.addEventListener('load', function() {
                const script = newWindow.document.createElement('script');
                script.textContent = `
                    document.addEventListener('click', function(e) {
                        if (e.target.closest('.category-link')) {
                            e.preventDefault();
                            const link = e.target.closest('.category-link');
                            const href = link.href;
                            const text = link.textContent.trim();
                            
                            // Send info back to parent window
                            window.opener.postMessage({
                                type: 'category-click',
                                href: href,
                                text: text,
                                timestamp: new Date().toISOString()
                            }, '*');
                            
                            // Also try to navigate to see what happens
                            console.log('Category clicked:', href);
                            window.location.href = href;
                        }
                    });
                `;
                newWindow.document.head.appendChild(script);
            });
        }
        
        // Listen for messages from intercepted clicks
        window.addEventListener('message', function(e) {
            if (e.data.type === 'category-click') {
                const logDiv = document.getElementById('interceptor-log');
                const logEntry = document.createElement('div');
                logEntry.className = 'alert alert-info';
                logEntry.innerHTML = `
                    <strong>Category Clicked:</strong> ${e.data.text}<br>
                    <strong>URL:</strong> ${e.data.href}<br>
                    <strong>Time:</strong> ${e.data.timestamp}
                `;
                logDiv.appendChild(logEntry);
            }
        });
    </script>
</body>
</html>
