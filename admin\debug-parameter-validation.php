<?php
/**
 * Debug Parameter Validation for manage-category.php
 * Test various parameter scenarios to ensure proper validation
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🐛 Parameter Validation Debug</h1>";

// Test scenarios
$test_scenarios = [
    [
        'name' => 'Valid Parameters',
        'description' => 'Test with valid event_id, sport_id, and category_id',
        'params' => ['event_id' => 1, 'sport_id' => 1, 'category_id' => 1],
        'expected' => 'Should load manage-category.php successfully'
    ],
    [
        'name' => 'Missing event_id',
        'description' => 'Test with missing event_id parameter',
        'params' => ['sport_id' => 1, 'category_id' => 1],
        'expected' => 'Should redirect to events.php'
    ],
    [
        'name' => 'Missing sport_id',
        'description' => 'Test with missing sport_id parameter',
        'params' => ['event_id' => 1, 'category_id' => 1],
        'expected' => 'Should redirect to events.php'
    ],
    [
        'name' => 'Missing category_id',
        'description' => 'Test with missing category_id parameter',
        'params' => ['event_id' => 1, 'sport_id' => 1],
        'expected' => 'Should redirect to events.php'
    ],
    [
        'name' => 'Non-numeric event_id',
        'description' => 'Test with non-numeric event_id',
        'params' => ['event_id' => 'abc', 'sport_id' => 1, 'category_id' => 1],
        'expected' => 'Should redirect to events.php'
    ],
    [
        'name' => 'Zero values',
        'description' => 'Test with zero values',
        'params' => ['event_id' => 0, 'sport_id' => 0, 'category_id' => 0],
        'expected' => 'Should redirect to events.php'
    ],
    [
        'name' => 'Non-existent IDs',
        'description' => 'Test with non-existent but valid numeric IDs',
        'params' => ['event_id' => 999, 'sport_id' => 999, 'category_id' => 999],
        'expected' => 'Should redirect to events.php (category not found)'
    ]
];

// Get available test data
try {
    $stmt = $conn->prepare("
        SELECT 
            sc.id as category_id,
            sc.category_name,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        ORDER BY sc.id
        LIMIT 1
    ");
    $stmt->execute();
    $test_data = $stmt->fetch();
    
    if ($test_data) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; border-left: 4px solid #28a745; margin-bottom: 20px;'>";
        echo "<h3>✅ Test Data Available</h3>";
        echo "<p><strong>Valid Test Parameters:</strong></p>";
        echo "<ul>";
        echo "<li><strong>event_id:</strong> {$test_data['event_id']} ({$test_data['event_name']})</li>";
        echo "<li><strong>sport_id:</strong> {$test_data['sport_id']} ({$test_data['sport_name']})</li>";
        echo "<li><strong>category_id:</strong> {$test_data['category_id']} ({$test_data['category_name']})</li>";
        echo "</ul>";
        echo "</div>";
        
        // Update the first test scenario with real data
        $test_scenarios[0]['params'] = [
            'event_id' => $test_data['event_id'],
            'sport_id' => $test_data['sport_id'],
            'category_id' => $test_data['category_id']
        ];
    } else {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; border-left: 4px solid #ffc107; margin-bottom: 20px;'>";
        echo "<h3>⚠️ No Test Data Available</h3>";
        echo "<p>No sport categories found for testing. Please create test data first.</p>";
        echo "<a href='check-navigation-data.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create Test Data</a>";
        echo "</div>";
    }
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; border-left: 4px solid #dc3545; margin-bottom: 20px;'>";
    echo "<h3>❌ Database Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>🧪 Parameter Validation Test Scenarios</h2>";

foreach ($test_scenarios as $index => $scenario) {
    $query_string = http_build_query($scenario['params']);
    $test_url = "manage-category.php?{$query_string}";
    $debug_url = "manage-category.php?{$query_string}&debug=1";
    
    echo "<div style='border: 1px solid #dee2e6; border-radius: 8px; padding: 15px; margin-bottom: 15px; background: white;'>";
    echo "<h4 style='margin-top: 0; color: #007bff;'>Test " . ($index + 1) . ": {$scenario['name']}</h4>";
    echo "<p><strong>Description:</strong> {$scenario['description']}</p>";
    echo "<p><strong>Parameters:</strong> " . json_encode($scenario['params']) . "</p>";
    echo "<p><strong>Expected Result:</strong> {$scenario['expected']}</p>";
    
    echo "<div style='display: flex; gap: 10px; margin-top: 10px;'>";
    echo "<a href='$test_url' target='_blank' style='background: #007bff; color: white; padding: 8px 12px; text-decoration: none; border-radius: 4px;'>";
    echo "🔗 Test Normal";
    echo "</a>";
    echo "<a href='$debug_url' target='_blank' style='background: #ffc107; color: black; padding: 8px 12px; text-decoration: none; border-radius: 4px;'>";
    echo "🐛 Test Debug Mode";
    echo "</a>";
    echo "</div>";
    echo "</div>";
}

echo "<h2>📋 Parameter Validation Logic Analysis</h2>";

echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px;'>";
echo "<h4>Current Validation Logic in manage-category.php:</h4>";
echo "<pre style='background: #e9ecef; padding: 10px; border-radius: 4px; overflow-x: auto;'>";
echo htmlspecialchars('
// Get URL parameters
$event_id = $_GET[\'event_id\'] ?? null;
$sport_id = $_GET[\'sport_id\'] ?? null;
$category_id = $_GET[\'category_id\'] ?? null;

// Validate required parameters
if (empty($event_id) || empty($sport_id) || empty($category_id) || 
    !is_numeric($event_id) || !is_numeric($sport_id) || !is_numeric($category_id)) {
    
    error_log("manage-category.php REDIRECT - Invalid parameters");
    
    // If debug mode, show error instead of redirecting
    if (isset($_GET[\'debug\'])) {
        die("DEBUG: Invalid parameters - event_id: $event_id, sport_id: $sport_id, category_id: $category_id");
    }
    
    header(\'Location: events.php\');
    exit;
}

// Convert to integers
$event_id = intval($event_id);
$sport_id = intval($sport_id);
$category_id = intval($category_id);
');
echo "</pre>";

echo "<h4>Validation Checks:</h4>";
echo "<ol>";
echo "<li><strong>empty() check:</strong> Ensures parameters are not null, empty string, or 0</li>";
echo "<li><strong>is_numeric() check:</strong> Ensures parameters are numeric values</li>";
echo "<li><strong>intval() conversion:</strong> Converts to integers for database queries</li>";
echo "<li><strong>Debug mode:</strong> Shows error details instead of redirecting when debug=1 is set</li>";
echo "</ol>";

echo "<h4>Potential Issues:</h4>";
echo "<ul>";
echo "<li><strong>empty() with 0:</strong> The empty() function returns true for 0, which might be a valid ID in some cases</li>";
echo "<li><strong>String '0':</strong> The string '0' would pass is_numeric() but fail empty()</li>";
echo "<li><strong>Negative numbers:</strong> Negative numbers pass is_numeric() but might not be valid IDs</li>";
echo "</ul>";

echo "<h4>Recommended Improvements:</h4>";
echo "<ul>";
echo "<li>Use <code>isset()</code> and <code>!== ''</code> instead of <code>empty()</code> for better control</li>";
echo "<li>Add explicit checks for positive integers: <code>$id > 0</code></li>";
echo "<li>Add more specific error messages for different validation failures</li>";
echo "<li>Consider using filter_var() with FILTER_VALIDATE_INT for more robust validation</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🔧 Suggested Improved Validation</h2>";
echo "<div style='background: #e7f3ff; padding: 15px; border-radius: 5px;'>";
echo "<pre style='background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;'>";
echo htmlspecialchars('
// Get URL parameters
$event_id = filter_var($_GET[\'event_id\'] ?? null, FILTER_VALIDATE_INT);
$sport_id = filter_var($_GET[\'sport_id\'] ?? null, FILTER_VALIDATE_INT);
$category_id = filter_var($_GET[\'category_id\'] ?? null, FILTER_VALIDATE_INT);

// Validate required parameters
if ($event_id === false || $sport_id === false || $category_id === false ||
    $event_id <= 0 || $sport_id <= 0 || $category_id <= 0) {
    
    $error_details = [
        "event_id" => $_GET[\'event_id\'] ?? "missing",
        "sport_id" => $_GET[\'sport_id\'] ?? "missing", 
        "category_id" => $_GET[\'category_id\'] ?? "missing"
    ];
    
    error_log("manage-category.php REDIRECT - Invalid parameters: " . json_encode($error_details));
    
    if (isset($_GET[\'debug\'])) {
        die("DEBUG: Invalid parameters - " . json_encode($error_details));
    }
    
    header(\'Location: events.php\');
    exit;
}
');
echo "</pre>";
echo "</div>";
?>
