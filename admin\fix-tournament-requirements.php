<?php
/**
 * Fix Tournament Requirements
 * SC_IMS Sports Competition and Event Management System
 * 
 * This script fixes two main issues:
 * 1. Reduces minimum participant requirements to 2 for all formats
 * 2. Adds missing tournament formats for judged sports
 */

require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Fixing Tournament Requirements</h2>";

try {
    $conn->beginTransaction();
    
    // =====================================================
    // STEP 1: Fix minimum participant requirements
    // =====================================================
    
    echo "<h3>Step 1: Fixing Minimum Participant Requirements</h3>";
    
    // Get all formats with min_participants > 2
    $stmt = $conn->prepare("SELECT id, name, min_participants FROM tournament_formats WHERE min_participants > 2");
    $stmt->execute();
    $high_min_formats = $stmt->fetchAll();
    
    if (!empty($high_min_formats)) {
        echo "<p>Found " . count($high_min_formats) . " formats with min_participants > 2:</p>";
        echo "<ul>";
        foreach ($high_min_formats as $format) {
            echo "<li><strong>" . htmlspecialchars($format['name']) . "</strong> - Current Min: " . $format['min_participants'] . "</li>";
        }
        echo "</ul>";
        
        // Update all formats to have minimum 2 participants
        $stmt = $conn->prepare("UPDATE tournament_formats SET min_participants = 2 WHERE min_participants > 2");
        $result = $stmt->execute();
        $affected_rows = $stmt->rowCount();
        
        echo "<p style='color: green;'>✅ Updated $affected_rows tournament formats to require minimum 2 participants</p>";
    } else {
        echo "<p style='color: green;'>✅ All tournament formats already have minimum 2 participants or less</p>";
    }
    
    // =====================================================
    // STEP 2: Add missing judged sport formats
    // =====================================================
    
    echo "<h3>Step 2: Adding Missing Judged Sport Formats</h3>";
    
    // Check if judged formats exist
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count FROM tournament_formats 
        WHERE sport_type_category IN ('judged', 'performance') 
        OR name LIKE '%judged%' OR name LIKE '%talent%' OR name LIKE '%performance%'
    ");
    $stmt->execute();
    $judged_count = $stmt->fetch()['count'];
    
    echo "<p>Current judged sport formats: $judged_count</p>";
    
    // Define comprehensive judged sport formats
    $judged_formats = [
        [
            'name' => 'Judged Rounds',
            'code' => 'judged_rounds',
            'description' => 'Multiple judged rounds with scoring criteria for competitions like singing, dancing, and talent shows',
            'sport_type_category' => 'judged',
            'min_participants' => 2,
            'max_participants' => null,
            'requires_seeding' => false,
            'supports_byes' => false,
            'advancement_type' => 'points',
            'rounds_formula' => '3',
            'matches_formula' => 'n*3',
            'algorithm_class' => 'JudgedRoundsAlgorithm',
            'configuration' => json_encode([
                'rounds' => ['preliminary', 'semifinal', 'final'],
                'criteria_based' => true,
                'judge_scoring' => true,
                'audience_voting' => false
            ])
        ],
        [
            'name' => 'Talent Showcase',
            'code' => 'talent_showcase',
            'description' => 'Showcase format with multiple performance rounds for talent competitions and pageants',
            'sport_type_category' => 'judged',
            'min_participants' => 2,
            'max_participants' => 50,
            'requires_seeding' => false,
            'supports_byes' => false,
            'advancement_type' => 'points',
            'rounds_formula' => 'variable',
            'matches_formula' => 'n*rounds',
            'algorithm_class' => 'TalentShowcaseAlgorithm',
            'configuration' => json_encode([
                'showcase_rounds' => 3,
                'audience_voting' => true,
                'judge_scoring' => true,
                'performance_time_limits' => true
            ])
        ],
        [
            'name' => 'Performance Competition',
            'code' => 'performance_competition',
            'description' => 'Structured performance competition with individual evaluation for singing, dancing, and artistic performances',
            'sport_type_category' => 'judged',
            'min_participants' => 2,
            'max_participants' => null,
            'requires_seeding' => false,
            'supports_byes' => true,
            'advancement_type' => 'points',
            'rounds_formula' => 'ceil(log2(n))',
            'matches_formula' => 'n-1',
            'algorithm_class' => 'PerformanceCompetitionAlgorithm',
            'configuration' => json_encode([
                'performance_time_limits' => true,
                'technical_scoring' => true,
                'artistic_scoring' => true,
                'elimination_rounds' => true
            ])
        ],
        [
            'name' => 'Artistic Judging',
            'code' => 'artistic_judging',
            'description' => 'Artistic performance judged on multiple criteria including technique, creativity, and presentation',
            'sport_type_category' => 'judged',
            'min_participants' => 2,
            'max_participants' => null,
            'requires_seeding' => false,
            'supports_byes' => false,
            'advancement_type' => 'points',
            'rounds_formula' => '1',
            'matches_formula' => 'n',
            'algorithm_class' => 'ArtisticJudgingAlgorithm',
            'configuration' => json_encode([
                'criteria' => ['technique', 'creativity', 'presentation', 'overall_impact'],
                'judge_panels' => true,
                'weighted_scoring' => true,
                'single_round' => true
            ])
        ],
        [
            'name' => 'Individual Performance Evaluation',
            'code' => 'individual_performance',
            'description' => 'Individual performance evaluation system for solo competitions like singing contests',
            'sport_type_category' => 'judged',
            'min_participants' => 2,
            'max_participants' => null,
            'requires_seeding' => false,
            'supports_byes' => false,
            'advancement_type' => 'ranking',
            'rounds_formula' => '1',
            'matches_formula' => 'n',
            'algorithm_class' => 'IndividualPerformanceAlgorithm',
            'configuration' => json_encode([
                'individual_scoring' => true,
                'performance_order' => 'random',
                'score_aggregation' => 'average',
                'ranking_system' => true
            ])
        ]
    ];
    
    $added_count = 0;
    foreach ($judged_formats as $format) {
        // Check if format already exists
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats WHERE code = ?");
        $stmt->execute([$format['code']]);
        $exists = $stmt->fetch()['count'] > 0;
        
        if (!$exists) {
            $stmt = $conn->prepare("
                INSERT INTO tournament_formats 
                (name, code, description, sport_type_category, min_participants, max_participants, 
                 requires_seeding, supports_byes, advancement_type, rounds_formula, matches_formula, 
                 algorithm_class, configuration)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $format['name'],
                $format['code'],
                $format['description'],
                $format['sport_type_category'],
                $format['min_participants'],
                $format['max_participants'],
                $format['requires_seeding'],
                $format['supports_byes'],
                $format['advancement_type'],
                $format['rounds_formula'],
                $format['matches_formula'],
                $format['algorithm_class'],
                $format['configuration']
            ]);
            
            echo "<p style='color: green;'>✅ Added: <strong>" . htmlspecialchars($format['name']) . "</strong></p>";
            $added_count++;
        } else {
            echo "<p style='color: blue;'>ℹ️ Already exists: <strong>" . htmlspecialchars($format['name']) . "</strong></p>";
        }
    }
    
    echo "<p style='color: green;'><strong>✅ Added $added_count new judged sport formats</strong></p>";
    
    // =====================================================
    // STEP 3: Verify changes
    // =====================================================
    
    echo "<h3>Step 3: Verification</h3>";
    
    // Check minimum participants
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats WHERE min_participants > 2");
    $stmt->execute();
    $high_min_count = $stmt->fetch()['count'];
    
    if ($high_min_count == 0) {
        echo "<p style='color: green;'>✅ All tournament formats now require 2 or fewer participants</p>";
    } else {
        echo "<p style='color: red;'>❌ Still have $high_min_count formats with min_participants > 2</p>";
    }
    
    // Check judged formats
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count FROM tournament_formats 
        WHERE sport_type_category IN ('judged', 'performance')
    ");
    $stmt->execute();
    $judged_total = $stmt->fetch()['count'];
    
    echo "<p style='color: green;'>✅ Total judged sport formats available: $judged_total</p>";
    
    $conn->commit();
    echo "<h3 style='color: green;'>🎉 Tournament requirements successfully fixed!</h3>";
    
} catch (Exception $e) {
    $conn->rollback();
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
h3 { color: #555; margin-top: 30px; }
ul { background: #f8f9fa; padding: 15px; border-radius: 5px; }
p { margin: 10px 0; }
</style>
