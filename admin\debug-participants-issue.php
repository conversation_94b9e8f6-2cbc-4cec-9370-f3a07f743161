<?php
/**
 * Debug Participants Issue
 * Find out why participants aren't showing in the category view
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔍 Debug Participants Issue</h1>";

$event_id = 4;
$sport_id = 40;
$category_id = 14;

try {
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
    echo "<h2>Debugging Why Participants Don't Show in Category</h2>";
    echo "<p>Let's trace exactly what the manage-category.php query is doing.</p>";
    echo "</div>";
    
    echo "<h2>1. Get Category Information</h2>";
    
    // Get category info first
    $stmt = $conn->prepare("
        SELECT 
            sc.*,
            e.name as event_name,
            s.name as sport_name,
            es.id as event_sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ?
    ");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();
    
    if ($category) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Category Found</h3>";
        echo "<ul>";
        echo "<li><strong>Category ID:</strong> {$category['id']}</li>";
        echo "<li><strong>Category Name:</strong> {$category['category_name']}</li>";
        echo "<li><strong>Event Sport ID:</strong> {$category['event_sport_id']}</li>";
        echo "<li><strong>Event ID:</strong> {$category['event_id']}</li>";
        echo "<li><strong>Sport ID:</strong> {$category['sport_id']}</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<p>❌ Category not found!</p>";
        echo "</div>";
        exit;
    }
    
    echo "<h2>2. Check Event Department Registrations</h2>";
    
    // Check what's in event_department_registrations for this event
    $stmt = $conn->prepare("
        SELECT 
            edr.*,
            d.name as department_name,
            d.abbreviation
        FROM event_department_registrations edr
        JOIN departments d ON edr.department_id = d.id
        WHERE edr.event_id = ?
        ORDER BY d.name
    ");
    $stmt->execute([$event_id]);
    $event_registrations = $stmt->fetchAll();
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>Event Department Registrations (event_id = $event_id)</h3>";
    echo "<p><strong>Found:</strong> " . count($event_registrations) . " registrations</p>";
    
    if (!empty($event_registrations)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='padding: 8px;'>ID</th>";
        echo "<th style='padding: 8px;'>Department</th>";
        echo "<th style='padding: 8px;'>Status</th>";
        echo "<th style='padding: 8px;'>Registration Date</th>";
        echo "</tr>";
        
        foreach ($event_registrations as $reg) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$reg['id']}</td>";
            echo "<td style='padding: 8px;'>{$reg['department_name']} ({$reg['abbreviation']})</td>";
            echo "<td style='padding: 8px;'>{$reg['status']}</td>";
            echo "<td style='padding: 8px;'>{$reg['registration_date']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: #dc3545;'>❌ No registrations found in event_department_registrations table</p>";
    }
    echo "</div>";
    
    echo "<h2>3. Test the Exact Query from manage-category.php</h2>";
    
    // This is the exact query from manage-category.php
    $stmt = $conn->prepare("
        SELECT 
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            edr.id as registration_id,
            edr.registration_date,
            COUNT(DISTINCT m.id) as matches_played,
            COUNT(DISTINCT CASE WHEN m.winner_id = d.id THEN m.id END) as wins,
            COUNT(DISTINCT CASE WHEN m.loser_id = d.id THEN m.id END) as losses,
            COALESCE(SUM(CASE WHEN m.winner_id = d.id THEN 3 WHEN m.loser_id = d.id THEN 1 ELSE 0 END), 0) as points
        FROM departments d
        JOIN event_department_registrations edr ON d.id = edr.department_id
        LEFT JOIN matches m ON (m.team_a_id = d.id OR m.team_b_id = d.id) 
                            AND m.sport_category_id = ?
        WHERE edr.event_id = ?
        GROUP BY d.id, d.name, d.abbreviation, d.color_code, edr.id, edr.registration_date
        ORDER BY points DESC, wins DESC, d.name
    ");
    $stmt->execute([$category_id, $event_id]);
    $participants_query_result = $stmt->fetchAll();
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🔍 manage-category.php Query Result</h3>";
    echo "<p><strong>Query Parameters:</strong></p>";
    echo "<ul>";
    echo "<li><strong>category_id (sport_category_id):</strong> $category_id</li>";
    echo "<li><strong>event_id:</strong> $event_id</li>";
    echo "</ul>";
    echo "<p><strong>Result:</strong> " . count($participants_query_result) . " participants found</p>";
    
    if (!empty($participants_query_result)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #e9ecef;'>";
        echo "<th style='padding: 8px;'>Department</th>";
        echo "<th style='padding: 8px;'>Abbreviation</th>";
        echo "<th style='padding: 8px;'>Registration Date</th>";
        echo "<th style='padding: 8px;'>Matches</th>";
        echo "<th style='padding: 8px;'>Points</th>";
        echo "</tr>";
        
        foreach ($participants_query_result as $participant) {
            echo "<tr>";
            echo "<td style='padding: 8px;'>{$participant['name']}</td>";
            echo "<td style='padding: 8px;'>{$participant['abbreviation']}</td>";
            echo "<td style='padding: 8px;'>{$participant['registration_date']}</td>";
            echo "<td style='padding: 8px;'>{$participant['matches_played']}</td>";
            echo "<td style='padding: 8px;'>{$participant['points']}</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: #dc3545;'>❌ No participants returned by the query</p>";
    }
    echo "</div>";
    
    echo "<h2>4. Simplified Query Test</h2>";
    
    // Test a simplified version without the matches join
    $stmt = $conn->prepare("
        SELECT 
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            edr.registration_date,
            edr.status
        FROM departments d
        JOIN event_department_registrations edr ON d.id = edr.department_id
        WHERE edr.event_id = ?
        ORDER BY d.name
    ");
    $stmt->execute([$event_id]);
    $simple_participants = $stmt->fetchAll();
    
    echo "<div style='background: #e2e3e5; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>🧪 Simplified Query (No Matches Join)</h3>";
    echo "<p><strong>Result:</strong> " . count($simple_participants) . " participants found</p>";
    
    if (!empty($simple_participants)) {
        echo "<ul>";
        foreach ($simple_participants as $participant) {
            echo "<li><strong>{$participant['name']}</strong> ({$participant['abbreviation']}) - Status: {$participant['status']}</li>";
        }
        echo "</ul>";
    } else {
        echo "<p style='color: #dc3545;'>❌ Even simplified query returns no results</p>";
    }
    echo "</div>";
    
    echo "<h2>5. Check Database Tables</h2>";
    
    // Check if tables exist and have data
    $tables_to_check = [
        'departments' => 'SELECT COUNT(*) as count FROM departments',
        'event_department_registrations' => 'SELECT COUNT(*) as count FROM event_department_registrations',
        'events' => 'SELECT COUNT(*) as count FROM events WHERE id = ' . $event_id
    ];
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<h3>📊 Table Status</h3>";
    
    foreach ($tables_to_check as $table => $query) {
        try {
            $result = $conn->query($query)->fetch();
            $count = $result['count'];
            
            if ($count > 0) {
                echo "<p>✅ <strong>$table:</strong> $count records</p>";
            } else {
                echo "<p>❌ <strong>$table:</strong> No records</p>";
            }
        } catch (Exception $e) {
            echo "<p>❌ <strong>$table:</strong> Error - " . $e->getMessage() . "</p>";
        }
    }
    echo "</div>";
    
    echo "<h2>6. Diagnosis & Solution</h2>";
    
    $has_event_registrations = !empty($event_registrations);
    $query_returns_results = !empty($participants_query_result);
    $simple_query_works = !empty($simple_participants);
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;'>";
    echo "<h3>🔍 Diagnosis</h3>";
    
    if (!$has_event_registrations) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>❌ Root Issue:</strong> No departments registered for event ID $event_id</p>";
        echo "<p><strong>Solution:</strong> Need to register departments for this specific event</p>";
        echo "</div>";
    } elseif (!$simple_query_works) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>❌ Database Issue:</strong> Join between departments and event_department_registrations failing</p>";
        echo "<p><strong>Solution:</strong> Check foreign key relationships</p>";
        echo "</div>";
    } elseif (!$query_returns_results) {
        echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>❌ Query Issue:</strong> The matches join or grouping is causing problems</p>";
        echo "<p><strong>Solution:</strong> Simplify the query in manage-category.php</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>✅ Everything looks good:</strong> The issue might be in the display logic</p>";
        echo "</div>";
    }
    echo "</div>";
    
    echo "<h2>7. Quick Fix</h2>";
    
    if (!$has_event_registrations) {
        echo "<div style='text-align: center; margin: 20px 0;'>";
        echo "<a href='fix-category-display.php' target='_blank' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
        echo "🔧 Fix Department Registration";
        echo "</a>";
        echo "</div>";
    }
    
    echo "<div style='text-align: center; margin: 20px 0;'>";
    echo "<a href='manage-category.php?event_id=$event_id&sport_id=$sport_id&category_id=$category_id' target='_blank' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "🎯 Test Category Page";
    echo "</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Debug Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
