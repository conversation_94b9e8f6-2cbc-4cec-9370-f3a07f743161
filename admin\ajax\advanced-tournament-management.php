<?php
/**
 * Advanced Tournament Management AJAX Handler
 * Handles bracket generation, match editing, and live scoring
 */

require_once '../../config/database.php';
require_once '../includes/auth.php';
require_once '../../includes/advanced_tournament_engine.php';
require_once '../../includes/tournament_algorithms_advanced.php';

// Require admin authentication
requireAdmin();

header('Content-Type: application/json');

$database = new Database();
$conn = $database->getConnection();

$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'generate_tournament':
            $result = generateTournament($conn);
            break;
            
        case 'get_match':
            $result = getMatchData($conn);
            break;
            
        case 'save_match_result':
            $result = saveMatchResult($conn);
            break;
            
        case 'send_to_referee':
            $result = sendToReferee($conn);
            break;
            
        case 'get_bracket_data':
            $result = getBracketData($conn);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
    
    echo json_encode($result);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

function generateTournament($conn) {
    $event_sport_id = $_POST['event_sport_id'] ?? 0;
    $category_id = $_POST['category_id'] ?? 0;
    
    if (!$event_sport_id) {
        throw new Exception('Event Sport ID is required');
    }
    
    // Use advanced tournament engine
    $engine = new AdvancedTournamentEngine($conn);
    $result = $engine->generateTournament($event_sport_id, $category_id, [
        'seeding_method' => $_POST['seeding_method'] ?? 'random',
        'third_place_playoff' => isset($_POST['third_place_playoff']),
        'scoring_config' => [
            'points_win' => 3,
            'points_draw' => 1,
            'points_loss' => 0
        ]
    ]);
    
    if ($result['success']) {
        // Log admin activity
        if (function_exists('logAdminActivity')) {
            logAdminActivity('GENERATE_TOURNAMENT', 'tournament_structures', $result['tournament_id']);
        }
    }
    
    return $result;
}

function getMatchData($conn) {
    $match_id = $_POST['match_id'] ?? 0;
    
    if (!$match_id) {
        throw new Exception('Match ID is required');
    }
    
    $stmt = $conn->prepare("
        SELECT 
            m.*,
            t1.name as team1_name,
            t2.name as team2_name,
            tw.name as winner_name
        FROM matches m
        LEFT JOIN tournament_participants tp1 ON m.team1_id = tp1.id
        LEFT JOIN departments t1 ON tp1.department_id = t1.id
        LEFT JOIN tournament_participants tp2 ON m.team2_id = tp2.id
        LEFT JOIN departments t2 ON tp2.department_id = t2.id
        LEFT JOIN tournament_participants tpw ON m.winner_id = tpw.id
        LEFT JOIN departments tw ON tpw.department_id = tw.id
        WHERE m.id = ?
    ");
    $stmt->execute([$match_id]);
    $match = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$match) {
        throw new Exception('Match not found');
    }
    
    return [
        'success' => true,
        'match' => $match
    ];
}

function saveMatchResult($conn) {
    $match_id = $_POST['match_id'] ?? 0;
    $team1_score = $_POST['team1_score'] ?? null;
    $team2_score = $_POST['team2_score'] ?? null;
    $winner = $_POST['winner'] ?? '';
    $status = $_POST['status'] ?? 'pending';
    $send_to_referee = isset($_POST['send_to_referee']);
    
    if (!$match_id) {
        throw new Exception('Match ID is required');
    }
    
    $conn->beginTransaction();
    
    try {
        // Get match data
        $stmt = $conn->prepare("SELECT * FROM matches WHERE id = ?");
        $stmt->execute([$match_id]);
        $match = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$match) {
            throw new Exception('Match not found');
        }
        
        // Determine winner ID
        $winner_id = null;
        if ($winner === 'team1') {
            $winner_id = $match['team1_id'];
        } elseif ($winner === 'team2') {
            $winner_id = $match['team2_id'];
        }
        
        // Update match
        $stmt = $conn->prepare("
            UPDATE matches 
            SET team1_score = ?, team2_score = ?, winner_id = ?, status = ?, updated_at = NOW()
            WHERE id = ?
        ");
        $stmt->execute([$team1_score, $team2_score, $winner_id, $status, $match_id]);
        
        // Update tournament participants stats if match is completed
        if ($status === 'completed' && $winner_id) {
            updateParticipantStats($conn, $match, $winner_id, $team1_score, $team2_score);
        }
        
        // Send to referee if requested
        if ($send_to_referee) {
            createRefereeSession($conn, $match_id);
        }
        
        $conn->commit();
        
        // Log admin activity
        if (function_exists('logAdminActivity')) {
            logAdminActivity('UPDATE_MATCH_RESULT', 'matches', $match_id);
        }
        
        return [
            'success' => true,
            'message' => 'Match result saved successfully'
        ];
        
    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}

function updateParticipantStats($conn, $match, $winner_id, $team1_score, $team2_score) {
    // Update winner stats
    $stmt = $conn->prepare("
        UPDATE tournament_participants 
        SET wins = wins + 1, points = points + 3
        WHERE id = ?
    ");
    $stmt->execute([$winner_id]);
    
    // Update loser stats
    $loser_id = ($winner_id == $match['team1_id']) ? $match['team2_id'] : $match['team1_id'];
    if ($loser_id) {
        $stmt = $conn->prepare("
            UPDATE tournament_participants 
            SET losses = losses + 1
            WHERE id = ?
        ");
        $stmt->execute([$loser_id]);
    }
}

function createRefereeSession($conn, $match_id) {
    // Create a referee session for live scoring
    $session_token = bin2hex(random_bytes(16));
    
    // Check if referee_sessions table exists, if not create it
    try {
        $stmt = $conn->prepare("
            CREATE TABLE IF NOT EXISTS referee_sessions (
                id INT AUTO_INCREMENT PRIMARY KEY,
                match_id INT NOT NULL,
                session_token VARCHAR(255) NOT NULL UNIQUE,
                status ENUM('active', 'completed', 'expired') DEFAULT 'active',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                FOREIGN KEY (match_id) REFERENCES matches(id) ON DELETE CASCADE
            )
        ");
        $stmt->execute();
    } catch (Exception $e) {
        // Table might already exist
    }
    
    $stmt = $conn->prepare("
        INSERT INTO referee_sessions (match_id, session_token, status, created_at)
        VALUES (?, ?, 'active', NOW())
        ON DUPLICATE KEY UPDATE session_token = ?, status = 'active', updated_at = NOW()
    ");
    $stmt->execute([$match_id, $session_token, $session_token]);
    
    return $session_token;
}

function sendToReferee($conn) {
    $match_id = $_POST['match_id'] ?? 0;
    
    if (!$match_id) {
        throw new Exception('Match ID is required');
    }
    
    $session_token = createRefereeSession($conn, $match_id);
    
    return [
        'success' => true,
        'session_token' => $session_token,
        'referee_url' => "/SC_IMS/referee/live-scoring.php?token=" . $session_token
    ];
}

function getBracketData($conn) {
    $tournament_id = $_POST['tournament_id'] ?? 0;
    
    if (!$tournament_id) {
        throw new Exception('Tournament ID is required');
    }
    
    $stmt = $conn->prepare("
        SELECT bracket_data FROM tournament_structures WHERE id = ?
    ");
    $stmt->execute([$tournament_id]);
    $tournament = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$tournament) {
        throw new Exception('Tournament not found');
    }
    
    return [
        'success' => true,
        'bracket_data' => json_decode($tournament['bracket_data'], true)
    ];
}
?>
