<?php
/**
 * Test Complete Navigation Flow End-to-End
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🧪 Test Complete Navigation Flow End-to-End</h1>";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; margin-bottom: 30px;'>";
echo "<h2 style='margin-top: 0;'>🎯 Navigation Flow Test</h2>";
echo "<p>This test verifies the complete navigation flow from sport-categories.php to manage-category.php with the three-tab interface.</p>";
echo "</div>";

try {
    // Verify categories exist for event_id=4, sport_id=40
    $stmt = $conn->prepare("
        SELECT 
            sc.id as category_id,
            sc.category_name,
            sc.category_type,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE es.event_id = 4 AND es.sport_id = 40
        ORDER BY sc.category_name
    ");
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
    if (empty($categories)) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ No Categories Found</h3>";
        echo "<p>No categories exist for event_id=4, sport_id=40. Please run the category creation script first.</p>";
        echo "<a href='verify-categories-created.php' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create Categories</a>";
        echo "</div>";
        exit;
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
    echo "<h3>✅ Categories Found</h3>";
    echo "<p>Found " . count($categories) . " categories for testing navigation flow.</p>";
    echo "<p><strong>Event:</strong> {$categories[0]['event_name']}</p>";
    echo "<p><strong>Sport:</strong> {$categories[0]['sport_name']}</p>";
    echo "</div>";
    
    echo "<h2>📋 Step-by-Step Navigation Test</h2>";
    
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin-bottom: 20px;'>";
    echo "<h4>🔍 Testing Instructions:</h4>";
    echo "<ol>";
    echo "<li><strong>Step 1:</strong> Open the Sport Categories page</li>";
    echo "<li><strong>Step 2:</strong> Verify category names are clickable (blue color, 🏆 icon)</li>";
    echo "<li><strong>Step 3:</strong> Click on a category name</li>";
    echo "<li><strong>Step 4:</strong> Verify navigation to manage-category.php (NOT events.php)</li>";
    echo "<li><strong>Step 5:</strong> Verify three-tab interface appears (Overview, Fixtures, Standings)</li>";
    echo "<li><strong>Step 6:</strong> Test tab switching functionality</li>";
    echo "</ol>";
    echo "</div>";
    
    // Step 1: Sport Categories Page
    $categories_url = "sport-categories.php?event_id=4&sport_id=40";
    echo "<h3>Step 1: Sport Categories Page</h3>";
    echo "<div style='border: 1px solid #007bff; border-radius: 8px; padding: 15px; margin-bottom: 15px;'>";
    echo "<p><strong>Test URL:</strong> $categories_url</p>";
    echo "<p><strong>Expected:</strong> Page loads with clickable category names in table</p>";
    echo "<a href='$categories_url' target='_blank' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>";
    echo "🔗 Open Sport Categories Page";
    echo "</a>";
    echo "</div>";
    
    // Step 2-6: Individual Category Tests
    echo "<h3>Steps 2-6: Category Navigation Tests</h3>";
    
    foreach ($categories as $index => $category) {
        $manage_url = "manage-category.php?event_id=4&sport_id=40&category_id={$category['category_id']}";
        
        echo "<div style='border: 1px solid #28a745; border-radius: 8px; padding: 15px; margin-bottom: 15px;'>";
        echo "<h4>Test Category " . ($index + 1) . ": {$category['category_name']}</h4>";
        echo "<p><strong>Category Type:</strong> " . ucfirst($category['category_type']) . "</p>";
        echo "<p><strong>Expected Navigation URL:</strong> $manage_url</p>";
        
        echo "<div style='display: flex; gap: 10px; margin-top: 10px;'>";
        echo "<a href='$manage_url' target='_blank' style='background: #28a745; color: white; padding: 8px 15px; text-decoration: none; border-radius: 4px; font-size: 0.9em;'>";
        echo "🧪 Test Direct Navigation";
        echo "</a>";
        echo "<a href='$manage_url&debug=1' target='_blank' style='background: #ffc107; color: black; padding: 8px 15px; text-decoration: none; border-radius: 4px; font-size: 0.9em;'>";
        echo "🐛 Test with Debug";
        echo "</a>";
        echo "</div>";
        
        echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 4px; margin-top: 10px; font-size: 0.9em;'>";
        echo "<strong>Expected Results:</strong>";
        echo "<ul style='margin: 5px 0;'>";
        echo "<li>✅ Page loads without redirecting to events.php</li>";
        echo "<li>✅ Three tabs visible: Overview, Fixtures, Standings</li>";
        echo "<li>✅ Overview tab active by default</li>";
        echo "<li>✅ Category information displayed correctly</li>";
        echo "<li>✅ Tab switching works smoothly</li>";
        echo "</ul>";
        echo "</div>";
        echo "</div>";
    }
    
    echo "<h2>🔧 Troubleshooting Guide</h2>";
    
    echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px;'>";
    echo "<h4>If Navigation Still Fails:</h4>";
    
    echo "<h5>1. Check Browser Console for Errors</h5>";
    echo "<ul>";
    echo "<li>Press F12 to open Developer Tools</li>";
    echo "<li>Go to Console tab</li>";
    echo "<li>Look for JavaScript errors or failed requests</li>";
    echo "</ul>";
    
    echo "<h5>2. Verify URL Parameters</h5>";
    echo "<ul>";
    echo "<li>Check that event_id=4, sport_id=40, category_id=[valid_id] are in URL</li>";
    echo "<li>Ensure all parameters are numeric</li>";
    echo "<li>Use debug mode (?debug=1) to see detailed error information</li>";
    echo "</ul>";
    
    echo "<h5>3. Database Issues</h5>";
    echo "<ul>";
    echo "<li>Verify categories exist in sport_categories table</li>";
    echo "<li>Check event_sports relationship exists</li>";
    echo "<li>Ensure foreign key constraints are satisfied</li>";
    echo "</ul>";
    
    echo "<h5>4. Server Issues</h5>";
    echo "<ul>";
    echo "<li>Check Apache/PHP error logs</li>";
    echo "<li>Verify file permissions on manage-category.php</li>";
    echo "<li>Ensure database connection is working</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>📊 Test Results Summary</h2>";
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff;'>";
    echo "<h4>✅ What Should Work Now:</h4>";
    echo "<ul>";
    echo "<li><strong>Sport Categories Page:</strong> Displays " . count($categories) . " clickable category names</li>";
    echo "<li><strong>Category Links:</strong> Navigate directly to manage-category.php</li>";
    echo "<li><strong>No Redirects:</strong> Should NOT redirect to events.php</li>";
    echo "<li><strong>Three-Tab Interface:</strong> Overview, Fixtures, Standings tabs functional</li>";
    echo "<li><strong>Tab Switching:</strong> JavaScript tab switching works smoothly</li>";
    echo "<li><strong>AJAX Features:</strong> Match scoring, standings refresh, bracket generation</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>🎯 Final Test</h2>";
    
    echo "<div style='text-align: center; padding: 30px; background: #d4edda; border-radius: 8px; border-left: 4px solid #28a745;'>";
    echo "<h3>🚀 Complete Navigation Flow Test</h3>";
    echo "<p>Click the button below to start the complete navigation test:</p>";
    echo "<a href='$categories_url' target='_blank' style='background: #28a745; color: white; padding: 20px 40px; text-decoration: none; border-radius: 8px; font-size: 1.2em; display: inline-block; margin: 10px;'>";
    echo "🧪 Start Navigation Test";
    echo "</a>";
    echo "<p style='margin-top: 15px; font-size: 0.9em; color: #155724;'>";
    echo "<strong>Instructions:</strong> Click category names in the table and verify they navigate to manage-category.php with three-tab interface";
    echo "</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
