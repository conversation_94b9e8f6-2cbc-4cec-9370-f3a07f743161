<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>🔍 Tournament Matches Debug</h2>";

echo "<h3>1. Check Tournament Structures</h3>";
$stmt = $conn->prepare("
    SELECT ts.*, tf.name as format_name
    FROM tournament_structures ts
    LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
    WHERE ts.event_sport_id = 52
    ORDER BY ts.created_at DESC
    LIMIT 1
");
$stmt->execute();
$tournament = $stmt->fetch(PDO::FETCH_ASSOC);

if ($tournament) {
    echo "<p>✅ Found tournament: ID " . $tournament['id'] . " (" . $tournament['format_name'] . ")</p>";
    $tournament_id = $tournament['id'];
    
    echo "<h3>2. Check Matches</h3>";
    $stmt = $conn->prepare("
        SELECT
            m.*,
            d1.name as team1_name,
            d1.abbreviation as team1_abbr,
            d2.name as team2_name,
            d2.abbreviation as team2_abbr
        FROM matches m
        LEFT JOIN departments d1 ON m.team1_id = d1.id
        LEFT JOIN departments d2 ON m.team2_id = d2.id
        WHERE m.tournament_structure_id = ?
        ORDER BY m.round_number, m.match_number
    ");
    $stmt->execute([$tournament_id]);
    $matches = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($matches) {
        echo "<p>✅ Found " . count($matches) . " matches:</p>";
        
        $current_round = null;
        foreach ($matches as $match) {
            if ($current_round !== $match['round_number']) {
                $current_round = $match['round_number'];
                echo "<h4>Round " . $current_round . "</h4>";
            }
            
            echo "<div style='border: 1px solid #ddd; padding: 10px; margin: 5px 0;'>";
            echo "<p><strong>Match " . $match['match_number'] . ":</strong> ";
            echo ($match['team1_name'] ?? 'TBD') . " vs " . ($match['team2_name'] ?? 'TBD');
            echo " (Status: " . $match['status'] . ")</p>";
            
            if ($match['team1_score'] !== null || $match['team2_score'] !== null) {
                echo "<p><strong>Score:</strong> " . ($match['team1_score'] ?? 0) . " - " . ($match['team2_score'] ?? 0) . "</p>";
            }
            
            echo "<p><strong>Position:</strong> " . $match['bracket_position'] . "</p>";
            echo "</div>";
        }
    } else {
        echo "<p>❌ No matches found for tournament ID: $tournament_id</p>";
    }
    
    echo "<h3>3. Test BracketDisplay</h3>";
    try {
        require_once 'includes/bracket_display.php';
        $bracketDisplay = new BracketDisplay($conn, $tournament_id, 52);
        echo "<p>✅ BracketDisplay created successfully</p>";
        
        $html = $bracketDisplay->renderBracket();
        echo "<p>✅ Bracket rendered successfully</p>";
        echo "<div style='border: 2px solid #007bff; padding: 15px; margin: 10px 0; background: #f8f9fa;'>";
        echo "<h4>Bracket Display Output:</h4>";
        echo $html;
        echo "</div>";
        
    } catch (Exception $e) {
        echo "<p>❌ BracketDisplay error: " . $e->getMessage() . "</p>";
        echo "<pre>" . $e->getTraceAsString() . "</pre>";
    }
    
} else {
    echo "<p>❌ No tournament found for event_sport_id: 52</p>";
}

echo "<p><a href='manage-category.php?event_id=4&sport_id=37&category_id=16'>← Back to Category Management</a></p>";
?>
