<?php
/**
 * Debug Category Issues
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔍 Debug Category Issues</h1>";

// Get the specific category we're looking at
$event_id = 4;
$sport_id = 40;
$category_id = 14;

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
echo "<h2>Debugging Category: Men's Singles A</h2>";
echo "<p><strong>Event ID:</strong> $event_id | <strong>Sport ID:</strong> $sport_id | <strong>Category ID:</strong> $category_id</p>";
echo "</div>";

try {
    echo "<h2>1. Check Category Information</h2>";
    
    $stmt = $conn->prepare("
        SELECT 
            sc.*,
            e.name as event_name,
            s.name as sport_name,
            s.type as sport_type,
            s.bracket_format as sport_bracket_format,
            es.id as event_sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ?
    ");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();
    
    if ($category) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Category Found</h3>";
        echo "<ul>";
        echo "<li><strong>Category:</strong> {$category['category_name']}</li>";
        echo "<li><strong>Sport:</strong> {$category['sport_name']} (Type: {$category['sport_type']})</li>";
        echo "<li><strong>Event:</strong> {$category['event_name']}</li>";
        echo "<li><strong>Sport Bracket Format:</strong> {$category['sport_bracket_format']}</li>";
        echo "<li><strong>Event Sport ID:</strong> {$category['event_sport_id']}</li>";
        echo "</ul>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<p>❌ Category not found!</p>";
        echo "</div>";
        exit;
    }
    
    echo "<h2>2. Check Tournament Tables</h2>";
    
    // Check if tournament tables exist
    $tables_to_check = ['tournament_formats', 'tournament_structures'];
    
    foreach ($tables_to_check as $table) {
        try {
            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
            $exists = $stmt->rowCount() > 0;
            
            if ($exists) {
                echo "<div style='background: #d4edda; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "✅ Table '$table' exists";
                
                // Get count
                $count_stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
                $count = $count_stmt->fetch()['count'];
                echo " ($count records)";
                echo "</div>";
                
                // Show sample data
                if ($count > 0) {
                    $sample_stmt = $conn->query("SELECT * FROM $table LIMIT 3");
                    $samples = $sample_stmt->fetchAll();
                    
                    echo "<div style='background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 5px 0; font-family: monospace; font-size: 0.9rem;'>";
                    echo "<strong>Sample data from $table:</strong><br>";
                    foreach ($samples as $sample) {
                        echo "• " . json_encode($sample) . "<br>";
                    }
                    echo "</div>";
                }
            } else {
                echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
                echo "❌ Table '$table' does not exist";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 5px 0;'>";
            echo "⚠️ Error checking table '$table': " . $e->getMessage();
            echo "</div>";
        }
    }
    
    echo "<h2>3. Check Tournament Format for This Category</h2>";
    
    try {
        $stmt = $conn->prepare("
            SELECT
                tf.name as format_name,
                tf.description as format_description,
                tf.code as format_code,
                ts.status as tournament_status,
                ts.participant_count,
                ts.total_rounds,
                ts.current_round,
                ts.seeding_method
            FROM tournament_structures ts
            JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
            WHERE ts.event_sport_id = ?
            ORDER BY ts.created_at DESC
            LIMIT 1
        ");
        $stmt->execute([$category['event_sport_id']]);
        $tournament_format = $stmt->fetch();
        
        if ($tournament_format) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h3>✅ Tournament Format Found</h3>";
            echo "<ul>";
            echo "<li><strong>Format Name:</strong> {$tournament_format['format_name']}</li>";
            echo "<li><strong>Format Code:</strong> {$tournament_format['format_code']}</li>";
            echo "<li><strong>Description:</strong> {$tournament_format['format_description']}</li>";
            echo "<li><strong>Status:</strong> {$tournament_format['tournament_status']}</li>";
            echo "<li><strong>Participant Count:</strong> {$tournament_format['participant_count']}</li>";
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
            echo "<h3>⚠️ No Tournament Structure Found</h3>";
            echo "<p>Using sport default format: <strong>{$category['sport_bracket_format']}</strong></p>";
            echo "</div>";
        }
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ Error Fetching Tournament Format</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "<h2>4. Check Participants (Department Registration)</h2>";
    
    try {
        // Check event_department_registrations table
        $stmt = $conn->prepare("
            SELECT 
                d.id,
                d.name,
                d.abbreviation,
                d.color_code,
                edr.registration_date,
                edr.id as registration_id
            FROM departments d
            JOIN event_department_registrations edr ON d.id = edr.department_id
            WHERE edr.event_id = ?
            ORDER BY d.name
        ");
        $stmt->execute([$event_id]);
        $participants = $stmt->fetchAll();
        
        if (!empty($participants)) {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<h3>✅ Found " . count($participants) . " Registered Departments</h3>";
            echo "<ul>";
            foreach ($participants as $participant) {
                echo "<li><strong>{$participant['name']}</strong> ({$participant['abbreviation']}) - Registered: {$participant['registration_date']}</li>";
            }
            echo "</ul>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
            echo "<h3>❌ No Participants Found</h3>";
            echo "<p>No departments are registered for this event.</p>";
            echo "</div>";
            
            // Check if any departments exist at all
            $stmt = $conn->query("SELECT COUNT(*) as count FROM departments");
            $dept_count = $stmt->fetch()['count'];
            
            echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p><strong>Total departments in system:</strong> $dept_count</p>";
            echo "</div>";
            
            // Check if event_department_registrations table exists
            try {
                $stmt = $conn->query("SHOW TABLES LIKE 'event_department_registrations'");
                $table_exists = $stmt->rowCount() > 0;
                
                if ($table_exists) {
                    $stmt = $conn->query("SELECT COUNT(*) as count FROM event_department_registrations");
                    $reg_count = $stmt->fetch()['count'];
                    echo "<div style='background: #e2e3e5; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                    echo "<p><strong>Total registrations in system:</strong> $reg_count</p>";
                    echo "</div>";
                } else {
                    echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                    echo "<p>❌ event_department_registrations table does not exist!</p>";
                    echo "</div>";
                }
            } catch (Exception $e) {
                echo "<div style='background: #fff3cd; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
                echo "<p>⚠️ Error checking registrations: " . $e->getMessage() . "</p>";
                echo "</div>";
            }
        }
    } catch (Exception $e) {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ Error Fetching Participants</h3>";
        echo "<p>" . $e->getMessage() . "</p>";
        echo "</div>";
    }
    
    echo "<h2>5. Recommended Solutions</h2>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;'>";
    echo "<h3>🔧 Issues & Solutions</h3>";
    
    echo "<h4>Issue 1: Tournament Format</h4>";
    echo "<p><strong>Problem:</strong> Showing 'Single Elimination' instead of correct format for Badminton</p>";
    echo "<p><strong>Solution:</strong> Either create proper tournament structure or fix sport's default bracket_format</p>";
    
    echo "<h4>Issue 2: No Participants</h4>";
    echo "<p><strong>Problem:</strong> No departments registered for this event</p>";
    echo "<p><strong>Solution:</strong> Need to register departments for the event first</p>";
    
    echo "<h4>Next Steps:</h4>";
    echo "<ol>";
    echo "<li><strong>Fix Tournament Format:</strong> Update sport's bracket_format or create tournament structure</li>";
    echo "<li><strong>Register Departments:</strong> Add departments to event_department_registrations</li>";
    echo "<li><strong>Test Category Page:</strong> Verify both issues are resolved</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Debug Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
