<?php
/**
 * Fix Sport Categorization and Tournament Formats
 * Ensure sports are properly categorized and tournament formats exist
 */

require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>🔧 Fixing Sport Categorization and Tournament Formats</h2>";

try {
    // 1. First, ensure sport_types table has the correct categories
    echo "<h3>1. Checking Sport Types Table</h3>";
    
    $stmt = $conn->prepare("SELECT * FROM sport_types ORDER BY category");
    $stmt->execute();
    $sport_types = $stmt->fetchAll();
    
    if (empty($sport_types)) {
        echo "<p style='color: orange;'>⚠️ No sport types found. Creating basic sport types...</p>";
        
        $basic_sport_types = [
            ['Traditional Team Sports', 'Team-based competitive sports', 'traditional', '#28a745', 'fas fa-users'],
            ['Individual Sports', 'Individual competitive sports', 'traditional', '#17a2b8', 'fas fa-user'],
            ['Academic Games', 'Knowledge-based competitions and academic challenges', 'academic', '#ffc107', 'fas fa-brain'],
            ['Judged Competitions', 'Performance-based competitions with subjective judging', 'judged', '#e83e8c', 'fas fa-star'],
            ['Performance Arts', 'Artistic and performance-based competitions', 'performance', '#6f42c1', 'fas fa-music']
        ];
        
        foreach ($basic_sport_types as $type) {
            $stmt = $conn->prepare("
                INSERT IGNORE INTO sport_types (name, description, category, color_code, icon_class)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute($type);
        }
        echo "<p style='color: green;'>✅ Created basic sport types</p>";
    } else {
        echo "<p style='color: green;'>✅ Sport types exist (" . count($sport_types) . " found)</p>";
    }
    
    // 2. Update sports to have proper sport_type_id
    echo "<h3>2. Updating Sports Categorization</h3>";
    
    // Get sport type IDs
    $stmt = $conn->prepare("SELECT id, category FROM sport_types");
    $stmt->execute();
    $sport_type_map = [];
    foreach ($stmt->fetchAll() as $type) {
        $sport_type_map[$type['category']] = $type['id'];
    }
    
    // Define sport categorizations
    $sport_categorizations = [
        // Judged Sports
        'judged' => [
            'Mr. and Ms. Intramurals',
            'Dance',
            'Dance Competition',
            'Singing Contest',
            'Talent Show',
            'Beauty Pageant',
            'Fashion Show',
            'Vocal Solo',
            'Instrumental Solo'
        ],
        // Academic Sports
        'academic' => [
            'Banner Raising',
            'Chess',
            'Scrabble',
            'Quiz Bowl',
            'Debate',
            'Essay Writing',
            'Spelling Bee',
            'Math Competition',
            'Science Quiz'
        ],
        // Traditional Sports (already mostly categorized)
        'traditional' => [
            'Basketball',
            'Volleyball',
            'Football',
            'Soccer',
            'Tennis',
            'Badminton',
            'Table Tennis',
            'Swimming'
        ]
    ];
    
    $updated_count = 0;
    foreach ($sport_categorizations as $category => $sport_names) {
        if (!isset($sport_type_map[$category])) {
            echo "<p style='color: red;'>❌ Sport type '{$category}' not found in database</p>";
            continue;
        }
        
        $sport_type_id = $sport_type_map[$category];
        
        foreach ($sport_names as $sport_name) {
            $stmt = $conn->prepare("
                UPDATE sports 
                SET sport_type_id = ?, type = ?
                WHERE name LIKE ? AND (sport_type_id IS NULL OR sport_type_id != ?)
            ");
            $result = $stmt->execute([$sport_type_id, $category, "%{$sport_name}%", $sport_type_id]);
            
            if ($stmt->rowCount() > 0) {
                echo "<p style='color: green;'>✅ Updated '{$sport_name}' to category '{$category}'</p>";
                $updated_count++;
            }
        }
    }
    
    echo "<p><strong>Total sports updated: {$updated_count}</strong></p>";
    
    // 3. Ensure tournament formats exist with proper structure
    echo "<h3>3. Creating Tournament Formats</h3>";
    
    // Check table structure first
    $stmt = $conn->prepare("SHOW COLUMNS FROM tournament_formats LIKE 'sport_type%'");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    $has_category_field = false;
    $has_types_field = false;
    
    foreach ($columns as $col) {
        if ($col['Field'] === 'sport_type_category') {
            $has_category_field = true;
        }
        if ($col['Field'] === 'sport_types') {
            $has_types_field = true;
        }
    }
    
    echo "<p>Table structure: ";
    echo $has_category_field ? "✓ sport_type_category " : "❌ sport_type_category ";
    echo $has_types_field ? "✓ sport_types" : "❌ sport_types";
    echo "</p>";
    
    // Create tournament formats based on available fields
    $tournament_formats = [];
    
    if ($has_category_field) {
        // Use ENUM field
        $tournament_formats = [
            // Academic formats
            ['Swiss System', 'swiss_system', 'Pairing system for academic competitions', 'academic', 4, null],
            ['Knockout Rounds', 'knockout_rounds', 'Academic elimination tournament', 'academic', 4, null],
            ['Quiz Bowl Format', 'quiz_bowl', 'Round robin for quiz competitions', 'academic', 3, 12],
            ['Academic Round Robin', 'academic_round_robin', 'All-vs-all academic challenges', 'academic', 3, 16],
            
            // Judged formats
            ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria', 'judged', 3, null],
            ['Performance Competition', 'performance_competition', 'Structured performance competition', 'judged', 3, 50],
            ['Talent Showcase', 'talent_showcase', 'Performance rounds with audience voting', 'judged', 3, 50],
            ['Artistic Judging', 'artistic_judging', 'Technical and artistic components', 'judged', 3, 30]
        ];
        
        $format_count = 0;
        foreach ($tournament_formats as $format) {
            $stmt = $conn->prepare("SELECT id FROM tournament_formats WHERE code = ?");
            $stmt->execute([$format[1]]);
            
            if (!$stmt->fetch()) {
                $stmt = $conn->prepare("
                    INSERT INTO tournament_formats (name, code, description, sport_type_category, min_participants, max_participants)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute($format);
                echo "<p style='color: green;'>✅ Created format: {$format[0]}</p>";
                $format_count++;
            } else {
                echo "<p style='color: orange;'>⚠️ Format already exists: {$format[0]}</p>";
            }
        }
        
    } else if ($has_types_field) {
        // Use TEXT field
        $tournament_formats = [
            // Academic formats
            ['Swiss System', 'swiss_system', 'Pairing system for academic competitions', 'academic', 4, null],
            ['Knockout Rounds', 'knockout_rounds', 'Academic elimination tournament', 'academic', 4, null],
            ['Quiz Bowl Format', 'quiz_bowl', 'Round robin for quiz competitions', 'academic', 3, 12],
            ['Academic Round Robin', 'academic_round_robin', 'All-vs-all academic challenges', 'academic', 3, 16],
            
            // Judged formats
            ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria', 'judged,performance', 3, null],
            ['Performance Competition', 'performance_competition', 'Structured performance competition', 'judged,performance', 3, 50],
            ['Talent Showcase', 'talent_showcase', 'Performance rounds with audience voting', 'judged,performance', 3, 50],
            ['Artistic Judging', 'artistic_judging', 'Technical and artistic components', 'judged,performance', 3, 30]
        ];
        
        $format_count = 0;
        foreach ($tournament_formats as $format) {
            $stmt = $conn->prepare("SELECT id FROM tournament_formats WHERE code = ?");
            $stmt->execute([$format[1]]);
            
            if (!$stmt->fetch()) {
                $stmt = $conn->prepare("
                    INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants)
                    VALUES (?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute($format);
                echo "<p style='color: green;'>✅ Created format: {$format[0]}</p>";
                $format_count++;
            } else {
                echo "<p style='color: orange;'>⚠️ Format already exists: {$format[0]}</p>";
            }
        }
    }
    
    echo "<p><strong>Tournament formats created: {$format_count}</strong></p>";
    
    // 4. Verify the fix
    echo "<h3>4. Verification</h3>";
    
    // Check judged sports
    $stmt = $conn->prepare("
        SELECT s.name, st.category 
        FROM sports s 
        LEFT JOIN sport_types st ON s.sport_type_id = st.id 
        WHERE st.category = 'judged'
    ");
    $stmt->execute();
    $judged_sports = $stmt->fetchAll();
    
    echo "<h4>Judged Sports:</h4>";
    if (empty($judged_sports)) {
        echo "<p style='color: red;'>❌ No judged sports found</p>";
    } else {
        foreach ($judged_sports as $sport) {
            echo "<li>{$sport['name']}</li>";
        }
    }
    
    // Check judged formats
    $where_clause = $has_category_field ? "sport_type_category = 'judged'" : "sport_types LIKE '%judged%'";
    $stmt = $conn->prepare("SELECT name FROM tournament_formats WHERE {$where_clause}");
    $stmt->execute();
    $judged_formats = $stmt->fetchAll();
    
    echo "<h4>Judged Tournament Formats:</h4>";
    if (empty($judged_formats)) {
        echo "<p style='color: red;'>❌ No judged tournament formats found</p>";
    } else {
        foreach ($judged_formats as $format) {
            echo "<li>{$format['name']}</li>";
        }
    }
    
    echo "<div style='background: #d4edda; padding: 15px; border-left: 4px solid #28a745; margin: 20px 0;'>";
    echo "<h4>✅ Fix Complete!</h4>";
    echo "<p>Sports have been properly categorized and tournament formats have been created.</p>";
    echo "<p><strong>Next steps:</strong></p>";
    echo "<ul>";
    echo "<li>Test the 'Add Sport to Event' modal with judged sports</li>";
    echo "<li>Verify that tournament format dropdown now populates</li>";
    echo "<li>Test academic sports as well</li>";
    echo "</ul>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<p><a href='test-sport-type-detection.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Sport Type Detection</a></p>";
echo "<p><a href='manage-event.php?id=1' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test in Event Management</a></p>";
?>
