<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>🧪 Creating Test Tournament Data</h2>";

try {
    // 1. Create test sports
    echo "<h3>1. Creating Test Sports</h3>";
    $testSports = [
        ['name' => 'Dance Competition', 'sport_type_id' => 4, 'description' => 'Judged dance performance competition'],
        ['name' => 'Quiz Bowl', 'sport_type_id' => 3, 'description' => 'Academic knowledge competition']
    ];
    
    foreach ($testSports as $sport) {
        $stmt = $conn->prepare("
            INSERT IGNORE INTO sports (name, sport_type_id, description, status, created_at)
            VALUES (?, ?, ?, 'active', NOW())
        ");
        $stmt->execute([$sport['name'], $sport['sport_type_id'], $sport['description']]);
        echo "<p>✅ Created sport: " . $sport['name'] . "</p>";
    }
    
    // 2. Add sports to event and create tournaments
    echo "<h3>2. Creating Test Tournaments</h3>";
    $eventId = 4;
    
    $stmt = $conn->prepare("
        SELECT s.id, s.name, st.category 
        FROM sports s 
        JOIN sport_types st ON s.sport_type_id = st.id 
        WHERE s.name IN ('Dance Competition', 'Quiz Bowl')
    ");
    $stmt->execute();
    $sports = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($sports as $sport) {
        echo "<h4>Processing: " . $sport['name'] . " (" . $sport['category'] . ")</h4>";
        
        // Add sport to event
        $stmt = $conn->prepare("
            INSERT IGNORE INTO event_sports (event_id, sport_id, created_at)
            VALUES (?, ?, NOW())
        ");
        $stmt->execute([$eventId, $sport['id']]);
        
        // Get event_sport_id
        $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
        $stmt->execute([$eventId, $sport['id']]);
        $eventSportId = $stmt->fetchColumn();
        echo "<p>Event Sport ID: $eventSportId</p>";
        
        // Create category
        $categoryName = $sport['category'] === 'judged' ? 'Open Division' : 'General Category';
        $stmt = $conn->prepare("
            INSERT IGNORE INTO sport_categories (event_sport_id, name, created_at)
            VALUES (?, ?, NOW())
        ");
        $stmt->execute([$eventSportId, $categoryName]);
        
        // Get category_id
        $stmt = $conn->prepare("SELECT id FROM sport_categories WHERE event_sport_id = ? AND name = ?");
        $stmt->execute([$eventSportId, $categoryName]);
        $categoryId = $stmt->fetchColumn();
        echo "<p>Category ID: $categoryId</p>";
        
        // Get tournament format
        $formatCode = $sport['category'] === 'judged' ? 'judged_rounds' : 'swiss_system';
        $stmt = $conn->prepare("SELECT id FROM tournament_formats WHERE code = ?");
        $stmt->execute([$formatCode]);
        $formatId = $stmt->fetchColumn();
        echo "<p>Format ID: $formatId (Code: $formatCode)</p>";
        
        if ($formatId) {
            // Create tournament structure
            $stmt = $conn->prepare("
                INSERT IGNORE INTO tournament_structures 
                (event_sport_id, tournament_format_id, total_participants, status, created_at)
                VALUES (?, ?, 5, 'active', NOW())
            ");
            $stmt->execute([$eventSportId, $formatId]);
            
            // Get tournament_id
            $stmt = $conn->prepare("
                SELECT id FROM tournament_structures 
                WHERE event_sport_id = ? AND tournament_format_id = ?
                ORDER BY created_at DESC LIMIT 1
            ");
            $stmt->execute([$eventSportId, $formatId]);
            $tournamentId = $stmt->fetchColumn();
            echo "<p>Tournament ID: $tournamentId</p>";
            
            if ($tournamentId) {
                // Create matches based on category
                if ($sport['category'] === 'judged') {
                    // Judged competition - individual performances
                    $departments = ['CICS', 'CAS', 'CTE', 'COED', 'CON'];
                    $matchNumber = 1;
                    
                    for ($round = 1; $round <= 3; $round++) {
                        foreach ($departments as $dept) {
                            $stmt = $conn->prepare("SELECT id FROM departments WHERE abbreviation = ?");
                            $stmt->execute([$dept]);
                            $deptId = $stmt->fetchColumn();
                            
                            if ($deptId) {
                                $stmt = $conn->prepare("
                                    INSERT INTO matches 
                                    (tournament_structure_id, round_number, match_number, team1_id, 
                                     status, bracket_position, is_bye_match, created_at)
                                    VALUES (?, ?, ?, ?, 'pending', ?, 0, NOW())
                                ");
                                $stmt->execute([
                                    $tournamentId, $round, $matchNumber, $deptId, 
                                    "R{$round}P{$matchNumber}"
                                ]);
                                echo "<p>Created judged performance: Round $round, $dept</p>";
                                $matchNumber++;
                            }
                        }
                    }
                } else {
                    // Academic competition - head-to-head matches
                    $departments = ['CICS', 'CAS', 'CTE', 'COED'];
                    $matchNumber = 1;
                    
                    for ($round = 1; $round <= 3; $round++) {
                        $shuffled = $departments;
                        shuffle($shuffled);
                        
                        for ($i = 0; $i < count($shuffled); $i += 2) {
                            if (isset($shuffled[$i + 1])) {
                                $stmt = $conn->prepare("SELECT id FROM departments WHERE abbreviation = ?");
                                $stmt->execute([$shuffled[$i]]);
                                $team1Id = $stmt->fetchColumn();
                                
                                $stmt = $conn->prepare("SELECT id FROM departments WHERE abbreviation = ?");
                                $stmt->execute([$shuffled[$i + 1]]);
                                $team2Id = $stmt->fetchColumn();
                                
                                if ($team1Id && $team2Id) {
                                    $stmt = $conn->prepare("
                                        INSERT INTO matches 
                                        (tournament_structure_id, round_number, match_number, 
                                         team1_id, team2_id, status, bracket_position, is_bye_match, created_at)
                                        VALUES (?, ?, ?, ?, ?, 'pending', ?, 0, NOW())
                                    ");
                                    $stmt->execute([
                                        $tournamentId, $round, $matchNumber, 
                                        $team1Id, $team2Id, "R{$round}M{$matchNumber}"
                                    ]);
                                    echo "<p>Created academic match: Round $round, {$shuffled[$i]} vs {$shuffled[$i + 1]}</p>";
                                    $matchNumber++;
                                }
                            }
                        }
                    }
                }
                
                // Verify matches created
                $stmt = $conn->prepare("SELECT COUNT(*) FROM matches WHERE tournament_structure_id = ?");
                $stmt->execute([$tournamentId]);
                $matchCount = $stmt->fetchColumn();
                echo "<p>✅ Total matches created: $matchCount</p>";
            }
        }
        echo "<hr>";
    }
    
    echo "<h3>3. Summary</h3>";
    $stmt = $conn->prepare("
        SELECT 
            s.name as sport_name,
            st.category,
            tf.name as format_name,
            COUNT(m.id) as match_count,
            es.id as event_sport_id,
            sc.id as category_id
        FROM tournament_structures ts
        JOIN event_sports es ON ts.event_sport_id = es.id
        JOIN sports s ON es.sport_id = s.id
        JOIN sport_types st ON s.sport_type_id = st.id
        JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
        LEFT JOIN sport_categories sc ON es.id = sc.event_sport_id
        LEFT JOIN matches m ON ts.id = m.tournament_structure_id
        WHERE s.name IN ('Dance Competition', 'Quiz Bowl')
        GROUP BY s.name, st.category, tf.name, es.id, sc.id
    ");
    $stmt->execute();
    $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Sport</th><th>Category</th><th>Format</th><th>Matches</th><th>View Link</th></tr>";
    foreach ($results as $result) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($result['sport_name']) . "</td>";
        echo "<td>" . ucfirst($result['category']) . "</td>";
        echo "<td>" . htmlspecialchars($result['format_name']) . "</td>";
        echo "<td>" . $result['match_count'] . "</td>";
        echo "<td>";
        if ($result['category_id']) {
            echo "<a href='manage-category.php?event_id=4&sport_id=" . $result['event_sport_id'] . "&category_id=" . $result['category_id'] . "'>View Bracket</a>";
        } else {
            echo "No category";
        }
        echo "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    echo "<p>🎉 <strong>Test tournament data created successfully!</strong></p>";
    echo "<p><a href='manage-category.php?event_id=4&sport_id=37&category_id=16'>← Back to Category Management</a></p>";
    
} catch (Exception $e) {
    echo "<p>❌ Error: " . $e->getMessage() . "</p>";
}
?>
