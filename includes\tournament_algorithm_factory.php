<?php
/**
 * Robust Tournament Algorithm Factory
 * SC_IMS Sports Competition and Event Management System
 * 
 * Dynamically loads tournament algorithms based on database algorithm_class field
 * with comprehensive error handling and fallback mechanisms
 */

require_once 'tournament_algorithms.php';
require_once 'tournament_formula_calculator.php';

class TournamentAlgorithmFactory {
    private $conn;
    private $loadedAlgorithms = [];
    private $calculator;
    
    public function __construct($conn) {
        $this->conn = $conn;
        $this->calculator = new TournamentFormulaCalculator($conn);
    }
    
    /**
     * Create tournament algorithm instance based on database configuration
     */
    public function createAlgorithm($format, $tournamentId = null, $config = []) {
        try {
            $algorithmClass = $format['algorithm_class'] ?? 'SingleEliminationAlgorithm';
            
            // Check if algorithm is already loaded
            if (isset($this->loadedAlgorithms[$algorithmClass])) {
                return $this->configureAlgorithm(
                    clone $this->loadedAlgorithms[$algorithmClass], 
                    $format, 
                    $tournamentId, 
                    $config
                );
            }
            
            // Validate algorithm class exists
            if (!class_exists($algorithmClass)) {
                throw new Exception("Algorithm class '{$algorithmClass}' not found");
            }
            
            // Create algorithm instance
            $algorithm = new $algorithmClass($this->conn, $tournamentId);
            
            // Cache the algorithm
            $this->loadedAlgorithms[$algorithmClass] = $algorithm;
            
            // Configure and return
            return $this->configureAlgorithm($algorithm, $format, $tournamentId, $config);
            
        } catch (Exception $e) {
            error_log("Tournament algorithm creation error: " . $e->getMessage());
            return $this->createFallbackAlgorithm($format, $tournamentId, $config);
        }
    }
    
    /**
     * Configure algorithm with database-driven parameters
     */
    private function configureAlgorithm($algorithm, $format, $tournamentId, $config) {
        // Merge database configuration with provided config
        $dbConfig = json_decode($format['configuration'] ?? '{}', true);
        $mergedConfig = array_merge($dbConfig, $config);
        
        // Set database-driven calculation methods
        $algorithm->setFormulaCalculator($this->calculator);
        $algorithm->setFormat($format);
        
        // Configure algorithm with merged settings
        if (method_exists($algorithm, 'configure')) {
            $algorithm->configure($mergedConfig);
        }
        
        return $algorithm;
    }
    
    /**
     * Create fallback algorithm when primary algorithm fails
     */
    private function createFallbackAlgorithm($format, $tournamentId, $config) {
        try {
            error_log("Creating fallback SingleEliminationAlgorithm");
            
            $algorithm = new SingleEliminationAlgorithm($this->conn, $tournamentId);
            
            // Create fallback format configuration
            $fallbackFormat = array_merge($format, [
                'algorithm_class' => 'SingleEliminationAlgorithm',
                'rounds_formula' => 'ceil(log2(n))',
                'matches_formula' => 'n-1',
                'configuration' => '{"bracket_seeding": true, "fallback": true}'
            ]);
            
            return $this->configureAlgorithm($algorithm, $fallbackFormat, $tournamentId, $config);
            
        } catch (Exception $e) {
            error_log("Fallback algorithm creation failed: " . $e->getMessage());
            throw new Exception("Unable to create tournament algorithm: " . $e->getMessage());
        }
    }
    
    /**
     * Get available algorithm classes
     */
    public function getAvailableAlgorithms() {
        return [
            'SingleEliminationAlgorithm' => [
                'name' => 'Single Elimination',
                'description' => 'Traditional knockout tournament',
                'supports_byes' => true,
                'advancement_type' => 'elimination'
            ],
            'DoubleEliminationAlgorithm' => [
                'name' => 'Double Elimination',
                'description' => 'Two-bracket elimination system',
                'supports_byes' => true,
                'advancement_type' => 'elimination'
            ],
            'RoundRobinAlgorithm' => [
                'name' => 'Round Robin',
                'description' => 'All participants play each other',
                'supports_byes' => false,
                'advancement_type' => 'points'
            ],
            'SwissSystemAlgorithm' => [
                'name' => 'Swiss System',
                'description' => 'Pairing based on standings',
                'supports_byes' => true,
                'advancement_type' => 'points'
            ],
            'MultiStageAlgorithm' => [
                'name' => 'Multi-Stage',
                'description' => 'Group stage + knockout',
                'supports_byes' => true,
                'advancement_type' => 'hybrid'
            ]
        ];
    }
    
    /**
     * Validate algorithm compatibility with format
     */
    public function validateAlgorithmCompatibility($algorithmClass, $format, $participantCount) {
        $validationRules = [
            'SingleEliminationAlgorithm' => [
                'min_participants' => 2,
                'max_participants' => null,
                'supports_byes' => true
            ],
            'DoubleEliminationAlgorithm' => [
                'min_participants' => 3,
                'max_participants' => null,
                'supports_byes' => true
            ],
            'RoundRobinAlgorithm' => [
                'min_participants' => 3,
                'max_participants' => 16,
                'supports_byes' => false
            ],
            'SwissSystemAlgorithm' => [
                'min_participants' => 4,
                'max_participants' => null,
                'supports_byes' => true
            ],
            'MultiStageAlgorithm' => [
                'min_participants' => 8,
                'max_participants' => null,
                'supports_byes' => true
            ]
        ];
        
        $rules = $validationRules[$algorithmClass] ?? null;
        if (!$rules) {
            return false;
        }
        
        // Check participant count constraints
        if ($participantCount < $rules['min_participants']) {
            return false;
        }
        
        if ($rules['max_participants'] && $participantCount > $rules['max_participants']) {
            return false;
        }
        
        // Check bye support if needed
        if (!$rules['supports_byes'] && $this->needsByes($participantCount, $algorithmClass)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Check if algorithm needs bye handling for given participant count
     */
    private function needsByes($participantCount, $algorithmClass) {
        switch ($algorithmClass) {
            case 'SingleEliminationAlgorithm':
            case 'DoubleEliminationAlgorithm':
                // Need byes if not a power of 2
                return ($participantCount & ($participantCount - 1)) !== 0;
                
            case 'SwissSystemAlgorithm':
                // Need byes if odd number of participants
                return $participantCount % 2 !== 0;
                
            default:
                return false;
        }
    }
    
    /**
     * Auto-select best algorithm for given parameters
     */
    public function autoSelectAlgorithm($format, $participantCount) {
        $preferredAlgorithm = $format['algorithm_class'] ?? 'SingleEliminationAlgorithm';
        
        // Check if preferred algorithm is compatible
        if ($this->validateAlgorithmCompatibility($preferredAlgorithm, $format, $participantCount)) {
            return $preferredAlgorithm;
        }
        
        // Try alternative algorithms based on format type
        $alternatives = $this->getAlternativeAlgorithms($format, $participantCount);
        
        foreach ($alternatives as $algorithm) {
            if ($this->validateAlgorithmCompatibility($algorithm, $format, $participantCount)) {
                return $algorithm;
            }
        }
        
        // Final fallback
        return 'SingleEliminationAlgorithm';
    }
    
    /**
     * Get alternative algorithms for a format
     */
    private function getAlternativeAlgorithms($format, $participantCount) {
        $alternatives = [];
        
        switch ($format['advancement_type']) {
            case 'elimination':
                $alternatives = ['SingleEliminationAlgorithm', 'DoubleEliminationAlgorithm'];
                break;
                
            case 'points':
                if ($participantCount <= 16) {
                    $alternatives = ['RoundRobinAlgorithm', 'SwissSystemAlgorithm'];
                } else {
                    $alternatives = ['SwissSystemAlgorithm', 'SingleEliminationAlgorithm'];
                }
                break;
                
            case 'hybrid':
                $alternatives = ['MultiStageAlgorithm', 'SingleEliminationAlgorithm'];
                break;
                
            default:
                $alternatives = ['SingleEliminationAlgorithm'];
        }
        
        return $alternatives;
    }
    
    /**
     * Create algorithm with comprehensive error handling
     */
    public function createRobustAlgorithm($format, $participantCount, $tournamentId = null, $config = []) {
        // Step 1: Auto-select best algorithm
        $algorithmClass = $this->autoSelectAlgorithm($format, $participantCount);
        
        // Step 2: Update format with selected algorithm
        $format['algorithm_class'] = $algorithmClass;
        
        // Step 3: Create algorithm with error handling
        try {
            return $this->createAlgorithm($format, $tournamentId, $config);
        } catch (Exception $e) {
            error_log("Robust algorithm creation failed: " . $e->getMessage());
            
            // Final attempt with minimal configuration
            $minimalFormat = [
                'algorithm_class' => 'SingleEliminationAlgorithm',
                'rounds_formula' => 'ceil(log2(n))',
                'matches_formula' => 'n-1',
                'configuration' => '{"bracket_seeding": true, "emergency_fallback": true}'
            ];
            
            return $this->createAlgorithm($minimalFormat, $tournamentId, $config);
        }
    }
}
?>
