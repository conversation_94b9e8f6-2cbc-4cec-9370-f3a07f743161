<?php
/**
 * Final Verification of Category Display Fixes
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>✅ Final Verification - Category Display Fixes</h1>";

$event_id = 4;
$sport_id = 40;
$category_id = 14;

try {
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
    echo "<h2>Final Verification for Men's Singles A Category</h2>";
    echo "<p>Confirming that both tournament format and participants are now displaying correctly.</p>";
    echo "</div>";
    
    echo "<h2>1. Tournament Format Verification</h2>";
    
    // Get event sport configuration (what manage-category.php uses)
    $stmt = $conn->prepare("
        SELECT 
            es.tournament_format_id,
            es.bracket_type,
            tf.name as format_name,
            tf.description as format_description,
            tf.code as format_code
        FROM event_sports es
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport_config = $stmt->fetch();
    
    if ($event_sport_config) {
        $display_format = 'Not configured';
        $format_source = 'None';
        
        if ($event_sport_config['format_name']) {
            $display_format = $event_sport_config['format_name'];
            $format_source = 'Tournament Format (tournament_formats table)';
        } elseif ($event_sport_config['bracket_type']) {
            $display_format = ucwords(str_replace('_', ' ', $event_sport_config['bracket_type']));
            $format_source = 'Bracket Type (event_sports.bracket_type)';
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Tournament Format Configuration</h3>";
        echo "<ul>";
        echo "<li><strong>Display Format:</strong> $display_format</li>";
        echo "<li><strong>Source:</strong> $format_source</li>";
        echo "<li><strong>Tournament Format ID:</strong> " . ($event_sport_config['tournament_format_id'] ?? 'Not set') . "</li>";
        echo "<li><strong>Bracket Type:</strong> " . ($event_sport_config['bracket_type'] ?? 'Not set') . "</li>";
        echo "</ul>";
        echo "</div>";
        
        if ($display_format !== 'Not configured') {
            echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>✅ <strong>SUCCESS!</strong> Tournament format is now properly configured and will display as: <strong>$display_format</strong></p>";
            echo "</div>";
        } else {
            echo "<div style='background: #f8d7da; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>❌ <strong>Issue:</strong> Tournament format is still not configured</p>";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ No Event Sport Configuration Found</h3>";
        echo "<p>The sport is not properly added to this event.</p>";
        echo "</div>";
    }
    
    echo "<h2>2. Participants Verification</h2>";
    
    // Get participants (what manage-category.php uses)
    $stmt = $conn->prepare("
        SELECT 
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            edr.registration_date,
            edr.status as registration_status,
            edr.total_participants
        FROM departments d
        JOIN event_department_registrations edr ON d.id = edr.department_id
        WHERE edr.event_id = ? AND edr.status IN ('approved', 'pending')
        ORDER BY d.name
    ");
    $stmt->execute([$event_id]);
    $participants = $stmt->fetchAll();
    
    $participant_count = count($participants);
    
    if ($participant_count > 0) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<h3>✅ Participants Found</h3>";
        echo "<p><strong>Total Registered Departments:</strong> $participant_count</p>";
        echo "</div>";
        
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<h4>📋 Registered Departments:</h4>";
        echo "<div style='display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px;'>";
        
        foreach ($participants as $participant) {
            $color = $participant['color_code'] ?? '#6c757d';
            echo "<div style='background: white; padding: 8px; border-radius: 5px; border-left: 3px solid $color; font-size: 0.9rem;'>";
            echo "<strong>{$participant['name']}</strong><br>";
            echo "<small>{$participant['abbreviation']} • {$participant['registration_status']}</small>";
            echo "</div>";
        }
        
        echo "</div>";
        echo "</div>";
        
        echo "<div style='background: #d1ecf1; padding: 10px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>SUCCESS!</strong> The category now has $participant_count registered participants.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<h3>❌ No Participants Found</h3>";
        echo "<p>No departments are registered for this event.</p>";
        echo "</div>";
    }
    
    echo "<h2>3. Category Page Preview</h2>";
    
    // Simulate what the category page will show
    $stmt = $conn->prepare("
        SELECT 
            sc.*,
            e.name as event_name,
            s.name as sport_name,
            es.id as event_sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ?
    ");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();
    
    if ($category && $event_sport_config) {
        echo "<div style='background: #e2e3e5; padding: 20px; border-radius: 8px;'>";
        echo "<h3>📊 Category Page Preview</h3>";
        echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 30px;'>";
        
        echo "<div>";
        echo "<h4>Category Information</h4>";
        echo "<div style='background: white; padding: 15px; border-radius: 5px;'>";
        echo "<p><strong>Category Name:</strong> {$category['category_name']}</p>";
        echo "<p><strong>Category Type:</strong> " . strtoupper($category['category_type']) . "</p>";
        echo "<p><strong>Sport:</strong> {$category['sport_name']}</p>";
        echo "<p><strong>Event:</strong> {$category['event_name']}</p>";
        
        $display_format = 'Not configured';
        if ($event_sport_config['format_name']) {
            $display_format = $event_sport_config['format_name'];
        } elseif ($event_sport_config['bracket_type']) {
            $display_format = ucwords(str_replace('_', ' ', $event_sport_config['bracket_type']));
        }
        
        echo "<p><strong>Tournament Format:</strong> <span style='color: #007bff; font-weight: 600;'>$display_format</span></p>";
        echo "</div>";
        echo "</div>";
        
        echo "<div>";
        echo "<h4>Statistics</h4>";
        echo "<div style='background: white; padding: 15px; border-radius: 5px;'>";
        echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 15px;'>";
        
        echo "<div style='text-align: center; padding: 10px; background: #f8f9fa; border-radius: 5px;'>";
        echo "<div style='font-size: 1.5rem; font-weight: bold; color: #007bff;'>$participant_count</div>";
        echo "<div style='font-size: 0.9rem; color: #6c757d;'>Participants</div>";
        echo "</div>";
        
        echo "<div style='text-align: center; padding: 10px; background: #f8f9fa; border-radius: 5px;'>";
        echo "<div style='font-size: 1.5rem; font-weight: bold; color: #007bff;'>0</div>";
        echo "<div style='font-size: 0.9rem; color: #6c757d;'>Total Matches</div>";
        echo "</div>";
        
        echo "<div style='text-align: center; padding: 10px; background: #f8f9fa; border-radius: 5px;'>";
        echo "<div style='font-size: 1.5rem; font-weight: bold; color: #007bff;'>0</div>";
        echo "<div style='font-size: 0.9rem; color: #6c757d;'>Completed</div>";
        echo "</div>";
        
        echo "<div style='text-align: center; padding: 10px; background: #f8f9fa; border-radius: 5px;'>";
        echo "<div style='font-size: 1.5rem; font-weight: bold; color: #007bff;'>0</div>";
        echo "<div style='font-size: 0.9rem; color: #6c757d;'>Pending</div>";
        echo "</div>";
        
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
        echo "</div>";
        echo "</div>";
    }
    
    echo "<h2>4. Overall Status</h2>";
    
    $format_fixed = ($event_sport_config && ($event_sport_config['format_name'] || $event_sport_config['bracket_type']));
    $participants_fixed = ($participant_count > 0);
    
    if ($format_fixed && $participants_fixed) {
        echo "<div style='background: #d4edda; padding: 25px; border-radius: 8px; border-left: 4px solid #28a745; text-align: center;'>";
        echo "<h3 style='color: #155724; margin-top: 0;'>🎉 All Issues Successfully Fixed!</h3>";
        echo "<div style='display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;'>";
        
        echo "<div style='background: rgba(21, 87, 36, 0.1); padding: 15px; border-radius: 5px;'>";
        echo "<h4 style='color: #155724;'>✅ Tournament Format</h4>";
        $format_display = $event_sport_config['format_name'] ?? ucwords(str_replace('_', ' ', $event_sport_config['bracket_type']));
        echo "<p>Now showing: <strong>$format_display</strong></p>";
        echo "<p>Configured at event level!</p>";
        echo "</div>";
        
        echo "<div style='background: rgba(21, 87, 36, 0.1); padding: 15px; border-radius: 5px;'>";
        echo "<h4 style='color: #155724;'>✅ Participants</h4>";
        echo "<p>Now showing: <strong>$participant_count Departments</strong></p>";
        echo "<p>Registered at event level!</p>";
        echo "</div>";
        
        echo "</div>";
        echo "<p style='font-size: 1.1rem; font-weight: 600; color: #155724;'>";
        echo "The category page now displays the correct tournament format and participants as configured at the event level!";
        echo "</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border-left: 4px solid #dc3545;'>";
        echo "<h3 style='color: #721c24;'>⚠️ Some Issues Remain</h3>";
        echo "<ul>";
        if (!$format_fixed) {
            echo "<li>❌ Tournament format still needs fixing</li>";
        }
        if (!$participants_fixed) {
            echo "<li>❌ Participants still need to be added</li>";
        }
        echo "</ul>";
        echo "</div>";
    }
    
    echo "<h2>5. Test the Category Page</h2>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='manage-category.php?event_id=$event_id&sport_id=$sport_id&category_id=$category_id' target='_blank' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "🎯 View Fixed Category Page";
    echo "</a>";
    
    echo "<a href='debug-event-configuration.php' target='_blank' style='background: #6c757d; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "🔍 Debug Configuration";
    echo "</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Verification Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
