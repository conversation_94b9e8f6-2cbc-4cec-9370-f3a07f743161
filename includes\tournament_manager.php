<?php
/**
 * Tournament Manager Class
 * SC_IMS Sports Competition and Event Management System
 * 
 * Central management class for all tournament operations
 */

require_once 'tournament_algorithms.php';

class TournamentManager {
    private $conn;
    private $algorithms;
    
    public function __construct($conn) {
        $this->conn = $conn;
        $this->algorithms = [];
    }
    
    /**
     * Get available tournament formats for a specific sport type
     */
    public function getAvailableFormats($sportTypeCategory) {
        $sql = "SELECT * FROM tournament_formats 
                WHERE sport_type_category = ? OR sport_type_category = 'all'
                ORDER BY name";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$sportTypeCategory]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get sport type category by sport ID
     */
    public function getSportTypeCategory($sportId) {
        $sql = "SELECT st.category 
                FROM sports s 
                JOIN sport_types st ON s.sport_type_id = st.id 
                WHERE s.id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$sportId]);
        $result = $stmt->fetch();
        return $result ? $result['category'] : 'individual';
    }
    
    /**
     * Create a new tournament structure with database-driven format selection
     */
    public function createTournament($eventSportId, $formatId, $name, $config = []) {
        try {
            $this->conn->beginTransaction();

            // Get participants first to determine count
            $participants = $this->getEventSportParticipants($eventSportId);
            $participantCount = count($participants);

            if ($participantCount < 2) {
                throw new Exception('At least 2 participants required for tournament');
            }

            // Use database-driven format selection
            require_once 'tournament_format_selector.php';
            require_once 'tournament_algorithm_factory.php';

            $formatSelector = new TournamentFormatSelector($this->conn);
            $algorithmFactory = new TournamentAlgorithmFactory($this->conn);

            // Get event and sport IDs from event_sport
            $eventSportInfo = $this->getEventSportInfo($eventSportId);
            if (!$eventSportInfo) {
                throw new Exception('Invalid event sport ID');
            }

            // Select optimal format based on database rules
            $selectedFormat = $formatSelector->selectFormat(
                $eventSportInfo['event_id'],
                $eventSportInfo['sport_id'],
                $participantCount
            );

            if (!$selectedFormat) {
                throw new Exception('Unable to determine suitable tournament format');
            }
            
            // Create tournament structure with database-driven data
            $sql = "INSERT INTO tournament_structures
                    (event_sport_id, tournament_format_id, name, participant_count, seeding_method, scoring_config, total_rounds)
                    VALUES (?, ?, ?, ?, ?, ?, ?)";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([
                $eventSportId,
                $selectedFormat['id'],
                $name,
                $participantCount,
                $config['seeding_method'] ?? 'random',
                json_encode($config['scoring_config'] ?? []),
                $selectedFormat['calculated_rounds']
            ]);

            $tournamentId = $this->conn->lastInsertId();

            // Create robust algorithm using factory
            $algorithm = $algorithmFactory->createRobustAlgorithm(
                $selectedFormat,
                $participantCount,
                $tournamentId,
                $config
            );

            // Generate bracket with comprehensive error handling
            $bracket = $this->generateBracketSafely($algorithm, $participants, $config);

            // Update tournament with bracket data
            $sql = "UPDATE tournament_structures
                    SET bracket_data = ?, status = 'in_progress'
                    WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([
                json_encode($bracket),
                $tournamentId
            ]);
            
            // Create tournament participants
            $this->createTournamentParticipants($tournamentId, $participants, $bracket);
            
            // Create tournament rounds
            $this->createTournamentRounds($tournamentId, $bracket);
            
            // Generate initial matches
            $this->generateInitialMatches($tournamentId, $bracket, $algorithm);
            
            $this->conn->commit();
            return $tournamentId;
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            throw $e;
        }
    }
    
    /**
     * Get event sport information
     */
    private function getEventSportInfo($eventSportId) {
        $sql = "SELECT event_id, sport_id FROM event_sports WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$eventSportId]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    /**
     * Generate bracket with comprehensive error handling
     */
    private function generateBracketSafely($algorithm, $participants, $config) {
        try {
            return $algorithm->generateBracket($participants, $config);
        } catch (Exception $e) {
            error_log("Bracket generation error: " . $e->getMessage());

            // Fallback to simple single elimination bracket
            return [
                'format' => 'single_elimination',
                'participants' => $participants,
                'rounds' => ceil(log(count($participants), 2)),
                'structure' => $this->generateFallbackBracket($participants)
            ];
        }
    }

    /**
     * Generate fallback bracket structure
     */
    private function generateFallbackBracket($participants) {
        $rounds = ceil(log(count($participants), 2));
        $structure = [];

        for ($round = 1; $round <= $rounds; $round++) {
            $structure["round_$round"] = [
                'round_number' => $round,
                'matches' => []
            ];
        }

        return $structure;
    }

    /**
     * Get tournament format by ID
     */
    private function getTournamentFormat($formatId) {
        $sql = "SELECT * FROM tournament_formats WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$formatId]);
        return $stmt->fetch();
    }
    
    /**
     * Get participants for an event sport using simplified approach
     */
    private function getEventSportParticipants($eventSportId) {
        // Use simplified direct department approach that works reliably
        try {
            $sql = "SELECT
                        d.id,
                        d.name as team_name,
                        d.id as department_id,
                        d.name as department_name,
                        d.color_code,
                        'active' as status,
                        NOW() as registration_date
                    FROM departments d
                    WHERE d.status = 'active'
                    ORDER BY d.name
                    LIMIT 10";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute();
            $participants = $stmt->fetchAll();

            if (!empty($participants)) {
                return $participants;
            }
        } catch (Exception $e) {
            // Tables don't exist, continue with fallback
        }

        // Fallback to old registration system
        $sql = "SELECT r.*, d.name as department_name, d.color_code
                FROM registrations r
                JOIN departments d ON r.department_id = d.id
                WHERE r.event_sport_id = ? AND r.status IN ('confirmed', 'approved')
                ORDER BY r.registration_date";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$eventSportId]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get algorithm instance
     */
    private function getAlgorithm($algorithmClass, $tournamentId, $config) {
        if (!isset($this->algorithms[$algorithmClass])) {
            if (!class_exists($algorithmClass)) {
                throw new Exception("Algorithm class {$algorithmClass} not found");
            }
            $this->algorithms[$algorithmClass] = new $algorithmClass($this->conn, $tournamentId, $config);
        }
        return $this->algorithms[$algorithmClass];
    }
    
    /**
     * Create tournament participants - simplified direct department linking
     */
    private function createTournamentParticipants($tournamentId, $participants, $bracket) {
        foreach ($bracket['participants'] as $index => $participant) {
            if (isset($participant['is_bye']) && $participant['is_bye']) {
                continue; // Skip bye participants
            }

            $departmentId = $participant['department_id'] ?? $participant['id'];
            $teamName = $participant['team_name'] ?? $participant['department_name'];

            $sql = "INSERT INTO tournament_participants
                    (tournament_structure_id, department_id, team_name, seed_number, current_status)
                    VALUES (?, ?, ?, ?, 'active')";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([
                $tournamentId,
                $departmentId,
                $teamName,
                $participant['seed'] ?? $index + 1
            ]);
        }
    }
    
    /**
     * Create tournament rounds
     */
    private function createTournamentRounds($tournamentId, $bracket) {
        foreach ($bracket['structure'] as $roundKey => $roundData) {
            $sql = "INSERT INTO tournament_rounds
                    (tournament_structure_id, round_number, round_name)
                    VALUES (?, ?, ?)";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([
                $tournamentId,
                $roundData['round_number'],
                $roundData['round_name']
            ]);
        }
    }
    
    /**
     * Generate initial matches
     */
    private function generateInitialMatches($tournamentId, $bracket, $algorithm) {
        // Get first round
        $firstRound = reset($bracket['structure']);

        // Get tournament structure info including event_sport_id
        $sql = "SELECT event_sport_id FROM tournament_structures WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$tournamentId]);
        $eventSportId = $stmt->fetchColumn();

        // Get round ID
        $sql = "SELECT id FROM tournament_rounds
                WHERE tournament_structure_id = ? AND round_number = 1";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$tournamentId]);
        $roundId = $stmt->fetchColumn();

        // Create matches
        foreach ($firstRound['matches'] as $matchData) {
            $this->createMatch($tournamentId, $roundId, $eventSportId, $matchData);
        }
    }
    
    /**
     * Create a match
     */
    private function createMatch($tournamentId, $roundId, $eventSportId, $matchData) {
        $team1Id = null;
        $team2Id = null;
        $isByeMatch = false;

        // Handle participant IDs
        if (!isset($matchData['participant1']['is_bye'])) {
            $team1Id = $matchData['participant1']['id'];
        }
        if (!isset($matchData['participant2']['is_bye'])) {
            $team2Id = $matchData['participant2']['id'];
        }

        if ($team1Id === null || $team2Id === null) {
            $isByeMatch = true;
        }

        $sql = "INSERT INTO matches
                (event_sport_id, tournament_structure_id, tournament_round_id, team1_id, team2_id,
                 round_number, bracket_position, is_bye_match, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([
            $eventSportId,
            $tournamentId,
            $roundId,
            $team1Id,
            $team2Id,
            $matchData['round_number'],
            $matchData['position'],
            $isByeMatch,
            $isByeMatch ? 'completed' : 'scheduled'
        ]);

        // If it's a bye match, automatically advance the non-bye participant
        if ($isByeMatch) {
            $winnerId = $team1Id ?? $team2Id;
            if ($winnerId) {
                $sql = "UPDATE matches SET winner_id = ?, status = 'completed' WHERE id = ?";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$winnerId, $this->conn->lastInsertId()]);
            }
        }
    }
    
    /**
     * Advance tournament to next round
     */
    public function advanceToNextRound($tournamentId) {
        try {
            $this->conn->beginTransaction();
            
            // Get tournament details
            $tournament = $this->getTournamentStructure($tournamentId);
            if (!$tournament) {
                throw new Exception('Tournament not found');
            }
            
            // Check if current round is complete
            if (!$this->isRoundComplete($tournamentId, $tournament['current_round'])) {
                throw new Exception('Current round is not complete');
            }
            
            // Get algorithm
            $format = $this->getTournamentFormat($tournament['tournament_format_id']);
            $algorithm = $this->getAlgorithm($format['algorithm_class'], $tournamentId, []);
            
            // Get current round results
            $roundResults = $this->getRoundResults($tournamentId, $tournament['current_round']);
            
            // Advance participants
            $advancingParticipants = $algorithm->advanceParticipants($roundResults);
            
            // Check if tournament is complete
            if ($algorithm->isComplete($tournament)) {
                $sql = "UPDATE tournaments SET status = 'completed' WHERE id = ?";
                $stmt = $this->conn->prepare($sql);
                $stmt->execute([$tournamentId]);
                
                $this->conn->commit();
                return ['status' => 'completed', 'message' => 'Tournament completed'];
            }
            
            // Generate next round
            $nextRoundNumber = $tournament['current_round'] + 1;
            $nextRoundMatches = $algorithm->generateNextRound(
                ['round_number' => $tournament['current_round']], 
                $advancingParticipants
            );
            
            // Get next round ID
            $sql = "SELECT id FROM tournament_rounds
                    WHERE tournament_structure_id = ? AND round_number = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$tournamentId, $nextRoundNumber]);
            $nextRoundId = $stmt->fetchColumn();

            // Create next round matches
            foreach ($nextRoundMatches as $matchData) {
                $this->createMatch($tournamentId, $nextRoundId, $tournament['event_sport_id'], $matchData);
            }
            
            // Update tournament current round
            $sql = "UPDATE tournaments SET current_round = ? WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->execute([$nextRoundNumber, $tournamentId]);
            
            $this->conn->commit();
            return ['status' => 'advanced', 'message' => 'Advanced to round ' . $nextRoundNumber];
            
        } catch (Exception $e) {
            $this->conn->rollBack();
            throw $e;
        }
    }
    
    /**
     * Get tournament structure
     */
    private function getTournamentStructure($tournamentId) {
        $sql = "SELECT * FROM tournaments WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$tournamentId]);
        return $stmt->fetch();
    }
    
    /**
     * Check if round is complete
     */
    private function isRoundComplete($tournamentId, $roundNumber) {
        $sql = "SELECT COUNT(*) as total, 
                       SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed
                FROM matches 
                WHERE tournament_structure_id = ? AND round_number = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$tournamentId, $roundNumber]);
        $result = $stmt->fetch();
        
        return $result['total'] > 0 && $result['total'] == $result['completed'];
    }
    
    /**
     * Get round results
     */
    private function getRoundResults($tournamentId, $roundNumber) {
        $sql = "SELECT * FROM matches 
                WHERE tournament_structure_id = ? AND round_number = ? AND status = 'completed'";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$tournamentId, $roundNumber]);
        return $stmt->fetchAll();
    }
    
    /**
     * Get tournament standings
     */
    public function getTournamentStandings($tournamentId) {
        $tournament = $this->getTournamentStructure($tournamentId);
        if (!$tournament) {
            return [];
        }
        
        $format = $this->getTournamentFormat($tournament['tournament_format_id']);
        $algorithm = $this->getAlgorithm($format['algorithm_class'], $tournamentId, []);
        
        return $algorithm->getRankings($tournament);
    }
    
    /**
     * Get tournament bracket visualization data
     */
    public function getBracketVisualization($tournamentId) {
        $tournament = $this->getTournamentStructure($tournamentId);
        if (!$tournament) {
            return null;
        }
        
        $bracketData = json_decode($tournament['bracket_data'], true);
        
        // Get current match results
        $sql = "SELECT * FROM matches WHERE tournament_structure_id = ? ORDER BY round_number, bracket_position";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute([$tournamentId]);
        $matches = $stmt->fetchAll();
        
        // Merge match results with bracket structure
        return $this->mergeBracketWithResults($bracketData, $matches);
    }
    
    /**
     * Merge bracket structure with match results
     */
    private function mergeBracketWithResults($bracketData, $matches) {
        $matchesByPosition = [];
        foreach ($matches as $match) {
            $matchesByPosition[$match['bracket_position']] = $match;
        }
        
        // Update bracket structure with match results
        foreach ($bracketData['structure'] as &$round) {
            foreach ($round['matches'] as &$match) {
                if (isset($matchesByPosition[$match['position']])) {
                    $match['result'] = $matchesByPosition[$match['position']];
                }
            }
        }
        
        return $bracketData;
    }
}
