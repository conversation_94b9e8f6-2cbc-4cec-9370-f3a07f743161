<?php
require_once 'includes/config.php';
require_once 'includes/functions.php';

// Check if user is admin
requireAdmin();

// Sample bracket data for demonstration
$sample_matches = [
    [
        'id' => 'demo_1',
        'round_number' => 1,
        'match_number' => 1,
        'team1_name' => 'Computer Science',
        'team1_department' => 'CS Department',
        'team1_score' => 15,
        'team2_name' => 'Information Technology',
        'team2_department' => 'IT Department',
        'team2_score' => 12,
        'status' => 'completed',
        'winner_id' => 1,
        'team1_id' => 1,
        'team2_id' => 2,
        'format_name' => 'Single Elimination',
        'scheduled_time' => '2024-01-15 14:00:00'
    ],
    [
        'id' => 'demo_2',
        'round_number' => 1,
        'match_number' => 2,
        'team1_name' => 'Business Administration',
        'team1_department' => 'BA Department',
        'team1_score' => 8,
        'team2_name' => 'Engineering',
        'team2_department' => 'ENG Department',
        'team2_score' => 10,
        'status' => 'completed',
        'winner_id' => 4,
        'team1_id' => 3,
        'team2_id' => 4,
        'format_name' => 'Single Elimination',
        'scheduled_time' => '2024-01-15 14:30:00'
    ],
    [
        'id' => 'demo_3',
        'round_number' => 2,
        'match_number' => 1,
        'team1_name' => 'Computer Science',
        'team1_department' => 'CS Department',
        'team1_score' => null,
        'team2_name' => 'Engineering',
        'team2_department' => 'ENG Department',
        'team2_score' => null,
        'status' => 'pending',
        'winner_id' => null,
        'team1_id' => 1,
        'team2_id' => 4,
        'format_name' => 'Single Elimination',
        'scheduled_time' => '2024-01-15 16:00:00'
    ]
];

// Group matches by rounds
$rounds = [];
foreach ($sample_matches as $match) {
    $rounds[$match['round_number']][] = $match;
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Bracket Interface Demo - <?php echo APP_NAME; ?></title>
    
    <?php include 'includes/admin-styles.php'; ?>
    <link rel="stylesheet" href="assets/css/bracket-styles.css">
    <link rel="stylesheet" href="assets/css/bracket-modals.css">
    
    <style>
        .demo-header {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .demo-title {
            font-size: 2.5em;
            margin: 0 0 10px 0;
            font-weight: 700;
        }
        
        .demo-subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin: 0;
        }
        
        .demo-features {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            border-color: #007bff;
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
        }
        
        .feature-icon {
            font-size: 2.5em;
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .feature-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #343a40;
            margin-bottom: 10px;
        }
        
        .feature-description {
            color: #6c757d;
            font-size: 0.9em;
            line-height: 1.5;
        }
        
        .demo-actions {
            text-align: center;
            margin: 30px 0;
        }
        
        .demo-btn {
            background: #28a745;
            color: white;
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-weight: 600;
            cursor: pointer;
            margin: 0 10px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        
        .demo-btn:hover {
            background: #1e7e34;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
        }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>
    
    <div class="admin-main">
        <!-- Demo Header -->
        <div class="demo-header">
            <h1 class="demo-title">
                <i class="fas fa-trophy"></i>
                Enhanced Tournament Bracket Interface
            </h1>
            <p class="demo-subtitle">
                Unique SC_IMS Design with Modal-Based Editing & Live Scoring
            </p>
        </div>
        
        <!-- Features Overview -->
        <div class="demo-features">
            <h2 style="text-align: center; color: #343a40; margin-bottom: 20px;">
                <i class="fas fa-star"></i>
                Key Features & Enhancements
            </h2>
            
            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-edit"></i>
                    </div>
                    <div class="feature-title">Modal-Based Editing</div>
                    <div class="feature-description">
                        Click edit buttons on match cards to open comprehensive modal dialogs with team selection, score input, and winner determination.
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="feature-title">Unique Visual Design</div>
                    <div class="feature-description">
                        Custom SC_IMS branding with distinctive color schemes, animations, and professional styling that sets us apart.
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-whistle"></i>
                    </div>
                    <div class="feature-title">Referee Integration</div>
                    <div class="feature-description">
                        Send matches directly to referees with secure access links for live scoring on mobile-optimized interfaces.
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="feature-title">Real-Time Updates</div>
                    <div class="feature-description">
                        Live synchronization across admin, referee, and public interfaces with automatic bracket progression.
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-mobile-alt"></i>
                    </div>
                    <div class="feature-title">Responsive Design</div>
                    <div class="feature-description">
                        Fully responsive interface that works seamlessly on desktop, tablet, and mobile devices for all user types.
                    </div>
                </div>
                
                <div class="feature-card">
                    <div class="feature-icon">
                        <i class="fas fa-database"></i>
                    </div>
                    <div class="feature-title">Advanced Integration</div>
                    <div class="feature-description">
                        Deep integration with unified registration system, tournament formats, and comprehensive bracket generation.
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Demo Actions -->
        <div class="demo-actions">
            <a href="#" class="demo-btn" onclick="openMatchEditModal('demo_3')">
                <i class="fas fa-edit"></i>
                Try Modal Editing
            </a>
            <a href="referee/match.php?token=demo_token" class="demo-btn" target="_blank">
                <i class="fas fa-whistle"></i>
                View Referee Interface
            </a>
        </div>
        
        <!-- Sample Bracket Display -->
        <div class="sc-bracket-container">
            <div class="sc-bracket-notice">
                <i class="fas fa-info-circle"></i>
                Interactive Demo - Click edit buttons on match cards to experience the modal interface
            </div>
            
            <?php foreach ($rounds as $round_number => $matches): ?>
            <div class="sc-round">
                <div class="sc-round-header">
                    <?php if ($round_number == 1): ?>
                        Quarterfinals
                    <?php elseif ($round_number == 2): ?>
                        Semifinals
                    <?php else: ?>
                        Round <?php echo $round_number; ?>
                    <?php endif; ?>
                </div>
                
                <div class="sc-round-matches">
                    <?php foreach ($matches as $match): ?>
                    <div class="sc-match-card <?php echo 'sc-match-' . $match['status']; ?>" data-match-id="<?php echo $match['id']; ?>">
                        <!-- Match Header -->
                        <div class="sc-match-header">
                            <div class="sc-match-info">
                                <span class="sc-match-number">R<?php echo $match['round_number']; ?>M<?php echo $match['match_number']; ?></span>
                                <span class="sc-match-format"><?php echo $match['format_name']; ?></span>
                            </div>
                            <div class="sc-match-actions">
                                <button class="sc-btn sc-btn-edit" onclick="openMatchEditModal('<?php echo $match['id']; ?>')" title="Edit Match">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="sc-btn sc-btn-referee" onclick="sendToReferee('<?php echo $match['id']; ?>')" title="Send to Referee">
                                    <i class="fas fa-whistle"></i>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Match Content -->
                        <div class="sc-match-content">
                            <!-- Team 1 -->
                            <div class="sc-team-container team1 <?php echo ($match['winner_id'] == $match['team1_id']) ? 'winner' : ''; ?>">
                                <div class="sc-team-avatar">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="sc-team-details">
                                    <div class="sc-team-name"><?php echo htmlspecialchars($match['team1_name']); ?></div>
                                    <div class="sc-team-department"><?php echo htmlspecialchars($match['team1_department']); ?></div>
                                </div>
                                <div class="sc-team-score">
                                    <span class="sc-score-value"><?php echo $match['team1_score'] ?? '-'; ?></span>
                                </div>
                            </div>
                            
                            <!-- VS Separator -->
                            <div class="sc-match-vs">
                                <div class="sc-vs-circle">
                                    <span>VS</span>
                                </div>
                            </div>
                            
                            <!-- Team 2 -->
                            <div class="sc-team-container team2 <?php echo ($match['winner_id'] == $match['team2_id']) ? 'winner' : ''; ?>">
                                <div class="sc-team-avatar">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div class="sc-team-details">
                                    <div class="sc-team-name"><?php echo htmlspecialchars($match['team2_name']); ?></div>
                                    <div class="sc-team-department"><?php echo htmlspecialchars($match['team2_department']); ?></div>
                                </div>
                                <div class="sc-team-score">
                                    <span class="sc-score-value"><?php echo $match['team2_score'] ?? '-'; ?></span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Match Footer -->
                        <div class="sc-match-footer">
                            <div class="sc-match-status">
                                <span class="sc-status-badge sc-status-<?php echo $match['status']; ?>">
                                    <i class="fas fa-<?php echo $match['status'] === 'completed' ? 'check-circle' : ($match['status'] === 'in_progress' ? 'play-circle' : 'clock'); ?>"></i>
                                    <?php echo ucfirst(str_replace('_', ' ', $match['status'])); ?>
                                </span>
                            </div>
                            <div class="sc-match-time">
                                <i class="fas fa-clock"></i>
                                <?php echo date('H:i', strtotime($match['scheduled_time'])); ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
    
    <!-- Modal System -->
    <script>
        // Set global variables for modal system
        window.currentEventId = 1;
        window.currentSportId = 1;
    </script>
    <script src="assets/js/bracket-modals.js"></script>
    
    <script>
        // Demo-specific functions
        function sendToReferee(matchId) {
            scBracketModals.showSuccessMessage('Match sent to referee successfully! Check the referee interface link above.');
        }
        
        // Override save function for demo
        scBracketModals.saveMatch = function() {
            this.showSuccessMessage('Demo: Match would be saved in real implementation!');
            this.closeModal('match-edit-modal');
        };
        
        // Override participant loading for demo
        scBracketModals.participants = [
            {id: 1, name: 'Computer Science', department: 'CS Department'},
            {id: 2, name: 'Information Technology', department: 'IT Department'},
            {id: 3, name: 'Business Administration', department: 'BA Department'},
            {id: 4, name: 'Engineering', department: 'ENG Department'},
            {id: 5, name: 'Liberal Arts', department: 'LA Department'},
            {id: 6, name: 'Health Sciences', department: 'HS Department'}
        ];
    </script>
</body>
</html>
