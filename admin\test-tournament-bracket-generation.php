<?php
/**
 * Test Tournament Bracket Generation
 * SC_IMS Sports Competition and Event Management System
 * 
 * This script tests the updated tournament bracket generation system:
 * 1. Tests 2-participant minimum requirement
 * 2. Tests judged sport tournament formats
 * 3. Verifies bracket generation for different sport types
 */

require_once '../config/database.php';
require_once '../includes/advanced_tournament_engine.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Tournament Bracket Generation Test</h2>";

try {
    // =====================================================
    // TEST 1: Verify minimum participant requirements
    // =====================================================
    
    echo "<h3>Test 1: Minimum Participant Requirements</h3>";
    
    $stmt = $conn->prepare("SELECT id, name, min_participants FROM tournament_formats WHERE min_participants > 2");
    $stmt->execute();
    $high_min_formats = $stmt->fetchAll();
    
    if (empty($high_min_formats)) {
        echo "<p style='color: green;'>✅ All tournament formats require 2 or fewer participants</p>";
    } else {
        echo "<p style='color: red;'>❌ Found " . count($high_min_formats) . " formats with min_participants > 2:</p>";
        foreach ($high_min_formats as $format) {
            echo "<li>" . htmlspecialchars($format['name']) . " - Min: " . $format['min_participants'] . "</li>";
        }
    }
    
    // =====================================================
    // TEST 2: Test judged sport formats availability
    // =====================================================
    
    echo "<h3>Test 2: Judged Sport Formats</h3>";
    
    $stmt = $conn->prepare("
        SELECT id, name, code, sport_type_category, min_participants, algorithm_class
        FROM tournament_formats 
        WHERE sport_type_category IN ('judged', 'performance')
        ORDER BY name
    ");
    $stmt->execute();
    $judged_formats = $stmt->fetchAll();
    
    if (!empty($judged_formats)) {
        echo "<p style='color: green;'>✅ Found " . count($judged_formats) . " judged sport formats:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>Name</th><th>Code</th><th>Category</th><th>Min Participants</th><th>Algorithm</th></tr>";
        
        foreach ($judged_formats as $format) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($format['name']) . "</td>";
            echo "<td>" . htmlspecialchars($format['code']) . "</td>";
            echo "<td>" . htmlspecialchars($format['sport_type_category']) . "</td>";
            echo "<td>" . $format['min_participants'] . "</td>";
            echo "<td>" . htmlspecialchars($format['algorithm_class']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p style='color: red;'>❌ No judged sport formats found!</p>";
    }
    
    // =====================================================
    // TEST 3: Test bracket generation with 2 participants
    // =====================================================
    
    echo "<h3>Test 3: Bracket Generation with 2 Participants</h3>";
    
    // Get a test event sport
    $stmt = $conn->prepare("
        SELECT es.*, s.name as sport_name, tf.name as format_name, tf.code as format_code
        FROM event_sports es
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.id = (SELECT event_sport_id FROM sport_categories WHERE id = 24)
        LIMIT 1
    ");
    $stmt->execute();
    $test_event_sport = $stmt->fetch();
    
    if ($test_event_sport) {
        echo "<p><strong>Test Event Sport:</strong> " . htmlspecialchars($test_event_sport['sport_name']) . "</p>";
        echo "<p><strong>Current Format:</strong> " . htmlspecialchars($test_event_sport['format_name'] ?? 'Not Set') . "</p>";
        
        // Get participants for this event
        $stmt = $conn->prepare("
            SELECT COUNT(*) as participant_count
            FROM event_department_registrations edr
            WHERE edr.event_id = ? AND edr.status IN ('pending', 'approved')
        ");
        $stmt->execute([$test_event_sport['event_id']]);
        $participant_count = $stmt->fetch()['participant_count'];
        
        echo "<p><strong>Current Participants:</strong> $participant_count</p>";
        
        if ($participant_count >= 2) {
            echo "<p style='color: green;'>✅ Sufficient participants for tournament generation</p>";
            
            // Test tournament generation
            if ($test_event_sport['tournament_format_id']) {
                echo "<h4>Testing Tournament Generation...</h4>";
                
                try {
                    $engine = new AdvancedTournamentEngine($conn);
                    $result = $engine->generateTournament($test_event_sport['id'], 24, [
                        'seeding_method' => 'random',
                        'test_mode' => true
                    ]);
                    
                    if ($result['success']) {
                        echo "<p style='color: green;'>✅ Tournament generation successful!</p>";
                        echo "<p><strong>Format:</strong> " . htmlspecialchars($result['format']) . "</p>";
                        echo "<p><strong>Participants:</strong> " . $result['participants_count'] . "</p>";
                        echo "<p><strong>Matches Generated:</strong> " . ($result['matches_count'] ?? 'N/A') . "</p>";
                    } else {
                        echo "<p style='color: red;'>❌ Tournament generation failed: " . htmlspecialchars($result['message']) . "</p>";
                    }
                } catch (Exception $e) {
                    echo "<p style='color: red;'>❌ Tournament generation error: " . htmlspecialchars($e->getMessage()) . "</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠ No tournament format assigned to test event sport</p>";
            }
        } else {
            echo "<p style='color: orange;'>⚠ Only $participant_count participants (need at least 2 for testing)</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Test event sport not found</p>";
    }
    
    // =====================================================
    // TEST 4: Test judged sport bracket generation
    // =====================================================
    
    echo "<h3>Test 4: Judged Sport Bracket Generation</h3>";
    
    // Test each judged format with mock data
    foreach ($judged_formats as $format) {
        echo "<h4>Testing: " . htmlspecialchars($format['name']) . "</h4>";
        
        // Create mock participants
        $mock_participants = [
            ['id' => 1, 'name' => 'Department A', 'department_id' => 1],
            ['id' => 2, 'name' => 'Department B', 'department_id' => 2],
            ['id' => 3, 'name' => 'Department C', 'department_id' => 3]
        ];
        
        try {
            // Load algorithm class
            require_once '../includes/tournament_algorithms.php';
            $algorithm_class = $format['algorithm_class'];
            
            if (class_exists($algorithm_class)) {
                $algorithm = new $algorithm_class($conn, null, []);
                $bracket_result = $algorithm->generateBracket($mock_participants);
                
                if ($bracket_result['success']) {
                    echo "<p style='color: green;'>✅ " . htmlspecialchars($format['name']) . " bracket generated successfully</p>";
                    echo "<p><strong>Format:</strong> " . htmlspecialchars($bracket_result['format']) . "</p>";
                    echo "<p><strong>Rounds:</strong> " . $bracket_result['total_rounds'] . "</p>";
                    echo "<p><strong>Scoring Type:</strong> " . htmlspecialchars($bracket_result['scoring_type']) . "</p>";
                } else {
                    echo "<p style='color: red;'>❌ " . htmlspecialchars($format['name']) . " bracket generation failed</p>";
                }
            } else {
                echo "<p style='color: orange;'>⚠ Algorithm class " . htmlspecialchars($algorithm_class) . " not found</p>";
            }
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error testing " . htmlspecialchars($format['name']) . ": " . htmlspecialchars($e->getMessage()) . "</p>";
        }
    }
    
    // =====================================================
    // TEST 5: Test format selection for judged sports
    // =====================================================
    
    echo "<h3>Test 5: Format Selection for Judged Sports</h3>";
    
    // Test format selector
    require_once '../includes/tournament_format_selector.php';
    $selector = new TournamentFormatSelector($conn);
    
    // Test with different sport types
    $test_sport_types = ['judged', 'performance'];
    
    foreach ($test_sport_types as $sport_type) {
        echo "<h4>Testing format selection for: " . ucfirst($sport_type) . " Sports</h4>";
        
        $available_formats = $selector->getAvailableFormats($sport_type, 3);
        
        if (!empty($available_formats)) {
            echo "<p style='color: green;'>✅ Found " . count($available_formats) . " formats for $sport_type sports:</p>";
            echo "<ul>";
            foreach ($available_formats as $format) {
                echo "<li><strong>" . htmlspecialchars($format['name']) . "</strong> - Min: " . $format['min_participants'] . " participants</li>";
            }
            echo "</ul>";
        } else {
            echo "<p style='color: red;'>❌ No formats found for $sport_type sports</p>";
        }
    }
    
    echo "<h3 style='color: green;'>🎉 Tournament Bracket Generation Testing Complete!</h3>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Test Error: " . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h2 { color: #333; border-bottom: 2px solid #007bff; padding-bottom: 10px; }
h3 { color: #555; margin-top: 30px; border-bottom: 1px solid #ddd; padding-bottom: 5px; }
h4 { color: #666; margin-top: 20px; }
table { margin: 10px 0; }
th { padding: 8px; background: #f8f9fa; }
td { padding: 8px; }
ul { background: #f8f9fa; padding: 15px; border-radius: 5px; }
p { margin: 10px 0; }
</style>
