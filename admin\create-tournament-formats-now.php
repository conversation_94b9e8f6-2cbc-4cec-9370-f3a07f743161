<?php
/**
 * Create Tournament Formats NOW
 * Immediate fix to create all necessary tournament formats
 */

require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>🚨 Creating Tournament Formats NOW</h2>";

try {
    // 1. Check table structure
    echo "<h3>1. Checking Table Structure</h3>";
    $stmt = $conn->prepare("DESCRIBE tournament_formats");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    $has_category_field = false;
    $has_types_field = false;
    
    foreach ($columns as $col) {
        if ($col['Field'] === 'sport_type_category') {
            $has_category_field = true;
        }
        if ($col['Field'] === 'sport_types') {
            $has_types_field = true;
        }
    }
    
    echo "<p>Table has sport_type_category: " . ($has_category_field ? "✅ YES" : "❌ NO") . "</p>";
    echo "<p>Table has sport_types: " . ($has_types_field ? "✅ YES" : "❌ NO") . "</p>";
    
    // 2. Clear existing formats and start fresh
    echo "<h3>2. Clearing Existing Formats</h3>";
    $stmt = $conn->prepare("DELETE FROM tournament_formats");
    $stmt->execute();
    echo "<p>✅ Cleared existing tournament formats</p>";
    
    // 3. Create comprehensive tournament formats
    echo "<h3>3. Creating Tournament Formats</h3>";
    
    if ($has_category_field) {
        // Use sport_type_category (ENUM field)
        $formats = [
            // Traditional Sports
            ['Single Elimination', 'single_elimination', 'Traditional knockout tournament', 'traditional', 2, null],
            ['Double Elimination', 'double_elimination', 'Two-bracket elimination system', 'traditional', 3, null],
            ['Round Robin', 'round_robin', 'Every team plays every other team', 'traditional', 3, 16],
            ['Multi-Stage', 'multi_stage', 'Group stage followed by playoffs', 'traditional', 8, null],
            
            // Academic Sports
            ['Swiss System', 'swiss_system', 'Pairing system for academic competitions where participants with similar records compete against each other', 'academic', 4, null],
            ['Knockout Rounds', 'knockout_rounds', 'Academic elimination tournament with question pools and time limits', 'academic', 4, null],
            ['Quiz Bowl Format', 'quiz_bowl', 'Round robin format specifically designed for quiz bowl competitions with toss-up and bonus questions', 'academic', 3, 12],
            ['Academic Round Robin', 'academic_round_robin', 'Every participant competes against every other participant in academic challenges', 'academic', 3, 16],
            
            // Judged Sports
            ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria where participants advance based on cumulative judge scores', 'judged', 3, null],
            ['Performance Competition', 'performance_competition', 'Structured performance competition with multiple rounds and detailed judging criteria', 'judged', 3, 50],
            ['Talent Showcase', 'talent_showcase', 'Showcase format with multiple performance rounds combining judge scores and audience voting', 'judged', 3, 50],
            ['Artistic Judging', 'artistic_judging', 'Comprehensive artistic competition with technical and artistic components similar to figure skating or gymnastics', 'judged', 3, 30],
        ];
        
        foreach ($formats as $format) {
            $stmt = $conn->prepare("
                INSERT INTO tournament_formats (name, code, description, sport_type_category, min_participants, max_participants)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute($format);
            echo "<p>✅ Created: {$format[0]} (Category: {$format[3]})</p>";
        }
        
    } else if ($has_types_field) {
        // Use sport_types (TEXT field)
        $formats = [
            // Traditional Sports
            ['Single Elimination', 'single_elimination', 'Traditional knockout tournament', 'traditional,team,individual', 2, null],
            ['Double Elimination', 'double_elimination', 'Two-bracket elimination system', 'traditional,team,individual', 3, null],
            ['Round Robin', 'round_robin', 'Every team plays every other team', 'traditional,team,individual', 3, 16],
            ['Multi-Stage', 'multi_stage', 'Group stage followed by playoffs', 'traditional,team,individual', 8, null],
            
            // Academic Sports
            ['Swiss System', 'swiss_system', 'Pairing system for academic competitions', 'academic', 4, null],
            ['Knockout Rounds', 'knockout_rounds', 'Academic elimination tournament', 'academic', 4, null],
            ['Quiz Bowl Format', 'quiz_bowl', 'Round robin for quiz competitions', 'academic', 3, 12],
            ['Academic Round Robin', 'academic_round_robin', 'All-vs-all academic challenges', 'academic', 3, 16],
            
            // Judged Sports
            ['Judged Rounds', 'judged_rounds', 'Multiple judged rounds with scoring criteria', 'judged,performance', 3, null],
            ['Performance Competition', 'performance_competition', 'Structured performance competition', 'judged,performance', 3, 50],
            ['Talent Showcase', 'talent_showcase', 'Performance rounds with audience voting', 'judged,performance', 3, 50],
            ['Artistic Judging', 'artistic_judging', 'Technical and artistic components', 'judged,performance', 3, 30],
        ];
        
        foreach ($formats as $format) {
            $stmt = $conn->prepare("
                INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute($format);
            echo "<p>✅ Created: {$format[0]} (Types: {$format[3]})</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ Cannot determine table structure!</p>";
        exit;
    }
    
    // 4. Verify creation
    echo "<h3>4. Verification</h3>";
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats");
    $stmt->execute();
    $count = $stmt->fetch()['count'];
    echo "<p>✅ Total tournament formats created: {$count}</p>";
    
    // Test specific queries
    if ($has_category_field) {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats WHERE sport_type_category = 'academic'");
        $stmt->execute();
        $academic_count = $stmt->fetch()['count'];
        
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats WHERE sport_type_category = 'judged'");
        $stmt->execute();
        $judged_count = $stmt->fetch()['count'];
    } else {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats WHERE sport_types LIKE '%academic%'");
        $stmt->execute();
        $academic_count = $stmt->fetch()['count'];
        
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM tournament_formats WHERE sport_types LIKE '%judged%'");
        $stmt->execute();
        $judged_count = $stmt->fetch()['count'];
    }
    
    echo "<p>✅ Academic formats: {$academic_count}</p>";
    echo "<p>✅ Judged formats: {$judged_count}</p>";
    
    // 5. Test AJAX endpoints immediately
    echo "<h3>5. Testing AJAX Endpoints</h3>";
    
    $test_types = ['academic', 'judged'];
    foreach ($test_types as $sport_type) {
        echo "<h4>Testing {$sport_type}:</h4>";
        
        $_POST['sport_type'] = $sport_type;
        
        ob_start();
        try {
            include 'ajax/get-tournament-formats.php';
            $response = ob_get_clean();
            
            $data = json_decode($response, true);
            if ($data && $data['success']) {
                echo "<p style='color: green;'>✅ AJAX Success: Found " . count($data['formats']) . " formats for {$sport_type}</p>";
                foreach ($data['formats'] as $format) {
                    echo "<li>{$format['name']}</li>";
                }
            } else {
                echo "<p style='color: red;'>❌ AJAX Failed for {$sport_type}: " . ($data['message'] ?? 'Unknown error') . "</p>";
            }
        } catch (Exception $e) {
            ob_end_clean();
            echo "<p style='color: red;'>❌ Exception for {$sport_type}: " . $e->getMessage() . "</p>";
        }
        
        unset($_POST['sport_type']);
    }
    
    echo "<div style='background: #d4edda; padding: 20px; border-left: 4px solid #28a745; margin: 20px 0;'>";
    echo "<h4>🎉 Tournament Formats Created Successfully!</h4>";
    echo "<p>All tournament formats have been created and tested. The dropdown should now work correctly.</p>";
    echo "<p><strong>Academic formats:</strong> {$academic_count} available</p>";
    echo "<p><strong>Judged formats:</strong> {$judged_count} available</p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . $e->getMessage() . "</p>";
}

echo "<div style='text-align: center; margin: 30px 0;'>";
echo "<a href='manage-event.php?id=1' style='background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 16px;'>";
echo "🎯 TEST IN EVENT MANAGEMENT NOW";
echo "</a>";
echo "</div>";

echo "<div style='text-align: center; margin: 20px 0;'>";
echo "<a href='debug-live-tournament-formats.php' style='background: #6c757d; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>";
echo "🔍 View Debug Report";
echo "</a>";
echo "</div>";
?>
