<?php
require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Get parameters
$event_id = $_GET['event_id'] ?? 0;
$sport_id = $_GET['sport_id'] ?? 0;
$category_id = $_GET['category_id'] ?? 0;

echo "<!DOCTYPE html>
<html lang='en'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Participants Debug - SC_IMS</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }
        .section { background: white; padding: 20px; margin: 20px 0; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .title { color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 10px; margin-bottom: 20px; }
        .status { padding: 8px 12px; border-radius: 5px; margin: 5px 0; display: inline-block; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        .warning { background: #fff3cd; color: #856404; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 10px; border: 1px solid #ddd; text-align: left; }
        th { background: #f8f9fa; font-weight: bold; }
        .btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; text-decoration: none; display: inline-block; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #1e7e34; }
    </style>
</head>
<body>";

echo "<h1>Participants Debug Tool</h1>";
echo "<div class='status info'>Event ID: $event_id | Sport ID: $sport_id | Category ID: $category_id</div>";

// Check if we have the category
try {
    $stmt = $conn->prepare("
        SELECT
            sc.*,
            e.name as event_name,
            s.name as sport_name,
            es.id as event_sport_id
        FROM sport_categories sc
        JOIN events e ON sc.event_id = e.id
        JOIN sports s ON sc.sport_id = s.id
        LEFT JOIN event_sports es ON (es.event_id = sc.event_id AND es.sport_id = sc.sport_id)
        WHERE sc.id = ?
    ");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch();
    
    if (!$category) {
        echo "<div class='status error'>❌ Category not found!</div>";
        exit;
    }
    
    echo "<div class='section'>
        <h2 class='title'>Category Information</h2>
        <p><strong>Event:</strong> {$category['event_name']}</p>
        <p><strong>Sport:</strong> {$category['sport_name']}</p>
        <p><strong>Category:</strong> {$category['name']}</p>
        <p><strong>Event Sport ID:</strong> {$category['event_sport_id']}</p>
    </div>";

    // Check 1: Event Department Registrations
    echo "<div class='section'>
        <h2 class='title'>1. Event Department Registrations</h2>";
    
    $stmt = $conn->prepare("
        SELECT
            edr.*,
            d.name as department_name,
            d.abbreviation
        FROM event_department_registrations edr
        JOIN departments d ON edr.department_id = d.id
        WHERE edr.event_id = ?
        ORDER BY d.name
    ");
    $stmt->execute([$event_id]);
    $event_registrations = $stmt->fetchAll();
    
    if (!empty($event_registrations)) {
        echo "<div class='status success'>✅ Found " . count($event_registrations) . " department(s) registered for this event</div>";
        echo "<table>
            <tr><th>Department</th><th>Status</th><th>Registration Date</th><th>Contact Person</th><th>Total Participants</th></tr>";
        foreach ($event_registrations as $reg) {
            echo "<tr>
                <td>{$reg['department_name']} ({$reg['abbreviation']})</td>
                <td>{$reg['status']}</td>
                <td>{$reg['registration_date']}</td>
                <td>{$reg['contact_person']}</td>
                <td>{$reg['total_participants']}</td>
            </tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='status warning'>⚠️ No departments registered for this event in the unified system</div>";
    }
    echo "</div>";

    // Check 2: Old Registrations Table
    echo "<div class='section'>
        <h2 class='title'>2. Old Registrations Table</h2>";
    
    $stmt = $conn->prepare("
        SELECT
            r.*,
            d.name as department_name,
            d.abbreviation
        FROM registrations r
        JOIN departments d ON r.department_id = d.id
        WHERE r.event_sport_id = ?
        ORDER BY d.name
    ");
    $stmt->execute([$category['event_sport_id']]);
    $old_registrations = $stmt->fetchAll();
    
    if (!empty($old_registrations)) {
        echo "<div class='status success'>✅ Found " . count($old_registrations) . " registration(s) in old system</div>";
        echo "<table>
            <tr><th>Department</th><th>Team Name</th><th>Status</th><th>Registration Date</th></tr>";
        foreach ($old_registrations as $reg) {
            echo "<tr>
                <td>{$reg['department_name']} ({$reg['abbreviation']})</td>
                <td>{$reg['team_name']}</td>
                <td>{$reg['status']}</td>
                <td>{$reg['registration_date']}</td>
            </tr>";
        }
        echo "</table>";
    } else {
        echo "<div class='status warning'>⚠️ No registrations found in old system</div>";
    }
    echo "</div>";

    // Check 3: Available Departments
    echo "<div class='section'>
        <h2 class='title'>3. Available Departments</h2>";
    
    $stmt = $conn->prepare("SELECT * FROM departments WHERE status = 'active' ORDER BY name");
    $stmt->execute();
    $departments = $stmt->fetchAll();
    
    if (!empty($departments)) {
        echo "<div class='status info'>ℹ️ Found " . count($departments) . " active department(s)</div>";
        echo "<table>
            <tr><th>Department</th><th>Abbreviation</th><th>Status</th><th>Action</th></tr>";
        foreach ($departments as $dept) {
            // Check if already registered
            $already_registered = false;
            foreach ($event_registrations as $reg) {
                if ($reg['department_id'] == $dept['id']) {
                    $already_registered = true;
                    break;
                }
            }
            
            echo "<tr>
                <td>{$dept['name']}</td>
                <td>{$dept['abbreviation']}</td>
                <td>{$dept['status']}</td>
                <td>";
            if ($already_registered) {
                echo "<span class='status success'>Already Registered</span>";
            } else {
                echo "<a href='?event_id=$event_id&sport_id=$sport_id&category_id=$category_id&register_dept={$dept['id']}' class='btn btn-success'>Register for Event</a>";
            }
            echo "</td>
            </tr>";
        }
        echo "</table>";
    }
    echo "</div>";

    // Handle registration action
    if (isset($_GET['register_dept'])) {
        $dept_id = $_GET['register_dept'];
        
        echo "<div class='section'>
            <h2 class='title'>Registration Action</h2>";
        
        try {
            // Check if already registered
            $stmt = $conn->prepare("SELECT id FROM event_department_registrations WHERE event_id = ? AND department_id = ?");
            $stmt->execute([$event_id, $dept_id]);
            
            if ($stmt->fetch()) {
                echo "<div class='status warning'>⚠️ Department already registered for this event</div>";
            } else {
                // Register the department
                $stmt = $conn->prepare("
                    INSERT INTO event_department_registrations 
                    (event_id, department_id, status, contact_person, total_participants)
                    VALUES (?, ?, 'approved', 'Admin Registration', 1)
                ");
                $stmt->execute([$event_id, $dept_id]);
                
                echo "<div class='status success'>✅ Department successfully registered for the event!</div>";
                echo "<a href='?event_id=$event_id&sport_id=$sport_id&category_id=$category_id' class='btn'>Refresh Page</a>";
            }
        } catch (Exception $e) {
            echo "<div class='status error'>❌ Error registering department: " . $e->getMessage() . "</div>";
        }
        echo "</div>";
    }

    // Quick Actions
    echo "<div class='section'>
        <h2 class='title'>Quick Actions</h2>
        <a href='manage-category.php?event_id=$event_id&sport_id=$sport_id&category_id=$category_id' class='btn'>Back to Category Management</a>
        <a href='manage-event.php?id=$event_id' class='btn'>Manage Event</a>
        <a href='?event_id=$event_id&sport_id=$sport_id&category_id=$category_id&register_all=1' class='btn btn-success'>Register All Active Departments</a>
    </div>";

    // Register all departments action
    if (isset($_GET['register_all'])) {
        echo "<div class='section'>
            <h2 class='title'>Bulk Registration</h2>";
        
        try {
            $registered_count = 0;
            foreach ($departments as $dept) {
                // Check if already registered
                $stmt = $conn->prepare("SELECT id FROM event_department_registrations WHERE event_id = ? AND department_id = ?");
                $stmt->execute([$event_id, $dept['id']]);
                
                if (!$stmt->fetch()) {
                    // Register the department
                    $stmt = $conn->prepare("
                        INSERT INTO event_department_registrations 
                        (event_id, department_id, status, contact_person, total_participants)
                        VALUES (?, ?, 'approved', 'Bulk Admin Registration', 1)
                    ");
                    $stmt->execute([$event_id, $dept['id']]);
                    $registered_count++;
                }
            }
            
            echo "<div class='status success'>✅ Successfully registered $registered_count department(s) for the event!</div>";
            echo "<a href='?event_id=$event_id&sport_id=$sport_id&category_id=$category_id' class='btn'>Refresh Page</a>";
        } catch (Exception $e) {
            echo "<div class='status error'>❌ Error in bulk registration: " . $e->getMessage() . "</div>";
        }
        echo "</div>";
    }

} catch (Exception $e) {
    echo "<div class='status error'>❌ Database Error: " . $e->getMessage() . "</div>";
}

echo "</body></html>";
?>
