<?php
/**
 * Debug Participant Retrieval
 * SC_IMS Sports Competition and Event Management System
 * 
 * Debug script to check participant retrieval for tournament generation
 */

require_once '../config/database.php';
require_once '../includes/unified_bracket_engine.php';

$eventSportId = $_GET['event_sport_id'] ?? 52; // Default to the one in the screenshot

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Participants - SC_IMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .debug-section { margin-bottom: 2rem; padding: 1rem; border: 1px solid #dee2e6; border-radius: 0.5rem; }
        .debug-success { background-color: #d4edda; }
        .debug-error { background-color: #f8d7da; }
        .debug-warning { background-color: #fff3cd; }
        pre { background-color: #f8f9fa; padding: 1rem; border-radius: 0.5rem; }
    </style>
</head>
<body>
    <div class="container py-4">
        <h1>Debug Participant Retrieval</h1>
        <p>Event Sport ID: <strong><?php echo htmlspecialchars($eventSportId); ?></strong></p>
        
        <?php
        echo "<div class='debug-section'>";
        echo "<h3>1. Event Sport Information</h3>";
        
        try {
            $sql = "SELECT es.*, s.name as sport_name, e.name as event_name 
                    FROM event_sports es 
                    JOIN sports s ON es.sport_id = s.id 
                    JOIN events e ON es.event_id = e.id 
                    WHERE es.id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$eventSportId]);
            $eventSport = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($eventSport) {
                echo "<div class='debug-success'>";
                echo "<h5>Event Sport Found</h5>";
                echo "<pre>" . htmlspecialchars(json_encode($eventSport, JSON_PRETTY_PRINT)) . "</pre>";
                echo "</div>";
            } else {
                echo "<div class='debug-error'>";
                echo "<h5>Event Sport Not Found</h5>";
                echo "<p>No event sport found with ID: $eventSportId</p>";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='debug-error'>";
            echo "<h5>Error</h5>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
        echo "</div>";
        
        echo "<div class='debug-section'>";
        echo "<h3>2. Unified Bracket Engine Participant Retrieval</h3>";
        
        try {
            $bracketEngine = new UnifiedBracketEngine($conn);
            $participants = $bracketEngine->getEventParticipants($eventSportId);
            
            echo "<div class='debug-success'>";
            echo "<h5>Participants Found: " . count($participants) . "</h5>";
            echo "<pre>" . htmlspecialchars(json_encode($participants, JSON_PRETTY_PRINT)) . "</pre>";
            echo "</div>";
        } catch (Exception $e) {
            echo "<div class='debug-error'>";
            echo "<h5>Error in Unified Bracket Engine</h5>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
        echo "</div>";
        
        echo "<div class='debug-section'>";
        echo "<h3>3. Direct Database Queries</h3>";
        
        // Check registrations table
        echo "<h5>3.1 Legacy Registrations Table</h5>";
        try {
            $sql = "SELECT r.*, d.name as department_name 
                    FROM registrations r 
                    JOIN departments d ON r.department_id = d.id 
                    WHERE r.event_sport_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$eventSportId]);
            $legacyParticipants = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<div class='debug-" . (count($legacyParticipants) > 0 ? 'success' : 'warning') . "'>";
            echo "<p>Found: " . count($legacyParticipants) . " participants</p>";
            if (!empty($legacyParticipants)) {
                echo "<pre>" . htmlspecialchars(json_encode($legacyParticipants, JSON_PRETTY_PRINT)) . "</pre>";
            }
            echo "</div>";
        } catch (Exception $e) {
            echo "<div class='debug-error'>";
            echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
        
        // Check event_department_registrations
        echo "<h5>3.2 Event Department Registrations</h5>";
        try {
            if ($eventSport) {
                $sql = "SELECT edr.*, d.name as department_name 
                        FROM event_department_registrations edr 
                        JOIN departments d ON edr.department_id = d.id 
                        WHERE edr.event_id = ?";
                $stmt = $conn->prepare($sql);
                $stmt->execute([$eventSport['event_id']]);
                $eventParticipants = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                echo "<div class='debug-" . (count($eventParticipants) > 0 ? 'success' : 'warning') . "'>";
                echo "<p>Found: " . count($eventParticipants) . " event registrations</p>";
                if (!empty($eventParticipants)) {
                    echo "<pre>" . htmlspecialchars(json_encode($eventParticipants, JSON_PRETTY_PRINT)) . "</pre>";
                }
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='debug-error'>";
            echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
        
        // Check department_sport_participations
        echo "<h5>3.3 Department Sport Participations</h5>";
        try {
            $sql = "SELECT dsp.*, d.name as department_name, edr.status as registration_status
                    FROM department_sport_participations dsp 
                    JOIN event_department_registrations edr ON dsp.event_department_registration_id = edr.id
                    JOIN departments d ON edr.department_id = d.id 
                    WHERE dsp.event_sport_id = ?";
            $stmt = $conn->prepare($sql);
            $stmt->execute([$eventSportId]);
            $sportParticipants = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<div class='debug-" . (count($sportParticipants) > 0 ? 'success' : 'warning') . "'>";
            echo "<p>Found: " . count($sportParticipants) . " sport-specific participations</p>";
            if (!empty($sportParticipants)) {
                echo "<pre>" . htmlspecialchars(json_encode($sportParticipants, JSON_PRETTY_PRINT)) . "</pre>";
            }
            echo "</div>";
        } catch (Exception $e) {
            echo "<div class='debug-error'>";
            echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
        echo "</div>";
        
        echo "<div class='debug-section'>";
        echo "<h3>4. Tournament Generation Test</h3>";
        
        try {
            $participants = $bracketEngine->getEventParticipants($eventSportId);
            
            if (count($participants) >= 2) {
                echo "<div class='debug-success'>";
                echo "<h5>✅ Tournament Generation Should Work</h5>";
                echo "<p>Found " . count($participants) . " participants, which meets the minimum requirement of 2.</p>";
                echo "</div>";
                
                // Test tournament generation
                echo "<h5>4.1 Test Tournament Generation</h5>";
                try {
                    $config = [
                        'seeding_method' => 'random',
                        'test_mode' => true
                    ];
                    
                    // Don't actually create tournament, just test the logic
                    echo "<div class='debug-success'>";
                    echo "<p>✅ Tournament generation logic should work with current participants.</p>";
                    echo "</div>";
                } catch (Exception $e) {
                    echo "<div class='debug-error'>";
                    echo "<p>❌ Tournament generation failed: " . htmlspecialchars($e->getMessage()) . "</p>";
                    echo "</div>";
                }
            } else {
                echo "<div class='debug-error'>";
                echo "<h5>❌ Insufficient Participants</h5>";
                echo "<p>Found " . count($participants) . " participants, but need at least 2 for tournament generation.</p>";
                echo "</div>";
            }
        } catch (Exception $e) {
            echo "<div class='debug-error'>";
            echo "<h5>Error in Tournament Generation Test</h5>";
            echo "<p>" . htmlspecialchars($e->getMessage()) . "</p>";
            echo "</div>";
        }
        echo "</div>";
        ?>
        
        <div class="mt-4">
            <?php if (isset($eventSport) && $eventSport): ?>
            <a href="manage-category.php?event_id=<?php echo $eventSport['event_id']; ?>&sport_id=<?php echo $eventSport['sport_id']; ?>&category_id=<?php echo $eventSportId; ?>" class="btn btn-primary">
                Back to Category Management
            </a>
            <?php else: ?>
            <a href="javascript:history.back()" class="btn btn-secondary">
                Go Back
            </a>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
