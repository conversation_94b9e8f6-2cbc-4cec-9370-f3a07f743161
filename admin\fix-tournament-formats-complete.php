<?php
/**
 * Complete Tournament Formats Fix
 * Resolves all tournament format issues including database constraints and AJAX problems
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Complete Tournament Formats Fix</h1>";

try {
    // Step 1: Check and fix database constraints
    echo "<h2>Step 1: Fixing Database Constraints</h2>";
    
    // Check if there are any foreign key references preventing deletion
    $stmt = $conn->prepare("
        SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            CONSTRAINT_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_NAME = 'tournament_formats'
    ");
    $stmt->execute();
    $foreign_keys = $stmt->fetchAll();
    
    if (!empty($foreign_keys)) {
        echo "<p><strong>Found foreign key constraints:</strong></p>";
        echo "<ul>";
        foreach ($foreign_keys as $fk) {
            echo "<li>{$fk['TABLE_NAME']}.{$fk['COLUMN_NAME']} → {$fk['REFERENCED_TABLE_NAME']}.{$fk['REFERENCED_COLUMN_NAME']}</li>";
        }
        echo "</ul>";
        
        // Temporarily disable foreign key checks
        echo "<p>Temporarily disabling foreign key checks...</p>";
        $conn->exec("SET FOREIGN_KEY_CHECKS = 0");
    }
    
    // Step 2: Clear existing tournament formats
    echo "<h2>Step 2: Clearing Existing Tournament Formats</h2>";
    
    $stmt = $conn->prepare("SELECT COUNT(*) FROM tournament_formats");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count > 0) {
        echo "<p>Found {$count} existing tournament formats. Clearing...</p>";
        $conn->exec("DELETE FROM tournament_formats");
        echo "<p style='color: green;'>✓ Cleared existing tournament formats</p>";
    } else {
        echo "<p>No existing tournament formats found.</p>";
    }
    
    // Step 3: Ensure proper table structure
    echo "<h2>Step 3: Ensuring Proper Table Structure</h2>";
    
    // Check current table structure
    $stmt = $conn->prepare("DESCRIBE tournament_formats");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    $has_sport_type_category = false;
    $has_sport_types = false;
    
    foreach ($columns as $col) {
        if ($col['Field'] === 'sport_type_category') {
            $has_sport_type_category = true;
        } elseif ($col['Field'] === 'sport_types') {
            $has_sport_types = true;
        }
    }
    
    // Add missing columns if needed
    if (!$has_sport_type_category && !$has_sport_types) {
        echo "<p>Adding sport_types column...</p>";
        $conn->exec("ALTER TABLE tournament_formats ADD COLUMN sport_types VARCHAR(255) DEFAULT 'team,individual'");
        echo "<p style='color: green;'>✓ Added sport_types column</p>";
        $has_sport_types = true;
    }
    
    // Step 4: Insert comprehensive tournament formats
    echo "<h2>Step 4: Creating Comprehensive Tournament Formats</h2>";
    
    $tournament_formats = [
        // Traditional Sports Formats
        [
            'name' => 'Single Elimination',
            'code' => 'single_elimination',
            'description' => 'Traditional knockout tournament where teams/participants are eliminated after one loss.',
            'sport_types' => 'traditional,team,individual',
            'min_participants' => 2,
            'max_participants' => null
        ],
        [
            'name' => 'Double Elimination',
            'code' => 'double_elimination',
            'description' => 'Two-bracket system with winner\'s and loser\'s brackets.',
            'sport_types' => 'traditional,team,individual',
            'min_participants' => 3,
            'max_participants' => null
        ],
        [
            'name' => 'Round Robin',
            'code' => 'round_robin',
            'description' => 'Every team/participant plays every other team/participant once.',
            'sport_types' => 'traditional,team,individual',
            'min_participants' => 3,
            'max_participants' => 16
        ],
        [
            'name' => 'Multi-Stage Tournament',
            'code' => 'multi_stage',
            'description' => 'Combination of group stage followed by elimination rounds.',
            'sport_types' => 'traditional,team,individual',
            'min_participants' => 8,
            'max_participants' => null
        ],
        
        // Academic Sports Formats
        [
            'name' => 'Swiss System',
            'code' => 'swiss_system',
            'description' => 'Pairing system commonly used for academic competitions.',
            'sport_types' => 'academic',
            'min_participants' => 4,
            'max_participants' => null
        ],
        [
            'name' => 'Knockout Rounds',
            'code' => 'knockout_rounds',
            'description' => 'Academic elimination tournament with question pools and time limits.',
            'sport_types' => 'academic',
            'min_participants' => 4,
            'max_participants' => null
        ],
        [
            'name' => 'Quiz Bowl Format',
            'code' => 'quiz_bowl',
            'description' => 'Round robin format specifically designed for quiz bowl competitions.',
            'sport_types' => 'academic',
            'min_participants' => 3,
            'max_participants' => 12
        ],
        [
            'name' => 'Academic Round Robin',
            'code' => 'academic_round_robin',
            'description' => 'Round robin tournament optimized for academic competitions.',
            'sport_types' => 'academic',
            'min_participants' => 3,
            'max_participants' => 16
        ],
        
        // Judged Sports Formats
        [
            'name' => 'Judged Rounds',
            'code' => 'judged_rounds',
            'description' => 'Multiple judged rounds with scoring criteria.',
            'sport_types' => 'judged,performance',
            'min_participants' => 3,
            'max_participants' => null
        ],
        [
            'name' => 'Performance Competition',
            'code' => 'performance_competition',
            'description' => 'Structured performance competition with multiple rounds.',
            'sport_types' => 'judged,performance',
            'min_participants' => 3,
            'max_participants' => null
        ],
        [
            'name' => 'Talent Showcase',
            'code' => 'talent_showcase',
            'description' => 'Showcase format with multiple performance rounds and audience voting.',
            'sport_types' => 'judged,performance',
            'min_participants' => 3,
            'max_participants' => 50
        ],
        [
            'name' => 'Artistic Judging',
            'code' => 'artistic_judging',
            'description' => 'Comprehensive artistic competition with technical and artistic components.',
            'sport_types' => 'judged,performance',
            'min_participants' => 3,
            'max_participants' => 30
        ]
    ];
    
    $inserted_count = 0;
    foreach ($tournament_formats as $format) {
        try {
            $stmt = $conn->prepare("
                INSERT INTO tournament_formats (name, code, description, sport_types, min_participants, max_participants)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $format['name'],
                $format['code'],
                $format['description'],
                $format['sport_types'],
                $format['min_participants'],
                $format['max_participants']
            ]);
            $inserted_count++;
            echo "<p style='color: green;'>✓ Created: {$format['name']}</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error creating {$format['name']}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p><strong>Successfully created {$inserted_count} tournament formats</strong></p>";
    
    // Step 5: Re-enable foreign key checks
    if (!empty($foreign_keys)) {
        echo "<h2>Step 5: Re-enabling Foreign Key Checks</h2>";
        $conn->exec("SET FOREIGN_KEY_CHECKS = 1");
        echo "<p style='color: green;'>✓ Foreign key checks re-enabled</p>";
    }
    
    // Step 6: Test AJAX endpoint
    echo "<h2>Step 6: Testing AJAX Endpoint</h2>";
    
    $test_sport_types = ['traditional', 'academic', 'judged', 'performance'];
    
    foreach ($test_sport_types as $sport_type) {
        echo "<h4>Testing sport type: {$sport_type}</h4>";
        
        $_POST['sport_type'] = $sport_type;
        
        ob_start();
        try {
            include 'ajax/get-tournament-formats.php';
            $response = ob_get_clean();
            
            $data = json_decode($response, true);
            if ($data && $data['success']) {
                echo "<p style='color: green;'>✓ AJAX Success: Found " . count($data['formats']) . " formats</p>";
                if (!empty($data['formats'])) {
                    echo "<ul>";
                    foreach ($data['formats'] as $format) {
                        echo "<li>{$format['name']}</li>";
                    }
                    echo "</ul>";
                }
            } else {
                echo "<p style='color: red;'>❌ AJAX Failed: " . ($data['message'] ?? 'Unknown error') . "</p>";
                echo "<pre>" . htmlspecialchars($response) . "</pre>";
            }
        } catch (Exception $e) {
            ob_end_clean();
            echo "<p style='color: red;'>❌ Exception: " . $e->getMessage() . "</p>";
        }
        
        unset($_POST['sport_type']);
    }
    
    echo "<h2>✅ Tournament Formats Fix Complete!</h2>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>✓ Database constraints resolved</li>";
    echo "<li>✓ Tournament formats table structure verified</li>";
    echo "<li>✓ {$inserted_count} tournament formats created</li>";
    echo "<li>✓ AJAX endpoint tested</li>";
    echo "</ul>";
    
    echo "<p><a href='manage-event.php' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>🎯 Test Add Sport Modal</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Critical Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
