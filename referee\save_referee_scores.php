<?php
require_once '../admin/includes/config.php';

header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!isset($input['token'])) {
        throw new Exception('Missing referee token');
    }
    
    $token = $input['token'];
    $team1_score = isset($input['team1_score']) ? (int)$input['team1_score'] : 0;
    $team2_score = isset($input['team2_score']) ? (int)$input['team2_score'] : 0;
    
    // Verify token and get match details
    $stmt = $pdo->prepare("
        SELECT rs.match_id, m.team1_id, m.team2_id
        FROM referee_sessions rs
        JOIN tournament_matches m ON rs.match_id = m.id
        WHERE rs.token = ? AND rs.status = 'active' AND rs.expires_at > NOW()
    ");
    
    $stmt->execute([$token]);
    $session = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$session) {
        throw new Exception('Invalid or expired referee session');
    }
    
    // Determine winner
    $winner_id = null;
    if ($team1_score > $team2_score) {
        $winner_id = $session['team1_id'];
    } elseif ($team2_score > $team1_score) {
        $winner_id = $session['team2_id'];
    }
    // If scores are equal, winner_id remains null (tie)
    
    // Update match scores
    $stmt = $pdo->prepare("
        UPDATE tournament_matches 
        SET team1_score = ?, 
            team2_score = ?, 
            winner_id = ?,
            updated_at = NOW()
        WHERE id = ?
    ");
    
    $stmt->execute([$team1_score, $team2_score, $winner_id, $session['match_id']]);
    
    // Update referee session last accessed
    $stmt = $pdo->prepare("UPDATE referee_sessions SET last_accessed = NOW() WHERE token = ?");
    $stmt->execute([$token]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Scores updated successfully',
        'winner_id' => $winner_id
    ]);
    
} catch (Exception $e) {
    error_log("Error in save_referee_scores.php: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => 'Error saving scores: ' . $e->getMessage()
    ]);
}
?>
