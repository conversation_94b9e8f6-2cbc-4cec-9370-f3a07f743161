<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h2>Event Sports Table Structure</h2>";

try {
    // Check table structure
    $stmt = $conn->prepare("DESCRIBE event_sports");
    $stmt->execute();
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>";
    
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($column['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Default']) . "</td>";
        echo "<td>" . htmlspecialchars($column['Extra']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check if tournament_format_id exists
    $has_tournament_format_id = false;
    foreach ($columns as $column) {
        if ($column['Field'] === 'tournament_format_id') {
            $has_tournament_format_id = true;
            break;
        }
    }
    
    echo "<h3>Tournament Format ID Field: " . ($has_tournament_format_id ? "EXISTS" : "MISSING") . "</h3>";
    
    // Check sample data
    echo "<h3>Sample Event Sports Data</h3>";
    $stmt = $conn->prepare("SELECT * FROM event_sports LIMIT 5");
    $stmt->execute();
    $sample_data = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!empty($sample_data)) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr>";
        foreach (array_keys($sample_data[0]) as $header) {
            echo "<th>" . htmlspecialchars($header) . "</th>";
        }
        echo "</tr>";
        
        foreach ($sample_data as $row) {
            echo "<tr>";
            foreach ($row as $value) {
                echo "<td>" . htmlspecialchars($value) . "</td>";
            }
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No data found in event_sports table</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>
