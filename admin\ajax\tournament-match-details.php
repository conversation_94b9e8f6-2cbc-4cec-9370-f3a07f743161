<?php
/**
 * Tournament Match Details
 * Handles match information retrieval for scoring
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Set JSON response header
header('Content-Type: application/json');

try {
    $input = json_decode(file_get_contents('php://input'), true);
    $action = $input['action'] ?? '';

    switch ($action) {
        case 'get_match_details':
            handleGetMatchDetails($conn, $input);
            break;
        default:
            throw new Exception('Invalid action');
    }

} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Get detailed match information for scoring
 */
function handleGetMatchDetails($conn, $input) {
    $matchId = $input['match_id'] ?? '';

    if (empty($matchId)) {
        throw new Exception('Match ID is required');
    }

    // Get match details with team information
    $stmt = $conn->prepare("
        SELECT 
            m.*,
            ta.name as team_a_name,
            ta.abbreviation as team_a_abbr,
            ta.color_code as team_a_color,
            tb.name as team_b_name,
            tb.abbreviation as team_b_abbr,
            tb.color_code as team_b_color,
            w.name as winner_name,
            w.abbreviation as winner_abbr,
            tr.round_name,
            tr.round_type,
            ts.name as tournament_name,
            tf.name as format_name,
            tf.code as format_code
        FROM matches m
        LEFT JOIN departments ta ON m.team1_id = ta.id
        LEFT JOIN departments tb ON m.team2_id = tb.id
        LEFT JOIN departments w ON m.winner_id = w.id
        LEFT JOIN tournament_rounds tr ON m.tournament_round_id = tr.id
        LEFT JOIN tournament_structures ts ON m.tournament_structure_id = ts.id
        LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
        WHERE m.id = ?
    ");
    $stmt->execute([$matchId]);
    $match = $stmt->fetch();

    if (!$match) {
        throw new Exception('Match not found');
    }

    // Format match data for frontend
    $matchData = [
        'id' => $match['id'],
        'round_number' => $match['round_number'],
        'match_number' => $match['match_number'],
        'round_name' => $match['round_name'] ?? "Round {$match['round_number']}",
        'round_type' => $match['round_type'] ?? 'elimination',
        'tournament_name' => $match['tournament_name'],
        'format_name' => $match['format_name'],
        'format_code' => $match['format_code'],
        
        // Team A details
        'team_a_id' => $match['team1_id'],
        'team_a_name' => $match['team_a_name'] ?? 'Team A',
        'team_a_abbr' => $match['team_a_abbr'] ?? '',
        'team_a_color' => $match['team_a_color'] ?? '#007bff',
        'team_a_score' => $match['team_a_score'],
        
        // Team B details
        'team_b_id' => $match['team2_id'],
        'team_b_name' => $match['team_b_name'] ?? 'Team B',
        'team_b_abbr' => $match['team_b_abbr'] ?? '',
        'team_b_color' => $match['team_b_color'] ?? '#6c757d',
        'team_b_score' => $match['team_b_score'],
        
        // Match details
        'status' => $match['status'],
        'winner_id' => $match['winner_id'],
        'winner_name' => $match['winner_name'],
        'venue' => $match['venue'],
        'scheduled_time' => $match['scheduled_time'],
        'actual_start_time' => $match['actual_start_time'],
        'actual_end_time' => $match['actual_end_time'],
        'referee_notes' => $match['referee_notes'],
        'is_bye_match' => $match['is_bye_match'],
        
        // Additional metadata
        'can_edit' => true, // TODO: Add permission checks
        'scoring_rules' => getScoringRules($match['format_code'])
    ];

    echo json_encode([
        'success' => true,
        'match' => $matchData
    ]);
}

/**
 * Get scoring rules based on tournament format
 */
function getScoringRules($formatCode) {
    switch ($formatCode) {
        case 'round_robin':
            return [
                'type' => 'points',
                'win_points' => 3,
                'draw_points' => 1,
                'loss_points' => 0,
                'allows_draws' => true,
                'description' => 'Standard league scoring: 3 points for win, 1 for draw, 0 for loss'
            ];
            
        case 'swiss_system':
            return [
                'type' => 'points',
                'win_points' => 1,
                'draw_points' => 0.5,
                'loss_points' => 0,
                'allows_draws' => true,
                'description' => 'Swiss system: 1 point for win, 0.5 for draw, 0 for loss'
            ];
            
        case 'single_elimination':
            return [
                'type' => 'elimination',
                'allows_draws' => false,
                'requires_winner' => true,
                'description' => 'Single elimination: Must have a winner to advance'
            ];
            
        case 'double_elimination':
            return [
                'type' => 'elimination',
                'allows_draws' => false,
                'requires_winner' => true,
                'has_losers_bracket' => true,
                'description' => 'Double elimination: Winner advances, loser goes to losers bracket'
            ];
            
        default:
            return [
                'type' => 'standard',
                'allows_draws' => false,
                'description' => 'Standard tournament scoring'
            ];
    }
}
