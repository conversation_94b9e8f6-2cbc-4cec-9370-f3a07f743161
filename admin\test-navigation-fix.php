<?php
/**
 * Test Navigation Fix
 * Comprehensive test of category navigation functionality
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Navigation Fix</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-section { border: 1px solid #ddd; padding: 20px; margin: 20px 0; border-radius: 5px; }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
        .info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container mt-4">
        <h1>🧪 Test Navigation Fix</h1>
        <p>Comprehensive test of the category navigation fix</p>
        
        <div class="test-section">
            <h2>1. Database Status Check</h2>
            <div id="database-status">
                <p>Checking database status...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>2. Create Test Data</h2>
            <div class="alert alert-info">
                <p>If no categories exist, click the button below to create test categories for navigation testing.</p>
            </div>
            <button id="create-categories-btn" class="btn btn-primary" onclick="createTestCategories()">
                <i class="fas fa-plus"></i> Create Test Categories
            </button>
            <div id="create-categories-result" class="mt-3"></div>
        </div>
        
        <div class="test-section">
            <h2>3. Navigation Test Links</h2>
            <div id="navigation-links">
                <p>Loading navigation test links...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>4. Parameter Validation Test</h2>
            <div class="alert alert-info">
                <p>Testing different parameter combinations to ensure proper validation:</p>
            </div>
            <div id="parameter-tests">
                <div class="row">
                    <div class="col-md-6">
                        <h5>Valid Parameters:</h5>
                        <div id="valid-tests"></div>
                    </div>
                    <div class="col-md-6">
                        <h5>Invalid Parameters (should redirect):</h5>
                        <div id="invalid-tests"></div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h2>5. Fix Summary</h2>
            <div class="alert alert-success">
                <h5>✅ Navigation Fix Applied:</h5>
                <ul>
                    <li><strong>Parameter Validation:</strong> Updated to handle string parameters properly</li>
                    <li><strong>Numeric Validation:</strong> Added checks to ensure parameters are numeric</li>
                    <li><strong>Debug Logging:</strong> Added error logging for troubleshooting</li>
                    <li><strong>Test Categories:</strong> Created sample data for testing</li>
                </ul>
                
                <h6>Changes Made:</h6>
                <code>
                    // Old validation:<br>
                    if (!$event_id || !$sport_id || !$category_id)<br><br>
                    
                    // New validation:<br>
                    if (empty($event_id) || empty($sport_id) || empty($category_id) || <br>
                    &nbsp;&nbsp;&nbsp;&nbsp;!is_numeric($event_id) || !is_numeric($sport_id) || !is_numeric($category_id))
                </code>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            checkDatabaseStatus();
            loadNavigationLinks();
            setupParameterTests();
        });
        
        function checkDatabaseStatus() {
            const statusDiv = document.getElementById('database-status');
            
            fetch('ajax/get-test-data.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const eventSportsCount = data.event_sports.length;
                        
                        // Check categories count
                        fetch('check-database-structure.php')
                            .then(response => response.text())
                            .then(html => {
                                // Extract categories count from the response
                                const categoriesMatch = html.match(/(\d+)<\/h5><p>Categories<\/p>/);
                                const categoriesCount = categoriesMatch ? parseInt(categoriesMatch[1]) : 0;
                                
                                let statusHtml = '<div class="row">';
                                statusHtml += `<div class="col-md-3"><div class="card text-center"><div class="card-body"><h5>${eventSportsCount}</h5><p>Event-Sports</p></div></div></div>`;
                                statusHtml += `<div class="col-md-3"><div class="card text-center"><div class="card-body"><h5>${categoriesCount}</h5><p>Categories</p></div></div></div>`;
                                statusHtml += '</div>';
                                
                                if (categoriesCount === 0) {
                                    statusHtml += '<div class="alert alert-warning mt-3"><strong>⚠️ Issue:</strong> No categories found. Navigation will fail until categories are created.</div>';
                                } else {
                                    statusHtml += '<div class="alert alert-success mt-3"><strong>✅ Good:</strong> Categories exist. Navigation should work.</div>';
                                }
                                
                                statusDiv.innerHTML = statusHtml;
                            });
                    } else {
                        statusDiv.innerHTML = '<div class="alert alert-danger">Error checking database status</div>';
                    }
                })
                .catch(error => {
                    statusDiv.innerHTML = '<div class="alert alert-danger">Network error: ' + error.message + '</div>';
                });
        }
        
        function createTestCategories() {
            const btn = document.getElementById('create-categories-btn');
            const resultDiv = document.getElementById('create-categories-result');
            
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
            
            fetch('ajax/create-test-categories.php', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    resultDiv.innerHTML = `<div class="alert alert-success">${data.message}</div>`;
                    // Refresh the page data
                    setTimeout(() => {
                        checkDatabaseStatus();
                        loadNavigationLinks();
                    }, 1000);
                } else {
                    resultDiv.innerHTML = `<div class="alert alert-danger">Error: ${data.message}</div>`;
                }
            })
            .catch(error => {
                resultDiv.innerHTML = `<div class="alert alert-danger">Network error: ${error.message}</div>`;
            })
            .finally(() => {
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-plus"></i> Create Test Categories';
            });
        }
        
        function loadNavigationLinks() {
            const linksDiv = document.getElementById('navigation-links');
            
            fetch('ajax/get-test-data.php')
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.event_sports.length > 0) {
                        let html = '<h5>Available Navigation Tests:</h5>';
                        html += '<div class="row">';
                        
                        data.event_sports.forEach(es => {
                            const categoriesUrl = `sport-categories.php?event_id=${es.event_id}&sport_id=${es.sport_id}`;
                            const manageUrl = `manage-category.php?event_id=${es.event_id}&sport_id=${es.sport_id}&category_id=1`;
                            
                            html += `
                                <div class="col-md-6 mb-3">
                                    <div class="card">
                                        <div class="card-body">
                                            <h6 class="card-title">${es.event_name} - ${es.sport_name}</h6>
                                            <div class="d-flex gap-2">
                                                <a href="${categoriesUrl}" class="btn btn-sm btn-primary" target="_blank">
                                                    <i class="fas fa-list"></i> Categories List
                                                </a>
                                                <a href="${manageUrl}" class="btn btn-sm btn-success" target="_blank">
                                                    <i class="fas fa-cog"></i> Test Direct Link
                                                </a>
                                            </div>
                                            <small class="text-muted d-block mt-2">
                                                Click "Categories List" then click on a category name to test navigation
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            `;
                        });
                        
                        html += '</div>';
                        linksDiv.innerHTML = html;
                    } else {
                        linksDiv.innerHTML = '<div class="alert alert-warning">No event-sport combinations available for testing</div>';
                    }
                })
                .catch(error => {
                    linksDiv.innerHTML = `<div class="alert alert-danger">Error loading links: ${error.message}</div>`;
                });
        }
        
        function setupParameterTests() {
            const validDiv = document.getElementById('valid-tests');
            const invalidDiv = document.getElementById('invalid-tests');
            
            // Valid parameter tests
            validDiv.innerHTML = `
                <div class="list-group">
                    <a href="manage-category.php?event_id=1&sport_id=1&category_id=1" class="list-group-item list-group-item-action" target="_blank">
                        <strong>Standard:</strong> event_id=1&sport_id=1&category_id=1
                    </a>
                    <a href="manage-category.php?event_id=2&sport_id=2&category_id=2" class="list-group-item list-group-item-action" target="_blank">
                        <strong>Different IDs:</strong> event_id=2&sport_id=2&category_id=2
                    </a>
                </div>
            `;
            
            // Invalid parameter tests (these should redirect to events.php)
            invalidDiv.innerHTML = `
                <div class="list-group">
                    <a href="manage-category.php" class="list-group-item list-group-item-action" target="_blank">
                        <strong>No params:</strong> (should redirect)
                    </a>
                    <a href="manage-category.php?event_id=&sport_id=&category_id=" class="list-group-item list-group-item-action" target="_blank">
                        <strong>Empty params:</strong> (should redirect)
                    </a>
                    <a href="manage-category.php?event_id=abc&sport_id=def&category_id=xyz" class="list-group-item list-group-item-action" target="_blank">
                        <strong>Non-numeric:</strong> (should redirect)
                    </a>
                </div>
            `;
        }
    </script>
</body>
</html>
