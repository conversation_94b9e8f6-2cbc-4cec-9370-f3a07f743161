<?php
/**
 * Shared Admin Styles Component
 * Common CSS styles for all admin pages
 */
?>

<!-- Font Awesome for Icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<!-- jQuery -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<style>
    :root {
        --primary-color: #2563eb;
        --primary-dark: #1d4ed8;
        --secondary-color: #10b981;
        --accent-color: #f59e0b;
        --danger-color: #ef4444;
        --success-color: #22c55e;
        --warning-color: #f59e0b;
        --info-color: #3b82f6;
        --dark-color: #1f2937;
        --light-color: #f8fafc;
        --border-color: #e5e7eb;
        --text-primary: #111827;
        --text-secondary: #6b7280;
        --sidebar-bg: #1e293b;
        --sidebar-hover: #334155;
        --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        --border-radius: 8px;
        --transition: all 0.3s ease;
    }

    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        background: var(--light-color);
        line-height: 1.6;
        color: var(--text-primary);
        margin-left: 280px;
        transition: margin-left 0.3s ease;
    }

    /* Sidebar Styles */
    .admin-sidebar {
        position: fixed;
        top: 0;
        left: 0;
        width: 280px;
        height: 100vh;
        background: var(--sidebar-bg);
        color: white;
        overflow-y: auto;
        z-index: 1000;
        box-shadow: var(--shadow-lg);
        transition: transform 0.3s ease;
    }

    .sidebar-header {
        padding: 24px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    }

    .sidebar-logo {
        display: flex;
        align-items: center;
        gap: 12px;
    }

    .sidebar-logo-icon {
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
    }

    .sidebar-logo-text h1 {
        font-size: 1.25rem;
        font-weight: 700;
        margin: 0;
    }

    .sidebar-logo-text p {
        font-size: 0.75rem;
        opacity: 0.8;
        margin: 0;
    }

    .sidebar-nav {
        padding: 20px 0;
    }

    .nav-section {
        margin-bottom: 24px;
    }

    .nav-section-title {
        padding: 0 20px 8px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: rgba(255, 255, 255, 0.6);
    }

    .nav-item {
        margin-bottom: 2px;
    }

    .nav-link {
        display: flex;
        align-items: center;
        padding: 12px 20px;
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        transition: var(--transition);
        border-left: 3px solid transparent;
    }

    .nav-link:hover,
    .nav-link.active {
        background: var(--sidebar-hover);
        color: white;
        border-left-color: var(--secondary-color);
    }

    .nav-link i {
        width: 20px;
        margin-right: 12px;
        text-align: center;
        font-size: 1rem;
    }

    /* Main Content Area */
    .admin-main {
        min-height: 100vh;
        padding: 0;
    }

    .admin-header {
        background: white;
        padding: 16px 24px;
        border-bottom: 1px solid var(--border-color);
        box-shadow: var(--shadow-sm);
        position: sticky;
        top: 0;
        z-index: 100;
    }

    .header-left {
        display: flex;
        align-items: center;
        gap: 16px;
    }

    .sidebar-toggle {
        background: none;
        border: none;
        font-size: 1.25rem;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 8px;
        border-radius: 4px;
        transition: var(--transition);
    }

    .sidebar-toggle:hover {
        background: var(--border-color);
        color: var(--text-primary);
    }

    .breadcrumb {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.875rem;
        color: var(--text-secondary);
    }

    .breadcrumb-item {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .header-right {
        display: flex;
        align-items: center;
        gap: 16px;
    }

    .admin-content {
        padding: 24px;
    }

    /* Page Header */
    .page-header {
        background: white;
        padding: 32px 24px;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        margin-bottom: 24px;
        border: 1px solid var(--border-color);
    }

    .page-title {
        font-size: 2rem;
        font-weight: 700;
        color: var(--text-primary);
        margin-bottom: 8px;
    }

    .page-description {
        color: var(--text-secondary);
        font-size: 1rem;
        margin: 0;
    }

    /* Cards */
    .card {
        background: white;
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-sm);
        border: 1px solid var(--border-color);
        margin-bottom: 24px;
        overflow: hidden;
    }

    .card-header {
        padding: 20px 24px;
        border-bottom: 1px solid var(--border-color);
        background: #f8fafc;
        font-weight: 600;
        color: var(--text-primary);
    }

    .card-body {
        padding: 24px;
    }

    /* Buttons */
    .btn {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 16px;
        background: var(--primary-color);
        color: white;
        text-decoration: none;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        font-size: 0.875rem;
        font-weight: 500;
        transition: var(--transition);
        box-shadow: var(--shadow-sm);
    }

    .btn:hover {
        background: var(--primary-dark);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    .btn-primary { background: var(--primary-color); }
    .btn-secondary { background: var(--text-secondary); }
    .btn-success { background: var(--success-color); }
    .btn-warning { background: var(--warning-color); }
    .btn-danger { background: var(--danger-color); }

    .btn-sm {
        padding: 6px 12px;
        font-size: 0.75rem;
    }

    .btn-lg {
        padding: 14px 20px;
        font-size: 1rem;
    }

    /* Tables */
    .table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 16px;
    }

    .table th,
    .table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }

    .table th {
        background: #f8fafc;
        font-weight: 600;
        color: var(--text-primary);
        font-size: 0.875rem;
    }

    .table-striped tbody tr:nth-child(odd) {
        background: #f9fafb;
    }

    /* Forms */
    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        color: var(--text-primary);
    }

    .form-control {
        width: 100%;
        padding: 10px 12px;
        border: 1px solid var(--border-color);
        border-radius: 6px;
        font-size: 0.875rem;
        transition: var(--transition);
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    }

    /* Responsive Design */
    @media (max-width: 1024px) {
        body {
            margin-left: 0;
        }

        .admin-sidebar {
            transform: translateX(-280px);
        }

        .admin-sidebar.show {
            transform: translateX(0);
        }
    }

    /* Sidebar Toggle Animation */
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 999;
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
    }

    .sidebar-overlay.show {
        opacity: 1;
        visibility: visible;
    }

    /* Modal Styles */
    .modal {
        display: none;
        position: fixed;
        z-index: 1050;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(2px);
        animation: fadeIn 0.3s ease;
    }

    .modal.show {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-dialog {
        background: white;
        border-radius: 12px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
        max-width: 600px;
        width: 90%;
        max-height: 90vh;
        overflow: hidden;
        transform: scale(0.9);
        transition: transform 0.3s ease;
        position: relative;
    }

    .modal.show .modal-dialog {
        transform: scale(1);
    }

    .modal-header {
        padding: 20px 24px 16px;
        border-bottom: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        justify-content: space-between;
        background: #f8fafc;
    }

    .modal-title {
        margin: 0;
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--text-primary);
    }

    .modal-close {
        background: none;
        border: none;
        font-size: 24px;
        color: var(--text-secondary);
        cursor: pointer;
        padding: 4px;
        border-radius: 4px;
        transition: all 0.2s ease;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-close:hover {
        background: var(--border-color);
        color: var(--text-primary);
    }

    .modal-body {
        padding: 24px;
        max-height: 60vh;
        overflow-y: auto;
    }

    .modal-footer {
        padding: 16px 24px 20px;
        border-top: 1px solid var(--border-color);
        display: flex;
        gap: 12px;
        justify-content: flex-end;
        background: #f8fafc;
    }

    .modal-loading {
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.9);
        z-index: 10;
        align-items: center;
        justify-content: center;
    }

    .modal-loading.show {
        display: flex;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid var(--border-color);
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }

    /* Modal Form Styles */
    .modal-form .form-group {
        margin-bottom: 20px;
    }

    .modal-form .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 20px;
    }

    .modal-form .form-row.single {
        grid-template-columns: 1fr;
    }

    .modal-form .form-control {
        width: 100%;
    }

    .modal-form .error-message {
        color: var(--danger-color);
        font-size: 0.875rem;
        margin-top: 4px;
        display: none;
    }

    .modal-form .error-message.show {
        display: block;
    }

    .modal-form .form-control.error {
        border-color: var(--danger-color);
        box-shadow: 0 0 0 2px rgba(220, 53, 69, 0.1);
    }

    /* Enhanced Form Section Styles */
    .form-section {
        margin-bottom: 32px;
        padding: 20px;
        background: #f8fafc;
        border-radius: 8px;
        border: 1px solid #e2e8f0;
    }

    .form-section:last-child {
        margin-bottom: 0;
    }

    .form-section-title {
        margin: 0 0 20px 0;
        font-size: 1rem;
        font-weight: 600;
        color: var(--text-primary);
        display: flex;
        align-items: center;
        gap: 8px;
        padding-bottom: 12px;
        border-bottom: 2px solid var(--primary-color);
    }

    .form-section-title i {
        color: var(--primary-color);
        font-size: 0.9rem;
    }

    .form-label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        color: var(--text-primary);
        font-size: 0.875rem;
    }

    .form-label .required {
        color: var(--danger-color);
        margin-left: 2px;
    }

    .form-control {
        width: 100%;
        padding: 12px 16px;
        border: 2px solid #e2e8f0;
        border-radius: 6px;
        font-size: 0.875rem;
        transition: all 0.2s ease;
        background: white;
    }

    .form-control:focus {
        outline: none;
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }

    .form-control::placeholder {
        color: #9ca3af;
    }

    .form-control:invalid {
        border-color: #fbbf24;
    }

    .form-control.error {
        border-color: var(--danger-color);
        box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    }

    /* Modal Actions Styling */
    .modal-footer .btn {
        padding: 12px 24px;
        font-weight: 500;
        border-radius: 6px;
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 0.875rem;
    }

    .modal-footer .btn-secondary {
        background: #6b7280;
        color: white;
        border: 2px solid #6b7280;
    }

    .modal-footer .btn-secondary:hover {
        background: #4b5563;
        border-color: #4b5563;
        transform: translateY(-1px);
    }

    .modal-footer .btn-primary {
        background: var(--primary-color);
        color: white;
        border: 2px solid var(--primary-color);
    }

    .modal-footer .btn-primary:hover {
        background: var(--primary-dark);
        border-color: var(--primary-dark);
        transform: translateY(-1px);
    }

    .modal-footer .btn:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    /* Color Picker Styles */
    .color-picker-container {
        display: flex;
        flex-direction: column;
        gap: 12px;
    }

    .color-input {
        width: 60px;
        height: 40px;
        padding: 0;
        border: 2px solid #ddd;
        border-radius: 6px;
        cursor: pointer;
        background: none;
    }

    .color-input::-webkit-color-swatch-wrapper {
        padding: 0;
        border: none;
        border-radius: 4px;
    }

    .color-input::-webkit-color-swatch {
        border: none;
        border-radius: 4px;
    }

    .color-preview {
        width: 100%;
        height: 30px;
        border: 2px solid #ddd;
        border-radius: 6px;
        background-color: #3498db;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: 500;
        font-size: 12px;
        text-shadow: 0 1px 2px rgba(0,0,0,0.3);
    }

    .predefined-colors {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;
        margin-top: 4px;
    }

    .color-option {
        width: 32px;
        height: 32px;
        border-radius: 6px;
        cursor: pointer;
        border: 2px solid #ddd;
        transition: all 0.2s ease;
        position: relative;
    }

    .color-option:hover {
        transform: scale(1.1);
        border-color: #333;
        box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .color-option.selected {
        border-color: #333;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
    }

    .color-option.selected::after {
        content: '✓';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-weight: bold;
        text-shadow: 0 1px 2px rgba(0,0,0,0.5);
        font-size: 14px;
    }

    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 8px;
        align-items: center;
    }

    .btn-modal-trigger {
        background: var(--primary-color);
        color: white;
        border: none;
        padding: 12px 20px;
        border-radius: 6px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.2s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        font-size: 0.875rem;
        box-shadow: var(--shadow-sm);
    }

    .btn-modal-trigger:hover {
        background: var(--primary-dark);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    .btn-edit {
        background: var(--warning-color);
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .btn-edit:hover {
        background: #e0a800;
    }

    .btn-delete {
        background: var(--danger-color);
        color: white;
        border: none;
        padding: 6px 12px;
        border-radius: 4px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s ease;
    }

    .btn-delete:hover {
        background: #c82333;
    }

    /* Alert Messages */
    .alert {
        padding: 12px 16px;
        border-radius: 6px;
        margin-bottom: 20px;
        border: 1px solid transparent;
    }

    .alert-success {
        background: #d1fae5;
        border-color: #a7f3d0;
        color: #065f46;
    }

    .alert-error {
        background: #fee2e2;
        border-color: #fecaca;
        color: #991b1b;
    }

    .alert-warning {
        background: #fef3c7;
        border-color: #fde68a;
        color: #92400e;
    }

    /* Responsive Modal */
    @media (max-width: 768px) {
        .modal-dialog {
            width: 95%;
            max-height: 95vh;
            margin: 10px;
        }

        .modal-form .form-row {
            grid-template-columns: 1fr;
        }

        .modal-header,
        .modal-body,
        .modal-footer {
            padding-left: 16px;
            padding-right: 16px;
        }
    }

    /* Enhanced Tournament Bracket Styles */
    .sc-bracket-container {
        background: #ffffff;
        border-radius: 12px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        padding: 25px;
        margin: 20px 0;
        border: 1px solid #e3f2fd;
    }

    .sc-bracket-container.judged {
        border: 1px solid #ffd54f;
        background: linear-gradient(135deg, #fffef7, #fff9c4);
    }

    .sc-bracket-container.academic {
        border: 1px solid #81c784;
        background: linear-gradient(135deg, #f1f8e9, #e8f5e8);
    }

    .sc-bracket-notice {
        background: linear-gradient(135deg, #1976d2, #42a5f5);
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        margin-bottom: 25px;
        font-weight: 600;
        text-align: center;
        box-shadow: 0 2px 10px rgba(25, 118, 210, 0.3);
    }

    .sc-bracket-notice.judged-notice {
        background: linear-gradient(135deg, #ff8f00, #ffb300);
        box-shadow: 0 2px 10px rgba(255, 143, 0, 0.3);
    }

    .sc-bracket-notice.academic-notice {
        background: linear-gradient(135deg, #388e3c, #66bb6a);
        box-shadow: 0 2px 10px rgba(56, 142, 60, 0.3);
    }

    .sc-bracket-notice i {
        margin-right: 8px;
        font-size: 1.1em;
    }

    /* Judged Competition Styles */
    .judging-criteria {
        background: #fff8e1;
        border: 1px solid #ffcc02;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 25px;
    }

    .judging-criteria h4 {
        margin: 0 0 15px 0;
        color: #e65100;
        font-size: 1.1em;
    }

    .criteria-list {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }

    .criterion {
        background: #ff8f00;
        color: white;
        padding: 6px 12px;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    /* Academic Competition Styles */
    .academic-info {
        background: #e8f5e8;
        border: 1px solid #4caf50;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 25px;
    }

    .academic-info h4 {
        margin: 0 0 15px 0;
        color: #2e7d32;
        font-size: 1.1em;
    }

    .format-details p {
        margin: 8px 0;
        color: #1b5e20;
        font-size: 0.9em;
    }

    /* Round Headers */
    .sc-round-header.judged-header {
        background: linear-gradient(135deg, #ff8f00, #ffb300);
        color: white;
    }

    .sc-round-header.academic-header {
        background: linear-gradient(135deg, #388e3c, #66bb6a);
        color: white;
    }

    /* Match Cards */
    .sc-match-card.judged-match {
        border-left: 4px solid #ff8f00;
        background: linear-gradient(135deg, #fffef7, #fff9c4);
    }

    .sc-match-card.academic-match {
        border-left: 4px solid #388e3c;
        background: linear-gradient(135deg, #f1f8e9, #e8f5e8);
    }

    .judged-match-header {
        background: #fff8e1;
        border-bottom: 1px solid #ffcc02;
    }

    .academic-match-header {
        background: #e8f5e8;
        border-bottom: 1px solid #4caf50;
    }

    .performer-info {
        text-align: center;
        padding: 20px;
    }

    .performer-name {
        font-size: 1.2em;
        font-weight: 600;
        color: #e65100;
        margin-bottom: 10px;
    }

    .performance-score {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 10px;
    }

    .score-label {
        font-weight: 500;
        color: #bf360c;
    }

    .score-value {
        background: #ff8f00;
        color: white;
        padding: 4px 12px;
        border-radius: 15px;
        font-weight: 600;
    }

    .academic-team {
        flex: 1;
        text-align: center;
        padding: 15px;
    }

    .academic-team .team-name {
        font-weight: 600;
        color: #2e7d32;
        margin-bottom: 8px;
    }

    .academic-team .team-score {
        background: #388e3c;
        color: white;
        padding: 6px 12px;
        border-radius: 15px;
        font-weight: 600;
        display: inline-block;
    }

    .academic-content {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }

    .vs-separator {
        background: #2e7d32;
        color: white;
        padding: 8px 12px;
        border-radius: 50%;
        font-weight: 600;
        font-size: 0.875rem;
    }

    /* Action Button Styles for Different Formats */
    .sc-btn i.fa-star {
        color: #ff8f00;
    }

    .sc-btn i.fa-brain {
        color: #388e3c;
    }

    .sc-btn:hover i.fa-star {
        color: #fff;
    }

    .sc-btn:hover i.fa-brain {
        color: #fff;
    }
</style>
