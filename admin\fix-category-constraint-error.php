<?php
/**
 * Fix Category Foreign Key Constraint Error
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Fix Category Constraint Error</h1>";

echo "<div style='background: #f8d7da; padding: 20px; border-radius: 8px; border-left: 4px solid #dc3545; margin-bottom: 20px;'>";
echo "<h2 style='margin-top: 0;'>❌ Error Detected</h2>";
echo "<p><strong>SQLSTATE[23000]: Integrity constraint violation: 1452 Cannot add or update a child row: a foreign key constraint fails</strong></p>";
echo "<p>The error indicates that the <code>event_sport_id</code> being used doesn't exist in the <code>event_sports</code> table.</p>";
echo "</div>";

try {
    echo "<h2>1. Diagnose the Problem</h2>";
    
    // Check the specific event_sport_id being used
    $event_id = 4;
    $sport_id = 40;
    
    echo "<h3>Checking event_id=$event_id, sport_id=$sport_id</h3>";
    
    // Check if the event exists
    $stmt = $conn->prepare("SELECT id, name FROM events WHERE id = ?");
    $stmt->execute([$event_id]);
    $event = $stmt->fetch();
    
    if ($event) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Event exists:</strong> {$event['name']} (ID: {$event['id']})</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Event does not exist:</strong> ID $event_id</p>";
        echo "</div>";
    }
    
    // Check if the sport exists
    $stmt = $conn->prepare("SELECT id, name FROM sports WHERE id = ?");
    $stmt->execute([$sport_id]);
    $sport = $stmt->fetch();
    
    if ($sport) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Sport exists:</strong> {$sport['name']} (ID: {$sport['id']})</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Sport does not exist:</strong> ID $sport_id</p>";
        echo "</div>";
    }
    
    // Check if the event_sport relationship exists
    $stmt = $conn->prepare("SELECT id FROM event_sports WHERE event_id = ? AND sport_id = ?");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    
    if ($event_sport) {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Event-Sport relationship exists:</strong> event_sport_id = {$event_sport['id']}</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>❌ <strong>Event-Sport relationship missing:</strong> No entry in event_sports for event_id=$event_id, sport_id=$sport_id</p>";
        echo "</div>";
        
        echo "<h3>🔧 Fix: Create Missing Event-Sport Relationship</h3>";
        
        if ($event && $sport) {
            echo "<form method='post' style='margin: 20px 0;'>";
            echo "<input type='hidden' name='create_event_sport' value='1'>";
            echo "<input type='hidden' name='event_id' value='$event_id'>";
            echo "<input type='hidden' name='sport_id' value='$sport_id'>";
            echo "<button type='submit' style='background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>";
            echo "Create Event-Sport Relationship";
            echo "</button>";
            echo "</form>";
        }
    }
    
    echo "<h2>2. Check All Event-Sport Relationships</h2>";
    
    $stmt = $conn->prepare("
        SELECT 
            es.id as event_sport_id,
            es.event_id,
            es.sport_id,
            e.name as event_name,
            s.name as sport_name,
            COUNT(sc.id) as categories_count
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN sport_categories sc ON es.id = sc.event_sport_id
        GROUP BY es.id, es.event_id, es.sport_id, e.name, s.name
        ORDER BY e.name, s.name
    ");
    $stmt->execute();
    $event_sports = $stmt->fetchAll();
    
    if (empty($event_sports)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<p>⚠️ <strong>No event-sport relationships found.</strong> You need to add sports to events first.</p>";
        echo "</div>";
    } else {
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #007bff; color: white;'>";
        echo "<th>Event Sport ID</th><th>Event</th><th>Sport</th><th>Categories</th><th>Actions</th>";
        echo "</tr>";
        
        foreach ($event_sports as $es) {
            $bg_color = ($es['event_id'] == $event_id && $es['sport_id'] == $sport_id) ? '#fff3cd' : 'white';
            echo "<tr style='background: $bg_color;'>";
            echo "<td>{$es['event_sport_id']}</td>";
            echo "<td>{$es['event_name']}</td>";
            echo "<td>{$es['sport_name']}</td>";
            echo "<td style='text-align: center;'>{$es['categories_count']}</td>";
            
            $categories_url = "sport-categories.php?event_id={$es['event_id']}&sport_id={$es['sport_id']}";
            echo "<td><a href='$categories_url' target='_blank' style='background: #007bff; color: white; padding: 5px 10px; text-decoration: none; border-radius: 3px;'>Manage Categories</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // Handle form submission to create event-sport relationship
    if (isset($_POST['create_event_sport'])) {
        $event_id = intval($_POST['event_id']);
        $sport_id = intval($_POST['sport_id']);
        
        echo "<h3>Creating Event-Sport Relationship</h3>";
        
        try {
            $stmt = $conn->prepare("
                INSERT INTO event_sports (event_id, sport_id, max_teams, venue, status)
                VALUES (?, ?, 8, 'Main Sports Hall', 'registration')
            ");
            $stmt->execute([$event_id, $sport_id]);
            $new_event_sport_id = $conn->lastInsertId();
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>✅ <strong>Success!</strong> Created event-sport relationship with ID: $new_event_sport_id</p>";
            echo "<p>You can now add categories to this sport.</p>";
            echo "</div>";
            
            // Refresh the page to show updated data
            echo "<script>setTimeout(() => window.location.reload(), 2000);</script>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>❌ <strong>Error creating relationship:</strong> " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
    echo "<h2>3. Test Category Creation</h2>";
    
    // Find a valid event_sport_id for testing
    $stmt = $conn->prepare("
        SELECT es.id as event_sport_id, e.name as event_name, s.name as sport_name
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        ORDER BY es.id DESC
        LIMIT 1
    ");
    $stmt->execute();
    $test_es = $stmt->fetch();
    
    if ($test_es) {
        echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px;'>";
        echo "<h4>Test with Valid Event-Sport ID</h4>";
        echo "<p><strong>Event-Sport ID:</strong> {$test_es['event_sport_id']}</p>";
        echo "<p><strong>Event:</strong> {$test_es['event_name']}</p>";
        echo "<p><strong>Sport:</strong> {$test_es['sport_name']}</p>";
        
        echo "<form method='post' style='margin: 15px 0;'>";
        echo "<input type='hidden' name='test_category_creation' value='1'>";
        echo "<input type='hidden' name='event_sport_id' value='{$test_es['event_sport_id']}'>";
        echo "<button type='submit' style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer;'>";
        echo "Test Category Creation";
        echo "</button>";
        echo "</form>";
        echo "</div>";
    }
    
    // Handle test category creation
    if (isset($_POST['test_category_creation'])) {
        $event_sport_id = intval($_POST['event_sport_id']);
        
        echo "<h3>Testing Category Creation</h3>";
        
        try {
            $stmt = $conn->prepare("
                INSERT INTO sport_categories 
                (event_sport_id, category_name, category_type, referee_name, referee_email, venue)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            
            $test_category_name = "Test Category " . date('His');
            $stmt->execute([
                $event_sport_id,
                $test_category_name,
                'mixed',
                'Test Referee',
                '<EMAIL>',
                'Test Venue'
            ]);
            
            $category_id = $conn->lastInsertId();
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>✅ <strong>Success!</strong> Created test category: $test_category_name (ID: $category_id)</p>";
            echo "<p>The category creation is working properly now.</p>";
            echo "</div>";
            
        } catch (Exception $e) {
            echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
            echo "<p>❌ <strong>Error creating category:</strong> " . $e->getMessage() . "</p>";
            echo "</div>";
        }
    }
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<h2>4. Summary & Next Steps</h2>";

echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;'>";
echo "<h3>✅ How to Fix the Constraint Error</h3>";
echo "<ol>";
echo "<li><strong>Ensure Event-Sport Relationship Exists:</strong> Before adding categories, make sure the sport is added to the event in the event_sports table.</li>";
echo "<li><strong>Use Correct event_sport_id:</strong> The category creation form should use the correct event_sport_id from the event_sports table.</li>";
echo "<li><strong>Check sport-categories.php:</strong> Make sure the page is getting the correct event_sport_id value.</li>";
echo "<li><strong>Test Category Creation:</strong> Use the working event-sport relationships to test category creation.</li>";
echo "</ol>";
echo "</div>";
?>
