<?php
/**
 * Unified Tournament System Setup
 * SC_IMS Sports Competition and Event Management System
 * 
 * Sets up and configures the unified tournament system with all required
 * tournament formats, database schema updates, and system integration.
 */

require_once '../config/database.php';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament System Setup - SC_IMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .setup-step { margin-bottom: 1.5rem; padding: 1rem; border-radius: 0.5rem; border: 1px solid #dee2e6; }
        .step-success { background-color: #d4edda; border-color: #c3e6cb; }
        .step-error { background-color: #f8d7da; border-color: #f5c6cb; }
        .step-warning { background-color: #fff3cd; border-color: #ffeaa7; }
        .step-info { background-color: #d1ecf1; border-color: #bee5eb; }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="row">
            <div class="col-12">
                <h1 class="mb-4">
                    <i class="fas fa-cogs"></i> Unified Tournament System Setup
                </h1>
                
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    This setup will configure the unified tournament system with all required components.
                </div>
                
                <?php
                $setupSteps = [];
                
                // Step 1: Ensure Tournament Formats Table
                echo "<div class='setup-step'>";
                echo "<h3><i class='fas fa-database'></i> Step 1: Tournament Formats Configuration</h3>";
                $formatStep = setupTournamentFormats($conn);
                displaySetupStep($formatStep);
                $setupSteps[] = $formatStep;
                echo "</div>";
                
                // Step 2: Update Database Schema
                echo "<div class='setup-step'>";
                echo "<h3><i class='fas fa-table'></i> Step 2: Database Schema Updates</h3>";
                $schemaStep = updateDatabaseSchema($conn);
                displaySetupStep($schemaStep);
                $setupSteps[] = $schemaStep;
                echo "</div>";
                
                // Step 3: Configure Default Tournament Settings
                echo "<div class='setup-step'>";
                echo "<h3><i class='fas fa-sliders-h'></i> Step 3: Default Tournament Settings</h3>";
                $settingsStep = configureDefaultSettings($conn);
                displaySetupStep($settingsStep);
                $setupSteps[] = $settingsStep;
                echo "</div>";
                
                // Step 4: Validate System Integration
                echo "<div class='setup-step'>";
                echo "<h3><i class='fas fa-check-double'></i> Step 4: System Integration Validation</h3>";
                $validationStep = validateSystemIntegration($conn);
                displaySetupStep($validationStep);
                $setupSteps[] = $validationStep;
                echo "</div>";
                
                // Setup Summary
                echo "<div class='setup-step'>";
                echo "<h3><i class='fas fa-flag-checkered'></i> Setup Summary</h3>";
                displaySetupSummary($setupSteps);
                echo "</div>";
                ?>
                
                <div class="mt-4">
                    <a href="test-unified-tournament-system.php" class="btn btn-primary">
                        <i class="fas fa-vial"></i> Run System Tests
                    </a>
                    <a href="manage-category.php" class="btn btn-success">
                        <i class="fas fa-play"></i> Start Using Tournament System
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

<?php

function setupTournamentFormats($conn) {
    try {
        // Check if tournament_formats table exists
        $stmt = $conn->query("SHOW TABLES LIKE 'tournament_formats'");
        $tableExists = $stmt->rowCount() > 0;
        
        if (!$tableExists) {
            // Create tournament_formats table
            $sql = "CREATE TABLE tournament_formats (
                id INT AUTO_INCREMENT PRIMARY KEY,
                name VARCHAR(255) NOT NULL,
                code VARCHAR(50) NOT NULL UNIQUE,
                description TEXT,
                sport_type_category ENUM('traditional', 'individual', 'academic', 'judged', 'performance', 'all') DEFAULT 'all',
                min_participants INT DEFAULT 2,
                max_participants INT NULL,
                requires_seeding BOOLEAN DEFAULT FALSE,
                supports_byes BOOLEAN DEFAULT TRUE,
                advancement_type ENUM('elimination', 'points', 'ranking', 'hybrid') DEFAULT 'elimination',
                rounds_formula VARCHAR(255),
                matches_formula VARCHAR(255),
                algorithm_class VARCHAR(100),
                configuration JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            )";
            $conn->exec($sql);
        }
        
        // Insert comprehensive tournament formats
        $formats = [
            [
                'name' => 'Single Elimination',
                'code' => 'single_elimination',
                'description' => 'Traditional knockout tournament where participants are eliminated after one loss',
                'sport_type_category' => 'all',
                'min_participants' => 2,
                'max_participants' => null,
                'requires_seeding' => true,
                'supports_byes' => true,
                'advancement_type' => 'elimination',
                'rounds_formula' => 'ceil(log2(n))',
                'matches_formula' => 'n-1',
                'algorithm_class' => 'SingleEliminationAlgorithm',
                'configuration' => '{"bracket_seeding": true, "third_place_match": false}'
            ],
            [
                'name' => 'Double Elimination',
                'code' => 'double_elimination',
                'description' => 'Two-bracket system with winner\'s and loser\'s brackets',
                'sport_type_category' => 'all',
                'min_participants' => 3,
                'max_participants' => null,
                'requires_seeding' => true,
                'supports_byes' => true,
                'advancement_type' => 'elimination',
                'rounds_formula' => 'ceil(log2(n))*2-1',
                'matches_formula' => '2*n-2',
                'algorithm_class' => 'DoubleEliminationAlgorithm',
                'configuration' => '{"winners_bracket": true, "losers_bracket": true}'
            ],
            [
                'name' => 'Round Robin',
                'code' => 'round_robin',
                'description' => 'Every participant plays every other participant once',
                'sport_type_category' => 'all',
                'min_participants' => 3,
                'max_participants' => 16,
                'requires_seeding' => false,
                'supports_byes' => false,
                'advancement_type' => 'points',
                'rounds_formula' => '1',
                'matches_formula' => 'n*(n-1)/2',
                'algorithm_class' => 'RoundRobinAlgorithm',
                'configuration' => '{"points_win": 3, "points_draw": 1, "points_loss": 0}'
            ],
            [
                'name' => 'Multi-Stage Tournament',
                'code' => 'multi_stage',
                'description' => 'Group stage round robin followed by single elimination playoffs',
                'sport_type_category' => 'traditional',
                'min_participants' => 8,
                'max_participants' => null,
                'requires_seeding' => true,
                'supports_byes' => true,
                'advancement_type' => 'hybrid',
                'rounds_formula' => 'variable',
                'matches_formula' => 'variable',
                'algorithm_class' => 'MultiStageAlgorithm',
                'configuration' => '{"groups_count": 4, "teams_advance_per_group": 2, "group_points_win": 3}'
            ],
            [
                'name' => 'Swiss System',
                'code' => 'swiss_system',
                'description' => 'Pairing system where participants with similar records compete',
                'sport_type_category' => 'academic',
                'min_participants' => 4,
                'max_participants' => null,
                'requires_seeding' => false,
                'supports_byes' => true,
                'advancement_type' => 'points',
                'rounds_formula' => 'ceil(log2(n))',
                'matches_formula' => 'n*rounds/2',
                'algorithm_class' => 'SwissSystemAlgorithm',
                'configuration' => '{"avoid_rematches": true, "points_win": 1, "points_draw": 0.5}'
            ]
        ];
        
        $insertedCount = 0;
        foreach ($formats as $format) {
            $sql = "INSERT IGNORE INTO tournament_formats 
                    (name, code, description, sport_type_category, min_participants, max_participants,
                     requires_seeding, supports_byes, advancement_type, rounds_formula, matches_formula,
                     algorithm_class, configuration)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
            
            $stmt = $conn->prepare($sql);
            $result = $stmt->execute([
                $format['name'], $format['code'], $format['description'], $format['sport_type_category'],
                $format['min_participants'], $format['max_participants'], $format['requires_seeding'],
                $format['supports_byes'], $format['advancement_type'], $format['rounds_formula'],
                $format['matches_formula'], $format['algorithm_class'], $format['configuration']
            ]);
            
            if ($result && $stmt->rowCount() > 0) {
                $insertedCount++;
            }
        }
        
        return [
            'success' => true,
            'message' => "Tournament formats configured successfully. Inserted $insertedCount new formats.",
            'details' => [
                'table_existed' => $tableExists,
                'formats_inserted' => $insertedCount,
                'total_formats' => count($formats)
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to setup tournament formats: ' . $e->getMessage(),
            'details' => ['error' => $e->getMessage()]
        ];
    }
}

function updateDatabaseSchema($conn) {
    try {
        $updates = [];
        
        // Add tournament_format_id to event_sports if not exists
        try {
            $conn->exec("ALTER TABLE event_sports ADD COLUMN tournament_format_id INT NULL");
            $updates[] = 'Added tournament_format_id to event_sports';
        } catch (Exception $e) {
            // Column probably already exists
        }
        
        // Add foreign key constraint
        try {
            $conn->exec("ALTER TABLE event_sports ADD FOREIGN KEY (tournament_format_id) REFERENCES tournament_formats(id)");
            $updates[] = 'Added foreign key constraint for tournament_format_id';
        } catch (Exception $e) {
            // Constraint probably already exists
        }
        
        // Ensure matches table has tournament-related columns
        $matchColumns = [
            'tournament_structure_id' => 'INT NULL',
            'tournament_round_id' => 'INT NULL',
            'bracket_position' => 'VARCHAR(50) NULL',
            'is_bye_match' => 'BOOLEAN DEFAULT FALSE'
        ];
        
        foreach ($matchColumns as $column => $definition) {
            try {
                $conn->exec("ALTER TABLE matches ADD COLUMN $column $definition");
                $updates[] = "Added $column to matches table";
            } catch (Exception $e) {
                // Column probably already exists
            }
        }
        
        return [
            'success' => true,
            'message' => 'Database schema updated successfully',
            'details' => [
                'updates_applied' => $updates,
                'update_count' => count($updates)
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to update database schema: ' . $e->getMessage(),
            'details' => ['error' => $e->getMessage()]
        ];
    }
}

function configureDefaultSettings($conn) {
    try {
        // Set default tournament formats for existing event sports
        $sql = "UPDATE event_sports 
                SET tournament_format_id = (
                    SELECT id FROM tournament_formats 
                    WHERE code = 'single_elimination' 
                    LIMIT 1
                )
                WHERE tournament_format_id IS NULL";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        $updatedRows = $stmt->rowCount();
        
        return [
            'success' => true,
            'message' => "Default settings configured. Updated $updatedRows event sports with default tournament format.",
            'details' => [
                'event_sports_updated' => $updatedRows,
                'default_format' => 'single_elimination'
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'Failed to configure default settings: ' . $e->getMessage(),
            'details' => ['error' => $e->getMessage()]
        ];
    }
}

function validateSystemIntegration($conn) {
    try {
        $validations = [];
        
        // Check if all required classes exist
        $requiredClasses = [
            'UnifiedBracketEngine',
            'AutomaticBracketGenerator',
            'MatchScheduler',
            'TournamentFormatSelector',
            'TournamentAlgorithmFactory'
        ];
        
        $missingClasses = [];
        foreach ($requiredClasses as $class) {
            $classFile = '../includes/' . strtolower(str_replace(['Engine', 'Generator', 'Scheduler', 'Selector', 'Factory'], ['_engine', '_generator', '_scheduler', '_selector', '_factory'], $class)) . '.php';
            
            if (file_exists($classFile)) {
                require_once $classFile;
                if (class_exists($class)) {
                    $validations[] = "$class: Available";
                } else {
                    $missingClasses[] = "$class: Class not found in file";
                }
            } else {
                $missingClasses[] = "$class: File not found";
            }
        }
        
        // Check tournament algorithms
        require_once '../includes/tournament_algorithms.php';
        $algorithmClasses = [
            'SingleEliminationAlgorithm',
            'DoubleEliminationAlgorithm',
            'RoundRobinAlgorithm',
            'SwissSystemAlgorithm',
            'MultiStageAlgorithm'
        ];
        
        foreach ($algorithmClasses as $algorithm) {
            if (class_exists($algorithm)) {
                $validations[] = "$algorithm: Available";
            } else {
                $missingClasses[] = "$algorithm: Not found";
            }
        }
        
        $success = empty($missingClasses);
        
        return [
            'success' => $success,
            'message' => $success ? 'All system components validated successfully' : 'Some components are missing',
            'details' => [
                'available_components' => $validations,
                'missing_components' => $missingClasses,
                'integration_ready' => $success
            ]
        ];
        
    } catch (Exception $e) {
        return [
            'success' => false,
            'message' => 'System integration validation failed: ' . $e->getMessage(),
            'details' => ['error' => $e->getMessage()]
        ];
    }
}

function displaySetupStep($step) {
    $class = $step['success'] ? 'step-success' : 'step-error';
    $icon = $step['success'] ? 'fa-check-circle text-success' : 'fa-times-circle text-danger';
    
    echo "<div class='$class'>";
    echo "<h5><i class='fas $icon'></i> " . ($step['success'] ? 'SUCCESS' : 'FAILED') . "</h5>";
    echo "<p>" . htmlspecialchars($step['message']) . "</p>";
    
    if (!empty($step['details'])) {
        echo "<details>";
        echo "<summary>View Details</summary>";
        echo "<pre class='mt-2'>" . htmlspecialchars(json_encode($step['details'], JSON_PRETTY_PRINT)) . "</pre>";
        echo "</details>";
    }
    echo "</div>";
}

function displaySetupSummary($steps) {
    $totalSteps = count($steps);
    $successfulSteps = count(array_filter($steps, function($step) { return $step['success']; }));
    $failedSteps = $totalSteps - $successfulSteps;
    
    $overallSuccess = $failedSteps === 0;
    $class = $overallSuccess ? 'step-success' : 'step-error';
    $icon = $overallSuccess ? 'fa-trophy text-success' : 'fa-exclamation-triangle text-warning';
    
    echo "<div class='$class'>";
    echo "<h5><i class='fas $icon'></i> Setup Complete</h5>";
    echo "<p><strong>Steps Completed:</strong> $successfulSteps / $totalSteps</p>";
    echo "<p><strong>Success Rate:</strong> " . round(($successfulSteps / $totalSteps) * 100, 1) . "%</p>";
    
    if ($overallSuccess) {
        echo "<p class='mb-0'><strong>🎉 Setup completed successfully! The unified tournament system is ready to use.</strong></p>";
    } else {
        echo "<p class='mb-0'><strong>⚠️ Setup completed with $failedSteps failed step(s). Please review and fix the issues.</strong></p>";
    }
    echo "</div>";
}
?>
