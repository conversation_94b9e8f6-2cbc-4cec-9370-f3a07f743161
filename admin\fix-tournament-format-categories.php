<?php
/**
 * Fix Tournament Format Categories
 * Corrects the sport_type_category values and filtering logic
 */

require_once '../config/database.php';
require_once '../includes/functions.php';

$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Fixing Tournament Format Categories</h1>";

try {
    // Step 1: Check current table structure
    echo "<h2>Step 1: Checking Table Structure</h2>";
    
    $stmt = $conn->prepare("SHOW COLUMNS FROM tournament_formats LIKE 'sport_type_category'");
    $stmt->execute();
    $column = $stmt->fetch();
    
    if ($column) {
        echo "<p>✓ sport_type_category column exists: {$column['Type']}</p>";
        
        // Check if it's an ENUM with the right values
        if (strpos($column['Type'], 'enum') !== false) {
            echo "<p>✓ Column is ENUM type</p>";
            
            // Update ENUM to include all needed values
            echo "<p>Updating ENUM values to include all sport types...</p>";
            $conn->exec("ALTER TABLE tournament_formats MODIFY COLUMN sport_type_category ENUM('traditional', 'team', 'individual', 'academic', 'judged', 'performance', 'all') NOT NULL DEFAULT 'traditional'");
            echo "<p style='color: green;'>✓ Updated ENUM values</p>";
        }
    } else {
        echo "<p style='color: red;'>❌ sport_type_category column not found</p>";
        exit;
    }
    
    // Step 2: Clear existing incorrect data
    echo "<h2>Step 2: Clearing Existing Tournament Formats</h2>";
    
    $stmt = $conn->prepare("SELECT COUNT(*) FROM tournament_formats");
    $stmt->execute();
    $count = $stmt->fetchColumn();
    
    if ($count > 0) {
        echo "<p>Found {$count} existing tournament formats. Clearing...</p>";
        $conn->exec("DELETE FROM tournament_formats");
        echo "<p style='color: green;'>✓ Cleared existing formats</p>";
    }
    
    // Step 3: Insert correct tournament formats with proper categories
    echo "<h2>Step 3: Creating Tournament Formats with Correct Categories</h2>";
    
    $tournament_formats = [
        // Traditional Sports Formats
        [
            'name' => 'Single Elimination',
            'code' => 'single_elimination',
            'description' => 'Traditional knockout tournament where teams/participants are eliminated after one loss.',
            'sport_type_category' => 'traditional',
            'min_participants' => 2,
            'max_participants' => null
        ],
        [
            'name' => 'Double Elimination',
            'code' => 'double_elimination',
            'description' => 'Two-bracket system with winner\'s and loser\'s brackets.',
            'sport_type_category' => 'traditional',
            'min_participants' => 3,
            'max_participants' => null
        ],
        [
            'name' => 'Round Robin',
            'code' => 'round_robin',
            'description' => 'Every team/participant plays every other team/participant once.',
            'sport_type_category' => 'traditional',
            'min_participants' => 3,
            'max_participants' => 16
        ],
        [
            'name' => 'Multi-Stage Tournament',
            'code' => 'multi_stage',
            'description' => 'Combination of group stage followed by elimination rounds.',
            'sport_type_category' => 'traditional',
            'min_participants' => 8,
            'max_participants' => null
        ],
        
        // Academic Sports Formats
        [
            'name' => 'Swiss System',
            'code' => 'swiss_system',
            'description' => 'Pairing system commonly used for academic competitions.',
            'sport_type_category' => 'academic',
            'min_participants' => 4,
            'max_participants' => null
        ],
        [
            'name' => 'Knockout Rounds',
            'code' => 'knockout_rounds',
            'description' => 'Academic elimination tournament with question pools and time limits.',
            'sport_type_category' => 'academic',
            'min_participants' => 4,
            'max_participants' => null
        ],
        [
            'name' => 'Quiz Bowl Format',
            'code' => 'quiz_bowl',
            'description' => 'Round robin format specifically designed for quiz bowl competitions.',
            'sport_type_category' => 'academic',
            'min_participants' => 3,
            'max_participants' => 12
        ],
        [
            'name' => 'Academic Round Robin',
            'code' => 'academic_round_robin',
            'description' => 'Round robin tournament optimized for academic competitions.',
            'sport_type_category' => 'academic',
            'min_participants' => 3,
            'max_participants' => 16
        ],
        
        // Judged Sports Formats
        [
            'name' => 'Judged Rounds',
            'code' => 'judged_rounds',
            'description' => 'Multiple judged rounds with scoring criteria.',
            'sport_type_category' => 'judged',
            'min_participants' => 3,
            'max_participants' => null
        ],
        [
            'name' => 'Performance Competition',
            'code' => 'performance_competition',
            'description' => 'Structured performance competition with multiple rounds.',
            'sport_type_category' => 'judged',
            'min_participants' => 3,
            'max_participants' => null
        ],
        [
            'name' => 'Talent Showcase',
            'code' => 'talent_showcase',
            'description' => 'Showcase format with multiple performance rounds and audience voting.',
            'sport_type_category' => 'judged',
            'min_participants' => 3,
            'max_participants' => 50
        ],
        [
            'name' => 'Artistic Judging',
            'code' => 'artistic_judging',
            'description' => 'Comprehensive artistic competition with technical and artistic components.',
            'sport_type_category' => 'judged',
            'min_participants' => 3,
            'max_parameters' => 30
        ]
    ];
    
    $inserted_count = 0;
    foreach ($tournament_formats as $format) {
        try {
            $stmt = $conn->prepare("
                INSERT INTO tournament_formats (name, code, description, sport_type_category, min_participants, max_participants)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([
                $format['name'],
                $format['code'],
                $format['description'],
                $format['sport_type_category'],
                $format['min_participants'],
                $format['max_participants']
            ]);
            $inserted_count++;
            echo "<p style='color: green;'>✓ Created: {$format['name']} (Category: {$format['sport_type_category']})</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>❌ Error creating {$format['name']}: " . $e->getMessage() . "</p>";
        }
    }
    
    echo "<p><strong>Successfully created {$inserted_count} tournament formats with correct categories</strong></p>";
    
    // Step 4: Verify the fix
    echo "<h2>Step 4: Verifying the Fix</h2>";
    
    $stmt = $conn->prepare("SELECT sport_type_category, COUNT(*) as count FROM tournament_formats GROUP BY sport_type_category ORDER BY sport_type_category");
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Sport Type Category</th><th>Number of Formats</th></tr>";
    foreach ($categories as $cat) {
        echo "<tr>";
        echo "<td>{$cat['sport_type_category']}</td>";
        echo "<td>{$cat['count']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Step 5: Test filtering
    echo "<h2>Step 5: Testing Format Filtering</h2>";
    
    $test_types = ['traditional', 'academic', 'judged'];
    
    foreach ($test_types as $sport_type) {
        echo "<h4>Testing {$sport_type} sports:</h4>";
        
        $stmt = $conn->prepare("
            SELECT name, code 
            FROM tournament_formats 
            WHERE sport_type_category = ? OR sport_type_category = 'all'
            ORDER BY name
        ");
        $stmt->execute([$sport_type]);
        $formats = $stmt->fetchAll();
        
        echo "<p>Found " . count($formats) . " formats:</p>";
        echo "<ul>";
        foreach ($formats as $format) {
            echo "<li>{$format['name']} ({$format['code']})</li>";
        }
        echo "</ul>";
    }
    
    echo "<h2>✅ Tournament Format Categories Fix Complete!</h2>";
    echo "<p><strong>Summary:</strong></p>";
    echo "<ul>";
    echo "<li>✓ Updated ENUM values to include all sport type categories</li>";
    echo "<li>✓ Created {$inserted_count} tournament formats with correct categories</li>";
    echo "<li>✓ Traditional formats: 4 (Single/Double Elimination, Round Robin, Multi-Stage)</li>";
    echo "<li>✓ Academic formats: 4 (Swiss System, Knockout Rounds, Quiz Bowl, Academic Round Robin)</li>";
    echo "<li>✓ Judged formats: 4 (Judged Rounds, Performance Competition, Talent Showcase, Artistic Judging)</li>";
    echo "</ul>";
    
    echo "<p><a href='fix-format-filtering-logic.php' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>➡️ Next: Fix Filtering Logic</a></p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Critical Error: " . $e->getMessage() . "</p>";
    echo "<p>Stack trace:</p>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}
?>
