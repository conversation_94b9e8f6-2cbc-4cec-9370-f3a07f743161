<?php
/**
 * Modal AJAX Handler for SC_IMS Admin Panel
 * Handles all CRUD operations for modal-based forms
 */

// Start output buffering to prevent any unwanted output
ob_start();

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Clean any output that might have been generated
ob_clean();

// Set JSON response header
header('Content-Type: application/json');

// Get database connection
$database = new Database();
$conn = $database->getConnection();

// Validate CSRF token
if (!verifyCSRFToken($_POST['csrf_token'] ?? '')) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid security token. Please refresh the page and try again.'
    ]);
    exit;
}

// Get request data
$action = $_POST['action'] ?? '';
$entity = $_POST['entity'] ?? '';

try {
    switch ($entity) {
        case 'event':
            handleEventOperations($conn, $action, $_POST);
            break;
        case 'sport':
            handleSportOperations($conn, $action, $_POST);
            break;
        case 'department':
            handleDepartmentOperations($conn, $action, $_POST);
            break;
        case 'match':
            handleMatchOperations($conn, $action, $_POST);
            break;
        case 'sport_category':
            handleSportCategoryOperations($conn, $action, $_POST);
            break;
        default:
            throw new Exception('Invalid entity type');
    }
} catch (Exception $e) {
    // Clean any output that might have been generated
    ob_clean();

    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

// Ensure clean output
ob_end_flush();

/**
 * Check event dependencies before deletion
 */
function checkEventDependencies($conn, $eventId) {
    $sql = "
        SELECT
            (SELECT COUNT(*) FROM event_sports es WHERE es.event_id = ?) as event_sports_count,
            (SELECT COUNT(*) FROM registrations r
             JOIN event_sports es ON r.event_sport_id = es.id
             WHERE es.event_id = ?) as registrations_count,
            (SELECT COUNT(*) FROM tournaments t
             JOIN event_sports es ON t.event_sport_id = es.id
             WHERE es.event_id = ?) as tournaments_count,
            (SELECT COUNT(*) FROM matches m
             JOIN event_sports es ON m.event_sport_id = es.id
             WHERE es.event_id = ?) as matches_count
    ";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$eventId, $eventId, $eventId, $eventId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    $result['can_delete'] = ($result['event_sports_count'] == 0 &&
                            $result['registrations_count'] == 0 &&
                            $result['tournaments_count'] == 0 &&
                            $result['matches_count'] == 0);

    return $result;
}

/**
 * Check if a sport can be safely deleted
 */
function checkSportDependencies($conn, $sportId) {
    $sql = "
        SELECT
            (SELECT COUNT(*) FROM event_sports WHERE sport_id = ?) as event_sports_count,
            (SELECT COUNT(*) FROM registrations r
             JOIN event_sports es ON r.event_sport_id = es.id
             WHERE es.sport_id = ?) as registrations_count,
            (SELECT COUNT(*) FROM matches m
             JOIN event_sports es ON m.event_sport_id = es.id
             WHERE es.sport_id = ?) as matches_count,
            (SELECT COUNT(*) FROM sport_categories sc
             JOIN event_sports es ON sc.event_sport_id = es.id
             WHERE es.sport_id = ?) as categories_count
    ";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$sportId, $sportId, $sportId, $sportId]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);

    $result['can_delete'] = ($result['event_sports_count'] == 0 &&
                            $result['registrations_count'] == 0 &&
                            $result['matches_count'] == 0 &&
                            $result['categories_count'] == 0);

    return $result;
}

function handleEventOperations($conn, $action, $data) {
    switch ($action) {
        case 'create':
            $stmt = $conn->prepare("
                INSERT INTO events (name, description, start_date, end_date, location, status, max_participants_per_department) 
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                sanitizeInput($data['name'] ?? ''),
                sanitizeInput($data['description'] ?? ''),
                $data['start_date'] ?? null,
                $data['end_date'] ?? null,
                sanitizeInput($data['location'] ?? ''),
                $data['status'] ?? 'upcoming',
                $data['max_participants_per_department'] ?? null
            ]);
            
            $eventId = $conn->lastInsertId();
            logAdminActivity('CREATE_EVENT', 'events', $eventId, null, $data);
            
            echo json_encode([
                'success' => true,
                'message' => 'Event created successfully',
                'id' => $eventId
            ]);
            break;
            
        case 'update':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Event ID is required');
            
            $stmt = $conn->prepare("
                UPDATE events 
                SET name = ?, description = ?, start_date = ?, end_date = ?, location = ?, status = ?, max_participants_per_department = ?
                WHERE id = ?
            ");
            
            $stmt->execute([
                sanitizeInput($data['name'] ?? ''),
                sanitizeInput($data['description'] ?? ''),
                $data['start_date'] ?? null,
                $data['end_date'] ?? null,
                sanitizeInput($data['location'] ?? ''),
                $data['status'] ?? 'upcoming',
                $data['max_participants_per_department'] ?? null,
                $id
            ]);
            
            logAdminActivity('UPDATE_EVENT', 'events', $id, null, $data);
            
            echo json_encode([
                'success' => true,
                'message' => 'Event updated successfully'
            ]);
            break;
            
        case 'delete':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Event ID is required');

            // Get event name for logging
            $stmt = $conn->prepare("SELECT name FROM events WHERE id = ?");
            $stmt->execute([$id]);
            $event = $stmt->fetch();

            if (!$event) throw new Exception('Event not found');

            // Check for dependencies
            $dependencies = checkEventDependencies($conn, $id);
            $forceCascade = ($data['force_cascade'] ?? '') === 'true';

            if (!$dependencies['can_delete'] && !$forceCascade) {
                $message = "Cannot delete event '{$event['name']}' because it has the following dependencies:\n";
                if ($dependencies['event_sports_count'] > 0) {
                    $message .= "• {$dependencies['event_sports_count']} sport(s) assigned\n";
                }
                if ($dependencies['registrations_count'] > 0) {
                    $message .= "• {$dependencies['registrations_count']} registration(s)\n";
                }
                if ($dependencies['tournaments_count'] > 0) {
                    $message .= "• {$dependencies['tournaments_count']} tournament(s)\n";
                }
                if ($dependencies['matches_count'] > 0) {
                    $message .= "• {$dependencies['matches_count']} match(es)\n";
                }
                $message .= "\nPlease remove these dependencies first, or use the cascade deletion option.";

                throw new Exception($message);
            }

            // Start transaction for safe deletion
            $conn->beginTransaction();

            try {
                // Delete in proper order to respect foreign key constraints
                // 1. Delete matches first
                $stmt = $conn->prepare("
                    DELETE m FROM matches m
                    JOIN event_sports es ON m.event_sport_id = es.id
                    WHERE es.event_id = ?
                ");
                $stmt->execute([$id]);

                // 2. Delete tournament participants
                $stmt = $conn->prepare("
                    DELETE tp FROM tournament_participants tp
                    JOIN tournaments t ON tp.tournament_id = t.id
                    JOIN event_sports es ON t.event_sport_id = es.id
                    WHERE es.event_id = ?
                ");
                $stmt->execute([$id]);

                // 3. Delete tournament rounds
                $stmt = $conn->prepare("
                    DELETE tr FROM tournament_rounds tr
                    JOIN tournaments t ON tr.tournament_id = t.id
                    JOIN event_sports es ON t.event_sport_id = es.id
                    WHERE es.event_id = ?
                ");
                $stmt->execute([$id]);

                // 4. Delete tournaments
                $stmt = $conn->prepare("
                    DELETE t FROM tournaments t
                    JOIN event_sports es ON t.event_sport_id = es.id
                    WHERE es.event_id = ?
                ");
                $stmt->execute([$id]);

                // 5. Delete registrations
                $stmt = $conn->prepare("
                    DELETE r FROM registrations r
                    JOIN event_sports es ON r.event_sport_id = es.id
                    WHERE es.event_id = ?
                ");
                $stmt->execute([$id]);

                // 6. Delete event_sports
                $stmt = $conn->prepare("DELETE FROM event_sports WHERE event_id = ?");
                $stmt->execute([$id]);

                // 7. Finally delete the event
                $stmt = $conn->prepare("DELETE FROM events WHERE id = ?");
                $stmt->execute([$id]);

                $conn->commit();

                logAdminActivity('DELETE_EVENT', 'events', $id, $event, null);

                echo json_encode([
                    'success' => true,
                    'message' => 'Event and all associated data deleted successfully'
                ]);

            } catch (Exception $e) {
                $conn->rollback();
                throw new Exception('Failed to delete event: ' . $e->getMessage());
            }
            break;
            
        default:
            throw new Exception('Invalid action for events');
    }
}

function handleSportOperations($conn, $action, $data) {
    switch ($action) {
        case 'create':
            // Get sport type info for legacy type field
            $sport_type_id = $data['sport_type_id'] ?? null;
            $legacy_type = 'traditional'; // default

            if ($sport_type_id) {
                $stmt = $conn->prepare("SELECT category FROM sport_types WHERE id = ?");
                $stmt->execute([$sport_type_id]);
                $sport_type = $stmt->fetch();
                if ($sport_type) {
                    $legacy_type = $sport_type['category'];
                }
            }

            $stmt = $conn->prepare("
                INSERT INTO sports (name, description, type, scoring_method, bracket_format, rules, max_participants, sport_type_id, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                sanitizeInput($data['name'] ?? ''),
                sanitizeInput($data['description'] ?? ''),
                $legacy_type,
                $data['scoring_method'] ?? 'point_based',
                $data['bracket_format'] ?? 'single_elimination',
                sanitizeInput($data['rules'] ?? ''),
                $data['max_participants'] ?? 0,
                $sport_type_id,
                true
            ]);

            $sportId = $conn->lastInsertId();
            logAdminActivity('CREATE_SPORT', 'sports', $sportId, null, $data);

            echo json_encode([
                'success' => true,
                'message' => 'Sport created successfully',
                'id' => $sportId
            ]);
            break;
            
        case 'update':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Sport ID is required');

            // Get sport type info for legacy type field
            $sport_type_id = $data['sport_type_id'] ?? null;
            $legacy_type = 'traditional'; // default

            if ($sport_type_id) {
                $stmt = $conn->prepare("SELECT category FROM sport_types WHERE id = ?");
                $stmt->execute([$sport_type_id]);
                $sport_type = $stmt->fetch();
                if ($sport_type) {
                    $legacy_type = $sport_type['category'];
                }
            }

            $stmt = $conn->prepare("
                UPDATE sports
                SET name = ?, description = ?, type = ?, scoring_method = ?, bracket_format = ?, rules = ?, max_participants = ?, sport_type_id = ?
                WHERE id = ?
            ");

            $stmt->execute([
                sanitizeInput($data['name'] ?? ''),
                sanitizeInput($data['description'] ?? ''),
                $legacy_type,
                $data['scoring_method'] ?? 'point_based',
                $data['bracket_format'] ?? 'single_elimination',
                sanitizeInput($data['rules'] ?? ''),
                $data['max_participants'] ?? 0,
                $sport_type_id,
                $id
            ]);

            logAdminActivity('UPDATE_SPORT', 'sports', $id, null, $data);

            echo json_encode([
                'success' => true,
                'message' => 'Sport updated successfully'
            ]);
            break;
            
        case 'delete':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Sport ID is required');

            // Get sport name for logging
            $stmt = $conn->prepare("SELECT name FROM sports WHERE id = ?");
            $stmt->execute([$id]);
            $sport = $stmt->fetch();

            if (!$sport) throw new Exception('Sport not found');

            // Check for dependencies before deletion
            $dependencies = checkSportDependencies($conn, $id);

            if (!$dependencies['can_delete']) {
                $dependencyList = [];
                if ($dependencies['event_sports_count'] > 0) {
                    $dependencyList[] = "{$dependencies['event_sports_count']} event association(s)";
                }
                if ($dependencies['registrations_count'] > 0) {
                    $dependencyList[] = "{$dependencies['registrations_count']} registration(s)";
                }
                if ($dependencies['matches_count'] > 0) {
                    $dependencyList[] = "{$dependencies['matches_count']} match(es)";
                }
                if ($dependencies['categories_count'] > 0) {
                    $dependencyList[] = "{$dependencies['categories_count']} categor(y/ies)";
                }

                throw new Exception(
                    "Cannot delete sport '{$sport['name']}' because it has: " .
                    implode(', ', $dependencyList) .
                    ". Please remove these associations first or contact an administrator for cascade deletion."
                );
            }

            $stmt = $conn->prepare("DELETE FROM sports WHERE id = ?");
            $stmt->execute([$id]);

            logAdminActivity('DELETE_SPORT', 'sports', $id, $sport, null);

            echo json_encode([
                'success' => true,
                'message' => 'Sport deleted successfully'
            ]);
            break;
            
        default:
            throw new Exception('Invalid action for sports');
    }
}

function handleDepartmentOperations($conn, $action, $data) {
    switch ($action) {
        case 'create':
            // Validate color format
            $color_code = sanitizeInput($data['color_code'] ?? '#3498db');
            if (!preg_match('/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/', $color_code)) {
                throw new Exception('Invalid color format. Please use a valid hex color code.');
            }

            $stmt = $conn->prepare("
                INSERT INTO departments (name, abbreviation, description, color_code, head_of_department, contact_email, contact_phone, location)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                sanitizeInput($data['name'] ?? ''),
                strtoupper(sanitizeInput($data['abbreviation'] ?? '')),
                sanitizeInput($data['description'] ?? ''),
                $color_code,
                sanitizeInput($data['head_of_department'] ?? ''),
                sanitizeInput($data['contact_email'] ?? ''),
                sanitizeInput($data['contact_phone'] ?? ''),
                sanitizeInput($data['location'] ?? '')
            ]);
            
            $deptId = $conn->lastInsertId();
            logAdminActivity('CREATE_DEPARTMENT', 'departments', $deptId, null, $data);
            
            echo json_encode([
                'success' => true,
                'message' => 'Department created successfully',
                'id' => $deptId
            ]);
            break;
            
        case 'update':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Department ID is required');

            // Validate color format
            $color_code = sanitizeInput($data['color_code'] ?? '#3498db');
            if (!preg_match('/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/', $color_code)) {
                throw new Exception('Invalid color format. Please use a valid hex color code.');
            }

            $stmt = $conn->prepare("
                UPDATE departments
                SET name = ?, abbreviation = ?, description = ?, color_code = ?, head_of_department = ?, contact_email = ?, contact_phone = ?, location = ?
                WHERE id = ?
            ");

            $stmt->execute([
                sanitizeInput($data['name'] ?? ''),
                strtoupper(sanitizeInput($data['abbreviation'] ?? '')),
                sanitizeInput($data['description'] ?? ''),
                $color_code,
                sanitizeInput($data['head_of_department'] ?? ''),
                sanitizeInput($data['contact_email'] ?? ''),
                sanitizeInput($data['contact_phone'] ?? ''),
                sanitizeInput($data['location'] ?? ''),
                $id
            ]);
            
            logAdminActivity('UPDATE_DEPARTMENT', 'departments', $id, null, $data);
            
            echo json_encode([
                'success' => true,
                'message' => 'Department updated successfully'
            ]);
            break;
            
        case 'delete':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Department ID is required');
            
            // Get department name for logging
            $stmt = $conn->prepare("SELECT name FROM departments WHERE id = ?");
            $stmt->execute([$id]);
            $dept = $stmt->fetch();
            
            if (!$dept) throw new Exception('Department not found');
            
            $stmt = $conn->prepare("DELETE FROM departments WHERE id = ?");
            $stmt->execute([$id]);
            
            logAdminActivity('DELETE_DEPARTMENT', 'departments', $id, $dept, null);
            
            echo json_encode([
                'success' => true,
                'message' => 'Department deleted successfully'
            ]);
            break;
            
        default:
            throw new Exception('Invalid action for departments');
    }
}

function handleMatchOperations($conn, $action, $data) {
    switch ($action) {
        case 'create':
            try {
                $stmt = $conn->prepare("INSERT INTO matches (event_sport_id, team1_id, team2_id, round_number, match_number, scheduled_time, venue, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $data['event_sport_id'],
                    $data['team1_id'],
                    !empty($data['team2_id']) ? $data['team2_id'] : null,
                    $data['round_number'] ?? 1,
                    $data['match_number'] ?? 1,
                    !empty($data['scheduled_time']) ? $data['scheduled_time'] : null,
                    $data['venue'] ?? null,
                    $data['status'] ?? 'scheduled'
                ]);

                $matchId = $conn->lastInsertId();
                logAdminActivity('CREATE_MATCH', 'matches', $matchId, null, $data);

                echo json_encode(['success' => true, 'message' => 'Match scheduled successfully!']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Failed to schedule match: ' . $e->getMessage()]);
            }
            break;

        case 'update':
            try {
                $stmt = $conn->prepare("UPDATE matches SET event_sport_id = ?, team1_id = ?, team2_id = ?, round_number = ?, match_number = ?, scheduled_time = ?, venue = ?, status = ? WHERE id = ?");
                $stmt->execute([
                    $data['event_sport_id'],
                    $data['team1_id'],
                    !empty($data['team2_id']) ? $data['team2_id'] : null,
                    $data['round_number'] ?? 1,
                    $data['match_number'] ?? 1,
                    !empty($data['scheduled_time']) ? $data['scheduled_time'] : null,
                    $data['venue'] ?? null,
                    $data['status'] ?? 'scheduled',
                    $data['id']
                ]);

                logAdminActivity('UPDATE_MATCH', 'matches', $data['id'], null, $data);

                echo json_encode(['success' => true, 'message' => 'Match updated successfully!']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Failed to update match: ' . $e->getMessage()]);
            }
            break;

        case 'delete':
            try {
                // Check if match has scores before deleting
                $stmt = $conn->prepare("SELECT COUNT(*) FROM scores WHERE match_id = ?");
                $stmt->execute([$data['id']]);
                $scoreCount = $stmt->fetchColumn();

                if ($scoreCount > 0) {
                    echo json_encode(['success' => false, 'message' => 'Cannot delete match with existing scores. Please remove scores first.']);
                    break;
                }

                $stmt = $conn->prepare("DELETE FROM matches WHERE id = ?");
                $stmt->execute([$data['id']]);

                logAdminActivity('DELETE_MATCH', 'matches', $data['id'], null, null);

                echo json_encode(['success' => true, 'message' => 'Match deleted successfully!']);
            } catch (Exception $e) {
                echo json_encode(['success' => false, 'message' => 'Failed to delete match: ' . $e->getMessage()]);
            }
            break;

        default:
            echo json_encode(['success' => false, 'message' => 'Invalid match action']);
    }
    exit;
}

/**
 * Handle sport category operations (CRUD)
 */
function handleSportCategoryOperations($conn, $action, $data) {
    switch ($action) {
        case 'create':
            $event_sport_id = $data['event_sport_id'] ?? 0;

            // Enhanced debugging and validation
            if (!$event_sport_id) {
                error_log("Sport Category Creation Error: event_sport_id is missing or 0");
                error_log("Form data: " . print_r($data, true));
                throw new Exception('Event sport ID is required');
            }

            // Verify that the event_sport_id actually exists
            $stmt = $conn->prepare("SELECT id FROM event_sports WHERE id = ?");
            $stmt->execute([$event_sport_id]);
            $event_sport_exists = $stmt->fetch();

            if (!$event_sport_exists) {
                error_log("Sport Category Creation Error: event_sport_id $event_sport_id does not exist in event_sports table");
                throw new Exception("Invalid event sport ID: $event_sport_id. The event-sport relationship does not exist.");
            }

            // Validate required fields
            $category_name = sanitizeInput($data['category_name'] ?? '');
            $category_type = sanitizeInput($data['category_type'] ?? '');

            if (empty($category_name)) throw new Exception('Category name is required');
            if (empty($category_type)) throw new Exception('Category type is required');

            // Validate category type
            $valid_types = ['men', 'women', 'mixed', 'open', 'youth', 'senior', 'other'];
            if (!in_array($category_type, $valid_types)) {
                throw new Exception('Invalid category type');
            }

            // Handle custom type
            $category_type_custom = null;
            if ($category_type === 'other') {
                $category_type_custom = sanitizeInput($data['category_type_custom'] ?? '');
                if (empty($category_type_custom)) {
                    throw new Exception('Custom category type description is required when type is "other"');
                }
            }

            // Validate email if provided
            $referee_email = sanitizeInput($data['referee_email'] ?? '');
            if (!empty($referee_email) && !filter_var($referee_email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Invalid referee email format');
            }

            try {
                $stmt = $conn->prepare("
                    INSERT INTO sport_categories (
                        event_sport_id, category_name, category_type, category_type_custom,
                        referee_name, referee_email, venue
                    ) VALUES (?, ?, ?, ?, ?, ?, ?)
                ");

                $stmt->execute([
                    $event_sport_id,
                    $category_name,
                    $category_type,
                    $category_type_custom,
                    sanitizeInput($data['referee_name'] ?? ''),
                    $referee_email,
                    sanitizeInput($data['venue'] ?? '')
                ]);

                $categoryId = $conn->lastInsertId();
                logAdminActivity('CREATE_SPORT_CATEGORY', 'sport_categories', $categoryId, null, $data);

                echo json_encode([
                    'success' => true,
                    'message' => 'Category created successfully',
                    'id' => $categoryId
                ]);
            } catch (PDOException $e) {
                error_log("Database error in sport category creation: " . $e->getMessage());
                error_log("SQL State: " . $e->getCode());
                error_log("Data being inserted: " . print_r([
                    'event_sport_id' => $event_sport_id,
                    'category_name' => $category_name,
                    'category_type' => $category_type,
                    'category_type_custom' => $category_type_custom
                ], true));

                if (strpos($e->getMessage(), 'foreign key constraint') !== false) {
                    throw new Exception("Database constraint error: The event-sport relationship (ID: $event_sport_id) is invalid. Please ensure the sport is properly added to the event.");
                } else {
                    throw new Exception("Database error: " . $e->getMessage());
                }
            }
            break;

        case 'update':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Category ID is required');

            // Get existing category for logging
            $stmt = $conn->prepare("SELECT * FROM sport_categories WHERE id = ?");
            $stmt->execute([$id]);
            $oldCategory = $stmt->fetch();

            if (!$oldCategory) throw new Exception('Category not found');

            // Validate required fields
            $category_name = sanitizeInput($data['category_name'] ?? '');
            $category_type = sanitizeInput($data['category_type'] ?? '');

            if (empty($category_name)) throw new Exception('Category name is required');
            if (empty($category_type)) throw new Exception('Category type is required');

            // Validate category type
            $valid_types = ['men', 'women', 'mixed', 'open', 'youth', 'senior', 'other'];
            if (!in_array($category_type, $valid_types)) {
                throw new Exception('Invalid category type');
            }

            // Handle custom type
            $category_type_custom = null;
            if ($category_type === 'other') {
                $category_type_custom = sanitizeInput($data['category_type_custom'] ?? '');
                if (empty($category_type_custom)) {
                    throw new Exception('Custom category type description is required when type is "other"');
                }
            }

            // Validate email if provided
            $referee_email = sanitizeInput($data['referee_email'] ?? '');
            if (!empty($referee_email) && !filter_var($referee_email, FILTER_VALIDATE_EMAIL)) {
                throw new Exception('Invalid referee email format');
            }

            $stmt = $conn->prepare("
                UPDATE sport_categories SET
                    category_name = ?, category_type = ?, category_type_custom = ?,
                    referee_name = ?, referee_email = ?, venue = ?
                WHERE id = ?
            ");

            $stmt->execute([
                $category_name,
                $category_type,
                $category_type_custom,
                sanitizeInput($data['referee_name'] ?? ''),
                $referee_email,
                sanitizeInput($data['venue'] ?? ''),
                $id
            ]);

            logAdminActivity('UPDATE_SPORT_CATEGORY', 'sport_categories', $id, $oldCategory, $data);

            echo json_encode([
                'success' => true,
                'message' => 'Category updated successfully'
            ]);
            break;

        case 'delete':
            $id = $data['id'] ?? 0;
            if (!$id) throw new Exception('Category ID is required');

            // Get category name for logging
            $stmt = $conn->prepare("SELECT category_name FROM sport_categories WHERE id = ?");
            $stmt->execute([$id]);
            $category = $stmt->fetch();

            if (!$category) throw new Exception('Category not found');

            $stmt = $conn->prepare("DELETE FROM sport_categories WHERE id = ?");
            $stmt->execute([$id]);

            logAdminActivity('DELETE_SPORT_CATEGORY', 'sport_categories', $id, $category, null);

            echo json_encode([
                'success' => true,
                'message' => 'Category deleted successfully'
            ]);
            break;

        default:
            throw new Exception('Invalid category action');
    }
}
?>
