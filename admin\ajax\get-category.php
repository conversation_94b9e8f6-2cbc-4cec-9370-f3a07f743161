<?php
/**
 * Get Category Data for Editing
 */

header('Content-Type: application/json');

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

try {
    requireAdmin();
    
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get category ID from request
    $categoryId = $_GET['id'] ?? 0;
    
    if (!$categoryId) {
        throw new Exception('Category ID is required');
    }
    
    // Fetch category data
    $stmt = $conn->prepare("
        SELECT 
            sc.*,
            e.name as event_name,
            s.name as sport_name,
            es.id as event_sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        WHERE sc.id = ?
    ");
    
    $stmt->execute([$categoryId]);
    $category = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$category) {
        throw new Exception('Category not found');
    }
    
    // Log the activity
    logAdminActivity('VIEW_SPORT_CATEGORY', 'sport_categories', $categoryId, null, [
        'category_name' => $category['category_name']
    ]);
    
    echo json_encode([
        'success' => true,
        'category' => $category
    ]);
    
} catch (Exception $e) {
    error_log("Get category error: " . $e->getMessage());
    
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}
?>
