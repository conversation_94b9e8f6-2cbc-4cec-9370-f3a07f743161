<?php
/**
 * Test Schema AJAX Endpoint
 * Returns the current sport_categories table structure
 */

require_once '../auth.php';
require_once '../../config/database.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

try {
    // Get database connection
    $database = new Database();
    $conn = $database->getConnection();
    
    // Get table structure
    $stmt = $conn->prepare("DESCRIBE sport_categories");
    $stmt->execute();
    $columns = $stmt->fetchAll();
    
    echo json_encode([
        'success' => true,
        'columns' => $columns
    ]);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => 'Error checking schema: ' . $e->getMessage()
    ]);
}
?>
