<?php
/**
 * Test Edit Functionality
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🧪 Test Edit Functionality</h1>";

echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; border-left: 4px solid #007bff; margin-bottom: 20px;'>";
echo "<h2 style='margin-top: 0;'>✏️ Edit Button Testing</h2>";
echo "<p>This page tests the edit functionality that was just implemented.</p>";
echo "</div>";

try {
    echo "<h2>1. Check Available Categories</h2>";
    
    // Get categories for testing
    $stmt = $conn->prepare("
        SELECT 
            sc.*,
            e.name as event_name,
            s.name as sport_name,
            es.id as event_sport_id
        FROM sport_categories sc
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        ORDER BY e.name, s.name, sc.category_name
        LIMIT 10
    ");
    $stmt->execute();
    $categories = $stmt->fetchAll();
    
    if (empty($categories)) {
        echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px;'>";
        echo "<p>⚠️ <strong>No categories found for testing.</strong></p>";
        echo "<p>Please create a category first to test the edit functionality.</p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p>✅ <strong>Found " . count($categories) . " categories for testing</strong></p>";
        echo "</div>";
        
        echo "<h3>Available Categories:</h3>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #007bff; color: white;'>";
        echo "<th>ID</th><th>Category Name</th><th>Type</th><th>Event</th><th>Sport</th><th>Test Edit</th>";
        echo "</tr>";
        
        foreach ($categories as $cat) {
            echo "<tr>";
            echo "<td>{$cat['id']}</td>";
            echo "<td>{$cat['category_name']}</td>";
            echo "<td>" . strtoupper($cat['category_type']) . "</td>";
            echo "<td>{$cat['event_name']}</td>";
            echo "<td>{$cat['sport_name']}</td>";
            echo "<td>";
            echo "<button onclick=\"testEditCategory({$cat['id']})\" style='background: #ffc107; color: #212529; padding: 5px 10px; border: none; border-radius: 3px; cursor: pointer;'>";
            echo "<i class='fas fa-edit'></i> Test Edit";
            echo "</button>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    echo "<h2>2. Test get-category.php Endpoint</h2>";
    
    if (!empty($categories)) {
        $testCategory = $categories[0];
        $testId = $testCategory['id'];
        
        echo "<h3>Testing with Category ID: $testId</h3>";
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace;'>";
        echo "<strong>Test URL:</strong> ajax/get-category.php?id=$testId<br>";
        echo "<strong>Category:</strong> {$testCategory['category_name']}<br>";
        echo "<strong>Type:</strong> {$testCategory['category_type']}<br>";
        echo "</div>";
        
        echo "<button onclick=\"testGetCategory($testId)\" style='background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 0;'>";
        echo "🔍 Test Get Category Data";
        echo "</button>";
        
        echo "<div id='getCategoryResult' style='margin: 10px 0;'></div>";
    }
    
    echo "<h2>3. Implementation Summary</h2>";
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;'>";
    echo "<h3>✅ Edit Functionality Implemented</h3>";
    echo "<ul>";
    echo "<li><strong>editCategory() function:</strong> Fetches category data and populates the modal</li>";
    echo "<li><strong>populateEditForm() function:</strong> Fills form fields with existing data</li>";
    echo "<li><strong>Updated openModal() function:</strong> Handles both create and edit modes</li>";
    echo "<li><strong>Updated submitCategoryForm() function:</strong> Handles both create and update operations</li>";
    echo "<li><strong>get-category.php endpoint:</strong> Provides category data for editing</li>";
    echo "<li><strong>Modal handler update case:</strong> Already implemented for database updates</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>4. How to Test</h2>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;'>";
    echo "<h3>📝 Testing Steps</h3>";
    echo "<ol>";
    echo "<li><strong>Go to sport-categories page:</strong> <a href='sport-categories.php?event_id=4&sport_id=40' target='_blank'>sport-categories.php</a></li>";
    echo "<li><strong>Click the Edit button</strong> on any category</li>";
    echo "<li><strong>Verify the modal opens</strong> with existing data populated</li>";
    echo "<li><strong>Modify some fields</strong> (e.g., change category name or referee)</li>";
    echo "<li><strong>Click 'Update Category'</strong> button</li>";
    echo "<li><strong>Verify:</strong>";
    echo "<ul>";
    echo "<li>Success notification appears</li>";
    echo "<li>Modal closes</li>";
    echo "<li>Page reloads with updated data</li>";
    echo "<li>Changes are reflected in the category list</li>";
    echo "</ul>";
    echo "</li>";
    echo "</ol>";
    echo "</div>";
    
    echo "<h2>5. Live Testing</h2>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='sport-categories.php?event_id=4&sport_id=40' target='_blank' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "🚀 Test Edit Functionality Live";
    echo "</a>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>

<script>
function testEditCategory(categoryId) {
    console.log('Testing edit for category:', categoryId);
    
    // Test the get-category endpoint
    fetch(`ajax/get-category.php?id=${categoryId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(`✅ Edit test successful!\n\nCategory: ${data.category.category_name}\nType: ${data.category.category_type}\nReferee: ${data.category.referee_name || 'N/A'}\nVenue: ${data.category.venue || 'N/A'}`);
            } else {
                alert(`❌ Edit test failed: ${data.message}`);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('❌ Network error during edit test');
        });
}

function testGetCategory(categoryId) {
    const resultDiv = document.getElementById('getCategoryResult');
    resultDiv.innerHTML = '<p>🔄 Testing...</p>';
    
    fetch(`ajax/get-category.php?id=${categoryId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const category = data.category;
                resultDiv.innerHTML = `
                    <div style='background: #d4edda; padding: 15px; border-radius: 5px;'>
                        <h4>✅ Success! Category Data Retrieved:</h4>
                        <ul>
                            <li><strong>ID:</strong> ${category.id}</li>
                            <li><strong>Name:</strong> ${category.category_name}</li>
                            <li><strong>Type:</strong> ${category.category_type}</li>
                            <li><strong>Custom Type:</strong> ${category.category_type_custom || 'N/A'}</li>
                            <li><strong>Referee Name:</strong> ${category.referee_name || 'N/A'}</li>
                            <li><strong>Referee Email:</strong> ${category.referee_email || 'N/A'}</li>
                            <li><strong>Venue:</strong> ${category.venue || 'N/A'}</li>
                            <li><strong>Event:</strong> ${category.event_name}</li>
                            <li><strong>Sport:</strong> ${category.sport_name}</li>
                        </ul>
                    </div>
                `;
            } else {
                resultDiv.innerHTML = `
                    <div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>
                        <h4>❌ Error:</h4>
                        <p>${data.message}</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error:', error);
            resultDiv.innerHTML = `
                <div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>
                    <h4>❌ Network Error:</h4>
                    <p>${error.message}</p>
                </div>
            `;
        });
}
</script>
