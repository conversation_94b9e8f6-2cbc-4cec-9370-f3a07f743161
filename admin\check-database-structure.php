<?php
/**
 * Check Database Structure for Category Navigation
 * Verify the relationships between events, sports, and categories
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require admin authentication
requireAdmin();

// Get database connection
$database = new Database();
$conn = $database->getConnection();

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Structure Check</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>🗄️ Database Structure Check</h1>
        <p>Examining the database structure to identify navigation issues</p>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>1. Events Table</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    $stmt = $conn->prepare("SELECT id, name, status FROM events ORDER BY id");
                    $stmt->execute();
                    $events = $stmt->fetchAll();
                    
                    if ($events) {
                        echo "<table class='table table-sm'>";
                        echo "<thead><tr><th>ID</th><th>Name</th><th>Status</th></tr></thead><tbody>";
                        foreach ($events as $event) {
                            echo "<tr>";
                            echo "<td>{$event['id']}</td>";
                            echo "<td>" . htmlspecialchars($event['name']) . "</td>";
                            echo "<td>{$event['status']}</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                    } else {
                        echo "<p class='text-warning'>No events found</p>";
                    }
                } catch (Exception $e) {
                    echo "<p class='text-danger'>Error: " . $e->getMessage() . "</p>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>2. Sports Table</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    $stmt = $conn->prepare("SELECT id, name, type FROM sports ORDER BY id");
                    $stmt->execute();
                    $sports = $stmt->fetchAll();
                    
                    if ($sports) {
                        echo "<table class='table table-sm'>";
                        echo "<thead><tr><th>ID</th><th>Name</th><th>Type</th></tr></thead><tbody>";
                        foreach ($sports as $sport) {
                            echo "<tr>";
                            echo "<td>{$sport['id']}</td>";
                            echo "<td>" . htmlspecialchars($sport['name']) . "</td>";
                            echo "<td>{$sport['type']}</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                    } else {
                        echo "<p class='text-warning'>No sports found</p>";
                    }
                } catch (Exception $e) {
                    echo "<p class='text-danger'>Error: " . $e->getMessage() . "</p>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>3. Event Sports Relationships</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    $stmt = $conn->prepare("
                        SELECT es.id, es.event_id, es.sport_id, e.name as event_name, s.name as sport_name
                        FROM event_sports es
                        JOIN events e ON es.event_id = e.id
                        JOIN sports s ON es.sport_id = s.id
                        ORDER BY es.id
                    ");
                    $stmt->execute();
                    $event_sports = $stmt->fetchAll();
                    
                    if ($event_sports) {
                        echo "<table class='table table-sm'>";
                        echo "<thead><tr><th>ES ID</th><th>Event ID</th><th>Sport ID</th><th>Event Name</th><th>Sport Name</th></tr></thead><tbody>";
                        foreach ($event_sports as $es) {
                            echo "<tr>";
                            echo "<td>{$es['id']}</td>";
                            echo "<td>{$es['event_id']}</td>";
                            echo "<td>{$es['sport_id']}</td>";
                            echo "<td>" . htmlspecialchars($es['event_name']) . "</td>";
                            echo "<td>" . htmlspecialchars($es['sport_name']) . "</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                    } else {
                        echo "<p class='text-warning'>No event-sport relationships found</p>";
                    }
                } catch (Exception $e) {
                    echo "<p class='text-danger'>Error: " . $e->getMessage() . "</p>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>4. Sport Categories Table</h3>
            </div>
            <div class="card-body">
                <?php
                try {
                    $stmt = $conn->prepare("
                        SELECT sc.id, sc.event_sport_id, sc.category_name, sc.category_type,
                               es.event_id, es.sport_id, e.name as event_name, s.name as sport_name
                        FROM sport_categories sc
                        LEFT JOIN event_sports es ON sc.event_sport_id = es.id
                        LEFT JOIN events e ON es.event_id = e.id
                        LEFT JOIN sports s ON es.sport_id = s.id
                        ORDER BY sc.id
                    ");
                    $stmt->execute();
                    $categories = $stmt->fetchAll();
                    
                    if ($categories) {
                        echo "<table class='table table-sm'>";
                        echo "<thead><tr><th>Cat ID</th><th>ES ID</th><th>Category Name</th><th>Type</th><th>Event</th><th>Sport</th><th>Test Link</th></tr></thead><tbody>";
                        foreach ($categories as $cat) {
                            $link_url = "manage-category.php?event_id={$cat['event_id']}&sport_id={$cat['sport_id']}&category_id={$cat['id']}";
                            echo "<tr>";
                            echo "<td>{$cat['id']}</td>";
                            echo "<td>{$cat['event_sport_id']}</td>";
                            echo "<td>" . htmlspecialchars($cat['category_name']) . "</td>";
                            echo "<td>{$cat['category_type']}</td>";
                            echo "<td>" . htmlspecialchars($cat['event_name'] ?? 'NULL') . "</td>";
                            echo "<td>" . htmlspecialchars($cat['sport_name'] ?? 'NULL') . "</td>";
                            echo "<td>";
                            if ($cat['event_id'] && $cat['sport_id']) {
                                echo "<a href='$link_url' class='btn btn-sm btn-primary' target='_blank'>Test</a>";
                            } else {
                                echo "<span class='text-danger'>Broken Link</span>";
                            }
                            echo "</td>";
                            echo "</tr>";
                        }
                        echo "</tbody></table>";
                    } else {
                        echo "<p class='text-warning'>No sport categories found</p>";
                    }
                } catch (Exception $e) {
                    echo "<p class='text-danger'>Error: " . $e->getMessage() . "</p>";
                }
                ?>
            </div>
        </div>
        
        <div class="card mb-4">
            <div class="card-header">
                <h3>5. Working Navigation Links</h3>
            </div>
            <div class="card-body">
                <p>Based on the database analysis, here are the working navigation links:</p>
                <?php
                try {
                    $stmt = $conn->prepare("
                        SELECT sc.id as category_id, sc.category_name,
                               es.event_id, es.sport_id,
                               e.name as event_name, s.name as sport_name
                        FROM sport_categories sc
                        JOIN event_sports es ON sc.event_sport_id = es.id
                        JOIN events e ON es.event_id = e.id
                        JOIN sports s ON es.sport_id = s.id
                        ORDER BY e.name, s.name, sc.category_name
                    ");
                    $stmt->execute();
                    $working_links = $stmt->fetchAll();
                    
                    if ($working_links) {
                        echo "<div class='row'>";
                        foreach ($working_links as $link) {
                            $manage_url = "manage-category.php?event_id={$link['event_id']}&sport_id={$link['sport_id']}&category_id={$link['category_id']}";
                            $categories_url = "sport-categories.php?event_id={$link['event_id']}&sport_id={$link['sport_id']}";
                            
                            echo "<div class='col-md-6 mb-3'>";
                            echo "<div class='card'>";
                            echo "<div class='card-body'>";
                            echo "<h6 class='card-title'>" . htmlspecialchars($link['category_name']) . "</h6>";
                            echo "<p class='card-text'>";
                            echo "<small class='text-muted'>" . htmlspecialchars($link['event_name']) . " - " . htmlspecialchars($link['sport_name']) . "</small>";
                            echo "</p>";
                            echo "<div class='d-flex gap-2'>";
                            echo "<a href='$categories_url' class='btn btn-sm btn-outline-primary' target='_blank'>Categories List</a>";
                            echo "<a href='$manage_url' class='btn btn-sm btn-success' target='_blank'>Manage Category</a>";
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                            echo "</div>";
                        }
                        echo "</div>";
                    } else {
                        echo "<p class='text-warning'>No working links found - database relationships may be broken</p>";
                    }
                } catch (Exception $e) {
                    echo "<p class='text-danger'>Error: " . $e->getMessage() . "</p>";
                }
                ?>
            </div>
        </div>
        
        <div class="card">
            <div class="card-header">
                <h3>6. Diagnosis</h3>
            </div>
            <div class="card-body">
                <?php
                // Count records in each table
                try {
                    $events_count = $conn->query("SELECT COUNT(*) FROM events")->fetchColumn();
                    $sports_count = $conn->query("SELECT COUNT(*) FROM sports")->fetchColumn();
                    $event_sports_count = $conn->query("SELECT COUNT(*) FROM event_sports")->fetchColumn();
                    $categories_count = $conn->query("SELECT COUNT(*) FROM sport_categories")->fetchColumn();
                    
                    echo "<div class='alert alert-info'>";
                    echo "<h5>Database Summary:</h5>";
                    echo "<ul>";
                    echo "<li><strong>Events:</strong> $events_count</li>";
                    echo "<li><strong>Sports:</strong> $sports_count</li>";
                    echo "<li><strong>Event-Sport Relationships:</strong> $event_sports_count</li>";
                    echo "<li><strong>Sport Categories:</strong> $categories_count</li>";
                    echo "</ul>";
                    
                    if ($categories_count == 0) {
                        echo "<div class='alert alert-warning mt-3'>";
                        echo "<strong>⚠️ Issue Found:</strong> No sport categories exist in the database. ";
                        echo "This is why the navigation fails - there are no categories to navigate to!";
                        echo "</div>";
                    } elseif ($event_sports_count == 0) {
                        echo "<div class='alert alert-warning mt-3'>";
                        echo "<strong>⚠️ Issue Found:</strong> No event-sport relationships exist. ";
                        echo "Categories need to be linked to event-sport combinations.";
                        echo "</div>";
                    } else {
                        echo "<div class='alert alert-success mt-3'>";
                        echo "<strong>✅ Database Structure Looks Good:</strong> All necessary tables have data. ";
                        echo "The navigation should work with the test links above.";
                        echo "</div>";
                    }
                    echo "</div>";
                } catch (Exception $e) {
                    echo "<div class='alert alert-danger'>";
                    echo "<strong>Database Error:</strong> " . $e->getMessage();
                    echo "</div>";
                }
                ?>
            </div>
        </div>
    </div>
</body>
</html>
