<?php
/**
 * Match Scoring AJAX Handler
 * Handles real-time match scoring and status updates
 */

require_once '../auth.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require admin authentication
requireAdmin();

// Set JSON response header
header('Content-Type: application/json');

// Verify CSRF token
if (!isset($_POST['csrf_token']) || !verifyCSRFToken($_POST['csrf_token'])) {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid security token'
    ]);
    exit;
}

// Get database connection
$database = new Database();
$conn = $database->getConnection();

$action = $_POST['action'] ?? '';

try {
    switch ($action) {
        case 'save_match_score':
            handleSaveMatchScore($conn, $_POST);
            break;
            
        case 'start_match':
            handleStartMatch($conn, $_POST);
            break;
            
        case 'complete_match':
            handleCompleteMatch($conn, $_POST);
            break;
            
        default:
            throw new Exception('Invalid action');
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
}

/**
 * Save match score and determine winner
 */
function handleSaveMatchScore($conn, $data) {
    $match_id = intval($data['match_id'] ?? 0);
    $team1_score = intval($data['team1_score'] ?? 0);
    $team2_score = intval($data['team2_score'] ?? 0);
    
    if (!$match_id) {
        throw new Exception('Match ID is required');
    }
    
    if ($team1_score === $team2_score) {
        throw new Exception('Scores cannot be tied');
    }
    
    // Get match details
    $stmt = $conn->prepare("
        SELECT m.*, sc.category_name, s.name as sport_name, e.name as event_name
        FROM matches m
        JOIN sport_categories sc ON m.sport_category_id = sc.id
        JOIN event_sports es ON sc.event_sport_id = es.id
        JOIN sports s ON es.sport_id = s.id
        JOIN events e ON es.event_id = e.id
        WHERE m.id = ?
    ");
    $stmt->execute([$match_id]);
    $match = $stmt->fetch();
    
    if (!$match) {
        throw new Exception('Match not found');
    }
    
    // Determine winner and loser
    $winner_id = $team1_score > $team2_score ? $match['team_a_id'] : $match['team_b_id'];
    $loser_id = $team1_score > $team2_score ? $match['team_b_id'] : $match['team_a_id'];
    
    // Update match with scores and winner
    $stmt = $conn->prepare("
        UPDATE matches SET 
            team_a_score = ?, 
            team_b_score = ?, 
            winner_id = ?, 
            loser_id = ?, 
            status = 'completed',
            completed_at = NOW()
        WHERE id = ?
    ");
    
    $stmt->execute([
        $team1_score,
        $team2_score,
        $winner_id,
        $loser_id,
        $match_id
    ]);
    
    // Update department standings
    updateDepartmentStandings($conn, $match['sport_category_id']);
    
    // Log admin activity
    logAdminActivity('UPDATE_MATCH_SCORE', 'matches', $match_id, null, [
        'team1_score' => $team1_score,
        'team2_score' => $team2_score,
        'winner_id' => $winner_id,
        'match_info' => $match['sport_name'] . ' - ' . $match['category_name']
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Match score saved successfully',
        'winner_id' => $winner_id,
        'team1_score' => $team1_score,
        'team2_score' => $team2_score
    ]);
}

/**
 * Start a match (change status to ongoing)
 */
function handleStartMatch($conn, $data) {
    $match_id = intval($data['match_id'] ?? 0);
    
    if (!$match_id) {
        throw new Exception('Match ID is required');
    }
    
    // Get match details
    $stmt = $conn->prepare("SELECT * FROM matches WHERE id = ?");
    $stmt->execute([$match_id]);
    $match = $stmt->fetch();
    
    if (!$match) {
        throw new Exception('Match not found');
    }
    
    if ($match['status'] === 'completed') {
        throw new Exception('Cannot start a completed match');
    }
    
    // Update match status
    $stmt = $conn->prepare("
        UPDATE matches SET 
            status = 'ongoing',
            started_at = NOW()
        WHERE id = ?
    ");
    
    $stmt->execute([$match_id]);
    
    // Log admin activity
    logAdminActivity('START_MATCH', 'matches', $match_id, null, [
        'previous_status' => $match['status']
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Match started successfully'
    ]);
}

/**
 * Complete a match without scores (for special cases)
 */
function handleCompleteMatch($conn, $data) {
    $match_id = intval($data['match_id'] ?? 0);
    $winner_id = intval($data['winner_id'] ?? 0);
    
    if (!$match_id || !$winner_id) {
        throw new Exception('Match ID and winner ID are required');
    }
    
    // Get match details
    $stmt = $conn->prepare("SELECT * FROM matches WHERE id = ?");
    $stmt->execute([$match_id]);
    $match = $stmt->fetch();
    
    if (!$match) {
        throw new Exception('Match not found');
    }
    
    // Determine loser
    $loser_id = ($winner_id == $match['team_a_id']) ? $match['team_b_id'] : $match['team_a_id'];
    
    // Update match
    $stmt = $conn->prepare("
        UPDATE matches SET 
            winner_id = ?, 
            loser_id = ?, 
            status = 'completed',
            completed_at = NOW()
        WHERE id = ?
    ");
    
    $stmt->execute([$winner_id, $loser_id, $match_id]);
    
    // Update department standings
    updateDepartmentStandings($conn, $match['sport_category_id']);
    
    // Log admin activity
    logAdminActivity('COMPLETE_MATCH', 'matches', $match_id, null, [
        'winner_id' => $winner_id,
        'loser_id' => $loser_id
    ]);
    
    echo json_encode([
        'success' => true,
        'message' => 'Match completed successfully',
        'winner_id' => $winner_id
    ]);
}

/**
 * Update department standings based on match results
 */
function updateDepartmentStandings($conn, $sport_category_id) {
    // Get all matches for this category
    $stmt = $conn->prepare("
        SELECT winner_id, loser_id 
        FROM matches 
        WHERE sport_category_id = ? AND status = 'completed'
    ");
    $stmt->execute([$sport_category_id]);
    $completed_matches = $stmt->fetchAll();
    
    // Calculate standings
    $standings = [];
    
    foreach ($completed_matches as $match) {
        // Winner gets points
        if (!isset($standings[$match['winner_id']])) {
            $standings[$match['winner_id']] = ['wins' => 0, 'losses' => 0, 'points' => 0];
        }
        $standings[$match['winner_id']]['wins']++;
        $standings[$match['winner_id']]['points'] += 3; // 3 points for a win
        
        // Loser gets recorded
        if (!isset($standings[$match['loser_id']])) {
            $standings[$match['loser_id']] = ['wins' => 0, 'losses' => 0, 'points' => 0];
        }
        $standings[$match['loser_id']]['losses']++;
        $standings[$match['loser_id']]['points'] += 1; // 1 point for participation
    }
    
    // Update or insert standings records
    foreach ($standings as $department_id => $stats) {
        $stmt = $conn->prepare("
            INSERT INTO department_overall_scores 
            (event_department_registration_id, sport_id, wins, losses, points, last_updated)
            VALUES (
                (SELECT id FROM event_department_registrations WHERE department_id = ? LIMIT 1),
                (SELECT s.id FROM sports s 
                 JOIN event_sports es ON s.id = es.sport_id 
                 JOIN sport_categories sc ON es.id = sc.event_sport_id 
                 WHERE sc.id = ? LIMIT 1),
                ?, ?, ?, NOW()
            )
            ON DUPLICATE KEY UPDATE
            wins = VALUES(wins),
            losses = VALUES(losses),
            points = VALUES(points),
            last_updated = NOW()
        ");
        
        $stmt->execute([
            $department_id,
            $sport_category_id,
            $stats['wins'],
            $stats['losses'],
            $stats['points']
        ]);
    }
}
?>
