-- Create referee sessions table for secure referee access
CREATE TABLE IF NOT EXISTS referee_sessions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    match_id VARCHAR(255) NOT NULL,
    token VARCHAR(64) NOT NULL UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    status ENUM('active', 'used', 'expired') DEFAULT 'active',
    last_accessed TIMESTAMP NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    INDEX idx_match_id (match_id),
    INDEX idx_token (token),
    INDEX idx_status (status),
    INDEX idx_expires_at (expires_at)
);

-- Create tournament standings table if not exists
CREATE TABLE IF NOT EXISTS tournament_standings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    event_sport_id INT NOT NULL,
    team_id INT NOT NULL,
    matches_played INT DEFAULT 0,
    wins INT DEFAULT 0,
    losses INT DEFAULT 0,
    draws INT DEFAULT 0,
    points_for INT DEFAULT 0,
    points_against INT DEFAULT 0,
    points INT DEFAULT 0,
    goal_difference INT GENERATED ALWAYS AS (points_for - points_against) STORED,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_team_event_sport (event_sport_id, team_id),
    FOREIGN KEY (event_sport_id) REFERENCES event_sports(id) ON DELETE CASCADE,
    INDEX idx_event_sport (event_sport_id),
    INDEX idx_team (team_id),
    INDEX idx_points (points),
    INDEX idx_goal_difference (goal_difference)
);

-- Add referee notification fields to tournament_matches if not exists
ALTER TABLE tournament_matches 
ADD COLUMN IF NOT EXISTS referee_notified_at TIMESTAMP NULL,
ADD COLUMN IF NOT EXISTS notes TEXT NULL;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_tournament_matches_status ON tournament_matches(status);
CREATE INDEX IF NOT EXISTS idx_tournament_matches_round ON tournament_matches(round_number, match_number);

-- Insert sample tournament formats if not exists
INSERT IGNORE INTO tournament_formats (name, type, description, rounds_formula, matches_formula, algorithm_class) VALUES
('Single Elimination', 'elimination', 'Traditional knockout tournament where teams are eliminated after one loss', 'CEIL(LOG2(participants))', '(participants - 1)', 'SingleEliminationAlgorithm'),
('Double Elimination', 'elimination', 'Tournament with winners and losers brackets, teams eliminated after two losses', 'CEIL(LOG2(participants)) + CEIL(LOG2(participants/2))', '(participants * 2 - 2)', 'DoubleEliminationAlgorithm'),
('Round Robin', 'round_robin', 'Every team plays every other team once', '(participants - 1)', '(participants * (participants - 1) / 2)', 'RoundRobinAlgorithm'),
('Swiss System', 'swiss', 'Pairing system where teams with similar records play each other', 'CEIL(LOG2(participants))', 'CEIL(LOG2(participants)) * participants / 2', 'SwissSystemAlgorithm'),
('Multi-Stage', 'multi_stage', 'Combination of group stage and knockout rounds', 'Variable', 'Variable', 'MultiStageAlgorithm'),
('Judged Rounds', 'judged', 'Competition judged by panel with multiple rounds', 'Variable', 'participants', 'JudgedRoundsAlgorithm'),
('Performance Competition', 'performance', 'Individual or group performances judged by criteria', '1', 'participants', 'PerformanceAlgorithm');

-- Update existing event_sports to have tournament formats if null
UPDATE event_sports 
SET tournament_format_id = (
    SELECT id FROM tournament_formats 
    WHERE name = 'Single Elimination' 
    LIMIT 1
) 
WHERE tournament_format_id IS NULL;

-- Create admin activity log table if not exists
CREATE TABLE IF NOT EXISTS admin_activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    action VARCHAR(50) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(255) NULL,
    details JSON NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_admin_id (admin_id),
    INDEX idx_action (action),
    INDEX idx_resource_type (resource_type),
    INDEX idx_created_at (created_at)
);
