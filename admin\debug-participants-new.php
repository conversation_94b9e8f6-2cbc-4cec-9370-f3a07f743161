<?php
require_once '../config/database.php';

$database = new Database();
$conn = $database->getConnection();

$event_sport_id = 52;

echo "<h2>🔍 Participant System Debug</h2>";

echo "<h3>1. Check Available Tables</h3>";
$tables = ['departments', 'event_department_registrations', 'department_sport_participations', 'registrations'];
foreach ($tables as $table) {
    try {
        $stmt = $conn->prepare("SELECT COUNT(*) as count FROM $table");
        $stmt->execute();
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>✅ <strong>$table:</strong> " . $result['count'] . " records</p>";
    } catch (Exception $e) {
        echo "<p>❌ <strong>$table:</strong> Table doesn't exist or error: " . $e->getMessage() . "</p>";
    }
}

echo "<h3>2. Check Participants for Event Sport ID $event_sport_id</h3>";

// Test the simplified query
echo "<h4>Simplified Query (unified registration):</h4>";
try {
    $stmt = $conn->prepare("
        SELECT DISTINCT
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            edr.status,
            edr.created_at as registration_date
        FROM event_department_registrations edr
        JOIN event_sports es ON edr.event_id = es.event_id
        JOIN departments d ON edr.department_id = d.id
        WHERE es.id = ? AND edr.status IN ('pending', 'approved')
        ORDER BY d.name
    ");
    $stmt->execute([$event_sport_id]);
    $simple_participants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($simple_participants) {
        echo "<p>✅ Found " . count($simple_participants) . " participants via simplified query</p>";
        foreach ($simple_participants as $p) {
            echo "<p>- " . $p['name'] . " (" . $p['status'] . ")</p>";
        }
    } else {
        echo "<p>❌ No participants found via simplified query</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Simplified query failed: " . $e->getMessage() . "</p>";
}

// Test the fallback query (all active departments)
echo "<h4>Fallback Query (all active departments):</h4>";
try {
    $stmt = $conn->prepare("
        SELECT
            d.id,
            d.name,
            d.abbreviation,
            d.color_code,
            'available' as status,
            NOW() as registration_date
        FROM departments d
        WHERE d.status = 'active'
        ORDER BY d.name
        LIMIT 10
    ");
    $stmt->execute();
    $fallback_participants = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if ($fallback_participants) {
        echo "<p>✅ Found " . count($fallback_participants) . " participants via fallback query</p>";
        foreach ($fallback_participants as $p) {
            echo "<p>- " . $p['name'] . " (" . $p['status'] . ")</p>";
        }
    } else {
        echo "<p>❌ No participants found via fallback query</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Fallback query failed: " . $e->getMessage() . "</p>";
}

echo "<p><a href='manage-category.php?event_id=4&sport_id=37&category_id=16'>← Back to Category Management</a></p>";
?>
