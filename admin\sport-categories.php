<?php
/**
 * Sport Category Management for SC_IMS Admin Panel
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$current_admin = getCurrentAdmin();
$database = new Database();
$conn = $database->getConnection();

$event_id = $_GET['event_id'] ?? 0;
$sport_id = $_GET['sport_id'] ?? 0;

if (!$event_id || !$sport_id) {
    header('Location: events.php');
    exit;
}

// Get event and sport information
$stmt = $conn->prepare("
    SELECT e.name as event_name, s.name as sport_name, es.id as event_sport_id
    FROM events e
    JOIN event_sports es ON e.id = es.event_id
    JOIN sports s ON es.sport_id = s.id
    WHERE e.id = ? AND s.id = ?
");
$stmt->execute([$event_id, $sport_id]);
$event_sport = $stmt->fetch();

if (!$event_sport) {
    header('Location: events.php');
    exit;
}

// Extract event_sport_id for use in forms
$event_sport_id = $event_sport['event_sport_id'];

// Get all categories for this sport in this event
$stmt = $conn->prepare("
    SELECT * FROM sport_categories
    WHERE event_sport_id = ?
    ORDER BY category_name ASC
");
$stmt->execute([$event_sport_id]);
$categories = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($event_sport['sport_name']); ?> Categories | SC_IMS Admin</title>
    <?php include 'includes/admin-styles.php'; ?>
    <style>
        .category-link {
            color: #007bff;
            text-decoration: none;
            font-weight: 600;
            transition: all 0.2s ease;
        }
        .category-link:hover {
            color: #0056b3;
            text-decoration: underline;
        }
        .badge {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
        }
        .badge-men { background: #e3f2fd; color: #1976d2; }
        .badge-women { background: #fce4ec; color: #c2185b; }
        .badge-mixed { background: #f3e5f5; color: #7b1fa2; }
        .badge-open { background: #e8f5e8; color: #388e3c; }
        .badge-youth { background: #fff3e0; color: #f57c00; }
        .badge-senior { background: #efebe9; color: #5d4037; }
        .badge-other { background: #f5f5f5; color: #616161; }
    </style>
</head>
<body>
    <?php include 'includes/sidebar.php'; ?>

    <div class="admin-main">
        <header class="admin-header">
            <div class="header-left">
                <button class="sidebar-toggle" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <div class="breadcrumb">
                    <div class="breadcrumb-item">
                        <a href="events.php"><i class="fas fa-calendar"></i> Events</a>
                    </div>
                    <div class="breadcrumb-separator">></div>
                    <div class="breadcrumb-item">
                        <a href="manage-event.php?id=<?php echo $event_id; ?>"><?php echo htmlspecialchars($event_sport['event_name']); ?></a>
                    </div>
                    <div class="breadcrumb-separator">></div>
                    <div class="breadcrumb-item active">
                        <i class="fas fa-layer-group"></i>
                        <span><?php echo htmlspecialchars($event_sport['sport_name']); ?> Categories</span>
                    </div>
                </div>
            </div>
            <div class="header-right">
                <div class="admin-user">
                    <i class="fas fa-user-circle"></i>
                    <span><?php echo htmlspecialchars($current_admin['username']); ?></span>
                    <a href="logout.php" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </header>

        <main class="admin-content">
            <div class="page-header">
                <h1>
                    <i class="fas fa-layer-group"></i>
                    <?php echo htmlspecialchars($event_sport['sport_name']); ?> Categories
                </h1>
                <p class="page-description">
                    Define competition categories for <?php echo htmlspecialchars($event_sport['sport_name']); ?>
                    in <?php echo htmlspecialchars($event_sport['event_name']); ?>. Click on category names to manage tournaments.
                </p>
            </div>

            <div class="card">
                <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
                    <h3>All Categories</h3>
                    <button class="btn-modal-trigger" onclick="openModal('categoryModal')">
                        <i class="fas fa-plus"></i>
                        Add New Category
                    </button>
                </div>
                <div class="card-body">
                    <?php if (!empty($categories)): ?>
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Category Name</th>
                                    <th>Type</th>
                                    <th>Referee</th>
                                    <th>Email</th>
                                    <th>Venue</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($categories as $category): ?>
                                <tr>
                                    <td>
                                        <strong>
                                            <a href="manage-category.php?event_id=<?php echo $event_id; ?>&sport_id=<?php echo $sport_id; ?>&category_id=<?php echo $category['id']; ?>"
                                               class="category-link"
                                               title="Manage <?php echo htmlspecialchars($category['category_name'] ?? ''); ?>">
                                                🏆 <?php echo htmlspecialchars($category['category_name'] ?? ''); ?>
                                            </a>
                                        </strong>
                                    </td>
                                    <td>
                                        <span class="badge badge-<?php echo $category['category_type']; ?>">
                                            <?php
                                            if ($category['category_type'] === 'other' && !empty($category['category_type_custom'])) {
                                                echo htmlspecialchars($category['category_type_custom']);
                                            } else {
                                                echo ucfirst($category['category_type']);
                                            }
                                            ?>
                                        </span>
                                    </td>
                                    <td><?php echo htmlspecialchars($category['referee_name'] ?? 'N/A'); ?></td>
                                    <td>
                                        <?php if (!empty($category['referee_email'])): ?>
                                            <a href="mailto:<?php echo htmlspecialchars($category['referee_email']); ?>">
                                                <?php echo htmlspecialchars($category['referee_email']); ?>
                                            </a>
                                        <?php else: ?>
                                            N/A
                                        <?php endif; ?>
                                    </td>
                                    <td><?php echo htmlspecialchars($category['venue'] ?? 'N/A'); ?></td>
                                    <td>
                                        <div class="action-buttons">
                                            <button class="btn-edit" onclick="editCategory(<?php echo $category['id']; ?>)">
                                                <i class="fas fa-edit"></i> Edit
                                            </button>
                                            <button class="btn-delete" onclick="deleteCategory(<?php echo $category['id']; ?>, '<?php echo htmlspecialchars($category['category_name'] ?? ''); ?>')">
                                                <i class="fas fa-trash"></i> Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php else: ?>
                        <div class="empty-state">
                            <i class="fas fa-layer-group"></i>
                            <h3>No Categories Yet</h3>
                            <p>Start by adding categories for this sport.</p>
                            <button class="btn btn-primary" onclick="openModal('categoryModal')">
                                <i class="fas fa-plus"></i> Add First Category
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </main>
    </div>

    <!-- Category Modal -->
    <div id="categoryModal" class="modal">
        <div class="modal-dialog">
            <div class="modal-loading">
                <div class="loading-spinner"></div>
            </div>
            <div class="modal-header">
                <h3 class="modal-title" id="categoryModalTitle">
                    <i class="fas fa-layer-group" style="color: var(--primary-color); margin-right: 8px;"></i>
                    Add New Category
                </h3>
                <button class="modal-close">&times;</button>
            </div>

            <div class="modal-body">
                <form class="modal-form" action="ajax/modal-handler.php" method="POST">
                    <input type="hidden" name="entity" value="sport_category">
                    <input type="hidden" name="action" value="create" id="categoryAction">
                    <input type="hidden" name="id" id="categoryId">
                    <input type="hidden" name="event_sport_id" value="<?php echo $event_sport_id; ?>">
                    <input type="hidden" name="csrf_token" value="<?php echo generateCSRFToken(); ?>">

                    <!-- Basic Information Section -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-info-circle"></i>
                            Basic Information
                        </h4>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="category_name" class="form-label">
                                    Category Name <span class="required">*</span>
                                </label>
                                <input type="text" id="category_name" name="category_name"
                                       class="form-control" required
                                       placeholder="e.g., Men's Singles A, Women's Doubles">
                                <div class="error-message" id="category_name_error"></div>
                            </div>

                            <div class="form-group">
                                <label for="category_type" class="form-label">
                                    Category Type <span class="required">*</span>
                                </label>
                                <select id="category_type" name="category_type" class="form-control" required>
                                    <option value="">Select category type</option>
                                    <option value="men">Men's</option>
                                    <option value="women">Women's</option>
                                    <option value="mixed">Mixed</option>
                                    <option value="open">Open</option>
                                    <option value="youth">Youth</option>
                                    <option value="senior">Senior</option>
                                    <option value="other">Other</option>
                                </select>
                                <div class="error-message" id="category_type_error"></div>
                            </div>
                        </div>

                        <div class="form-row single" id="customTypeGroup" style="display: none;">
                            <div class="form-group">
                                <label for="category_type_custom" class="form-label">
                                    Custom Category Type
                                </label>
                                <input type="text" id="category_type_custom" name="category_type_custom"
                                       class="form-control" placeholder="Enter custom category type">
                                <div class="error-message" id="category_type_custom_error"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Officials & Venue Section -->
                    <div class="form-section">
                        <h4 class="form-section-title">
                            <i class="fas fa-users"></i>
                            Officials & Venue
                        </h4>

                        <div class="form-row">
                            <div class="form-group">
                                <label for="referee_name" class="form-label">
                                    Referee Name
                                </label>
                                <input type="text" id="referee_name" name="referee_name"
                                       class="form-control" placeholder="Enter referee name">
                                <div class="error-message" id="referee_name_error"></div>
                            </div>

                            <div class="form-group">
                                <label for="referee_email" class="form-label">
                                    Referee Email
                                </label>
                                <input type="email" id="referee_email" name="referee_email"
                                       class="form-control" placeholder="<EMAIL>">
                                <div class="error-message" id="referee_email_error"></div>
                            </div>
                        </div>

                        <div class="form-row single">
                            <div class="form-group">
                                <label for="venue" class="form-label">
                                    Venue
                                </label>
                                <input type="text" id="venue" name="venue"
                                       class="form-control" placeholder="Enter venue location">
                                <div class="error-message" id="venue_error"></div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" onclick="closeModal('categoryModal')">
                    <i class="fas fa-times"></i> Cancel
                </button>
                <button type="submit" class="btn btn-primary" form="categoryModal" onclick="submitCategoryForm()">
                    <span class="btn-text">
                        <i class="fas fa-plus"></i> Create Category
                    </span>
                    <span class="btn-loading" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i> Creating...
                    </span>
                </button>
            </div>
        </div>
    </div>

    <?php include 'includes/admin-scripts.php'; ?>
    <script>
        // Handle category type selection
        document.getElementById('category_type').addEventListener('change', function() {
            const customTypeGroup = document.getElementById('customTypeGroup');
            const customTypeInput = document.getElementById('category_type_custom');

            if (this.value === 'other') {
                customTypeGroup.style.display = 'block';
                customTypeInput.required = true;
            } else {
                customTypeGroup.style.display = 'none';
                customTypeInput.required = false;
                customTypeInput.value = '';
            }
        });

        // Handle category form submission
        function submitCategoryForm() {
            const form = document.querySelector('#categoryModal .modal-form');
            const formData = new FormData(form);
            const submitBtn = document.querySelector('#categoryModal .btn-primary');
            const btnText = submitBtn.querySelector('.btn-text');
            const btnLoading = submitBtn.querySelector('.btn-loading');

            // Determine if this is create or update
            const action = document.getElementById('categoryAction').value;
            const isUpdate = action === 'update';

            // Clear previous errors
            clearFormErrors();

            // Validate required fields
            if (!validateForm()) {
                return;
            }

            // Show loading state
            submitBtn.disabled = true;
            btnText.style.display = 'none';
            btnLoading.style.display = 'inline-flex';

            // Update loading text based on action
            const loadingText = btnLoading.querySelector('span') || btnLoading;
            if (isUpdate) {
                loadingText.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Updating...';
            } else {
                loadingText.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Creating...';
            }

            fetch('ajax/modal-handler.php', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const successMessage = isUpdate ? 'Category updated successfully!' : 'Category created successfully!';
                    showNotification(successMessage, 'success');
                    closeModal('categoryModal');
                    // Reload page to show updated data
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    const errorMessage = isUpdate ? 'Failed to update category' : 'Failed to create category';
                    showNotification(data.message || errorMessage, 'error');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                const errorMessage = isUpdate ? 'An error occurred while updating the category' : 'An error occurred while creating the category';
                showNotification(errorMessage, 'error');
            })
            .finally(() => {
                // Reset button state
                submitBtn.disabled = false;
                btnText.style.display = 'inline-flex';
                btnLoading.style.display = 'none';
            });
        }

        // Form validation function
        function validateForm() {
            let isValid = true;

            // Validate category name
            const categoryName = document.getElementById('category_name');
            if (!categoryName.value.trim()) {
                showFieldError('category_name', 'Category name is required');
                isValid = false;
            }

            // Validate category type
            const categoryType = document.getElementById('category_type');
            if (!categoryType.value) {
                showFieldError('category_type', 'Category type is required');
                isValid = false;
            }

            // Validate custom type if "other" is selected
            if (categoryType.value === 'other') {
                const customType = document.getElementById('category_type_custom');
                if (!customType.value.trim()) {
                    showFieldError('category_type_custom', 'Custom category type is required');
                    isValid = false;
                }
            }

            // Validate email format if provided
            const email = document.getElementById('referee_email');
            if (email.value && !isValidEmail(email.value)) {
                showFieldError('referee_email', 'Please enter a valid email address');
                isValid = false;
            }

            return isValid;
        }

        // Helper functions
        function showFieldError(fieldId, message) {
            const field = document.getElementById(fieldId);
            const errorDiv = document.getElementById(fieldId + '_error');

            field.classList.add('error');
            errorDiv.textContent = message;
            errorDiv.classList.add('show');
        }

        function clearFormErrors() {
            const errorMessages = document.querySelectorAll('#categoryModal .error-message');
            const errorFields = document.querySelectorAll('#categoryModal .form-control.error');

            errorMessages.forEach(error => {
                error.classList.remove('show');
                error.textContent = '';
            });

            errorFields.forEach(field => {
                field.classList.remove('error');
            });
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function editCategory(categoryId) {
            console.log('Edit category:', categoryId);

            // Fetch category data
            fetch(`ajax/get-category.php?id=${categoryId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        populateEditForm(data.category);
                        openModal('categoryModal');
                    } else {
                        showNotification(data.message || 'Failed to load category data', 'error');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    showNotification('An error occurred while loading category data', 'error');
                });
        }

        function populateEditForm(category) {
            // Set form action to update
            document.getElementById('categoryAction').value = 'update';
            document.getElementById('categoryId').value = category.id;

            // Update modal title
            document.getElementById('categoryModalTitle').innerHTML = `
                <i class="fas fa-edit" style="color: var(--primary-color); margin-right: 8px;"></i>
                Edit Category
            `;

            // Populate form fields
            document.getElementById('category_name').value = category.category_name || '';
            document.getElementById('category_type').value = category.category_type || '';
            document.getElementById('referee_name').value = category.referee_name || '';
            document.getElementById('referee_email').value = category.referee_email || '';
            document.getElementById('venue').value = category.venue || '';

            // Handle custom category type
            if (category.category_type === 'other' && category.category_type_custom) {
                document.getElementById('customTypeGroup').style.display = 'block';
                document.getElementById('category_type_custom').value = category.category_type_custom;
                document.getElementById('category_type_custom').required = true;
            } else {
                document.getElementById('customTypeGroup').style.display = 'none';
                document.getElementById('category_type_custom').value = '';
                document.getElementById('category_type_custom').required = false;
            }

            // Update button text
            const submitBtn = document.querySelector('#categoryModal .btn-primary');
            const btnText = submitBtn.querySelector('.btn-text');
            btnText.innerHTML = '<i class="fas fa-save"></i> Update Category';

            // Clear any previous errors
            clearFormErrors();
        }

        function deleteCategory(categoryId, categoryName) {
            if (confirm(`Delete category "${categoryName}"?`)) {
                console.log('Delete category:', categoryId);
                // TODO: Implement delete functionality
            }
        }

        // Reset form when modal opens (for create mode)
        function openModal(modalId) {
            if (modalId === 'categoryModal') {
                // Only reset for create mode (when action is not already set to update)
                const currentAction = document.getElementById('categoryAction').value;

                if (currentAction !== 'update') {
                    // Reset form for create mode
                    document.querySelector('#categoryModal .modal-form').reset();
                    document.getElementById('customTypeGroup').style.display = 'none';
                    document.getElementById('category_type_custom').required = false;

                    // Set action to create
                    document.getElementById('categoryAction').value = 'create';
                    document.getElementById('categoryId').value = '';
                    document.getElementById('categoryModalTitle').innerHTML = `
                        <i class="fas fa-layer-group" style="color: var(--primary-color); margin-right: 8px;"></i>
                        Add New Category
                    `;

                    // Reset button text for create mode
                    const submitBtn = document.querySelector('#categoryModal .btn-primary');
                    const btnText = submitBtn.querySelector('.btn-text');
                    btnText.innerHTML = '<i class="fas fa-plus"></i> Create Category';
                }

                // Clear any previous errors
                clearFormErrors();

                // Reset button state
                const submitBtn = document.querySelector('#categoryModal .btn-primary');
                const btnText = submitBtn.querySelector('.btn-text');
                const btnLoading = submitBtn.querySelector('.btn-loading');

                submitBtn.disabled = false;
                btnText.style.display = 'inline-flex';
                btnLoading.style.display = 'none';
            }

            // Call the global openModal function
            if (typeof window.openModal === 'function') {
                window.openModal(modalId);
            }
        }
    </script>
</body>
</html>