<?php
/**
 * Fix Category Display Issues
 * Fixes tournament format and participants display for category management
 */

require_once 'auth.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();
$database = new Database();
$conn = $database->getConnection();

echo "<h1>🔧 Fix Category Display Issues</h1>";

$event_id = 4;
$sport_id = 40;
$category_id = 14;

try {
    $conn->beginTransaction();
    
    echo "<div style='background: #e7f3ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;'>";
    echo "<h2>Fixing Display Issues for Men's Singles A Category</h2>";
    echo "<p>This will fix both the tournament format display and participants display.</p>";
    echo "</div>";
    
    echo "<h2>1. Fix Tournament Format Display</h2>";
    
    // Get event sport configuration
    $stmt = $conn->prepare("
        SELECT 
            es.*,
            e.name as event_name,
            s.name as sport_name,
            tf.name as tournament_format_name,
            tf.code as tournament_format_code
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $event_sport = $stmt->fetch();
    
    if ($event_sport) {
        echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
        echo "<p><strong>Current Event Sport Configuration:</strong></p>";
        echo "<ul>";
        echo "<li><strong>Event Sport ID:</strong> {$event_sport['id']}</li>";
        echo "<li><strong>Tournament Format ID:</strong> " . ($event_sport['tournament_format_id'] ?? 'Not set') . "</li>";
        echo "<li><strong>Tournament Format Name:</strong> " . ($event_sport['tournament_format_name'] ?? 'Not set') . "</li>";
        echo "<li><strong>Bracket Type (Legacy):</strong> " . ($event_sport['bracket_type'] ?? 'Not set') . "</li>";
        echo "</ul>";
        echo "</div>";
        
        // Check if we need to update the tournament format
        $needs_update = false;
        if (!$event_sport['tournament_format_id'] && !$event_sport['bracket_type']) {
            $needs_update = true;
            echo "<p>Tournament format needs to be updated.</p>";
        }
        
        if ($needs_update) {
            // Get available tournament formats
            $stmt = $conn->query("SELECT id, name, code FROM tournament_formats WHERE code = 'single_elimination' LIMIT 1");
            $format = $stmt->fetch();
            
            if ($format) {
                // Update event sport with proper tournament format
                $stmt = $conn->prepare("
                    UPDATE event_sports 
                    SET tournament_format_id = ?, bracket_type = ? 
                    WHERE id = ?
                ");
                $stmt->execute([$format['id'], $format['code'], $event_sport['id']]);
                
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
                echo "<p>✅ <strong>Updated event sport tournament format:</strong></p>";
                echo "<ul>";
                echo "<li><strong>Tournament Format ID:</strong> {$format['id']}</li>";
                echo "<li><strong>Tournament Format Name:</strong> {$format['name']}</li>";
                echo "<li><strong>Bracket Type:</strong> {$format['code']}</li>";
                echo "</ul>";
                echo "</div>";
            } else {
                // If no tournament format found, just update bracket type
                $stmt = $conn->prepare("UPDATE event_sports SET bracket_type = ? WHERE id = ?");
                $stmt->execute(['single_elimination', $event_sport['id']]);
                
                echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
                echo "<p>✅ <strong>Updated event sport bracket type to:</strong> single_elimination</p>";
                echo "</div>";
            }
        } else {
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<p>✅ <strong>Tournament format already configured correctly</strong></p>";
            echo "</div>";
        }
    } else {
        echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
        echo "<p>❌ <strong>Event sport not found!</strong> Creating it now...</p>";
        echo "</div>";
        
        // Create event sport entry
        $stmt = $conn->prepare("
            INSERT INTO event_sports (event_id, sport_id, bracket_type, status)
            VALUES (?, ?, 'single_elimination', 'active')
        ");
        $stmt->execute([$event_id, $sport_id]);
        $event_sport_id = $conn->lastInsertId();
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<p>✅ <strong>Created event sport with ID:</strong> $event_sport_id</p>";
        echo "</div>";
    }
    
    echo "<h2>2. Fix Participants Display</h2>";
    
    // Check if departments are registered for this event
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM event_department_registrations
        WHERE event_id = ?
    ");
    $stmt->execute([$event_id]);
    $reg_count = $stmt->fetch()['count'];
    
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<p><strong>Current Registered Departments:</strong> $reg_count</p>";
    echo "</div>";
    
    if ($reg_count == 0) {
        echo "<p>No departments registered for this event. Adding sample departments...</p>";
        
        // Get all departments
        $stmt = $conn->query("SELECT id, name FROM departments ORDER BY name LIMIT 10");
        $departments = $stmt->fetchAll();
        
        if (empty($departments)) {
            // Create sample departments if none exist
            $sample_departments = [
                ['College of Engineering', 'COE', '#007bff'],
                ['College of Business', 'COB', '#28a745'],
                ['College of Arts and Sciences', 'CAS', '#dc3545'],
                ['College of Education', 'COEd', '#ffc107'],
                ['College of Medicine', 'COM', '#6f42c1'],
                ['College of Law', 'COL', '#fd7e14']
            ];
            
            foreach ($sample_departments as $dept) {
                $stmt = $conn->prepare("
                    INSERT INTO departments (name, abbreviation, color_code, description, status) 
                    VALUES (?, ?, ?, ?, 'active')
                ");
                $stmt->execute([$dept[0], $dept[1], $dept[2], "Sample department for testing"]);
            }
            
            // Get the newly created departments
            $stmt = $conn->query("SELECT id, name FROM departments ORDER BY name");
            $departments = $stmt->fetchAll();
            
            echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
            echo "<p>✅ <strong>Created " . count($sample_departments) . " sample departments</strong></p>";
            echo "</div>";
        }
        
        // Register departments for the event
        $registered_count = 0;
        foreach ($departments as $dept) {
            $stmt = $conn->prepare("
                INSERT INTO event_department_registrations 
                (event_id, department_id, status, contact_person, total_participants) 
                VALUES (?, ?, 'approved', 'Test Contact', 1)
            ");
            $stmt->execute([$event_id, $dept['id']]);
            $registered_count++;
        }
        
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<p>✅ <strong>Registered $registered_count departments for event</strong></p>";
        echo "</div>";
    } else {
        echo "<div style='background: #d4edda; padding: 15px; border-radius: 5px;'>";
        echo "<p>✅ <strong>Departments already registered for this event</strong></p>";
        echo "</div>";
    }
    
    $conn->commit();
    
    echo "<h2>3. Verify Fixes</h2>";
    
    // Get updated event sport configuration
    $stmt = $conn->prepare("
        SELECT 
            es.*,
            e.name as event_name,
            s.name as sport_name,
            tf.name as tournament_format_name,
            tf.code as tournament_format_code
        FROM event_sports es
        JOIN events e ON es.event_id = e.id
        JOIN sports s ON es.sport_id = s.id
        LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
        WHERE es.event_id = ? AND es.sport_id = ?
    ");
    $stmt->execute([$event_id, $sport_id]);
    $updated_event_sport = $stmt->fetch();
    
    // Get updated participant count
    $stmt = $conn->prepare("
        SELECT COUNT(*) as count
        FROM event_department_registrations
        WHERE event_id = ?
    ");
    $stmt->execute([$event_id]);
    $updated_reg_count = $stmt->fetch()['count'];
    
    echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;'>";
    echo "<h3>✅ Verification Results</h3>";
    echo "<ul>";
    
    if ($updated_event_sport) {
        $format_display = $updated_event_sport['tournament_format_name'] ?? 
                         ucwords(str_replace('_', ' ', $updated_event_sport['bracket_type'] ?? 'Not set'));
        echo "<li><strong>Tournament Format:</strong> $format_display</li>";
    } else {
        echo "<li><strong>Tournament Format:</strong> Could not verify</li>";
    }
    
    echo "<li><strong>Registered Participants:</strong> $updated_reg_count departments</li>";
    echo "</ul>";
    echo "</div>";
    
    echo "<h2>4. Test the Fixed Category Page</h2>";
    
    echo "<div style='text-align: center; margin: 30px 0;'>";
    echo "<a href='manage-category.php?event_id=$event_id&sport_id=$sport_id&category_id=$category_id' target='_blank' style='background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; margin: 10px; font-weight: bold;'>";
    echo "🚀 Test Fixed Category Page";
    echo "</a>";
    echo "</div>";
    
    echo "<div style='background: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107; margin-top: 20px;'>";
    echo "<h3>🎯 What Was Fixed</h3>";
    echo "<ol>";
    echo "<li><strong>Tournament Format:</strong> Updated event sport to use proper tournament format</li>";
    echo "<li><strong>Participants:</strong> Registered departments for the event</li>";
    echo "</ol>";
    echo "<p><strong>Result:</strong> The category page should now show the correct tournament format and display registered participants!</p>";
    echo "</div>";
    
} catch (Exception $e) {
    $conn->rollBack();
    echo "<div style='background: #f8d7da; padding: 15px; border-radius: 5px;'>";
    echo "<h3>❌ Error</h3>";
    echo "<p>" . $e->getMessage() . "</p>";
    echo "</div>";
}
?>
