<?php
/**
 * Interactive Bracket Display Component
 * Renders tournament brackets with match editing capabilities
 */

class BracketDisplay {
    private $conn;
    private $tournament_id;
    private $bracket_data;
    
    public function __construct($conn, $tournament_id) {
        $this->conn = $conn;
        $this->tournament_id = $tournament_id;
        $this->loadBracketData();
    }
    
    private function loadBracketData() {
        $stmt = $this->conn->prepare("
            SELECT ts.*, tf.name as format_name, tf.code as format_code
            FROM tournament_structures ts
            LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
            WHERE ts.id = ?
        ");
        $stmt->execute([$this->tournament_id]);
        $tournament = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($tournament && $tournament['bracket_data']) {
            $this->bracket_data = json_decode($tournament['bracket_data'], true);
        } else {
            $this->bracket_data = null;
        }
    }

    private function getTournamentRequirements() {
        // Get tournament info
        $stmt = $this->conn->prepare("
            SELECT es.*, tf.name as format_name, tf.min_participants, tf.max_participants,
                   s.name as sport_name, s.type as sport_type
            FROM event_sports es
            LEFT JOIN tournament_formats tf ON es.tournament_format_id = tf.id
            LEFT JOIN sports s ON es.sport_id = s.id
            WHERE es.id = ?
        ");
        $stmt->execute([$this->tournament_id]);
        $tournament = $stmt->fetch(PDO::FETCH_ASSOC);

        // Get current participants using unified registration system
        $stmt = $this->conn->prepare("
            SELECT COUNT(DISTINCT edr.department_id) as participant_count
            FROM event_department_registrations edr
            JOIN department_sport_participations dsp ON edr.id = dsp.event_department_registration_id
            WHERE dsp.event_sport_id = ?
            AND edr.status IN ('pending', 'approved')
            AND dsp.status IN ('registered', 'confirmed')
        ");
        $stmt->execute([$this->tournament_id]);
        $participants = $stmt->fetch(PDO::FETCH_ASSOC);
        $current_participants = $participants['participant_count'] ?? 0;

        $requirements = [];
        $all_met = true;

        // Check minimum participants
        $min_participants = $tournament['min_participants'] ?? 2;
        $participants_met = $current_participants >= $min_participants;
        $requirements[] = [
            'label' => 'Minimum Participants',
            'status' => $participants_met,
            'description' => "Need at least {$min_participants} participants",
            'current' => "{$current_participants}/{$min_participants} registered"
        ];
        if (!$participants_met) $all_met = false;

        // Check tournament format
        $format_set = !empty($tournament['tournament_format_id']);
        $requirements[] = [
            'label' => 'Tournament Format',
            'status' => $format_set,
            'description' => 'Tournament format must be configured',
            'current' => $format_set ? $tournament['format_name'] : 'Not set'
        ];
        if (!$format_set) $all_met = false;

        // Check referee assignment
        $referee_assigned = !empty($tournament['referee_id']);
        $requirements[] = [
            'label' => 'Referee Assignment',
            'status' => $referee_assigned,
            'description' => 'Referee must be assigned to manage matches',
            'current' => $referee_assigned ? 'Assigned' : 'Not assigned'
        ];
        if (!$referee_assigned) $all_met = false;

        // Generate HTML
        $html = '<div class="requirements-list">';
        foreach ($requirements as $req) {
            $status_class = $req['status'] ? 'requirement-met' : 'requirement-pending';
            $icon = $req['status'] ? 'fas fa-check-circle' : 'fas fa-clock';
            $color = $req['status'] ? '#28a745' : '#ffc107';

            $html .= '<div class="requirement-item" style="display: flex; align-items: center; margin-bottom: 12px; padding: 10px; background: white; border-radius: 6px; border-left: 4px solid ' . $color . ';">
                <i class="' . $icon . '" style="color: ' . $color . '; margin-right: 12px; font-size: 1.1em;"></i>
                <div style="flex: 1;">
                    <div style="font-weight: 600; color: #495057; margin-bottom: 2px;">' . $req['label'] . '</div>
                    <div style="font-size: 0.9em; color: #6c757d;">' . $req['description'] . '</div>
                    <div style="font-size: 0.85em; color: #495057; margin-top: 2px;"><strong>Current:</strong> ' . $req['current'] . '</div>
                </div>
            </div>';
        }
        $html .= '</div>';

        return [
            'can_generate' => $all_met,
            'requirements' => $requirements,
            'html' => $html
        ];
    }
    
    public function renderBracket() {
        // Always render enhanced bracket with real match data
        return $this->renderEnhancedBracket();
    }

    private function renderEnhancedBracket() {
        // Get actual matches from database
        $matches = $this->getMatches();

        if (empty($matches)) {
            return $this->renderEmptyBracket();
        }

        // Group matches by rounds
        $rounds = [];
        foreach ($matches as $match) {
            $rounds[$match['round_number']][] = $match;
        }

        $html = '<div class="sc-bracket-container">';
        $html .= '<div class="sc-bracket-notice">';
        $html .= '<i class="fas fa-trophy"></i>';
        $html .= 'Interactive Tournament Bracket - Click edit buttons to manage matches';
        $html .= '</div>';

        foreach ($rounds as $round_number => $round_matches) {
            $html .= $this->renderEnhancedRound($round_number, $round_matches);
        }

        $html .= '</div>';
        return $html;
    }

    private function getMatches() {
        try {
            $stmt = $this->conn->prepare("
                SELECT
                    m.*,
                    d1.name as team1_name,
                    d1.abbreviation as team1_department,
                    d2.name as team2_name,
                    d2.abbreviation as team2_department,
                    tf.name as format_name
                FROM tournament_matches m
                LEFT JOIN departments d1 ON m.team1_id = d1.id
                LEFT JOIN departments d2 ON m.team2_id = d2.id
                LEFT JOIN tournament_structures ts ON m.tournament_structure_id = ts.id
                LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
                WHERE m.tournament_structure_id = ?
                ORDER BY m.round_number, m.match_number
            ");
            $stmt->execute([$this->tournament_id]);
            return $stmt->fetchAll(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("Error getting matches: " . $e->getMessage());
            return [];
        }
    }

    private function renderEnhancedRound($round_number, $matches) {
        $round_name = $this->getRoundName($round_number, count($matches));

        $html = '<div class="sc-round">';
        $html .= '<div class="sc-round-header">';
        $html .= $round_name;
        $html .= '</div>';

        $html .= '<div class="sc-round-matches">';
        foreach ($matches as $match) {
            $html .= $this->renderMatch($match);
        }
        $html .= '</div>';

        $html .= '</div>';
        return $html;
    }

    private function getRoundName($round_number, $match_count) {
        // Determine round name based on round number and match count
        if ($round_number == 1) {
            if ($match_count > 4) return 'First Round';
            if ($match_count == 4) return 'Quarterfinals';
            if ($match_count == 2) return 'Semifinals';
            if ($match_count == 1) return 'Final';
        } elseif ($round_number == 2) {
            if ($match_count == 2) return 'Semifinals';
            if ($match_count == 1) return 'Final';
        } elseif ($round_number == 3) {
            return 'Final';
        }

        return 'Round ' . $round_number;
    }
    
    private function renderEmptyBracket() {
        // Get tournament requirements
        $requirements = $this->getTournamentRequirements();

        return '<div class="sc-bracket-container">
            <div class="tournament-requirements" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 8px; padding: 20px; margin-bottom: 20px;">
                <h4 style="color: #495057; margin-bottom: 15px; display: flex; align-items: center;">
                    <i class="fas fa-list-check" style="margin-right: 8px; color: #6c757d;"></i>
                    Tournament Generation Requirements
                </h4>
                ' . $requirements['html'] . '
            </div>
            <div class="empty-state" style="text-align: center; padding: 40px;">
                <i class="fas fa-sitemap" style="font-size: 4em; color: #6c757d; margin-bottom: 20px;"></i>
                <h4 style="color: #343a40; margin-bottom: 15px;">Tournament Bracket</h4>
                <p style="color: #6c757d; margin-bottom: 25px;">
                    ' . ($requirements['can_generate'] ?
                        'Tournament will be automatically generated when all requirements are met' :
                        'Complete the requirements above to enable automatic tournament generation') . '
                </p>
                ' . ($requirements['can_generate'] ?
                    '<div class="auto-generation-indicator" style="background: #d4edda; border: 1px solid #c3e6cb; border-radius: 6px; padding: 12px; color: #155724;">
                        <i class="fas fa-check-circle" style="margin-right: 8px;"></i>
                        Ready for automatic generation
                    </div>' : '') . '
            </div>
        </div>';
    }
    
    private function renderSingleElimination() {
        $html = '<div class="bracket-container single-elimination">';
        
        if (isset($this->bracket_data['rounds'])) {
            foreach ($this->bracket_data['rounds'] as $round) {
                $html .= $this->renderRound($round);
            }
        }
        
        $html .= '</div>';
        return $html;
    }
    
    private function renderRound($round) {
        $html = '<div class="bracket-round" data-round="' . $round['round_number'] . '">';
        $html .= '<div class="round-header">';
        $html .= '<h5>' . htmlspecialchars($round['round_name']) . '</h5>';
        $html .= '</div>';
        $html .= '<div class="round-matches">';
        
        foreach ($round['matches'] as $match) {
            $html .= $this->renderMatch($match);
        }
        
        $html .= '</div>';
        $html .= '</div>';
        return $html;
    }
    
    private function renderMatch($match) {
        $matchId = $match['id'] ?? uniqid();
        $status = $match['status'] ?? 'pending';
        $statusClass = $this->getStatusClass($status);

        $html = '<div class="sc-match-card ' . $statusClass . '" data-match-id="' . $matchId . '">';

        // Match header with SC_IMS branding
        $html .= '<div class="sc-match-header">';
        $html .= '<div class="sc-match-info">';
        $html .= '<span class="sc-match-number">R' . ($match['round_number'] ?? '1') . 'M' . ($match['match_number'] ?? '1') . '</span>';
        $html .= '<span class="sc-match-format">' . ($match['format_name'] ?? 'Single Elimination') . '</span>';
        $html .= '</div>';
        $html .= '<div class="sc-match-actions">';
        $html .= '<button class="sc-btn sc-btn-edit" onclick="openMatchEditModal(\'' . $matchId . '\')" title="Edit Match">';
        $html .= '<i class="fas fa-edit"></i>';
        $html .= '</button>';
        $html .= '<button class="sc-btn sc-btn-referee" onclick="sendToReferee(\'' . $matchId . '\')" title="Send to Referee">';
        $html .= '<i class="fas fa-whistle"></i>';
        $html .= '</button>';
        $html .= '</div>';
        $html .= '</div>';

        // Match content with unique SC_IMS design
        $html .= '<div class="sc-match-content">';

        // Team 1 with enhanced design
        $isTeam1Winner = isset($match['winner_id']) && $match['winner_id'] == $match['team1_id'];
        $html .= '<div class="sc-team-container team1 ' . ($isTeam1Winner ? 'winner' : '') . '">';
        $html .= '<div class="sc-team-avatar">';
        $html .= '<i class="fas fa-users"></i>';
        $html .= '</div>';
        $html .= '<div class="sc-team-details">';
        $html .= '<div class="sc-team-name">' . htmlspecialchars($match['team1_name'] ?? 'TBD') . '</div>';
        $html .= '<div class="sc-team-department">' . htmlspecialchars($match['team1_department'] ?? '') . '</div>';
        $html .= '</div>';
        $html .= '<div class="sc-team-score">';
        $html .= '<span class="sc-score-value">' . ($match['team1_score'] ?? '-') . '</span>';
        $html .= '</div>';
        $html .= '</div>';

        // VS separator with SC_IMS styling
        $html .= '<div class="sc-match-vs">';
        $html .= '<div class="sc-vs-circle">';
        $html .= '<span>VS</span>';
        $html .= '</div>';
        $html .= '</div>';

        // Team 2 with enhanced design
        $isTeam2Winner = isset($match['winner_id']) && $match['winner_id'] == $match['team2_id'];
        $html .= '<div class="sc-team-container team2 ' . ($isTeam2Winner ? 'winner' : '') . '">';
        $html .= '<div class="sc-team-avatar">';
        $html .= '<i class="fas fa-users"></i>';
        $html .= '</div>';
        $html .= '<div class="sc-team-details">';
        $html .= '<div class="sc-team-name">' . htmlspecialchars($match['team2_name'] ?? 'TBD') . '</div>';
        $html .= '<div class="sc-team-department">' . htmlspecialchars($match['team2_department'] ?? '') . '</div>';
        $html .= '</div>';
        $html .= '<div class="sc-team-score">';
        $html .= '<span class="sc-score-value">' . ($match['team2_score'] ?? '-') . '</span>';
        $html .= '</div>';
        $html .= '</div>';

        $html .= '</div>'; // End match content

        // Match footer with status and timing
        $html .= '<div class="sc-match-footer">';
        $html .= '<div class="sc-match-status">';
        $html .= '<span class="sc-status-badge sc-status-' . $status . '">';
        $html .= '<i class="fas fa-' . $this->getStatusIcon($status) . '"></i>';
        $html .= ucfirst(str_replace('_', ' ', $status));
        $html .= '</span>';
        $html .= '</div>';
        if (isset($match['scheduled_time'])) {
            $html .= '<div class="sc-match-time">';
            $html .= '<i class="fas fa-clock"></i>';
            $html .= date('H:i', strtotime($match['scheduled_time']));
            $html .= '</div>';
        }
        $html .= '</div>';

        $html .= '</div>'; // End match card
        return $html;
    }
    
    private function renderRoundRobin() {
        $html = '<div class="bracket-container round-robin">';
        $html .= '<div class="round-robin-table">';
        
        // Standings table
        $html .= $this->renderStandingsTable();
        
        // Matches list
        $html .= '<div class="matches-section">';
        $html .= '<h5>Matches</h5>';
        
        if (isset($this->bracket_data['rounds'][0]['matches'])) {
            foreach ($this->bracket_data['rounds'][0]['matches'] as $match) {
                $html .= $this->renderMatch($match);
            }
        }
        
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        return $html;
    }
    
    private function renderMultiStage() {
        $html = '<div class="bracket-container multi-stage">';
        
        // Group stage
        if (isset($this->bracket_data['group_stage'])) {
            $html .= '<div class="group-stage">';
            $html .= '<h4>Group Stage</h4>';
            
            foreach ($this->bracket_data['group_stage'] as $groupName => $groupData) {
                $html .= '<div class="group-container">';
                $html .= '<h5>Group ' . $groupName . '</h5>';
                $html .= $this->renderGroupMatches($groupData);
                $html .= '</div>';
            }
            
            $html .= '</div>';
        }
        
        // Knockout stage
        if (isset($this->bracket_data['knockout_stage'])) {
            $html .= '<div class="knockout-stage">';
            $html .= '<h4>Knockout Stage</h4>';
            $html .= $this->renderSingleEliminationRounds($this->bracket_data['knockout_stage']);
            $html .= '</div>';
        }
        
        $html .= '</div>';
        return $html;
    }
    
    private function renderGroupMatches($groupData) {
        $html = '<div class="group-matches">';
        
        if (isset($groupData['rounds'][0]['matches'])) {
            foreach ($groupData['rounds'][0]['matches'] as $match) {
                $html .= $this->renderMatch($match);
            }
        }
        
        $html .= '</div>';
        return $html;
    }
    
    private function renderStandingsTable() {
        $html = '<div class="standings-table">';
        $html .= '<h5>Standings</h5>';
        $html .= '<table class="table table-sm">';
        $html .= '<thead>';
        $html .= '<tr><th>Pos</th><th>Team</th><th>P</th><th>W</th><th>D</th><th>L</th><th>Pts</th></tr>';
        $html .= '</thead>';
        $html .= '<tbody>';
        
        // This would be populated with actual standings data
        $html .= '<tr><td colspan="7">Standings will be calculated after matches</td></tr>';
        
        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';
        return $html;
    }
    
    private function getStatusClass($status) {
        switch ($status) {
            case 'completed': return 'sc-match-completed';
            case 'in_progress': return 'sc-match-active';
            case 'pending': return 'sc-match-pending';
            default: return 'sc-match-pending';
        }
    }

    private function getStatusIcon($status) {
        switch ($status) {
            case 'completed': return 'check-circle';
            case 'in_progress': return 'play-circle';
            case 'pending': return 'clock';
            default: return 'clock';
        }
    }

    private function renderDoubleElimination() {
        $html = '<div class="sc-bracket-container double-elimination">';
        $html .= '<div class="sc-bracket-notice">';
        $html .= '<i class="fas fa-info-circle"></i>';
        $html .= 'Double Elimination format - Teams get a second chance in the losers bracket';
        $html .= '</div>';

        // For now, render as single elimination - can be enhanced later
        if (isset($this->bracket_data['rounds'])) {
            foreach ($this->bracket_data['rounds'] as $round) {
                $html .= $this->renderRound($round);
            }
        }

        $html .= '</div>';
        return $html;
    }

    private function renderGenericBracket() {
        $html = '<div class="sc-bracket-container generic">';
        $html .= '<div class="sc-bracket-notice">';
        $html .= '<i class="fas fa-trophy"></i>';
        $html .= 'Tournament bracket will be displayed here';
        $html .= '</div>';
        $html .= '</div>';
        return $html;
    }

    private function renderSingleEliminationRounds($rounds) {
        $html = '<div class="sc-knockout-rounds">';

        if (isset($rounds['rounds'])) {
            foreach ($rounds['rounds'] as $round) {
                $html .= $this->renderRound($round);
            }
        }

        $html .= '</div>';
        return $html;
    }
    
    public function renderMatchEditModal() {
        return '
        <div class="modal fade" id="editMatchModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Match Result</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="editMatchForm">
                            <input type="hidden" id="editMatchId" name="match_id">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Team 1</h6>
                                    <div class="form-group">
                                        <label>Team Name</label>
                                        <select class="form-control" id="editTeam1" name="team1_id">
                                            <option value="">Select Team</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>Score</label>
                                        <input type="number" class="form-control" id="editTeam1Score" name="team1_score" min="0">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6>Team 2</h6>
                                    <div class="form-group">
                                        <label>Team Name</label>
                                        <select class="form-control" id="editTeam2" name="team2_id">
                                            <option value="">Select Team</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>Score</label>
                                        <input type="number" class="form-control" id="editTeam2Score" name="team2_score" min="0">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>Winner</label>
                                <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                                    <label class="btn btn-outline-primary">
                                        <input type="radio" name="winner" value="team1"> Team 1
                                    </label>
                                    <label class="btn btn-outline-secondary">
                                        <input type="radio" name="winner" value="tie"> Tie
                                    </label>
                                    <label class="btn btn-outline-primary">
                                        <input type="radio" name="winner" value="team2"> Team 2
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>Match Status</label>
                                <select class="form-control" name="status">
                                    <option value="pending">Pending</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="sendToReferee" name="send_to_referee">
                                    <label class="custom-control-label" for="sendToReferee">
                                        Send to Referee for Live Scoring
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="saveMatchResult()">Save Changes</button>
                    </div>
                </div>
            </div>
        </div>';
    }
}
?>
