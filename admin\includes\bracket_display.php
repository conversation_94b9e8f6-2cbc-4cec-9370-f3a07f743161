<?php
/**
 * Interactive Bracket Display Component
 * Renders tournament brackets with match editing capabilities
 */

class BracketDisplay {
    private $conn;
    private $tournament_id;
    private $bracket_data;
    
    public function __construct($conn, $tournament_id) {
        $this->conn = $conn;
        $this->tournament_id = $tournament_id;
        $this->loadBracketData();
    }
    
    private function loadBracketData() {
        $stmt = $this->conn->prepare("
            SELECT ts.*, tf.name as format_name, tf.code as format_code
            FROM tournament_structures ts
            LEFT JOIN tournament_formats tf ON ts.tournament_format_id = tf.id
            WHERE ts.id = ?
        ");
        $stmt->execute([$this->tournament_id]);
        $tournament = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($tournament && $tournament['bracket_data']) {
            $this->bracket_data = json_decode($tournament['bracket_data'], true);
        } else {
            $this->bracket_data = null;
        }
    }
    
    public function renderBracket() {
        if (!$this->bracket_data) {
            return $this->renderEmptyBracket();
        }
        
        $format = $this->bracket_data['format'] ?? 'single_elimination';
        
        switch ($format) {
            case 'single_elimination':
                return $this->renderSingleElimination();
            case 'double_elimination':
                return $this->renderDoubleElimination();
            case 'round_robin':
                return $this->renderRoundRobin();
            case 'multi_stage':
                return $this->renderMultiStage();
            default:
                return $this->renderGenericBracket();
        }
    }
    
    private function renderEmptyBracket() {
        return '
        <div class="empty-bracket">
            <div class="empty-state">
                <i class="fas fa-sitemap"></i>
                <h4>No Tournament Bracket</h4>
                <p>Tournament bracket will appear here once generated</p>
                <button class="btn btn-primary" onclick="generateTournament()">
                    <i class="fas fa-play"></i> Generate Tournament
                </button>
            </div>
        </div>';
    }
    
    private function renderSingleElimination() {
        $html = '<div class="bracket-container single-elimination">';
        
        if (isset($this->bracket_data['rounds'])) {
            foreach ($this->bracket_data['rounds'] as $round) {
                $html .= $this->renderRound($round);
            }
        }
        
        $html .= '</div>';
        return $html;
    }
    
    private function renderRound($round) {
        $html = '<div class="bracket-round" data-round="' . $round['round_number'] . '">';
        $html .= '<div class="round-header">';
        $html .= '<h5>' . htmlspecialchars($round['round_name']) . '</h5>';
        $html .= '</div>';
        $html .= '<div class="round-matches">';
        
        foreach ($round['matches'] as $match) {
            $html .= $this->renderMatch($match);
        }
        
        $html .= '</div>';
        $html .= '</div>';
        return $html;
    }
    
    private function renderMatch($match) {
        $matchId = $match['id'] ?? uniqid();
        $status = $match['status'] ?? 'pending';
        $statusClass = $this->getStatusClass($status);
        
        $html = '<div class="match-card ' . $statusClass . '" data-match-id="' . $matchId . '">';
        
        // Match header with edit button
        $html .= '<div class="match-header">';
        $html .= '<span class="match-number">Match ' . ($match['match_number'] ?? '') . '</span>';
        $html .= '<div class="match-actions">';
        $html .= '<button class="btn btn-sm btn-primary edit-match-btn" onclick="editMatch(\'' . $matchId . '\')">';
        $html .= '<i class="fas fa-edit"></i>';
        $html .= '</button>';
        $html .= '</div>';
        $html .= '</div>';
        
        // Team 1
        $html .= '<div class="match-team team1 ' . ($match['winner_id'] == $match['team1_id'] ? 'winner' : '') . '">';
        $html .= '<div class="team-info">';
        $html .= '<span class="team-name">' . htmlspecialchars($match['team1_name'] ?? 'TBD') . '</span>';
        if (isset($match['team1_score'])) {
            $html .= '<span class="team-score">' . $match['team1_score'] . '</span>';
        }
        $html .= '</div>';
        $html .= '</div>';
        
        // VS separator
        $html .= '<div class="match-vs">VS</div>';
        
        // Team 2
        $html .= '<div class="match-team team2 ' . ($match['winner_id'] == $match['team2_id'] ? 'winner' : '') . '">';
        $html .= '<div class="team-info">';
        $html .= '<span class="team-name">' . htmlspecialchars($match['team2_name'] ?? 'TBD') . '</span>';
        if (isset($match['team2_score'])) {
            $html .= '<span class="team-score">' . $match['team2_score'] . '</span>';
        }
        $html .= '</div>';
        $html .= '</div>';
        
        // Match status
        $html .= '<div class="match-status">';
        $html .= '<span class="status-badge status-' . $status . '">' . ucfirst($status) . '</span>';
        $html .= '</div>';
        
        $html .= '</div>';
        return $html;
    }
    
    private function renderRoundRobin() {
        $html = '<div class="bracket-container round-robin">';
        $html .= '<div class="round-robin-table">';
        
        // Standings table
        $html .= $this->renderStandingsTable();
        
        // Matches list
        $html .= '<div class="matches-section">';
        $html .= '<h5>Matches</h5>';
        
        if (isset($this->bracket_data['rounds'][0]['matches'])) {
            foreach ($this->bracket_data['rounds'][0]['matches'] as $match) {
                $html .= $this->renderMatch($match);
            }
        }
        
        $html .= '</div>';
        $html .= '</div>';
        $html .= '</div>';
        return $html;
    }
    
    private function renderMultiStage() {
        $html = '<div class="bracket-container multi-stage">';
        
        // Group stage
        if (isset($this->bracket_data['group_stage'])) {
            $html .= '<div class="group-stage">';
            $html .= '<h4>Group Stage</h4>';
            
            foreach ($this->bracket_data['group_stage'] as $groupName => $groupData) {
                $html .= '<div class="group-container">';
                $html .= '<h5>Group ' . $groupName . '</h5>';
                $html .= $this->renderGroupMatches($groupData);
                $html .= '</div>';
            }
            
            $html .= '</div>';
        }
        
        // Knockout stage
        if (isset($this->bracket_data['knockout_stage'])) {
            $html .= '<div class="knockout-stage">';
            $html .= '<h4>Knockout Stage</h4>';
            $html .= $this->renderSingleEliminationRounds($this->bracket_data['knockout_stage']);
            $html .= '</div>';
        }
        
        $html .= '</div>';
        return $html;
    }
    
    private function renderGroupMatches($groupData) {
        $html = '<div class="group-matches">';
        
        if (isset($groupData['rounds'][0]['matches'])) {
            foreach ($groupData['rounds'][0]['matches'] as $match) {
                $html .= $this->renderMatch($match);
            }
        }
        
        $html .= '</div>';
        return $html;
    }
    
    private function renderStandingsTable() {
        $html = '<div class="standings-table">';
        $html .= '<h5>Standings</h5>';
        $html .= '<table class="table table-sm">';
        $html .= '<thead>';
        $html .= '<tr><th>Pos</th><th>Team</th><th>P</th><th>W</th><th>D</th><th>L</th><th>Pts</th></tr>';
        $html .= '</thead>';
        $html .= '<tbody>';
        
        // This would be populated with actual standings data
        $html .= '<tr><td colspan="7">Standings will be calculated after matches</td></tr>';
        
        $html .= '</tbody>';
        $html .= '</table>';
        $html .= '</div>';
        return $html;
    }
    
    private function getStatusClass($status) {
        switch ($status) {
            case 'completed': return 'match-completed';
            case 'in_progress': return 'match-active';
            case 'pending': return 'match-pending';
            default: return 'match-pending';
        }
    }
    
    public function renderMatchEditModal() {
        return '
        <div class="modal fade" id="editMatchModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Edit Match Result</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="editMatchForm">
                            <input type="hidden" id="editMatchId" name="match_id">
                            
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>Team 1</h6>
                                    <div class="form-group">
                                        <label>Team Name</label>
                                        <select class="form-control" id="editTeam1" name="team1_id">
                                            <option value="">Select Team</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>Score</label>
                                        <input type="number" class="form-control" id="editTeam1Score" name="team1_score" min="0">
                                    </div>
                                </div>
                                
                                <div class="col-md-6">
                                    <h6>Team 2</h6>
                                    <div class="form-group">
                                        <label>Team Name</label>
                                        <select class="form-control" id="editTeam2" name="team2_id">
                                            <option value="">Select Team</option>
                                        </select>
                                    </div>
                                    <div class="form-group">
                                        <label>Score</label>
                                        <input type="number" class="form-control" id="editTeam2Score" name="team2_score" min="0">
                                    </div>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>Winner</label>
                                <div class="btn-group btn-group-toggle w-100" data-toggle="buttons">
                                    <label class="btn btn-outline-primary">
                                        <input type="radio" name="winner" value="team1"> Team 1
                                    </label>
                                    <label class="btn btn-outline-secondary">
                                        <input type="radio" name="winner" value="tie"> Tie
                                    </label>
                                    <label class="btn btn-outline-primary">
                                        <input type="radio" name="winner" value="team2"> Team 2
                                    </label>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label>Match Status</label>
                                <select class="form-control" name="status">
                                    <option value="pending">Pending</option>
                                    <option value="in_progress">In Progress</option>
                                    <option value="completed">Completed</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <div class="custom-control custom-checkbox">
                                    <input type="checkbox" class="custom-control-input" id="sendToReferee" name="send_to_referee">
                                    <label class="custom-control-label" for="sendToReferee">
                                        Send to Referee for Live Scoring
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="saveMatchResult()">Save Changes</button>
                    </div>
                </div>
            </div>
        </div>';
    }
}
?>
